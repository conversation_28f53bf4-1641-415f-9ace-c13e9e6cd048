/**
 * Background Removal Page - Refactored
 * Clean, optimized implementation using custom hooks and components
 */

import React, { useState } from "react";
import { <PERSON> } from "react-router-dom";
import { ArrowLeft, ImageIcon, Info } from "lucide-react";
import { Button } from "@/components/ui/button";
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from "@/components/ui/tooltip";
import { Tabs, TabsList, TabsTrigger, TabsContent } from "@/components/ui/tabs";
import DashboardSidebar from "@/components/DashboardSidebar";
import BackgroundRemovalUpload from "@/components/BackgroundRemovalUpload";
import BackgroundRemovalPreview from "@/components/BackgroundRemovalPreview";
import BackgroundRemovalResult from "@/components/BackgroundRemovalResult";
import BackgroundRemovalUseCases from "@/components/BackgroundRemovalUseCases";
import { useImageUpload } from "@/hooks/useImageUpload";
import { useBackgroundRemoval } from "@/hooks/useBackgroundRemoval";
import { DEFAULTS, MESSAGES } from "@/constants/backgroundRemoval";

const BackgroundRemovalPage: React.FC = () => {
  const [activeTab, setActiveTab] = useState<string>(DEFAULTS.ACTIVE_TAB);

  // Custom hooks for functionality
  const {
    sourceImage,
    isUploading,
    uploadError,
    fileInputRef,
    handleFileUpload,
    triggerFileInput,
    resetUpload,
    getBase64Data,
  } = useImageUpload();

  const {
    resultImage,
    isProcessing,
    processingError,
    processImage,
    resetResult,
    downloadResult,
  } = useBackgroundRemoval();

  // Handle image processing
  const handleProcessImage = async () => {
    const base64Data = await getBase64Data();
    if (base64Data) {
      await processImage(base64Data);
      setActiveTab("result");
    }
  };

  // Handle complete reset
  const handleCompleteReset = () => {
    resetUpload();
    resetResult();
    setActiveTab(DEFAULTS.ACTIVE_TAB);
  };

  return (
    <>
      <DashboardSidebar />

      <main className="ml-64 min-h-screen bg-white dark:bg-gray-900 overflow-auto">
        <div className="container mx-auto px-4 py-6">
          {/* Tool header */}
          <div className="flex items-center mb-4">
            <div className="flex items-center gap-2">
              <Link
                to="/advanced-image-tools"
                className="flex items-center text-gray-500 hover:text-gray-700 transition-colors"
              >
                <ArrowLeft size={16} />
              </Link>
              <div className="h-6 w-6 rounded-full bg-purple-100 dark:bg-purple-900/30 flex items-center justify-center">
                <ImageIcon size={14} className="text-brand-purple" />
              </div>
              <h2 className="text-sm font-medium text-brand-purple">Remove Image Background</h2>
              <TooltipProvider>
                <Tooltip>
                  <TooltipTrigger asChild>
                    <Button variant="ghost" size="sm" className="h-6 w-6 rounded-full p-0 ml-1">
                      <Info size={12} className="text-gray-400" />
                    </Button>
                  </TooltipTrigger>
                  <TooltipContent side="right" className="max-w-sm">
                    <div className="text-xs">
                      <p>{MESSAGES.INFO.SELECT_IMAGE_PROMPT}</p>
                    </div>
                  </TooltipContent>
                </Tooltip>
              </TooltipProvider>
            </div>
          </div>

          {/* Main tool area */}
          <div className="mb-6">
            <Tabs value={activeTab} onValueChange={setActiveTab}>
              <TabsList className="grid grid-cols-2 w-full max-w-md mx-auto mb-4">
                <TabsTrigger value="upload" disabled={activeTab === "result"}>
                  Upload & Preview
                </TabsTrigger>
                <TabsTrigger value="result" disabled={!resultImage}>
                  Result
                </TabsTrigger>
              </TabsList>

              <TabsContent value="upload" className="m-0">
                <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                  {/* Upload section */}
                  <BackgroundRemovalUpload
                    sourceImage={sourceImage}
                    isUploading={isUploading}
                    uploadError={uploadError}
                    fileInputRef={fileInputRef}
                    onFileUpload={handleFileUpload}
                    onTriggerFileInput={triggerFileInput}
                    onReset={handleCompleteReset}
                  />

                  {/* Preview section */}
                  <BackgroundRemovalPreview
                    sourceImage={sourceImage}
                    isProcessing={isProcessing}
                    onProcess={handleProcessImage}
                    onReset={handleCompleteReset}
                  />
                </div>

                {/* Show processing error if any */}
                {processingError && (
                  <div className="mt-4 p-3 bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded text-sm text-red-600 dark:text-red-400">
                    {processingError}
                  </div>
                )}
              </TabsContent>

              <TabsContent value="result" className="m-0">
                <BackgroundRemovalResult
                  resultImage={resultImage}
                  onDownload={downloadResult}
                  onStartOver={handleCompleteReset}
                />
              </TabsContent>
            </Tabs>
          </div>

          {/* Use cases */}
          <BackgroundRemovalUseCases />
        </div>
      </main>
    </>
  );
};

export default BackgroundRemovalPage;
