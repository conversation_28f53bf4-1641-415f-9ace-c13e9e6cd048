import React, { useState } from "react";
import DashboardSidebar from "@/components/DashboardSidebar";
import {
  Palette,
  ArrowLeft,
  Info,
  Download,
  Trash2
} from "lucide-react";
import { But<PERSON> } from "@/components/ui/button";
import { Link } from "react-router-dom";
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from "@/components/ui/tooltip";
import { Tabs, TabsList, TabsTrigger, TabsContent } from "@/components/ui/tabs";
import { useColorGuidedGeneration } from "@/hooks/useColorGuidedGeneration";
import { useColorPalette } from "@/hooks/useColorPalette";
import ColorPaletteManager from "@/components/ColorPaletteManager";
import ColorGuidedForm from "@/components/ColorGuidedForm";
import ColorGuidedResult from "@/components/ColorGuidedResult";
import ColorGuidedUseCases from "@/components/ColorGuidedUseCases";
import { COLOR_GUIDED_DEFAULTS } from "@/constants/colorGuided";

const ColorGuidedGenerationPage = () => {
  const [activeTab, setActiveTab] = useState("colors");
  const [prompt, setPrompt] = useState("");
  const [negativePrompt, setNegativePrompt] = useState<string>(COLOR_GUIDED_DEFAULTS.DEFAULT_NEGATIVE_PROMPT);

  // Custom hooks
  const colorPalette = useColorPalette();
  const colorGeneration = useColorGuidedGeneration({
    onSuccess: () => setActiveTab("result")
  });

  const handleGenerate = () => {
    colorGeneration.generateImage(
      prompt,
      colorPalette.getColorValues(),
      negativePrompt
    );
  };

  const handleReset = () => {
    colorGeneration.reset();
    colorPalette.resetColors();
    setPrompt("");
    setNegativePrompt(COLOR_GUIDED_DEFAULTS.DEFAULT_NEGATIVE_PROMPT);
    setActiveTab("colors");
  };

  const downloadImage = () => {
    if (!colorGeneration.resultImage) return;
    const link = document.createElement('a');
    link.href = colorGeneration.resultImage;
    link.download = `color-guided-${Date.now()}.png`;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  };

  return (
    <>
      <DashboardSidebar />
      <main className="ml-64 min-h-screen bg-white dark:bg-gray-900 overflow-y-auto">
        <div className="container mx-auto px-4 py-6">
          {/* Tool header */}
          <div className="flex items-center justify-between mb-4">
            <div className="flex items-center gap-2">
              <Link
                to="/advanced-image-tools"
                className="flex items-center text-gray-500 hover:text-gray-700 transition-colors"
              >
                <ArrowLeft size={16} />
              </Link>
              <div className="h-6 w-6 rounded-full bg-pink-100 dark:bg-pink-900/30 flex items-center justify-center">
                <Palette size={14} className="text-brand-purple" />
              </div>
              <h2 className="text-sm font-medium text-brand-purple">Color-Guided Generation</h2>
              <TooltipProvider>
                <Tooltip>
                  <TooltipTrigger asChild>
                    <Button variant="ghost" size="sm" className="h-6 w-6 rounded-full p-0 ml-1">
                      <Info size={12} className="text-gray-400" />
                    </Button>
                  </TooltipTrigger>
                  <TooltipContent side="right" className="max-w-sm">
                    <div className="text-xs">
                      <p>Generate images that follow your specific color palette.</p>
                    </div>
                  </TooltipContent>
                </Tooltip>
              </TooltipProvider>
            </div>
            <div className="flex items-center gap-2">
              {activeTab === "result" && colorGeneration.resultImage && (
                <>
                  <Button
                    size="sm"
                    variant="outline"
                    className="h-8 text-xs gap-1"
                    onClick={handleReset}
                  >
                    <Trash2 size={12} />
                    Start Over
                  </Button>
                  <Button
                    size="sm"
                    className="h-8 text-xs gap-1 bg-brand-purple hover:bg-brand-purple/90"
                    onClick={downloadImage}
                  >
                    <Download size={12} />
                    Download
                  </Button>
                </>
              )}
            </div>
          </div>

          {/* Use Cases Section */}
          <ColorGuidedUseCases />

          {/* Main tool area */}
          <div className="mb-6">
            <Tabs value={activeTab} onValueChange={setActiveTab}>
              <TabsList className="grid grid-cols-2 w-full max-w-md mx-auto mb-4">
                <TabsTrigger value="colors" disabled={activeTab === "result"}>
                  Colors & Prompt
                </TabsTrigger>
                <TabsTrigger value="result" disabled={!colorGeneration.resultImage}>
                  Result
                </TabsTrigger>
              </TabsList>

              <TabsContent value="colors" className="m-0">
                <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                  {/* Left Column: Color Palette (1/3 width on md screens) */}
                  <div className="space-y-6 md:col-span-1">
                    <ColorPaletteManager
                      colors={colorPalette.colors}
                      onColorChange={colorPalette.handleColorChange}
                      onAddColor={() => colorPalette.addColor()}
                      onRemoveColor={colorPalette.removeColor}
                      canAddColor={colorPalette.canAddColor()}
                      canRemoveColor={colorPalette.canRemoveColor()}
                    />
                  </div>

                  {/* Right Column: Prompts and Generate Button (2/3 width on md screens) */}
                  <div className="space-y-6 md:col-span-2">
                    <ColorGuidedForm
                      prompt={prompt}
                      negativePrompt={negativePrompt}
                      colors={colorPalette.colors}
                      isProcessing={colorGeneration.isProcessing}
                      onPromptChange={setPrompt}
                      onNegativePromptChange={setNegativePrompt}
                      onGenerate={handleGenerate}
                    />
                  </div>
                </div>
              </TabsContent>

              <TabsContent value="result" className="m-0">
                {colorGeneration.resultImage && (
                  <ColorGuidedResult
                    resultImage={colorGeneration.resultImage}
                    onReset={handleReset}
                  />
                )}
              </TabsContent>
            </Tabs>
          </div>
        </div>
      </main>
    </>
  );
};

export default ColorGuidedGenerationPage;
