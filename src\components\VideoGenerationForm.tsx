import React, { useState, useRef } from "react";
import { useForm } from "react-hook-form";
import { z } from "zod";
import { zodResolver } from "@hookform/resolvers/zod";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
  FormDescription,
} from "@/components/ui/form";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Textarea } from "@/components/ui/textarea";
import { Input } from "@/components/ui/input";
import { Slider } from "@/components/ui/slider";
import { Badge } from "@/components/ui/badge";
import { toast } from "sonner";
import {
  Upload,
  Video,
  Clock,
  Sparkles,
  Image as ImageIcon,
  X,
  Info,
  Wand2
} from "lucide-react";
import { generateVideo, getEstimatedProcessingTime, VideoGenerationOptions } from "@/services/videoService";
import { fileToBase64 } from "@/utils/base64Utils";
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from "@/components/ui/tooltip";
import { useAuth } from "@/contexts/AuthContext";
import { useQueryClient } from "@tanstack/react-query";

// Form validation schema
const videoFormSchema = z.object({
  prompt: z.string()
    .min(10, "Prompt must be at least 10 characters")
    .max(512, "Prompt cannot exceed 512 characters"),
  videoType: z.enum(['TEXT_VIDEO'], {
    required_error: "Please select a video type",
  }),
  seed: z.number().min(0).max(**********).optional(),
  referenceImage: z.string().optional(),
});

type VideoFormValues = z.infer<typeof videoFormSchema>;

interface VideoGenerationFormProps {
  onVideoGenerated?: (jobId: string, videoId: string, estimatedCompletionTime: string) => void;
  className?: string;
}

const VideoGenerationForm: React.FC<VideoGenerationFormProps> = ({
  onVideoGenerated,
  className
}) => {
  const { user } = useAuth();
  const queryClient = useQueryClient();
  const [isGenerating, setIsGenerating] = useState(false);
  const [referenceImagePreview, setReferenceImagePreview] = useState<string | null>(null);
  const [estimatedTime, setEstimatedTime] = useState<number>(90);
  const fileInputRef = useRef<HTMLInputElement>(null);

  const form = useForm<VideoFormValues>({
    resolver: zodResolver(videoFormSchema),
    defaultValues: {
      prompt: "",
      videoType: "TEXT_VIDEO",
      seed: Math.floor(Math.random() * 1000000),
    },
  });

  const watchedVideoType = form.watch("videoType");
  const watchedPrompt = form.watch("prompt");

  const maxChars = 512; // Fixed for single shot videos
  const charCount = (watchedPrompt || "").length;

  // Update estimated processing time when form values change
  React.useEffect(() => {
    const options: VideoGenerationOptions = {
      durationSeconds: 6, // Fixed duration for single shot
    };
    const time = getEstimatedProcessingTime(watchedVideoType, options);
    setEstimatedTime(time);
  }, [watchedVideoType]);

  // Handle reference image upload
  const handleImageUpload = async (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (!file) return;

    // Validate file type
    if (!file.type.startsWith('image/')) {
      toast.error("Please select a valid image file");
      return;
    }

    // Validate file size (max 10MB)
    if (file.size > 10 * 1024 * 1024) {
      toast.error("Image file size must be less than 10MB");
      return;
    }

    try {
      const base64 = await fileToBase64(file);
      setReferenceImagePreview(base64);
      form.setValue("referenceImage", base64);
      toast.success("Reference image uploaded successfully");
    } catch (error) {
      if (import.meta.env.DEV) {
        console.error("Error uploading image:", error);
      }
      toast.error("Failed to upload image");
    }
  };

  // Remove reference image
  const removeReferenceImage = () => {
    setReferenceImagePreview(null);
    form.setValue("referenceImage", undefined);
    if (fileInputRef.current) {
      fileInputRef.current.value = "";
    }
  };

  // Generate random seed
  const generateRandomSeed = () => {
    const randomSeed = Math.floor(Math.random() * 1000000);
    form.setValue("seed", randomSeed);
  };

  // Handle form submission
  const onSubmit = async (values: VideoFormValues) => {
    if (!user?.id) {
      toast.error("Please sign in to generate videos");
      return;
    }

    // Additional client-side validation
    if (values.prompt.trim().length < 10) {
      toast.error("Please provide a more detailed prompt (at least 10 characters)");
      return;
    }

    if (values.prompt.length > 512) {
      toast.error("Prompt is too long. Please keep it under 512 characters for single-shot videos.");
      return;
    }

    // Validate reference image if provided
    if (values.referenceImage) {
      try {
        const base64Data = values.referenceImage.replace(/^data:image\/[a-z]+;base64,/, '');
        const buffer = atob(base64Data);
        if (buffer.length > 10 * 1024 * 1024) {
          toast.error("Reference image is too large. Please use an image smaller than 10MB.");
          return;
        }
      } catch (error) {
        toast.error("Invalid reference image format. Please upload a valid image.");
        return;
      }
    }

    setIsGenerating(true);

    try {
      const options: VideoGenerationOptions = {
        seed: values.seed || Math.floor(Math.random() * 1000000),
        durationSeconds: 6, // Fixed duration for single shot videos
        referenceImage: values.referenceImage,
      };

      const result = await generateVideo(
        values.videoType,
        values.prompt,
        options,
        user.id
      );

      if (result.success && result.jobId) {
        toast.success("Video generation started! You can track the progress below.");
        onVideoGenerated?.(
          result.jobId,
          result.videoId || "",
          result.estimatedCompletionTime || ""
        );

        // Immediately refresh video history to show the new pending video
        // Use setTimeout to ensure database transaction is committed
        setTimeout(() => {
          if (user?.id) {
            try {
              queryClient.invalidateQueries({ queryKey: ['videoHistory'] });
              queryClient.invalidateQueries({ queryKey: ['videoHistory', user.id] });
              queryClient.refetchQueries({ queryKey: ['videoHistory'] });
              queryClient.refetchQueries({ queryKey: ['videoHistory', user.id] });
            } catch (queryError) {
              if (import.meta.env.DEV) {
                console.error('Failed to invalidate video history queries:', queryError);
              }
            }
          }
        }, 500); // Shorter delay for pending videos

        // Reset form
        form.reset({
          prompt: "",
          videoType: "TEXT_VIDEO",
          seed: Math.floor(Math.random() * 1000000),
        });
        removeReferenceImage();
      } else {
        // Provide more specific error messages
        const errorMessage = result.error || "Failed to start video generation";
        
        if (errorMessage.includes("prompt")) {
          toast.error("Prompt validation failed. Please check your prompt and try again.");
        } else if (errorMessage.includes("image")) {
          toast.error("Reference image validation failed. Please try a different image.");
        } else if (errorMessage.includes("rate limit") || errorMessage.includes("quota")) {
          toast.error("Rate limit exceeded. Please wait a moment before generating another video.");
        } else if (errorMessage.includes("authentication") || errorMessage.includes("unauthorized")) {
          toast.error("Authentication error. Please sign in again.");
        } else {
          toast.error(errorMessage);
        }
      }
    } catch (error) {
      if (import.meta.env.DEV) {
        console.error("Error generating video:", error);
      }
      
      // Provide more specific error handling
      if (error instanceof Error) {
        if (error.message.includes("network") || error.message.includes("fetch")) {
          toast.error("Network error. Please check your connection and try again.");
        } else if (error.message.includes("timeout")) {
          toast.error("Request timed out. Please try again.");
        } else {
          toast.error(`Generation failed: ${error.message}`);
        }
      } else {
        toast.error("An unexpected error occurred. Please try again.");
      }
    } finally {
      setIsGenerating(false);
    }
  };

  return (
    <div className={className}>
      <Form {...form}>
        <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
          {/* Video Type Selection */}
          <FormField
            control={form.control}
            name="videoType"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Video Type</FormLabel>
                <Select onValueChange={field.onChange} defaultValue={field.value}>
                  <FormControl>
                    <SelectTrigger>
                      <SelectValue placeholder="Select video type" />
                    </SelectTrigger>
                  </FormControl>
                  <SelectContent>
                    <SelectItem value="TEXT_VIDEO">
                      <div className="flex items-center gap-2">
                        <Sparkles className="h-4 w-4" />
                        <div>
                          <div className="font-medium">Single Shot Video (6s)</div>
                          <div className="text-xs text-muted-foreground">
                            AI-generated 6-second video from text
                          </div>
                        </div>
                      </div>
                    </SelectItem>
                  </SelectContent>
                </Select>
                <FormMessage />
              </FormItem>
            )}
          />

          {/* Prompt */}
          <FormField
            control={form.control}
            name="prompt"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Video Prompt</FormLabel>
                <FormControl>
                  <Textarea
                    placeholder="Describe the video you want to generate..."
                    className="min-h-[80px] resize-none"
                    {...field}
                  />
                </FormControl>
                <div className="flex justify-between text-xs text-muted-foreground">
                  <span>Describe your video in detail for best results</span>
                  <span className={charCount > maxChars * 0.9 ? "text-red-500" : ""}>
                    {charCount}/{maxChars}
                  </span>
                </div>
                <FormMessage />
              </FormItem>
            )}
          />

          {/* Bottom row - Reference Image and Seed */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            {/* Reference Image Upload */}
            {watchedVideoType === 'TEXT_VIDEO' && (
              <FormField
                control={form.control}
                name="referenceImage"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Reference Image (Optional)</FormLabel>
                    <FormControl>
                      <div className="space-y-2">
                        {!referenceImagePreview ? (
                          <div
                            className="border-2 border-dashed border-gray-300 rounded-lg p-4 text-center cursor-pointer hover:border-gray-400 transition-colors h-24"
                            onClick={() => fileInputRef.current?.click()}
                          >
                            <Upload className="h-6 w-6 mx-auto mb-1 text-gray-400" />
                            <p className="text-xs text-gray-600">Click to upload</p>
                          </div>
                        ) : (
                          <div className="relative">
                            <img
                              src={referenceImagePreview}
                              alt="Reference"
                              className="w-full h-24 object-cover rounded-lg"
                            />
                            <Button
                              type="button"
                              variant="destructive"
                              size="icon"
                              className="absolute top-1 right-1 h-5 w-5"
                              onClick={removeReferenceImage}
                            >
                              <X className="h-3 w-3" />
                            </Button>
                          </div>
                        )}
                        <input
                          ref={fileInputRef}
                          type="file"
                          accept="image/*"
                          onChange={handleImageUpload}
                          className="hidden"
                        />
                      </div>
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            )}

            {/* Seed */}
            <FormField
              control={form.control}
              name="seed"
              render={({ field }) => (
                <FormItem>
                  <FormLabel className="flex items-center gap-2">
                    Seed
                    <TooltipProvider>
                      <Tooltip>
                        <TooltipTrigger>
                          <Info className="h-3 w-3 text-muted-foreground" />
                        </TooltipTrigger>
                        <TooltipContent>
                          <p className="text-xs">
                            Use the same seed to reproduce similar results
                          </p>
                        </TooltipContent>
                      </Tooltip>
                    </TooltipProvider>
                  </FormLabel>
                  <FormControl>
                    <div className="flex gap-2">
                      <Input
                        type="number"
                        placeholder="Random seed"
                        {...field}
                        onChange={(e) => field.onChange(parseInt(e.target.value) || 0)}
                      />
                      <Button
                        type="button"
                        variant="outline"
                        onClick={generateRandomSeed}
                        className="px-3"
                      >
                        Random
                      </Button>
                    </div>
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
          </div>

          {/* Bottom row - Processing Time and Submit */}
          <div className="flex items-center justify-between gap-4">
            <div className="flex items-center gap-2 text-sm">
              <Clock className="h-4 w-4 text-blue-600" />
              <span className="font-medium">Est. Time:</span>
              <Badge variant="secondary">
                {estimatedTime < 60
                  ? `~${estimatedTime}s`
                  : `~${Math.round(estimatedTime / 60)}m`
                }
              </Badge>
            </div>

            <Button
              type="submit"
              className="px-8"
              disabled={isGenerating}
            >
              {isGenerating ? (
                <>
                  <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2" />
                  Starting...
                </>
              ) : (
                <>
                  <Video className="h-4 w-4 mr-2" />
                  Generate Video
                </>
              )}
            </Button>
          </div>
        </form>
      </Form>
    </div>
  );
};

export default VideoGenerationForm;
