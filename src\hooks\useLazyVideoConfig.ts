import { useMemo } from 'react';

interface LazyVideoConfig {
  rootMargin: string;
  threshold: number;
  enableLazyLoading: boolean;
  preloadStrategy: 'none' | 'metadata' | 'auto';
  bandwidthOptimization: boolean;
  performanceTracking: boolean;
}

interface UseLazyVideoConfigOptions {
  connectionType?: 'slow-2g' | '2g' | '3g' | '4g' | 'unknown';
  deviceMemory?: number;
  isLowEndDevice?: boolean;
  userPreferences?: {
    dataSaver?: boolean;
    autoPlay?: boolean;
    highQuality?: boolean;
  };
}

interface UseLazyVideoConfigReturn {
  config: LazyVideoConfig;
  shouldUseLazyLoading: boolean;
  estimatedBandwidthSavings: number;
  recommendedRootMargin: string;
  recommendedThreshold: number;
}

/**
 * Lazy Video Configuration Hook - Sprint 18 Phase 3
 * 
 * Provides intelligent configuration for lazy video loading based on device capabilities,
 * network conditions, and user preferences. Optimizes loading strategy for best performance.
 * 
 * Features:
 * - Adaptive configuration based on network conditions
 * - Device capability detection
 * - User preference integration
 * - Performance optimization recommendations
 * - Bandwidth savings estimation
 */
export const useLazyVideoConfig = ({
  connectionType = 'unknown',
  deviceMemory = 4,
  isLowEndDevice = false,
  userPreferences = {},
}: UseLazyVideoConfigOptions = {}): UseLazyVideoConfigReturn => {
  
  const config = useMemo<LazyVideoConfig>(() => {
    // Base configuration
    let baseConfig: LazyVideoConfig = {
      rootMargin: '200px',
      threshold: 0.1,
      enableLazyLoading: true,
      preloadStrategy: 'metadata',
      bandwidthOptimization: true,
      performanceTracking: import.meta.env.DEV,
    };

    // Adjust for network conditions
    switch (connectionType) {
      case 'slow-2g':
      case '2g':
        baseConfig = {
          ...baseConfig,
          rootMargin: '50px', // Smaller margin for slow connections
          threshold: 0.3, // Higher threshold - more of video must be visible
          preloadStrategy: 'none', // No preloading on slow connections
          bandwidthOptimization: true,
        };
        break;
      
      case '3g':
        baseConfig = {
          ...baseConfig,
          rootMargin: '100px',
          threshold: 0.2,
          preloadStrategy: 'metadata',
          bandwidthOptimization: true,
        };
        break;
      
      case '4g':
        baseConfig = {
          ...baseConfig,
          rootMargin: '300px', // Larger margin for fast connections
          threshold: 0.05, // Lower threshold - load earlier
          preloadStrategy: userPreferences.highQuality ? 'auto' : 'metadata',
          bandwidthOptimization: false,
        };
        break;
      
      default:
        // Unknown connection - use conservative defaults
        baseConfig = {
          ...baseConfig,
          rootMargin: '150px',
          threshold: 0.1,
          preloadStrategy: 'metadata',
          bandwidthOptimization: true,
        };
    }

    // Adjust for device capabilities
    if (isLowEndDevice || deviceMemory < 2) {
      baseConfig = {
        ...baseConfig,
        rootMargin: '50px', // Smaller margin for low-end devices
        threshold: 0.3, // Higher threshold
        preloadStrategy: 'none', // No preloading
        bandwidthOptimization: true,
        enableLazyLoading: true, // Always enable for low-end devices
      };
    } else if (deviceMemory >= 8) {
      baseConfig = {
        ...baseConfig,
        rootMargin: '400px', // Larger margin for high-end devices
        threshold: 0.05, // Lower threshold
        preloadStrategy: userPreferences.highQuality ? 'auto' : 'metadata',
      };
    }

    // Apply user preferences
    if (userPreferences.dataSaver) {
      baseConfig = {
        ...baseConfig,
        rootMargin: '25px', // Very small margin for data saver
        threshold: 0.5, // High threshold - must be mostly visible
        preloadStrategy: 'none',
        bandwidthOptimization: true,
        enableLazyLoading: true,
      };
    }

    if (userPreferences.autoPlay === false) {
      baseConfig = {
        ...baseConfig,
        preloadStrategy: 'none', // No preloading if autoplay is disabled
      };
    }

    return baseConfig;
  }, [connectionType, deviceMemory, isLowEndDevice, userPreferences]);

  const shouldUseLazyLoading = useMemo(() => {
    // Always use lazy loading for:
    // - Slow connections
    // - Low-end devices
    // - Data saver mode
    // - Unknown connection types (conservative approach)
    
    if (userPreferences.dataSaver) return true;
    if (isLowEndDevice) return true;
    if (connectionType === 'slow-2g' || connectionType === '2g') return true;
    if (connectionType === 'unknown') return true;
    
    // For fast connections and high-end devices, still use lazy loading
    // but with more aggressive preloading
    return config.enableLazyLoading;
  }, [config.enableLazyLoading, connectionType, isLowEndDevice, userPreferences.dataSaver]);

  const estimatedBandwidthSavings = useMemo(() => {
    // Estimate bandwidth savings based on configuration
    // These are rough estimates based on typical video sizes
    
    const averageVideoSize = 5 * 1024 * 1024; // 5MB average video
    let savingsMultiplier = 0;

    if (!shouldUseLazyLoading) {
      return 0; // No savings if lazy loading is disabled
    }

    // Base savings from lazy loading (videos not in viewport)
    savingsMultiplier += 0.6; // Assume 60% of videos are not immediately visible

    // Additional savings from preload strategy
    switch (config.preloadStrategy) {
      case 'none':
        savingsMultiplier += 0.3; // Additional 30% savings from no preloading
        break;
      case 'metadata':
        savingsMultiplier += 0.1; // Small additional savings
        break;
      case 'auto':
        // No additional savings, might even increase usage
        break;
    }

    // Bandwidth optimization savings
    if (config.bandwidthOptimization) {
      savingsMultiplier += 0.1; // 10% additional savings from optimization
    }

    return Math.min(averageVideoSize * savingsMultiplier, averageVideoSize * 0.9);
  }, [shouldUseLazyLoading, config.preloadStrategy, config.bandwidthOptimization]);

  const recommendedRootMargin = useMemo(() => {
    return config.rootMargin;
  }, [config.rootMargin]);

  const recommendedThreshold = useMemo(() => {
    return config.threshold;
  }, [config.threshold]);

  return {
    config,
    shouldUseLazyLoading,
    estimatedBandwidthSavings,
    recommendedRootMargin,
    recommendedThreshold,
  };
};

export default useLazyVideoConfig;
