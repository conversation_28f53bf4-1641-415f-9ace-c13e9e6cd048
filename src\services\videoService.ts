/**
 * Service for managing video generation and history
 * Handles API calls to the video generation backend endpoints
 */

import { supabase } from '@/lib/supabase'; // Import supabase
import { generateVideoPresignedUrlsBatch, videoBatchResultsToUrlMap } from '@/services/videoPresignedUrlService';
import { invalidateVideoCache } from '@/services/videoCacheInvalidation';

// Types for video generation
export interface VideoGenerationOptions {
  seed?: number;
  durationSeconds?: number;
  referenceImage?: string;
  shots?: VideoShot[];
  fps?: number; // Frame rate (Nova Reel only supports 24 FPS)
  dimension?: string; // Video dimensions (Nova Reel only supports '1280x720')
}

export interface VideoShot {
  text: string;
  prompt?: string; // Add prompt field for compatibility
  referenceImageKey?: string;
  referenceImageFormat?: 'png' | 'jpeg';
  seed?: number;
}

export interface VideoGenerationResult {
  success: boolean;
  jobId?: string;
  videoId?: string;
  estimatedCompletionTime?: string;
  message?: string;
  error?: string;
}

export interface VideoStatusResult {
  success: boolean;
  status: 'pending' | 'processing' | 'completed' | 'failed';
  jobId: string;
  videoUrl?: string;
  videoId?: string;
  error?: string;
}

export interface VideoHistoryItem {
  id: string;
  created_at: string;
  user_id: string;
  job_id: string;
  prompt: string;
  video_type: 'text-to-video' | 'multi-shot-auto' | 'multi-shot-manual';
  status: 'pending' | 'processing' | 'completed' | 'failed';
  duration_seconds: number;
  s3_key?: string;
  s3_url?: string;
  presigned_url?: string;
  parameters?: {
    taskType: string;
    options: VideoGenerationOptions;
    seed?: number;
  };
  reference_images?: string[];
  error_message?: string;
  estimated_completion_at?: string;
  completed_at?: string;
  video_shots?: VideoShotRecord[];
}

export interface VideoShotRecord {
  id: string;
  shot_number: number;
  prompt: string;
  reference_image_s3_key?: string;
  reference_image_format?: string;
}

/**
 * Generate a video using AWS Bedrock Nova Reel
 * @param taskType - The type of video generation task
 * @param prompt - The text prompt for video generation
 * @param options - Additional options for video generation
 * @param userId - The ID of the user generating the video
 * @returns A promise that resolves to the video generation result
 */
export const generateVideo = async (
  taskType: 'TEXT_VIDEO' | 'MULTI_SHOT_AUTOMATED' | 'MULTI_SHOT_MANUAL',
  prompt: string,
  options: VideoGenerationOptions = {},
  userId: string
): Promise<VideoGenerationResult> => {
  try {
    // Transform shots to match backend validation schema
    let transformedShots = undefined;
    if (options.shots && taskType === 'MULTI_SHOT_MANUAL') {
      transformedShots = options.shots.map((shot, index) => ({
        prompt: shot.text || shot.prompt, // Handle both 'text' and 'prompt' fields
        text: shot.text || shot.prompt, // Ensure both fields are available
        order: index + 1, // Backend expects 'order' field starting from 1
        referenceImage: shot.referenceImageKey, // Backend expects 'referenceImage', not 'referenceImageKey'
        referenceImageKey: shot.referenceImageKey, // Keep for backward compatibility
        referenceImageFormat: shot.referenceImageFormat || 'png',
        seed: shot.seed || Math.floor(Math.random() * 1000000)
      }));
    }

    const { data: { session } } = await supabase.auth.getSession();
    const token = session?.access_token;
    const headers: HeadersInit = {
      'Content-Type': 'application/json',
    };
    if (token) {
      headers['Authorization'] = `Bearer ${token}`;
    }

    const apiUrl = import.meta.env.VITE_API_URL || 'http://localhost:3001';
    const response = await fetch(`${apiUrl}/api/generate-video`, {
      method: 'POST',
      headers: headers,
      body: JSON.stringify({
        videoType: taskType, // Backend expects 'videoType', not 'taskType'
        prompt,
        duration: options.durationSeconds || 6, // Backend expects 'duration' at root level
        seed: options.seed,
        referenceImage: options.referenceImage,
        shots: transformedShots,
        userId
      }),
    });

    const result = await response.json();

    if (!response.ok) {
      throw new Error(result.error || 'Failed to generate video');
    }

    return result;
  } catch (error) {
    if (import.meta.env.DEV) {
      console.error('Error generating video:', error);
    }
    return {
      success: false,
      error: error instanceof Error ? error.message : 'An unexpected error occurred'
    };
  }
};

/**
 * Check the status of a video generation job
 * @param jobId - The job ID to check
 * @param userId - The ID of the user (optional, for database updates)
 * @returns A promise that resolves to the video status result
 */
export const checkVideoStatus = async (
  jobId: string,
  userId?: string
): Promise<VideoStatusResult> => {
  try {
    // Encode the jobId properly for URL
    const encodedJobId = encodeURIComponent(jobId);
    const apiUrl = import.meta.env.VITE_API_URL || 'http://localhost:3001';
    const url = new URL(`/api/video-status/${encodedJobId}`, apiUrl);
    if (userId) {
      url.searchParams.append('userId', userId);
    }
    if (import.meta.env.DEV) {
      console.log(`[videoService.checkVideoStatus] Fetching URL: ${url.toString()}`);
    }

    const { data: { session } } = await supabase.auth.getSession();
    const token = session?.access_token;
    const headers: HeadersInit = {};
    if (token) {
      headers['Authorization'] = `Bearer ${token}`;
    }

    // Add timeout to prevent hanging requests
    const controller = new AbortController();
    const timeoutId = setTimeout(() => {
      if (import.meta.env.DEV) {
        console.log(`[videoService.checkVideoStatus] Request timeout for job ${jobId}`);
      }
      controller.abort();
    }, 30000); // 30 second timeout

    try {
      const response = await fetch(url.toString(), {
        headers,
        signal: controller.signal
      });
      clearTimeout(timeoutId);
      
      if (import.meta.env.DEV) {
        console.log(`[videoService.checkVideoStatus] Response received for job ${jobId}, status: ${response.status}`);
      }
      
      const result = await response.json();

      if (!response.ok) {
        throw new Error(result.error || 'Failed to check video status');
      }

      return result;
    } catch (fetchError) {
      clearTimeout(timeoutId);
      throw fetchError;
    }
  } catch (error) {
    if (import.meta.env.DEV) {
      console.error(`[videoService.checkVideoStatus] Error for job ${jobId}:`, error);
    }
    return {
      success: false,
      status: 'failed',
      jobId,
      error: error instanceof Error ? error.message : 'An unexpected error occurred'
    };
  }
};

/**
 * Get video generation history for a user with Phase 1 Video Caching
 * @param userId - The ID of the user
 * @param limit - The maximum number of videos to return (default: 50)
 * @param videoType - Optional filter by video type
 * @param status - Optional filter by status
 * @param useVideoCache - Enable Phase 1 video caching strategy (default: true)
 * @returns A promise that resolves to an array of video history items with presigned URLs
 */
export const getVideoHistory = async (
  userId: string,
  limit: number = 50,
  videoType?: 'text-to-video' | 'multi-shot-auto' | 'multi-shot-manual',
  status?: 'pending' | 'processing' | 'completed' | 'failed',
  useVideoCache: boolean = true
): Promise<VideoHistoryItem[]> => {
  try {
    const apiUrl = import.meta.env.VITE_API_URL || 'http://localhost:3001';
    const url = new URL('/api/video-history', apiUrl);
    url.searchParams.append('userId', userId);
    url.searchParams.append('limit', limit.toString());

    if (videoType) {
      url.searchParams.append('videoType', videoType);
    }

    if (status) {
      url.searchParams.append('status', status);
    }

    const { data: { session } } = await supabase.auth.getSession();
    const token = session?.access_token;
    const headers: HeadersInit = {};
    if (token) {
      headers['Authorization'] = `Bearer ${token}`;
    }

    const response = await fetch(url.toString(), { headers });
    const result = await response.json();

    if (!response.ok) {
      throw new Error(result.error || 'Failed to fetch video history');
    }

    const videos = result.videos || [];

    // Phase 1: Apply video caching system if enabled
    if (useVideoCache && videos.length > 0) {
      if (import.meta.env.DEV) {
        console.log(`[VideoService] Fetching presigned URLs for ${videos.length} videos using video caching system`);
      }

      try {
        // Extract s3_keys and create video metadata map
        const s3_keys: string[] = [];
        const fallbackUrls: Record<string, string> = {};
        const videoMetadata: Record<string, { type?: string; duration?: number; fileSize?: number }> = {};

        videos.forEach(video => {
          if (video.s3_key && video.status === 'completed') {
            s3_keys.push(video.s3_key);
            fallbackUrls[video.s3_key] = video.s3_url || ''; // Use existing s3_url as fallback
            videoMetadata[video.s3_key] = {
              type: video.video_type,
              duration: video.duration_seconds,
              fileSize: video.file_size_bytes,
            };
          } else if (import.meta.env.DEV && !video.s3_key) {
            console.warn(`[VideoService] Video with id ${video.id} has no s3_key. Cannot fetch presigned URL.`);
          }
        });

        if (s3_keys.length > 0) {
          // Use video-specific batch processing with caching
          const batchResult = await generateVideoPresignedUrlsBatch(s3_keys, {
            timeout: 8000, // Longer timeout for videos
            maxRetries: 2,
            retryDelay: 1000,
            concurrencyLimit: 6, // Lower concurrency for videos
            fallbackUrls,
            enableMetrics: import.meta.env.DEV,
            useCache: true,
            cacheTtl: 45 * 60 * 1000, // 45 minute cache TTL
            forceRefresh: false,
            videoMetadata,
          });

          // Convert results to URL map
          const presignedUrls = videoBatchResultsToUrlMap(batchResult.results);

          // Add presigned URLs to video objects
          videos.forEach(video => {
            if (video.s3_key && presignedUrls[video.s3_key]) {
              video.presigned_url = presignedUrls[video.s3_key];
            } else if (video.s3_key && fallbackUrls[video.s3_key]) {
              // Use fallback URL if presigned URL generation failed
              video.presigned_url = fallbackUrls[video.s3_key];
            }
          });

          if (import.meta.env.DEV) {
            console.log(`[VideoService] Video caching completed: ${batchResult.successCount}/${s3_keys.length} successful, ${batchResult.totalTime.toFixed(0)}ms total, ${batchResult.cacheHitRate.toFixed(1)}% cache hit rate`);
          }
        }
      } catch (error) {
        if (import.meta.env.DEV) {
          console.error('[VideoService] Video caching failed, videos will use fallback URLs:', error);
        }
        // Videos will use their existing s3_url or no URL if caching fails
      }
    }

    return videos;
  } catch (error) {
    if (import.meta.env.DEV) {
      console.error('Error fetching video history:', error);
    }
    throw error;
  }
};

/**
 * Delete a video
 * @param videoId - The ID of the video to delete
 * @param userId - The ID of the user
 * @returns A promise that resolves to the deletion result
 */
export const deleteVideo = async (
  videoId: string,
  userId: string
): Promise<{ success: boolean; message?: string; error?: string }> => {
  try {
    const apiUrl = import.meta.env.VITE_API_URL || 'http://localhost:3001';
    const url = new URL(`/api/video/${videoId}`, apiUrl);
    url.searchParams.append('userId', userId);

    const { data: { session } } = await supabase.auth.getSession();
    const token = session?.access_token;
    const headers: HeadersInit = {};
    if (token) {
      headers['Authorization'] = `Bearer ${token}`;
    }

    const response = await fetch(url.toString(), {
      method: 'DELETE',
      headers: headers,
    });

    const result = await response.json();

    if (!response.ok) {
      throw new Error(result.error || 'Failed to delete video');
    }

    // Phase 1: Invalidate video cache when video is deleted
    if (result.success) {
      try {
        await invalidateVideoCache.onVideoDeleted(videoId, userId, result.s3_key);
        if (import.meta.env.DEV) {
          console.log(`[VideoService] Cache invalidated for deleted video ${videoId}`);
        }
      } catch (cacheError) {
        if (import.meta.env.DEV) {
          console.warn('[VideoService] Failed to invalidate cache for deleted video:', cacheError);
        }
        // Don't fail the deletion if cache invalidation fails
      }
    }

    return result;
  } catch (error) {
    if (import.meta.env.DEV) {
      console.error('Error deleting video:', error);
    }
    return {
      success: false,
      error: error instanceof Error ? error.message : 'An unexpected error occurred'
    };
  }
};

/**
 * Get estimated processing time for a video generation task
 * @param taskType - The type of video generation task
 * @param options - The video generation options
 * @returns Estimated processing time in seconds
 */
export const getEstimatedProcessingTime = (
  taskType: 'TEXT_VIDEO' | 'MULTI_SHOT_AUTOMATED' | 'MULTI_SHOT_MANUAL',
  options: VideoGenerationOptions = {}
): number => {
  switch (taskType) {
    case 'TEXT_VIDEO':
      return 90; // ~90 seconds for 6-second videos
    case 'MULTI_SHOT_AUTOMATED':
      const duration = options.durationSeconds || 12;
      return Math.round((duration / 120) * 17 * 60); // 14-17 minutes for 2-minute videos
    case 'MULTI_SHOT_MANUAL':
      const shotCount = options.shots?.length || 1;
      return shotCount * 90; // ~90 seconds per shot
    default:
      return 90;
  }
};

/**
 * Format duration in seconds to human-readable string
 * @param seconds - Duration in seconds
 * @returns Formatted duration string
 */
export const formatDuration = (seconds: number): string => {
  if (seconds < 60) {
    return `${seconds}s`;
  } else if (seconds < 3600) {
    const minutes = Math.floor(seconds / 60);
    const remainingSeconds = seconds % 60;
    return remainingSeconds > 0 ? `${minutes}m ${remainingSeconds}s` : `${minutes}m`;
  } else {
    const hours = Math.floor(seconds / 3600);
    const minutes = Math.floor((seconds % 3600) / 60);
    return minutes > 0 ? `${hours}h ${minutes}m` : `${hours}h`;
  }
};

/**
 * Get video type display name
 * @param videoType - The video type
 * @returns Human-readable video type name
 */
export const getVideoTypeDisplayName = (
  videoType: 'text-to-video' | 'multi-shot-auto' | 'multi-shot-manual'
): string => {
  switch (videoType) {
    case 'text-to-video':
      return 'Text to Video';
    case 'multi-shot-auto':
      return 'Multi-Shot Auto';
    case 'multi-shot-manual':
      return 'Multi-Shot Manual';
    default:
      return 'Unknown';
  }
};

/**
 * Force S3 fallback check for processing videos
 * This function specifically checks S3 for videos stuck in processing status
 * @param userId - The ID of the user
 * @returns A promise that resolves to the fallback check result
 */
export const forceVideoFallbackCheck = async (
  userId: string
): Promise<{
  success: boolean;
  message?: string;
  totalProcessing?: number;
  recoveredVideos?: Array<{
    id: string;
    jobId: string;
    prompt: string;
    s3Key: string;
    videoUrl: string;
  }>;
  error?: string;
}> => {
  try {
    if (import.meta.env.DEV) {
      console.log(`[videoService.forceVideoFallbackCheck] Starting fallback check for user ${userId}`);
    }
    
    // Skip session check for S3 fallback - the server endpoint handles auth via middleware
    const headers: HeadersInit = {
      'Content-Type': 'application/json',
    };

    const apiUrl = import.meta.env.VITE_API_URL || 'http://localhost:3001';
    const url = `${apiUrl}/api/force-video-fallback-check`;
    
    if (import.meta.env.DEV) {
      console.log(`[videoService.forceVideoFallbackCheck] Making request to: ${url}`);
      console.log(`[videoService.forceVideoFallbackCheck] Request headers:`, headers);
      console.log(`[videoService.forceVideoFallbackCheck] Request body:`, { userId });
    }
    
    const response = await fetch(url, {
      method: 'POST',
      headers: headers,
      body: JSON.stringify({ userId }),
    });

    if (import.meta.env.DEV) {
      console.log(`[videoService.forceVideoFallbackCheck] Response status: ${response.status}`);
    }

    const result = await response.json();
    
    if (import.meta.env.DEV) {
      console.log(`[videoService.forceVideoFallbackCheck] Response result:`, result);
    }

    if (!response.ok) {
      throw new Error(result.error || 'Failed to perform fallback check');
    }

    return result;
  } catch (error) {
    if (import.meta.env.DEV) {
      console.error('[videoService.forceVideoFallbackCheck] Error:', error);
      console.error('[videoService.forceVideoFallbackCheck] Error stack:', error.stack);
    }
    return {
      success: false,
      error: error instanceof Error ? error.message : 'An unexpected error occurred'
    };
  }
};
