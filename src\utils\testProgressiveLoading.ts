/**
 * Test utilities for Sprint 17 Phase 4: Progressive Loading Strategy
 * 
 * This file contains test functions to verify that the progressive loading
 * implementation is working correctly and providing the expected performance benefits.
 */

import { progressiveLoadingService } from '@/services/progressiveLoadingService';
import { presignedUrlCache } from '@/services/presignedUrlCache';

export interface ProgressiveLoadingTestResult {
  testName: string;
  passed: boolean;
  metrics: {
    totalImages: number;
    immediatelyAvailable: number;
    cacheHitRate: number;
    enhancementTime: number;
  };
  details: string[];
  errors: string[];
}

/**
 * Test 1: Verify immediate availability of cached URLs
 */
export async function testImmediateAvailability(): Promise<ProgressiveLoadingTestResult> {
  const testName = "Immediate Availability Test";
  const details: string[] = [];
  const errors: string[] = [];
  let passed = false;

  try {
    // Setup: Add some URLs to cache
    const testKeys = ['test-image-1.jpg', 'test-image-2.jpg', 'test-image-3.jpg'];
    const testUrls = testKeys.map(key => `https://example.com/${key}`);
    
    // Cache some URLs
    testKeys.forEach((key, index) => {
      if (index < 2) { // Cache first 2 URLs
        presignedUrlCache.set(key, testUrls[index], 60000);
      }
    });

    details.push(`Cached ${2} out of ${testKeys.length} URLs`);

    // Test progressive loading
    const testItems = testKeys.map(key => ({ s3_key: key, s3_url: `fallback-${key}` }));
    const result = await progressiveLoadingService.loadImagesProgressively(testItems);

    const immediatelyAvailable = Object.keys(result.immediateUrls).length;
    const expectedCacheHits = 2;
    
    details.push(`Immediately available: ${immediatelyAvailable}/${testKeys.length}`);
    details.push(`Cache hit rate: ${result.metrics.cacheHitRate.toFixed(1)}%`);

    // Verify that cached URLs are immediately available
    passed = immediatelyAvailable === testKeys.length && result.metrics.immediatelyAvailable === expectedCacheHits;

    if (passed) {
      details.push("✅ All images available immediately (cached + fallback)");
      details.push("✅ Cached URLs returned instantly");
    } else {
      errors.push("❌ Not all images were immediately available");
    }

    return {
      testName,
      passed,
      metrics: {
        totalImages: testKeys.length,
        immediatelyAvailable: result.metrics.immediatelyAvailable,
        cacheHitRate: result.metrics.cacheHitRate,
        enhancementTime: 0,
      },
      details,
      errors,
    };
  } catch (error) {
    errors.push(`Test failed with error: ${error}`);
    return {
      testName,
      passed: false,
      metrics: { totalImages: 0, immediatelyAvailable: 0, cacheHitRate: 0, enhancementTime: 0 },
      details,
      errors,
    };
  }
}

/**
 * Test 2: Verify background enhancement works
 */
export async function testBackgroundEnhancement(): Promise<ProgressiveLoadingTestResult> {
  const testName = "Background Enhancement Test";
  const details: string[] = [];
  const errors: string[] = [];
  let passed = false;

  try {
    // Clear cache for clean test
    presignedUrlCache.clear();
    
    const testKeys = ['enhance-test-1.jpg', 'enhance-test-2.jpg'];
    const testItems = testKeys.map(key => ({ s3_key: key, s3_url: `fallback-${key}` }));

    details.push(`Testing background enhancement for ${testKeys.length} images`);

    const startTime = performance.now();
    const result = await progressiveLoadingService.loadImagesProgressively(testItems, {
      enableBackgroundEnhancement: true,
      enhancementDelay: 10, // Very short delay for testing
    });

    const immediateTime = performance.now() - startTime;
    details.push(`Immediate response time: ${immediateTime.toFixed(1)}ms`);

    // Verify immediate URLs are available (should be fallback URLs)
    const immediateCount = Object.keys(result.immediateUrls).length;
    passed = immediateCount === testKeys.length;

    if (passed) {
      details.push("✅ Immediate URLs provided (fallback)");
      details.push("✅ Background enhancement started");
    } else {
      errors.push("❌ Not all immediate URLs were provided");
    }

    // Wait for background enhancement to complete (with timeout)
    try {
      const enhancementStartTime = performance.now();
      await Promise.race([
        result.enhancementPromise,
        new Promise((_, reject) => setTimeout(() => reject(new Error('Enhancement timeout')), 5000))
      ]);
      const enhancementTime = performance.now() - enhancementStartTime;
      details.push(`Background enhancement completed in ${enhancementTime.toFixed(1)}ms`);
    } catch (enhancementError) {
      // Enhancement might fail in test environment, but that's okay
      details.push(`Background enhancement: ${enhancementError}`);
    }

    return {
      testName,
      passed,
      metrics: {
        totalImages: testKeys.length,
        immediatelyAvailable: result.metrics.immediatelyAvailable,
        cacheHitRate: result.metrics.cacheHitRate,
        enhancementTime: immediateTime,
      },
      details,
      errors,
    };
  } catch (error) {
    errors.push(`Test failed with error: ${error}`);
    return {
      testName,
      passed: false,
      metrics: { totalImages: 0, immediatelyAvailable: 0, cacheHitRate: 0, enhancementTime: 0 },
      details,
      errors,
    };
  }
}

/**
 * Test 3: Verify performance improvements
 */
export async function testPerformanceImprovement(): Promise<ProgressiveLoadingTestResult> {
  const testName = "Performance Improvement Test";
  const details: string[] = [];
  const errors: string[] = [];
  let passed = false;

  try {
    // Setup: Create a mix of cached and uncached URLs
    const testKeys = Array.from({ length: 10 }, (_, i) => `perf-test-${i}.jpg`);
    
    // Cache 70% of URLs (simulating good cache hit rate)
    testKeys.forEach((key, index) => {
      if (index < 7) {
        presignedUrlCache.set(key, `https://cached.example.com/${key}`, 60000);
      }
    });

    const testItems = testKeys.map(key => ({ s3_key: key, s3_url: `fallback-${key}` }));

    details.push(`Testing with ${testKeys.length} images (70% cached)`);

    const startTime = performance.now();
    const result = await progressiveLoadingService.loadImagesProgressively(testItems);
    const responseTime = performance.now() - startTime;

    details.push(`Response time: ${responseTime.toFixed(1)}ms`);
    details.push(`Cache hit rate: ${result.metrics.cacheHitRate.toFixed(1)}%`);
    details.push(`Immediately available: ${result.metrics.immediatelyAvailable}/${result.metrics.totalRequested}`);

    // Performance criteria:
    // 1. Response time should be very fast (< 50ms for immediate response)
    // 2. All images should be immediately available (cached + fallback)
    // 3. Cache hit rate should match our setup (70%)
    
    const fastResponse = responseTime < 50;
    const allImagesAvailable = Object.keys(result.immediateUrls).length === testKeys.length;
    const correctCacheHitRate = Math.abs(result.metrics.cacheHitRate - 70) < 5; // Allow 5% tolerance

    passed = fastResponse && allImagesAvailable && correctCacheHitRate;

    if (fastResponse) {
      details.push("✅ Fast immediate response (< 50ms)");
    } else {
      errors.push("❌ Response time too slow");
    }

    if (allImagesAvailable) {
      details.push("✅ All images immediately available");
    } else {
      errors.push("❌ Not all images immediately available");
    }

    if (correctCacheHitRate) {
      details.push("✅ Cache hit rate as expected");
    } else {
      errors.push("❌ Cache hit rate doesn't match expected value");
    }

    return {
      testName,
      passed,
      metrics: {
        totalImages: testKeys.length,
        immediatelyAvailable: result.metrics.immediatelyAvailable,
        cacheHitRate: result.metrics.cacheHitRate,
        enhancementTime: responseTime,
      },
      details,
      errors,
    };
  } catch (error) {
    errors.push(`Test failed with error: ${error}`);
    return {
      testName,
      passed: false,
      metrics: { totalImages: 0, immediatelyAvailable: 0, cacheHitRate: 0, enhancementTime: 0 },
      details,
      errors,
    };
  }
}

/**
 * Run all progressive loading tests
 */
export async function runAllProgressiveLoadingTests(): Promise<ProgressiveLoadingTestResult[]> {
  console.log('🧪 Running Sprint 17 Phase 4 Progressive Loading Tests...');
  
  const tests = [
    testImmediateAvailability,
    testBackgroundEnhancement,
    testPerformanceImprovement,
  ];

  const results: ProgressiveLoadingTestResult[] = [];

  for (const test of tests) {
    console.log(`Running ${test.name}...`);
    const result = await test();
    results.push(result);
    
    console.log(`${result.passed ? '✅' : '❌'} ${result.testName}`);
    result.details.forEach(detail => console.log(`  ${detail}`));
    result.errors.forEach(error => console.log(`  ${error}`));
  }

  const passedTests = results.filter(r => r.passed).length;
  console.log(`\n📊 Test Results: ${passedTests}/${results.length} tests passed`);

  return results;
}

// Export for use in development console
if (import.meta.env.DEV) {
  (window as any).testProgressiveLoading = {
    runAll: runAllProgressiveLoadingTests,
    testImmediateAvailability,
    testBackgroundEnhancement,
    testPerformanceImprovement,
  };
}
