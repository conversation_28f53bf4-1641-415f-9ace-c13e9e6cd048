/**
 * Service for deleting media assets (images and videos)
 * Handles deletion of both database records and S3 objects
 */

import { supabase } from '@/lib/supabase';

export interface DeleteResult {
  success: boolean;
  message?: string;
  warning?: string;
  error?: string;
}

/**
 * Delete an image and its associated S3 file
 * @param imageId - The ID of the image to delete
 * @param userId - The ID of the user who owns the image
 * @returns Promise<DeleteResult>
 */
export const deleteImage = async (imageId: string, userId: string): Promise<DeleteResult> => {
  try {
    const { data: { session } } = await supabase.auth.getSession();
    const token = session?.access_token;

    const headers: HeadersInit = {
      'Content-Type': 'application/json',
    };
    if (token) {
      headers['Authorization'] = `Bearer ${token}`;
    }

    const apiUrl = import.meta.env.VITE_API_URL || 'http://localhost:3001';
    const response = await fetch(`${apiUrl}/api/image/${imageId}?userId=${encodeURIComponent(userId)}`, {
      method: 'DELETE',
      headers: headers,
    });

    if (!response.ok) {
      const errorData = await response.json();
      return {
        success: false,
        error: errorData.error || `Server error: ${response.status}`
      };
    }

    const data = await response.json();

    if (!data.success) {
      return {
        success: false,
        error: data.error || 'Unknown error occurred during deletion'
      };
    }

    return {
      success: true,
      message: data.message,
      warning: data.warning
    };
  } catch (error) {
    if (import.meta.env.DEV) {
      console.error('Image deletion failed:', error);
    }
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Image deletion failed'
    };
  }
};

/**
 * Delete a video and its associated S3 file
 * @param videoId - The ID of the video to delete
 * @param userId - The ID of the user who owns the video
 * @returns Promise<DeleteResult>
 */
export const deleteVideo = async (videoId: string, userId: string): Promise<DeleteResult> => {
  try {
    const { data: { session } } = await supabase.auth.getSession();
    const token = session?.access_token;

    const headers: HeadersInit = {
      'Content-Type': 'application/json',
    };
    if (token) {
      headers['Authorization'] = `Bearer ${token}`;
    }

    const apiUrl = import.meta.env.VITE_API_URL || 'http://localhost:3001';
    const response = await fetch(`${apiUrl}/api/video/${videoId}?userId=${encodeURIComponent(userId)}`, {
      method: 'DELETE',
      headers: headers,
    });

    if (!response.ok) {
      const errorData = await response.json();
      return {
        success: false,
        error: errorData.error || `Server error: ${response.status}`
      };
    }

    const data = await response.json();

    if (!data.success) {
      return {
        success: false,
        error: data.error || 'Unknown error occurred during deletion'
      };
    }

    return {
      success: true,
      message: data.message,
      warning: data.warning
    };
  } catch (error) {
    if (import.meta.env.DEV) {
      console.error('Video deletion failed:', error);
    }
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Video deletion failed'
    };
  }
};

/**
 * Check if user has permission to delete a media item
 * @param mediaOwnerId - The user ID of the media owner
 * @param currentUserId - The current user's ID
 * @returns boolean
 */
export const canDeleteMedia = (mediaOwnerId: string, currentUserId: string): boolean => {
  return mediaOwnerId === currentUserId;
};