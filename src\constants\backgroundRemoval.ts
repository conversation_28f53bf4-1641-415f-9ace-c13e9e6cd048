/**
 * Constants for Background Removal functionality
 * Centralized configuration for file handling, validation, and UI messages
 */

// File handling constants
export const FILE_CONSTRAINTS = {
  MAX_SIZE: 5 * 1024 * 1024, // 5MB in bytes
  SUPPORTED_FORMATS: ['image/png', 'image/jpeg', 'image/jpg', 'image/webp'] as string[],
  SUPPORTED_EXTENSIONS: ['.png', '.jpg', '.jpeg', '.webp'] as string[],
} as const;

// Image processing constants
export const IMAGE_PROCESSING = {
  MAX_DIMENSION: 1024, // Maximum dimension for compression
  COMPRESSION_QUALITY: 0.7, // JPEG compression quality
  COMPRESSION_THRESHOLD: 1000000, // ~1MB threshold for compression
  OUTPUT_FORMAT: 'png', // Default output format for transparent backgrounds
} as const;

// UI Messages
export const MESSAGES = {
  SUCCESS: {
    BACKGROUND_REMOVED: 'Background removal successful!',
    IMAGE_DOWNLOADED: 'Image downloaded successfully!',
  },
  ERROR: {
    NO_FILE_SELECTED: 'Please select an image first',
    FILE_TOO_LARGE: 'File size exceeds 5MB limit',
    INVALID_FILE_TYPE: 'Please select an image file',
    PROCESSING_FAILED: 'Failed to process image',
    DOWNLOAD_FAILED: 'Failed to download image',
    UPLOAD_FAILED: 'Failed to read the selected file',
    BACKGROUND_REMOVAL_FAILED: 'Failed to remove background',
    SAVE_TO_HISTORY_FAILED: 'Background removal successful, but failed to save to history',
  },
  WARNING: {
    SAVE_PARTIAL_FAILURE: 'Background removal successful, but failed to save to history',
  },
  INFO: {
    NO_IMAGE_SELECTED: 'No image selected',
    NO_PROCESSED_IMAGE: 'No processed image yet',
    UPLOAD_INSTRUCTIONS: 'Upload an image to preview',
    PROCESSING: 'Processing...',
    SELECT_IMAGE_PROMPT: 'Upload an image to automatically remove its background.',
  },
} as const;

// Default values
export const DEFAULTS = {
  ACTIVE_TAB: 'upload',
  IMAGE_TYPE: 'background-removal' as const,
  PLATFORM: 'Advanced Tools',
  STYLE: 'background-removal',
  PROMPT: 'Background Removal',
} as const;

// File validation helper
export const isValidImageFile = (file: File): boolean => {
  return FILE_CONSTRAINTS.SUPPORTED_FORMATS.includes(file.type as any);
};

// File size validation helper
export const isValidFileSize = (file: File): boolean => {
  return file.size <= FILE_CONSTRAINTS.MAX_SIZE;
};

// Generate filename helper
export const generateFilename = (prefix: string = 'background-removed', extension: string = 'png'): string => {
  return `${prefix}-${Date.now()}.${extension}`;
};

// Use cases for the background removal tool
export const USE_CASES = [
  {
    title: 'Product Photography',
    description: 'Create clean, professional product images with transparent backgrounds for e-commerce listings, catalogs, and marketing materials.',
  },
  {
    title: 'Portrait Enhancement',
    description: 'Remove distracting backgrounds from portraits for professional headshots, social media profiles, and personal branding.',
  },
  {
    title: 'Design Elements',
    description: 'Extract objects from images to create design elements for graphics, presentations, websites, and digital collages.',
  },
  {
    title: 'Social Media Content',
    description: 'Create eye-catching social media posts with clean, isolated subjects that can be placed on any background or template.',
  },
] as const;