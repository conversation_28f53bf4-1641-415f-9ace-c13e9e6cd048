import React from 'react';
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { Eye, Clock, AlertTriangle, CheckCircle } from 'lucide-react';

interface LazyLoadingMetrics {
  totalImages: number;
  loadedImages: number;
  failedImages: number;
  averageLoadTime: number;
  imagesInView: number;
}

interface LazyLoadingDebuggerProps {
  metrics: LazyLoadingMetrics;
  className?: string;
}

const LazyLoadingDebugger: React.FC<LazyLoadingDebuggerProps> = ({
  metrics,
  className,
}) => {
  // Only show in development
  if (!import.meta.env.DEV) {
    return null;
  }

  const { totalImages, loadedImages, failedImages, averageLoadTime, imagesInView } = metrics;
  const loadedPercentage = totalImages > 0 ? (loadedImages / totalImages) * 100 : 0;
  const failedPercentage = totalImages > 0 ? (failedImages / totalImages) * 100 : 0;
  const pendingImages = totalImages - loadedImages - failedImages;

  const getPerformanceColor = (loadTime: number) => {
    if (loadTime < 500) return 'text-green-600';
    if (loadTime < 1000) return 'text-yellow-600';
    return 'text-red-600';
  };

  const getPerformanceBadge = (loadTime: number) => {
    if (loadTime < 500) return { variant: 'default' as const, label: 'Fast' };
    if (loadTime < 1000) return { variant: 'secondary' as const, label: 'Good' };
    return { variant: 'destructive' as const, label: 'Slow' };
  };

  return (
    <Card className={`fixed bottom-4 right-4 w-80 z-50 bg-white/95 backdrop-blur-sm border shadow-lg ${className}`}>
      <CardHeader className="pb-3">
        <CardTitle className="text-sm font-medium flex items-center gap-2">
          <Eye className="h-4 w-4" />
          Lazy Loading Performance
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-3">
        {/* Overall Progress */}
        <div className="space-y-2">
          <div className="flex justify-between text-xs">
            <span>Loading Progress</span>
            <span>{loadedPercentage.toFixed(1)}%</span>
          </div>
          <Progress value={loadedPercentage} className="h-2" />
        </div>

        {/* Statistics Grid */}
        <div className="grid grid-cols-2 gap-3 text-xs">
          <div className="space-y-1">
            <div className="flex items-center gap-1">
              <CheckCircle className="h-3 w-3 text-green-600" />
              <span>Loaded: {loadedImages}</span>
            </div>
            <div className="flex items-center gap-1">
              <AlertTriangle className="h-3 w-3 text-red-600" />
              <span>Failed: {failedImages}</span>
            </div>
          </div>
          <div className="space-y-1">
            <div className="flex items-center gap-1">
              <Eye className="h-3 w-3 text-blue-600" />
              <span>In View: {imagesInView}</span>
            </div>
            <div className="flex items-center gap-1">
              <Clock className="h-3 w-3 text-gray-600" />
              <span>Pending: {pendingImages}</span>
            </div>
          </div>
        </div>

        {/* Performance Metrics */}
        {averageLoadTime > 0 && (
          <div className="flex items-center justify-between pt-2 border-t">
            <div className="flex items-center gap-2">
              <Clock className="h-3 w-3" />
              <span className="text-xs">Avg Load Time:</span>
            </div>
            <div className="flex items-center gap-2">
              <span className={`text-xs font-medium ${getPerformanceColor(averageLoadTime)}`}>
                {averageLoadTime}ms
              </span>
              <Badge 
                variant={getPerformanceBadge(averageLoadTime).variant}
                className="text-xs px-1 py-0"
              >
                {getPerformanceBadge(averageLoadTime).label}
              </Badge>
            </div>
          </div>
        )}

        {/* Performance Tips */}
        {totalImages > 0 && (
          <div className="text-xs text-gray-600 pt-2 border-t">
            {failedPercentage > 10 && (
              <div className="text-red-600">⚠️ High failure rate ({failedPercentage.toFixed(1)}%)</div>
            )}
            {averageLoadTime > 1000 && (
              <div className="text-yellow-600">⚡ Consider optimizing image sizes</div>
            )}
            {loadedPercentage === 100 && failedPercentage === 0 && (
              <div className="text-green-600">✅ All images loaded successfully!</div>
            )}
          </div>
        )}
      </CardContent>
    </Card>
  );
};

export default LazyLoadingDebugger;
