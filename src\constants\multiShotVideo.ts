// Multi-Shot Video Generator Constants
// Centralized configuration for multi-shot video generation

export const DURATION_LIMITS = {
  MIN: 12,
  MAX: 120,
  STEP: 6,
  DEFAULT_AUTOMATED: 30,
  DEFAULT_PER_SHOT: 6,
} as const;

export const SHOT_LIMITS = {
  MAX_SHOTS: 20,
  MIN_SHOTS: 1,
} as const;

export const PROMPT_LIMITS = {
  AUTOMATED_MIN: 10,
  AUTOMATED_MAX: 4000,
  SHOT_MAX: 512,
  WARNING_THRESHOLD_AUTOMATED: 3600,
  WARNING_THRESHOLD_SHOT: 450,
} as const;

export const FILE_CONSTRAINTS = {
  MAX_SIZE: 10 * 1024 * 1024, // 10MB
  SUPPORTED_FORMATS: ['image/png', 'image/jpeg', 'image/jpg', 'image/webp'],
  SUPPORTED_EXTENSIONS: ['PNG', 'JPG', 'JPEG', 'WEBP'],
} as const;

export const PROCESSING_TIME = {
  AUTOMATED_MULTIPLIER_MIN: 2.5,
  AUTOMATED_MULTIPLIER_MAX: 4,
  MANUAL_PER_SHOT_MIN: 45,
  MANUAL_PER_SHOT_MAX: 75,
} as const;

export const DEFAULT_VALUES = {
  SEED_RANGE: 1000000,
  NEGATIVE_PROMPT: '',
  REFERENCE_IMAGE_FORMAT: 'jpeg' as const,
} as const;

export const ERROR_MESSAGES = {
  SIGN_IN_REQUIRED: 'Please sign in to generate videos',
  PROMPT_TOO_SHORT: 'Please provide a more detailed story description (at least 10 characters)',
  MAX_SHOTS_EXCEEDED: 'Maximum 20 shots allowed',
  MIN_SHOTS_REQUIRED: 'At least one shot is required',
  NO_SHOTS_WITH_CONTENT: 'Please add at least one shot with a description',
  INVALID_IMAGE_FILE: 'Please select a valid image file',
  FILE_SIZE_EXCEEDED: 'Image file size must be less than 10MB',
  IMAGE_UPLOAD_FAILED: 'Failed to upload image',
  GENERATION_FAILED: 'Failed to start video generation',
  UNEXPECTED_ERROR: 'An unexpected error occurred',
} as const;

export const SUCCESS_MESSAGES = {
  AUTOMATED_STARTED: 'Automated multi-shot video generation started!',
  TEMPLATE_STARTED: 'Template video generation started!',
  STORYBOARD_STARTED: 'Storyboard video generation started!',
  IMAGE_UPLOADED: 'Reference image uploaded successfully',
} as const;

export const UI_TEXT = {
  AUTOMATED_TITLE: 'Automated Multi-Shot',
  AUTOMATED_SUBTITLE: 'AI-driven composition',
  MANUAL_TITLE: 'Manual Storyboard',
  MANUAL_SUBTITLE: 'Shot-by-shot control',
  STORY_DESCRIPTION_LABEL: 'Story Description',
  STORY_DESCRIPTION_PLACEHOLDER: 'Describe your multi-shot video story in detail. Include scenes, characters, actions, and visual style...',
  STORY_DESCRIPTION_HELP: 'Provide detailed descriptions for better AI composition',
  DURATION_LABEL: 'Duration (seconds)',
  SEED_LABEL: 'Seed (Optional)',
  SEED_PLACEHOLDER: 'Random seed',
  SHOT_DESCRIPTION_LABEL: 'Shot Description',
  SHOT_DESCRIPTION_PLACEHOLDER: 'Describe this shot in detail...',
  SHOT_DESCRIPTION_HELP: 'Be specific about camera angles, actions, and visuals',
  REFERENCE_IMAGE_LABEL: 'Reference Image (Optional)',
  REFERENCE_IMAGE_HELP: 'PNG or JPEG, max 10MB',
  REFERENCE_IMAGE_UPLOAD: 'Click to upload reference image',
  GLOBAL_SEED_TITLE: 'Global Seed for Visual Consistency',
  GLOBAL_SEED_HELP: 'All shots will use the same seed to ensure visual similarity between scenes',
  GENERATING_AUTOMATED: 'Generating Your Multi-Shot Video',
  GENERATING_AUTOMATED_DESCRIPTION: 'AI is automatically creating a {duration}-second multi-shot video from your story',
  GENERATING_STORYBOARD: 'Generating Your Storyboard Video',
  GENERATING_STORYBOARD_DESCRIPTION: 'AI is creating your custom multi-shot video with {count} scene{plural}',
  ESTIMATED_TIME: 'Estimated Time:',
  LATEST_VIDEO_READY: 'Latest Video Ready',
  VIEW_VIDEO: 'View Video',
  VIDEO_READY_MESSAGE: 'Your multi-shot video has been generated successfully! Click "View Video" to see the result.',
  GENERATED_VIDEO_TITLE: 'Your Generated Multi-Shot Video',
  VIDEO_SUCCESS_MESSAGE: 'Multi-shot video generated successfully!',
  GENERATE_NEW: 'Generate New',
  DOWNLOAD: 'Download',
  RANDOM: 'Random',
  GENERATE_BUTTON: 'Generate Multi-Shot Video',
  BUILD_STORYBOARD: 'Build Custom Storyboard',
  GENERATE_STORYBOARD: 'Generate Storyboard Video',
} as const;

export const GENERATION_METHODS = {
  AUTOMATED: 'automated' as const,
  MANUAL: 'manual' as const,
} as const;

export const TAB_VALUES = {
  GENERATE: 'generate' as const,
  TEMPLATES: 'templates' as const,
  RESULT: 'result' as const,
} as const;

export const USE_CASES = [
  {
    title: 'Professional Marketing',
    description: 'Create comprehensive marketing campaigns with multiple scenes, product showcases, and brand storytelling.',
    icon: 'Film',
  },
  {
    title: 'Educational Series',
    description: 'Develop detailed educational content with step-by-step explanations, demonstrations, and visual learning aids.',
    icon: 'Film',
  },
  {
    title: 'Documentary Style',
    description: 'Produce documentary-style content with multiple perspectives, interviews, and narrative progression.',
    icon: 'Film',
  },
  {
    title: 'Complex Narratives',
    description: 'Tell complex stories with character development, plot progression, and cinematic transitions.',
    icon: 'Film',
  },
  {
    title: 'Product Demonstrations',
    description: 'Showcase products with detailed demonstrations, feature highlights, and usage scenarios across multiple shots.',
    icon: 'Film',
  },
  {
    title: 'Event Coverage',
    description: 'Create comprehensive event coverage with multiple angles, highlights, and seamless transitions between scenes.',
    icon: 'Film',
  },
] as const;

// Utility functions
export const calculateEstimatedTime = (durationSeconds: number, isAutomated: boolean) => {
  if (isAutomated) {
    return {
      min: Math.round(durationSeconds * PROCESSING_TIME.AUTOMATED_MULTIPLIER_MIN),
      max: Math.round(durationSeconds * PROCESSING_TIME.AUTOMATED_MULTIPLIER_MAX),
    };
  } else {
    const shotCount = Math.ceil(durationSeconds / DURATION_LIMITS.DEFAULT_PER_SHOT);
    return {
      min: Math.round(shotCount * PROCESSING_TIME.MANUAL_PER_SHOT_MIN),
      max: Math.round(shotCount * PROCESSING_TIME.MANUAL_PER_SHOT_MAX),
    };
  }
};

export const calculateEstimatedTimeForShots = (shotCount: number) => {
  return {
    min: Math.round(shotCount * PROCESSING_TIME.MANUAL_PER_SHOT_MIN),
    max: Math.round(shotCount * PROCESSING_TIME.MANUAL_PER_SHOT_MAX),
  };
};

export const generateRandomSeed = () => Math.floor(Math.random() * DEFAULT_VALUES.SEED_RANGE);

export const validateImageFile = (file: File) => {
  if (!FILE_CONSTRAINTS.SUPPORTED_FORMATS.includes(file.type as any)) {
    return { valid: false, error: ERROR_MESSAGES.INVALID_IMAGE_FILE };
  }
  
  if (file.size > FILE_CONSTRAINTS.MAX_SIZE) {
    return { valid: false, error: ERROR_MESSAGES.FILE_SIZE_EXCEEDED };
  }
  
  return { valid: true };
};

export const getImageFormat = (file: File): 'png' | 'jpeg' => {
  return file.type.includes('png') ? 'png' : 'jpeg';
};