
import React, { useState, useEffect } from "react";
import { useAuth } from "@/contexts/AuthContext";
import SuperadminRedirect from "@/components/SuperadminRedirect";
import DashboardSidebar from "@/components/DashboardSidebar";
import DashboardHeader from "@/components/DashboardHeader";
import DashboardStatsCards from "@/components/DashboardStatsCards";
import DashboardTabs from "@/components/DashboardTabs";
import ImageDetailsDialog from "@/components/ImageDetailsDialog";
import VideoDetailsDialog from "@/components/VideoDetailsDialog";
import DeleteConfirmationDialog from "@/components/DeleteConfirmationDialog";
import { toast } from "sonner";
import { useQueryClient } from "@tanstack/react-query";
import { useDashboardData } from "@/hooks/useDashboardData";
import { useDashboardStats } from "@/hooks/useDashboardStats";
import { useDashboardActions } from "@/hooks/useDashboardActions";
import { handleImageSelection, handleVideoSelection, handleImageDownload, handleMediaDeletion } from "@/utils/mediaHandlers";
import { getMediaName } from "@/utils/dashboardUtils";
import { DashboardState, DeleteItem } from "@/types/dashboard";
import { ImageHistoryItem } from "@/services/imageHistoryService";
import { VideoHistoryItem } from "@/services/videoService";

const Dashboard = () => {
  const { user } = useAuth();
  const queryClient = useQueryClient();
  
  // Dashboard state
  const [dashboardState, setDashboardState] = useState<DashboardState>({
    selectedImage: null,
    selectedVideo: null,
    dialogOpen: false,
    videoDialogOpen: false,
    imageUrl: "",
    activeTab: localStorage.getItem("dashboardActiveTab") || "videos",
    deleteDialogOpen: false,
    itemToDelete: null,
  });



  // Reset any stuck query states when component mounts
  useEffect(() => {
    if (user?.id) {
      // Cancel any ongoing queries to prevent stuck states
      queryClient.cancelQueries({ queryKey: ['imageHistory'] });
      queryClient.cancelQueries({ queryKey: ['videoHistory'] });
      
      if (import.meta.env.DEV) {
        console.log('Dashboard mounted, cancelled any ongoing queries');
      }
    }
  }, [user?.id, queryClient]);

  // Use custom hooks
  const data = useDashboardData();


  const stats = useDashboardStats({
    images: data.images,
    videos: data.videos,
    voices: data.voices,
    voiceUsage: data.voiceUsage,
  });
  const actions = useDashboardActions({
    user,
    refetchImages: data.refetchImages,
    refetchVideos: data.refetchVideos,
    refetchVoices: data.refetchVoices,
    refetchVoiceUsage: data.refetchVoiceUsage,
  });

  // Handle image selection
  const handleSelectImage = async (image: ImageHistoryItem) => {
    await handleImageSelection(
      image,
      (img) => setDashboardState(prev => ({ ...prev, selectedImage: img })),
      (url) => setDashboardState(prev => ({ ...prev, imageUrl: url })),
      (open) => setDashboardState(prev => ({ ...prev, dialogOpen: open }))
    );
  };

  // Handle video selection
  const handleSelectVideo = (video: VideoHistoryItem) => {
    handleVideoSelection(
      video,
      (vid) => setDashboardState(prev => ({ ...prev, selectedVideo: vid })),
      (open) => setDashboardState(prev => ({ ...prev, videoDialogOpen: open }))
    );
  };

  // Handle delete confirmation
  const handleDeleteImage = (image: ImageHistoryItem) => {
    setDashboardState(prev => ({
      ...prev,
      itemToDelete: { type: 'image', item: image },
      deleteDialogOpen: true,
    }));
  };

  const handleDeleteVideo = (video: VideoHistoryItem) => {
    setDashboardState(prev => ({
      ...prev,
      itemToDelete: { type: 'video', item: video },
      deleteDialogOpen: true,
    }));
  };

  const confirmDelete = async () => {
    if (!dashboardState.itemToDelete || !user) return;
    
    try {
      await handleMediaDeletion(dashboardState.itemToDelete, user, queryClient);
    } catch (error) {
      throw error; // Re-throw to be handled by DeleteConfirmationDialog
    }
  };

  const setActiveTab = (tab: string) => {
    setDashboardState(prev => ({ ...prev, activeTab: tab }));
  };

  return (
    <>
      <SuperadminRedirect />
      <DashboardSidebar />

      <main className="ml-64 min-h-screen bg-gray-50 dark:bg-gray-900 p-6 lg:p-8">
        <div className="max-w-7xl mx-auto space-y-8">
          {/* Header */}
          <DashboardHeader user={user} />

          {/* Stats Cards */}
          <DashboardStatsCards
            stats={stats}
            isLoading={data.isLoadingImages || data.isLoadingVideos || data.isLoadingVoices}
          />

          {/* Content Tabs */}
          <DashboardTabs
            activeTab={dashboardState.activeTab}
            setActiveTab={setActiveTab}
            stats={stats}
            data={data}
            actions={actions}
            onSelectImage={handleSelectImage}
            onSelectVideo={handleSelectVideo}
            onDeleteImage={handleDeleteImage}
            onDeleteVideo={handleDeleteVideo}
          />

          {/* Image Details Dialog */}
          {dashboardState.selectedImage && (
            <ImageDetailsDialog
              image={dashboardState.selectedImage}
              imageUrl={dashboardState.imageUrl}
              open={dashboardState.dialogOpen}
              onOpenChange={(open) => setDashboardState(prev => ({ ...prev, dialogOpen: open }))}
              onDownload={handleImageDownload}
            />
          )}

          {/* Video Details Dialog */}
          {dashboardState.selectedVideo && (
            <VideoDetailsDialog
              video={dashboardState.selectedVideo}
              open={dashboardState.videoDialogOpen}
              onOpenChange={(open) => setDashboardState(prev => ({ ...prev, videoDialogOpen: open }))}
              onRegenerate={(video) => {
                // Handle video regeneration if needed
                toast.info("Video regeneration feature coming soon!");
              }}
              onShare={(video) => {
                // Handle video sharing
                if (video.presigned_url) {
                  navigator.clipboard.writeText(video.presigned_url);
                  toast.success("Video link copied to clipboard!");
                } else {
                  toast.error("No video URL available to share");
                }
              }}
              onDelete={handleDeleteVideo}
            />
          )}

          {/* Delete Confirmation Dialog */}
          {dashboardState.itemToDelete && (
            <DeleteConfirmationDialog
              open={dashboardState.deleteDialogOpen}
              onOpenChange={(open) => setDashboardState(prev => ({ ...prev, deleteDialogOpen: open }))}
              onConfirm={confirmDelete}
              mediaType={dashboardState.itemToDelete.type}
              mediaName={getMediaName(dashboardState.itemToDelete.item, dashboardState.itemToDelete.type)}
              mediaId={dashboardState.itemToDelete.item.id || 'unknown'}
            />
          )}
        </div>
      </main>
    </>
  );
};

export default Dashboard;
