import { useQuery, useQueryClient } from '@tanstack/react-query';
import { generatePresignedUrlsBatch, PresignedUrlBatchOptions, PresignedUrlBatchResult } from '@/services/presignedUrlService';
import { presignedUrlCache } from '@/services/presignedUrlCache';
import { useCallback, useEffect } from 'react';

interface UsePresignedUrlQueryOptions extends PresignedUrlBatchOptions {
  enabled?: boolean;
  staleTime?: number;
  gcTime?: number;
  refetchInterval?: number | false;
  refetchOnWindowFocus?: boolean;
  refetchOnMount?: boolean;
  retry?: number;
  retryDelay?: number;
}

interface UsePresignedUrlQueryResult {
  data: PresignedUrlBatchResult | undefined;
  urlMap: Record<string, string>;
  isLoading: boolean;
  isFetching: boolean;
  isError: boolean;
  error: Error | null;
  refetch: () => Promise<any>;
  invalidateUrls: (s3_keys?: string[]) => void;
  refreshUrls: (s3_keys: string[]) => Promise<void>;
  preloadUrls: (s3_keys: string[]) => void;
  getCacheMetrics: () => any;
}

export const usePresignedUrlQuery = (
  s3_keys: string[],
  fallbackUrls: Record<string, string> = {},
  options: UsePresignedUrlQueryOptions = {}
): UsePresignedUrlQueryResult => {
  const queryClient = useQueryClient();

  const {
    enabled = true,
    staleTime = 30 * 60 * 1000, // 30 minutes
    gcTime = 45 * 60 * 1000, // 45 minutes
    refetchInterval = false,
    refetchOnWindowFocus = false,
    refetchOnMount = true,
    retry = 2,
    retryDelay = 1000,
    useCache = true,
    cacheTtl = 45 * 60 * 1000, // 45 minutes
    forceRefresh = false,
    timeout = 5000,
    maxRetries = 2,
    concurrencyLimit = 8,
    enableMetrics = import.meta.env.DEV,
    ...batchOptions
  } = options;

  // Create a stable query key
  const queryKey = ['presignedUrls', s3_keys.sort().join(',')];

  const {
    data,
    isLoading,
    isFetching,
    isError,
    error,
    refetch,
  } = useQuery<PresignedUrlBatchResult, Error>({
    queryKey,
    queryFn: async (): Promise<PresignedUrlBatchResult> => {
      if (s3_keys.length === 0) {
        return {
          results: [],
          successCount: 0,
          failureCount: 0,
          totalTime: 0,
          averageTime: 0,
          retryCount: 0,
          cacheHits: 0,
          cacheMisses: 0,
          cacheHitRate: 0,
        };
      }

      return generatePresignedUrlsBatch(s3_keys, {
        fallbackUrls,
        useCache,
        cacheTtl,
        forceRefresh,
        timeout,
        maxRetries,
        retryDelay,
        concurrencyLimit,
        enableMetrics,
        ...batchOptions,
      });
    },
    enabled: enabled && s3_keys.length > 0,
    staleTime,
    gcTime,
    refetchInterval,
    refetchOnWindowFocus,
    refetchOnMount,
    retry,
    retryDelay,
  });

  // Convert results to URL map
  const urlMap = data?.results.reduce((acc, result) => {
    if (result.success && result.url) {
      acc[result.s3_key] = result.url;
    }
    return acc;
  }, {} as Record<string, string>) || {};

  // Invalidate specific URLs in both React Query and our cache
  const invalidateUrls = useCallback((targetS3Keys?: string[]) => {
    if (targetS3Keys) {
      // Invalidate specific URLs
      targetS3Keys.forEach(s3_key => {
        presignedUrlCache.delete(s3_key);
      });
      
      // Invalidate React Query cache for queries that include these keys
      queryClient.invalidateQueries({
        predicate: (query) => {
          const queryKeys = query.queryKey[1] as string;
          return targetS3Keys.some(key => queryKeys.includes(key));
        },
      });
    } else {
      // Invalidate all URLs
      presignedUrlCache.clear();
      queryClient.invalidateQueries({
        queryKey: ['presignedUrls'],
      });
    }
  }, [queryClient]);

  // Refresh specific URLs with force refresh
  const refreshUrls = useCallback(async (targetS3Keys: string[]) => {
    // Remove from cache first
    targetS3Keys.forEach(s3_key => {
      presignedUrlCache.delete(s3_key);
    });

    // Force refresh these URLs
    await generatePresignedUrlsBatch(targetS3Keys, {
      fallbackUrls,
      useCache,
      cacheTtl,
      forceRefresh: true,
      timeout,
      maxRetries,
      retryDelay,
      concurrencyLimit,
      enableMetrics,
      ...batchOptions,
    });

    // Invalidate React Query cache to trigger refetch
    queryClient.invalidateQueries({ queryKey });
  }, [queryClient, queryKey, fallbackUrls, useCache, cacheTtl, timeout, maxRetries, retryDelay, concurrencyLimit, enableMetrics, batchOptions]);

  // Preload URLs in the background
  const preloadUrls = useCallback((targetS3Keys: string[]) => {
    queryClient.prefetchQuery({
      queryKey: ['presignedUrls', targetS3Keys.sort().join(',')],
      queryFn: () => generatePresignedUrlsBatch(targetS3Keys, {
        fallbackUrls,
        useCache,
        cacheTtl,
        timeout,
        maxRetries,
        retryDelay,
        concurrencyLimit,
        enableMetrics,
        ...batchOptions,
      }),
      staleTime,
    });
  }, [queryClient, fallbackUrls, useCache, cacheTtl, timeout, maxRetries, retryDelay, concurrencyLimit, enableMetrics, staleTime, batchOptions]);

  // Get cache metrics
  const getCacheMetrics = useCallback(() => {
    return presignedUrlCache.getMetrics();
  }, []);

  // Auto-refresh URLs that are about to expire
  useEffect(() => {
    if (!useCache || s3_keys.length === 0) return;

    const checkExpiringUrls = () => {
      const expiringUrls = presignedUrlCache.getExpiringUrls(10 * 60 * 1000); // 10 minutes threshold
      const relevantExpiringUrls = expiringUrls.filter(key => s3_keys.includes(key));
      
      if (relevantExpiringUrls.length > 0 && enableMetrics) {
        console.log(`[usePresignedUrlQuery] ${relevantExpiringUrls.length} URLs expiring soon, consider refreshing`);
      }
    };

    const interval = setInterval(checkExpiringUrls, 5 * 60 * 1000); // Check every 5 minutes
    return () => clearInterval(interval);
  }, [s3_keys, useCache, enableMetrics]);

  // Log cache performance in development
  useEffect(() => {
    if (enableMetrics && data) {
      const metrics = presignedUrlCache.getMetrics();
      console.log(`[usePresignedUrlQuery] Cache performance: ${presignedUrlCache.getHitRate().toFixed(1)}% hit rate, ${metrics.cacheSize} cached URLs`);
    }
  }, [data, enableMetrics]);

  return {
    data,
    urlMap,
    isLoading,
    isFetching,
    isError,
    error: error as Error | null,
    refetch,
    invalidateUrls,
    refreshUrls,
    preloadUrls,
    getCacheMetrics,
  };
};
