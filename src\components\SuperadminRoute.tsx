import { useEffect, useState } from "react";
import { useNavigate } from "react-router-dom";
import { useAuth } from "@/contexts/AuthContext";
import { toast } from "sonner";
import { supabase } from "@/lib/supabase";

const SuperadminRoute = ({ children }: { children: React.ReactNode }) => {
  const { user, isAuthenticated, isLoading, isLoggingOut, checkSuperadminAccess } = useAuth();
  const navigate = useNavigate();
  const [isCheckingPermissions, setIsCheckingPermissions] = useState(false);
  const [hasVerified, setHasVerified] = useState(false);

  useEffect(() => {
    let mounted = true;
    
    // Check if the user has superadmin access
    const checkPermissions = async () => {
      // Skip check if we're in the process of logging out or already verified
      if (isLoggingOut || !mounted || hasVerified) return;

      if (!isLoading && mounted) {
        setIsCheckingPermissions(true);

        try {
          // First check if user is authenticated
          if (!isAuthenticated || !user) {
            toast.error("Please sign in to access this page");
            navigate("/signin");
            return;
          }

          // If local superadmin check passes, trust it and skip server verification
          if (checkSuperadminAccess()) {
            setHasVerified(true);
            setIsCheckingPermissions(false);
            return;
          }

          // Only do server-side verification if local check fails
          const { data } = await supabase.auth.getSession();
          if (!data.session && mounted) {
            toast.error("Please sign in to access this page");
            navigate("/signin");
            return;
          }

          // Server-side verification with timeout
          try {
            const controller = new AbortController();
            const timeoutId = setTimeout(() => controller.abort(), 5000); // 5 second timeout

            const response = await fetch('/api/superadmin/stats', {
              method: 'GET',
              headers: {
                'Authorization': `Bearer ${data.session?.access_token}`,
                'Content-Type': 'application/json',
              },
              signal: controller.signal,
            });

            clearTimeout(timeoutId);

            if (response.status === 403 || response.status === 401) {
              toast.error("Access denied. Insufficient permissions.");
              navigate("/dashboard");
              return;
            }

            if (!response.ok) {
              throw new Error('Permission check failed');
            }

            setHasVerified(true);
          } catch (error: any) {
            if (error.name === 'AbortError') {
              toast.error("Permission check timed out. Please try again.");
            } else {
              console.error("Error checking superadmin permissions:", error);
              toast.error("Access denied. Unable to verify permissions.");
            }
            navigate("/dashboard");
            return;
          }
        } catch (error) {
          console.error("Error in superadmin permission check:", error);
          if (mounted) {
            toast.error("Authentication error. Please sign in again.");
            navigate("/signin");
          }
        } finally {
          if (mounted) {
            setIsCheckingPermissions(false);
          }
        }
      }
    };

    checkPermissions();
    
    return () => {
      mounted = false;
    };
  }, [isAuthenticated, isLoading, navigate, isLoggingOut, user, checkSuperadminAccess, hasVerified]);

  if (isLoading || isCheckingPermissions) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="animate-pulse text-brand-purple font-semibold">
          Verifying permissions...
        </div>
      </div>
    );
  }

  // Only render children if user is authenticated and has superadmin access
  return isAuthenticated && checkSuperadminAccess() ? <>{children}</> : null;
};

export default SuperadminRoute;