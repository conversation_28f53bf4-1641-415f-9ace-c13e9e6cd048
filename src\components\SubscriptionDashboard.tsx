import React, { useState } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { <PERSON><PERSON>, <PERSON><PERSON>Content, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Button } from '@/components/ui/button';
import { CreditC<PERSON>, Settings, Sparkles, History } from 'lucide-react';
import { toast } from 'sonner';
import SubscriptionInfo from '@/components/SubscriptionInfo';

const SubscriptionDashboard: React.FC = () => {
  const [activeTab, setActiveTab] = useState('overview');

  const handleUpgrade = () => {
    // This will be implemented in Sprint 7 with Stripe integration
    if (import.meta.env.DEV) {
      console.log('Upgrade button clicked');
    }
    // Use toast instead of alert for better UX
    toast.info('Subscription upgrade will be available in the next release!');
  };

  return (
    <div className="container mx-auto py-6 space-y-6">
      <div className="flex justify-between items-center">
        <h1 className="text-3xl font-bold">Subscription</h1>
        <Button variant="outline" size="sm">
          <Settings className="mr-2 h-4 w-4" />
          Manage
        </Button>
      </div>

      <Tabs defaultValue="overview" value={activeTab} onValueChange={setActiveTab} className="w-full">
        <TabsList className="grid w-full grid-cols-3">
          <TabsTrigger value="overview">Overview</TabsTrigger>
          <TabsTrigger value="billing">Billing</TabsTrigger>
          <TabsTrigger value="usage">Usage History</TabsTrigger>
        </TabsList>
        
        <TabsContent value="overview" className="space-y-4 mt-4">
          <SubscriptionInfo onUpgrade={handleUpgrade} />
          
          <Card>
            <CardHeader>
              <CardTitle>Quick Actions</CardTitle>
              <CardDescription>Manage your subscription and billing</CardDescription>
            </CardHeader>
            <CardContent className="grid grid-cols-2 gap-4">
              <Button variant="outline" className="h-20 flex flex-col items-center justify-center">
                <CreditCard className="h-5 w-5 mb-1" />
                <span>Payment Methods</span>
              </Button>
              <Button variant="outline" className="h-20 flex flex-col items-center justify-center">
                <History className="h-5 w-5 mb-1" />
                <span>Billing History</span>
              </Button>
            </CardContent>
          </Card>
        </TabsContent>
        
        <TabsContent value="billing" className="space-y-4 mt-4">
          <Card>
            <CardHeader>
              <CardTitle>Billing Information</CardTitle>
              <CardDescription>Manage your payment methods and billing history</CardDescription>
            </CardHeader>
            <CardContent className="py-4">
              <p className="text-muted-foreground text-center py-8">
                Billing management will be available in the next release.
              </p>
              <div className="flex justify-center">
                <Button variant="outline" onClick={() => setActiveTab('overview')}>
                  Back to Overview
                </Button>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
        
        <TabsContent value="usage" className="space-y-4 mt-4">
          <Card>
            <CardHeader>
              <CardTitle>Usage History</CardTitle>
              <CardDescription>Track your image generation usage over time</CardDescription>
            </CardHeader>
            <CardContent className="py-4">
              <p className="text-muted-foreground text-center py-8">
                Detailed usage history will be available in the next release.
              </p>
              <div className="flex justify-center">
                <Button variant="outline" onClick={() => setActiveTab('overview')}>
                  Back to Overview
                </Button>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
};

export default SubscriptionDashboard;
