const winston = require('winston');
const path = require('path');
const fs = require('fs');

// Ensure logs directory exists
const logsDir = path.join(__dirname, '..', 'logs');
try {
  if (!fs.existsSync(logsDir)) {
    fs.mkdirSync(logsDir, { recursive: true });
  }
} catch (error) {
  console.error('Failed to create logs directory:', error);
  // Fallback to console-only logging if directory creation fails
}

// Custom format for better readability
const customFormat = winston.format.combine(
  winston.format.timestamp({
    format: 'YYYY-MM-DD HH:mm:ss'
  }),
  winston.format.errors({ stack: true }),
  winston.format.printf(({ level, message, timestamp, stack, ...meta }) => {
    let log = `${timestamp} [${level.toUpperCase()}]: ${message}`;
    
    // Add stack trace for errors
    if (stack) {
      log += `\n${stack}`;
    }
    
    // Add metadata if present
    if (Object.keys(meta).length > 0) {
      log += `\nMetadata: ${JSON.stringify(meta, null, 2)}`;
    }
    
    return log;
  })
);

// Create logger instance
const logger = winston.createLogger({
  level: process.env.LOG_LEVEL || 'info',
  format: customFormat,
  transports: [
    // Error log file
    new winston.transports.File({
      filename: path.join(logsDir, 'error.log'),
      level: 'error',
      maxsize: 5242880, // 5MB
      maxFiles: 5,
      tailable: true
    }),
    
    // Combined log file
    new winston.transports.File({
      filename: path.join(logsDir, 'combined.log'),
      maxsize: 5242880, // 5MB
      maxFiles: 5,
      tailable: true
    }),
    
    // Performance log file for monitoring
    new winston.transports.File({
      filename: path.join(logsDir, 'performance.log'),
      level: 'info',
      maxsize: 5242880, // 5MB
      maxFiles: 3,
      tailable: true,
      format: winston.format.combine(
        winston.format.timestamp(),
        winston.format.json()
      )
    })
  ]
});

// Add console transport for development
if (process.env.NODE_ENV !== 'production') {
  logger.add(new winston.transports.Console({
    format: winston.format.combine(
      winston.format.colorize(),
      winston.format.simple()
    )
  }));
}

// Helper functions for common logging patterns
const logHelpers = {
  // Log API requests
  logRequest: (req, res, responseTime) => {
    const logData = {
      method: req.method,
      url: req.url,
      statusCode: res.statusCode,
      responseTime: `${responseTime}ms`,
      userAgent: req.get('User-Agent'),
      ip: req.ip,
      userId: req.user?.id || 'anonymous'
    };
    
    if (res.statusCode >= 400) {
      logger.error('HTTP Request Error', logData);
    } else {
      logger.info('HTTP Request', logData);
    }
  },

  // Log video generation events
  logVideoGeneration: (event, data) => {
    logger.info(`Video Generation: ${event}`, {
      event,
      ...data,
      timestamp: new Date().toISOString()
    });
  },

  // Log performance metrics
  logPerformance: (metric, value, context = {}) => {
    logger.info('Performance Metric', {
      metric,
      value,
      context,
      timestamp: new Date().toISOString()
    });
  },

  // Log security events
  logSecurity: (event, data) => {
    logger.warn(`Security Event: ${event}`, {
      event,
      ...data,
      timestamp: new Date().toISOString()
    });
  },

  // Log database operations
  logDatabase: (operation, duration, query = null) => {
    const logData = {
      operation,
      duration: `${duration}ms`,
      timestamp: new Date().toISOString()
    };
    
    if (query) {
      logData.query = query.substring(0, 200); // Truncate long queries
    }
    
    if (duration > 1000) { // Log slow queries
      logger.warn('Slow Database Operation', logData);
    } else {
      logger.info('Database Operation', logData);
    }
  }
};

// Export logger and helpers
module.exports = {
  logger,
  ...logHelpers
};
