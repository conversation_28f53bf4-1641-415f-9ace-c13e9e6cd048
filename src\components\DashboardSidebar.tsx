
import React, { useState } from "react";
import { Link, useLocation, useNavigate } from "react-router-dom";
import { useAuth } from "@/contexts/AuthContext";
import { cn } from "@/lib/utils";
import { Button } from "@/components/ui/button";
import {
  Home,
  Image,
  LogOut,
  Settings,
  User,
  ChevronRight,
  ChevronDown,
  Sparkles,
  Palette,
  Wand2,
  Copy,
  Loader2,
  Video,
  History,
  Film,
  Mic,
  Music
} from "lucide-react";
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from "@/components/ui/tooltip";
import { Separator } from "@/components/ui/separator";
import { Collapsible, CollapsibleContent, CollapsibleTrigger } from "@/components/ui/collapsible";

interface NavItemProps {
  to: string;
  icon: React.ElementType;
  label: string;
  active: boolean;
  disabled?: boolean; // Added for top-level items
  isComingSoon?: boolean; // Added for top-level items
  subItems?: Array<{
    to: string;
    icon: React.ElementType;
    label: string;
    disabled?: boolean;
    isComingSoon?: boolean;
  }>;
}

const NavItem = ({
  to,
  icon: Icon,
  label,
  active,
  subItems,
  disabled, // Added prop
  isComingSoon, // Added prop
}: NavItemProps) => {
  const location = useLocation();
  const [isOpen, setIsOpen] = useState(
    subItems?.some(item => location.pathname === item.to) || false
  );

  const hasActiveSubItem = subItems?.some(item => location.pathname === item.to);

  if (subItems) {
    return (
      <Collapsible open={isOpen} onOpenChange={setIsOpen}>
        <CollapsibleTrigger asChild>
          <button
            className={cn(
              "w-full flex items-center justify-between px-3 py-2.5 rounded-md transition-all",
              "text-sm font-medium",
              (active || hasActiveSubItem)
                ? "bg-gray-100 dark:bg-gray-800 text-brand-purple"
                : "text-gray-600 dark:text-gray-400 hover:bg-gray-50 dark:hover:bg-gray-800/50"
            )}
          >
            <div className="flex items-center gap-3">
              <div className={cn(
                "flex items-center justify-center w-7 h-7 rounded-md",
                (active || hasActiveSubItem) ? "bg-brand-purple/10 text-brand-purple" : "text-gray-500"
              )}>
                <Icon size={16} />
              </div>
              <span>{label}</span>
            </div>
            <ChevronDown
              size={14}
              className={cn(
                "text-gray-500 transition-transform duration-200",
                isOpen && "rotate-180"
              )}
            />
          </button>
        </CollapsibleTrigger>
        <CollapsibleContent>
          <div className="pl-10 pr-2 mt-1 space-y-1">
            {subItems.map((item) => (
              <Link
                key={item.label} // Use label for key if 'to' might be '#' for multiple disabled items
                to={item.disabled ? "#" : item.to}
                onClick={item.disabled ? (e) => e.preventDefault() : undefined}
                className={cn(
                  "flex items-center justify-between px-3 py-2 rounded-md text-sm transition-all w-full",
                  (location.pathname === item.to && !item.disabled)
                    ? "bg-brand-purple/10 text-brand-purple font-medium"
                    : "text-gray-600 dark:text-gray-400",
                  item.disabled
                    ? "opacity-60 pointer-events-none text-gray-500 dark:text-gray-500" // Disabled styles
                    : "hover:bg-gray-50 dark:hover:bg-gray-800/50"
                )}
                aria-disabled={item.disabled}
                tabIndex={item.disabled ? -1 : undefined}
              >
                <div className="flex items-center gap-2">
                  <item.icon size={14} />
                  <span>{item.label}</span>
                </div>
                {item.isComingSoon && (
                  <span className="text-xs text-gray-400 dark:text-gray-500 ml-2 whitespace-nowrap">
                    Coming soon
                  </span>
                )}
              </Link>
            ))}
          </div>
        </CollapsibleContent>
      </Collapsible>
    );
  }

  return (
    <Link
      to={disabled ? "#" : to}
      onClick={disabled ? (e) => e.preventDefault() : undefined}
      className={cn(
        "flex items-center justify-between px-3 py-2.5 rounded-md transition-all",
        "text-sm font-medium",
        active && !disabled
          ? "bg-gray-100 dark:bg-gray-800 text-brand-purple"
          : "text-gray-600 dark:text-gray-400",
        disabled
          ? "opacity-60 pointer-events-none text-gray-500 dark:text-gray-500"
          : "hover:bg-gray-50 dark:hover:bg-gray-800/50"
      )}
      aria-disabled={disabled}
      tabIndex={disabled ? -1 : undefined}
    >
      <div className="flex items-center gap-3">
        <div className={cn(
          "flex items-center justify-center w-7 h-7 rounded-md",
          active ? "bg-brand-purple/10 text-brand-purple" : "text-gray-500"
        )}>
          <Icon size={16} />
        </div>
        <span>{label}</span>
      </div>
      {isComingSoon && !subItems && (
        <span className="text-xs text-gray-400 dark:text-gray-500 ml-auto whitespace-nowrap">
          Coming soon
        </span>
      )}
      {active && !disabled && !subItems && <ChevronRight size={14} className="text-brand-purple" />}
    </Link>
  );
};

const DashboardSidebar = () => {
  const location = useLocation();
  const navigate = useNavigate();
  const { logout, user, isLoggingOut } = useAuth();

  const handleLogout = () => {
    logout(() => navigate("/"));
  };

  const navItems = [
    {
      label: "Dashboard",
      icon: Home,
      to: "/dashboard",
    },
    {
      label: "Image Tools",
      icon: Image,
      to: "/image-tools", // Main link, can be first sub-item or a dedicated page
      subItems: [
        {
          label: "Image Generator",
          icon: Sparkles, // Or Image
          to: "/image-generator",
        },
        {
          label: "Image Conditioning",
          icon: Wand2,
          to: "/image-conditioning",
        },
        {
          label: "Background Removal",
          icon: Image, // Consider a more specific icon if available
          to: "/background-removal",
        },
        {
          label: "Color-Guided",
          icon: Palette,
          to: "/color-guided-generation",
        },
        {
          label: "Image Variation",
          icon: Copy,
          to: "/image-variation",
        },
      ]
    },
    {
      label: "Video Tools",
      icon: Video,
      to: "/video-generator",
      subItems: [
        {
          label: "Single Shot Video",
          icon: Video,
          to: "/video-generator",
        },
        {
          label: "Multi Shot Video",
          icon: Film,
          to: "/multi-shot-video-generator",
        },
        // Voice Tools removed from here
      ]
    },
    {
      label: "Audio Tools",
      icon: Mic,
      to: "/audio-tools",
      subItems: [
        {
          label: "Audio Generator",
          icon: Mic,
          to: "/voice-generator",
        },
        {
          label: "Music Generator",
          icon: Music,
          to: "/music-generator",
          disabled: true,
          isComingSoon: true,
        },
      ]
    },
    {
      label: "Your Profile",
      icon: User,
      to: "/profile",
    },
  ];

  return (
    <div className="fixed left-0 top-0 w-64 h-screen flex flex-col bg-white dark:bg-gray-900 border-r border-gray-200 dark:border-gray-800 z-40">
      {/* Header */}
      <div className="p-4 flex items-center justify-between">
        <Link to="/" className="flex items-center gap-2">
          <img src="/logo.png" alt="VibeNecto Logo" className="h-8 w-auto" />
          <span className="text-lg font-semibold bg-clip-text text-transparent bg-gradient-to-r from-brand-purple to-brand-teal">
            VibeNecto
          </span>
        </Link>
      </div>

      <Separator className="mb-4" />

      {/* User info */}
      <div className="px-4 mb-6">
        <div className="flex items-center gap-3 p-2 rounded-lg bg-gray-50 dark:bg-gray-800/50">
          <div className="flex items-center justify-center w-8 h-8 rounded-full bg-brand-purple/10 text-brand-purple">
            <User size={14} />
          </div>
          <div className="flex-1 min-w-0">
            <p className="text-sm font-medium truncate">{user?.name || 'User'}</p>
            <p className="text-xs text-gray-500 truncate">{user?.email || '<EMAIL>'}</p>
          </div>
        </div>
      </div>

      {/* Navigation */}
      <div className="px-3 space-y-1 flex-grow">
        <p className="text-xs font-medium text-gray-500 dark:text-gray-400 px-3 mb-2">MAIN MENU</p>
        {navItems.map((item) => (
          <NavItem
            key={item.to}
            to={item.to}
            icon={item.icon}
            label={item.label}
            active={location.pathname === item.to && !('disabled' in item && item.disabled)}
            subItems={item.subItems}
            disabled={'disabled' in item ? item.disabled : false}
            isComingSoon={'isComingSoon' in item ? item.isComingSoon : false}
          />
        ))}
      </div>

      {/* Footer */}
      <div className="p-3 mt-auto">
        <Separator className="mb-3" />
        <div className="flex items-center justify-between mb-3">
          <TooltipProvider>
            <Tooltip>
              <TooltipTrigger asChild>
                <Button variant="ghost" size="icon" className="h-8 w-8 rounded-full" asChild>
                  <Link to="/profile">
                    <Settings size={14} />
                  </Link>
                </Button>
              </TooltipTrigger>
              <TooltipContent>
                <p className="text-xs">Profile Settings</p>
              </TooltipContent>
            </Tooltip>
          </TooltipProvider>

          <Button
            variant="ghost"
            size="sm"
            className="text-xs text-gray-500 hover:text-gray-900 dark:text-gray-400 dark:hover:text-gray-300"
            onClick={handleLogout}
            disabled={isLoggingOut}
          >
            {isLoggingOut ? (
              <>
                <Loader2 size={14} className="mr-1.5 animate-spin" />
                Signing Out
              </>
            ) : (
              <>
                <LogOut size={14} className="mr-1.5" />
                Sign Out
              </>
            )}
          </Button>
        </div>
        <p className="text-xs text-center text-gray-400">VibeNecto v2.0</p>
      </div>
    </div>
  );
};

export default DashboardSidebar;
