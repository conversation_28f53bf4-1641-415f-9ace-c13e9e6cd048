import React from 'react';
import { Download, Trash2 } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { toast } from 'sonner';
import { UI_TEXT, TOAST_MESSAGES, PROCESSING } from '@/constants/imageVariation';

interface ImageVariationResultProps {
  resultImage: string | null;
  onReset: () => void;
}

const ImageVariationResult: React.FC<ImageVariationResultProps> = ({
  resultImage,
  onReset,
}) => {
  const downloadImage = () => {
    if (!resultImage) return;

    const link = document.createElement('a');
    link.href = resultImage;
    link.download = PROCESSING.generateFilename();
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);

    toast.success(TOAST_MESSAGES.DOWNLOAD_SUCCESS);
  };

  if (!resultImage) {
    return null;
  }

  return (
    <div>
      <div className="rounded-lg overflow-hidden bg-gray-100 dark:bg-gray-800 mb-4 h-[400px] flex items-center justify-center">
        <img
          src={resultImage}
          alt="Result"
          className="max-w-full max-h-full object-contain"
        />
      </div>
      <p className="text-center text-sm text-gray-500 mb-4">
        {UI_TEXT.RESULT.SUCCESS_MESSAGE}
      </p>
      <div className="flex items-center justify-center gap-2">
        <Button
          size="sm"
          variant="outline"
          className="h-8 text-xs gap-1"
          onClick={onReset}
        >
          <Trash2 size={12} />
          {UI_TEXT.BUTTONS.START_OVER}
        </Button>
        <Button
          size="sm"
          className="h-8 text-xs gap-1 bg-brand-purple hover:bg-brand-purple/90"
          onClick={downloadImage}
        >
          <Download size={12} />
          {UI_TEXT.BUTTONS.DOWNLOAD}
        </Button>
      </div>
    </div>
  );
};

export default ImageVariationResult;