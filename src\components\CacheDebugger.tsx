import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { 
  Database, 
  Trash2, 
  RefreshCw, 
  TrendingUp, 
  Clock, 
  HardDrive,
  Zap,
  Target
} from 'lucide-react';
import { presignedUrlCache } from '@/services/presignedUrlCache';

interface CacheDebuggerProps {
  className?: string;
  onCacheAction?: (action: string, data?: any) => void;
}

const CacheDebugger: React.FC<CacheDebuggerProps> = ({
  className,
  onCacheAction,
}) => {
  // Only show in development
  if (!import.meta.env.DEV) {
    return null;
  }

  const [metrics, setMetrics] = useState(presignedUrlCache.getMetrics());
  const [isExpanded, setIsExpanded] = useState(false);

  // Update metrics every 5 seconds
  useEffect(() => {
    const interval = setInterval(() => {
      setMetrics(presignedUrlCache.getMetrics());
    }, 5000);

    return () => clearInterval(interval);
  }, []);

  const handleClearCache = () => {
    presignedUrlCache.clear();
    setMetrics(presignedUrlCache.getMetrics());
    onCacheAction?.('clear');
  };

  const handleRefreshMetrics = () => {
    setMetrics(presignedUrlCache.getMetrics());
    onCacheAction?.('refresh', metrics);
  };

  const hitRate = presignedUrlCache.getHitRate();
  const efficiency = metrics.totalRequests > 0 ? ((metrics.hits / metrics.totalRequests) * 100) : 0;

  const getEfficiencyColor = (rate: number) => {
    if (rate >= 80) return 'text-green-600';
    if (rate >= 60) return 'text-yellow-600';
    return 'text-red-600';
  };

  const getEfficiencyBadge = (rate: number) => {
    if (rate >= 80) return { variant: 'default' as const, label: 'Excellent' };
    if (rate >= 60) return { variant: 'secondary' as const, label: 'Good' };
    if (rate >= 40) return { variant: 'outline' as const, label: 'Fair' };
    return { variant: 'destructive' as const, label: 'Poor' };
  };

  const cacheUtilization = (metrics.cacheSize / 500) * 100; // Assuming max size of 500

  return (
    <Card className={`fixed top-4 right-4 w-80 z-50 bg-white/95 backdrop-blur-sm border shadow-lg ${className}`}>
      <CardHeader className="pb-3">
        <CardTitle className="text-sm font-medium flex items-center justify-between">
          <div className="flex items-center gap-2">
            <Database className="h-4 w-4" />
            Cache Performance
          </div>
          <Button
            variant="ghost"
            size="sm"
            onClick={() => setIsExpanded(!isExpanded)}
            className="h-6 w-6 p-0"
          >
            {isExpanded ? '−' : '+'}
          </Button>
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-3">
        {/* Quick Stats */}
        <div className="grid grid-cols-2 gap-3 text-xs">
          <div className="space-y-1">
            <div className="flex items-center gap-1">
              <Target className="h-3 w-3 text-green-600" />
              <span>Hit Rate: {hitRate.toFixed(1)}%</span>
            </div>
            <div className="flex items-center gap-1">
              <HardDrive className="h-3 w-3 text-blue-600" />
              <span>Cached: {metrics.cacheSize}</span>
            </div>
          </div>
          <div className="space-y-1">
            <div className="flex items-center gap-1">
              <TrendingUp className="h-3 w-3 text-purple-600" />
              <span>Requests: {metrics.totalRequests}</span>
            </div>
            <div className="flex items-center gap-1">
              <Clock className="h-3 w-3 text-orange-600" />
              <span>Avg: {metrics.averageLoadTime.toFixed(0)}ms</span>
            </div>
          </div>
        </div>

        {/* Cache Utilization */}
        <div className="space-y-2">
          <div className="flex justify-between text-xs">
            <span>Cache Utilization</span>
            <span>{cacheUtilization.toFixed(1)}%</span>
          </div>
          <Progress value={cacheUtilization} className="h-2" />
        </div>

        {/* Efficiency Badge */}
        <div className="flex items-center justify-between">
          <span className="text-xs">Efficiency:</span>
          <Badge 
            variant={getEfficiencyBadge(efficiency).variant}
            className="text-xs px-2 py-0"
          >
            {getEfficiencyBadge(efficiency).label}
          </Badge>
        </div>

        {/* Expanded Details */}
        {isExpanded && (
          <>
            <div className="border-t pt-3 space-y-2">
              <div className="grid grid-cols-2 gap-2 text-xs">
                <div>
                  <span className="text-gray-500">Hits:</span>
                  <span className="ml-1 font-medium text-green-600">{metrics.hits}</span>
                </div>
                <div>
                  <span className="text-gray-500">Misses:</span>
                  <span className="ml-1 font-medium text-red-600">{metrics.misses}</span>
                </div>
                <div>
                  <span className="text-gray-500">Evictions:</span>
                  <span className="ml-1 font-medium text-yellow-600">{metrics.evictions}</span>
                </div>
                <div>
                  <span className="text-gray-500">Size:</span>
                  <span className="ml-1 font-medium">{metrics.cacheSize}/500</span>
                </div>
              </div>
            </div>

            {/* Performance Insights */}
            <div className="border-t pt-3 text-xs text-gray-600">
              {hitRate < 50 && (
                <div className="text-red-600">⚠️ Low hit rate - consider longer TTL</div>
              )}
              {metrics.evictions > metrics.cacheSize && (
                <div className="text-yellow-600">🔄 High eviction rate - consider larger cache</div>
              )}
              {metrics.averageLoadTime > 1000 && (
                <div className="text-orange-600">⏱️ Slow load times - check network</div>
              )}
              {hitRate >= 80 && metrics.evictions < 10 && (
                <div className="text-green-600">✅ Optimal cache performance!</div>
              )}
            </div>

            {/* Action Buttons */}
            <div className="border-t pt-3 flex gap-2">
              <Button
                variant="outline"
                size="sm"
                onClick={handleRefreshMetrics}
                className="flex-1 text-xs h-7"
              >
                <RefreshCw className="h-3 w-3 mr-1" />
                Refresh
              </Button>
              <Button
                variant="destructive"
                size="sm"
                onClick={handleClearCache}
                className="flex-1 text-xs h-7"
              >
                <Trash2 className="h-3 w-3 mr-1" />
                Clear
              </Button>
            </div>
          </>
        )}

        {/* Cache Status Indicator */}
        <div className="flex items-center justify-between text-xs pt-2 border-t">
          <span className="text-gray-500">Status:</span>
          <div className="flex items-center gap-1">
            <div className={`w-2 h-2 rounded-full ${hitRate >= 70 ? 'bg-green-500' : hitRate >= 40 ? 'bg-yellow-500' : 'bg-red-500'}`} />
            <span className={getEfficiencyColor(hitRate)}>
              {hitRate >= 70 ? 'Healthy' : hitRate >= 40 ? 'Moderate' : 'Poor'}
            </span>
          </div>
        </div>
      </CardContent>
    </Card>
  );
};

export default CacheDebugger;
