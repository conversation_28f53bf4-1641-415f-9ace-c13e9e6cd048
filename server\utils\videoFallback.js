/**
 * Video S3 fallback utilities for VibeNecto
 * Handles complex S3 video discovery and fallback logic
 */

const { S3Client, ListObjectsV2Command, HeadObjectCommand } = require('@aws-sdk/client-s3');
const { logger } = require('./logger');
const { generatePresignedUrl } = require('../services/s3-service');

// Initialize S3 client
const s3Client = new S3Client({ region: process.env.AWS_REGION || 'us-east-1' });
const bucketName = process.env.AWS_S3_BUCKET_NAME || 'vibenecto-storage';

/**
 * Extract job ID from ARN format
 */
function extractJobIdFromArn(jobId) {
  return jobId.split('/').pop();
}

/**
 * Generate common S3 search paths for a job
 */
function generateSearchPaths(jobIdFromArn) {
  return [
    `videos/job-${jobIdFromArn}/output.mp4`,
    `videos/${jobIdFromArn}/output.mp4`,
    `videos/job-${jobIdFromArn}/${jobIdFromArn}/output.mp4`
  ];
}

/**
 * Try to find video at direct S3 paths
 */
async function tryDirectPaths(jobId, requestId) {
  const jobIdFromArn = extractJobIdFromArn(jobId);
  const searchPaths = generateSearchPaths(jobIdFromArn);
  
  logger.info('🔍 [DEBUG] Trying direct S3 paths', {
    jobId,
    jobIdFromArn,
    pathsToTry: searchPaths.length,
    requestId
  });
  
  for (const path of searchPaths) {
    try {
      const headCommand = new HeadObjectCommand({
        Bucket: bucketName,
        Key: path
      });
      
      await s3Client.send(headCommand);
      
      // File exists! Generate presigned URL
      logger.info('✅ [DEBUG] Found video at direct path', { jobId, path, requestId });
      
      const presignedResult = await generatePresignedUrl({
        key: path,
        expirySeconds: 3600
      });
      
      return {
        found: true,
        s3Key: path,
        videoUrl: presignedResult.success ? presignedResult.url : null,
        presignedUrl: presignedResult.success ? presignedResult.url : null
      };
    } catch (headError) {
      logger.debug('❌ [DEBUG] Path not found', { path, error: headError.name, requestId });
    }
  }
  
  return { found: false };
}

/**
 * Search for recent video files in S3
 */
async function searchRecentVideos(jobId, requestId) {
  const jobIdFromArn = extractJobIdFromArn(jobId);
  
  logger.info('🔍 [DEBUG] Searching recent videos in S3', { jobId, requestId });
  
  try {
    const listCommand = new ListObjectsV2Command({
      Bucket: bucketName,
      Prefix: 'videos/',
      MaxKeys: 50
    });
    
    const listResponse = await s3Client.send(listCommand);
    const files = listResponse.Contents || [];
    
    // Look for recent video files (within last 2 hours) that might match our job
    const twoHoursAgo = new Date(Date.now() - 2 * 60 * 60 * 1000);
    const recentVideoFiles = files.filter(file => {
      if (!file.Key || !file.LastModified) return false;
      
      const isVideo = file.Key.toLowerCase().endsWith('.mp4');
      const isRecent = new Date(file.LastModified) > twoHoursAgo;
      const mightMatch = file.Key.includes(jobIdFromArn) ||
                        file.Key.includes('output') ||
                        file.Key.includes('video');
      
      return isVideo && isRecent && mightMatch;
    });
    
    if (recentVideoFiles.length > 0) {
      // Sort by most recent and take the first one
      const sortedFiles = recentVideoFiles.sort((a, b) =>
        new Date(b.LastModified).getTime() - new Date(a.LastModified).getTime()
      );
      
      const mostRecentVideo = sortedFiles[0];
      logger.info('✅ [DEBUG] Found recent video file', {
        jobId,
        path: mostRecentVideo.Key,
        lastModified: mostRecentVideo.LastModified,
        requestId
      });
      
      const presignedResult = await generatePresignedUrl({
        key: mostRecentVideo.Key,
        expirySeconds: 3600
      });
      
      return {
        found: true,
        s3Key: mostRecentVideo.Key,
        videoUrl: presignedResult.success ? presignedResult.url : null,
        presignedUrl: presignedResult.success ? presignedResult.url : null
      };
    }
    
    return { found: false };
  } catch (listError) {
    logger.error('❌ [DEBUG] Failed to list recent videos', {
      jobId,
      error: listError.message,
      requestId
    });
    return { found: false, error: listError.message };
  }
}

/**
 * Main S3 fallback function to check for completed videos when status polling fails
 */
async function tryS3VideoFallback(jobId, userId, requestId) {
  try {
    logger.info('🔍 [DEBUG] Starting S3 fallback search', { jobId, userId, requestId });
    
    // Try direct paths first
    const directResult = await tryDirectPaths(jobId, requestId);
    if (directResult.found) {
      return {
        success: true,
        ...directResult
      };
    }
    
    // If direct paths failed, try searching recent videos
    logger.info('🔍 [DEBUG] Direct paths failed, trying recent video search', { jobId, requestId });
    const recentResult = await searchRecentVideos(jobId, requestId);
    
    if (recentResult.found) {
      return {
        success: true,
        ...recentResult
      };
    }
    
    logger.warn('❌ [DEBUG] S3 fallback found no videos', { jobId, requestId });
    return { found: false };
    
  } catch (error) {
    logger.error('🚨 [DEBUG] S3 fallback error', {
      jobId,
      error: error.message,
      requestId
    });
    return { found: false, error: error.message };
  }
}

/**
 * Check if job should use S3 fallback based on age
 */
function shouldUseFallback(createdAt, thresholdMinutes = 2) {
  const jobAge = Date.now() - new Date(createdAt).getTime();
  const threshold = thresholdMinutes * 60 * 1000;
  return jobAge > threshold;
}

/**
 * Batch check multiple videos for S3 fallback
 */
async function batchCheckS3Fallback(videos, requestId) {
  const results = [];
  
  for (const video of videos) {
    try {
      logger.info('🔍 [DEBUG] Checking S3 fallback for video', {
        videoId: video.id,
        jobId: video.job_id,
        requestId
      });

      const fallbackResult = await tryS3VideoFallback(video.job_id, video.user_id, requestId);
      
      results.push({
        video,
        fallbackResult,
        recovered: fallbackResult.found
      });
    } catch (error) {
      logger.error('Error during batch S3 fallback check', {
        videoId: video.id,
        jobId: video.job_id,
        error: error.message,
        requestId
      });
      
      results.push({
        video,
        fallbackResult: { found: false, error: error.message },
        recovered: false
      });
    }
  }
  
  return results;
}
/**
 * Handle video status check with comprehensive fallback logic
 */
async function handleVideoStatusCheck(jobId, requestId) {
  const { updateVideoStatus, getVideoByJobId } = require('./databaseHelpers');
  const { checkVideoGenerationStatus } = require('../services/bedrock-service');
  const { generatePresignedUrl } = require('../services/s3-service');
  const { cacheHelpers } = require('./cache');
  const { logger } = require('./logger');

  // Check cache first
  const cachedStatus = cacheHelpers.getVideoStatus(jobId);
  if (cachedStatus && cachedStatus !== 'pending') {
    logger.debug('Returning cached video status', {
      jobId,
      status: cachedStatus,
      requestId
    });

    return {
      success: true,
      status: cachedStatus,
      jobId: jobId,
      cached: true
    };
  }

  // Get the database record
  const videoResult = await getVideoByJobId(jobId, requestId);
  if (videoResult.error || !videoResult.data) {
    return {
      success: false,
      error: 'Video record not found',
      jobId: jobId
    };
  }

  const videoRecord = videoResult.data;

  // If already completed with S3 key, return immediately
  if (videoRecord.status === 'completed' && videoRecord.s3_key) {
    try {
      const presignedResult = await generatePresignedUrl({
        key: videoRecord.s3_key,
        expirySeconds: 3600
      });
      
      return {
        success: true,
        status: 'completed',
        jobId: jobId,
        videoId: videoRecord.id,
        videoUrl: presignedResult.success ? presignedResult.url : null
      };
    } catch (presignedError) {
      logger.warn('Failed to generate presigned URL for completed video', {
        jobId,
        error: presignedError.message,
        requestId
      });
    }
  }

  // Check the job status with Bedrock
  const statusResult = await checkVideoGenerationStatus({ jobId });

  // If Bedrock status check failed, try S3 fallback
  if (!statusResult.success) {
    const s3FallbackResult = await tryS3VideoFallback(jobId, videoRecord.user_id, requestId);
    
    if (s3FallbackResult.found) {
      // Update database with found video
      await updateVideoStatus(jobId, {
        status: 'completed',
        s3_key: s3FallbackResult.s3Key,
        completed_at: new Date().toISOString()
      }, requestId);

      return {
        success: true,
        status: 'completed',
        jobId: jobId,
        videoUrl: s3FallbackResult.presignedUrl,
        videoId: videoRecord.id,
        fallback: true
      };
    } else {
      return {
        success: false,
        error: statusResult.error || 'Failed to check video status'
      };
    }
  }

  // Check if job should use S3 fallback based on age
  if (statusResult.success && statusResult.status === 'processing' && 
      shouldUseFallback(videoRecord.created_at, 2)) {
    
    const s3FallbackResult = await tryS3VideoFallback(jobId, videoRecord.user_id, requestId);
    if (s3FallbackResult.found) {
      // Update database with found video
      await updateVideoStatus(jobId, {
        status: 'completed',
        s3_key: s3FallbackResult.s3Key,
        completed_at: new Date().toISOString()
      }, requestId);

      return {
        success: true,
        status: 'completed',
        jobId: jobId,
        videoUrl: s3FallbackResult.presignedUrl,
        videoId: videoRecord.id,
        fallback: true,
        foundViaLongRunningFallback: true
      };
    }
  }

  // Update the database record with the current status
  const updateData = {
    status: statusResult.status
  };

  if (statusResult.error) {
    updateData.error_message = statusResult.error;
  }

  if (statusResult.status === 'completed') {
    updateData.completed_at = new Date().toISOString();
    if (statusResult.videoS3Key) {
      updateData.s3_key = statusResult.videoS3Key;
    }
  }

  const updateResult = await updateVideoStatus(jobId, updateData, requestId);
  const updatedVideo = updateResult.data;

  // Build response
  const response = {
    success: true,
    status: statusResult.status,
    jobId: jobId
  };

  // If completed, try to generate presigned URL
  if (statusResult.status === 'completed') {
    const s3KeyToUse = updatedVideo?.s3_key || updateData?.s3_key;
    
    if (s3KeyToUse) {
      try {
        const presignedResult = await generatePresignedUrl({ 
          key: s3KeyToUse, 
          expirySeconds: 3600 
        });
        
        if (presignedResult.success) {
          response.videoUrl = presignedResult.url;
        }
      } catch (presignedUrlError) {
        logger.error('Exception generating presigned URL', {
          jobId,
          s3_key: s3KeyToUse,
          error: presignedUrlError.message,
          requestId
        });
      }
    }
    
    if (updatedVideo) {
      response.videoId = updatedVideo.id;
    }
  }

  if (statusResult.error) {
    response.error = statusResult.error;
  }

  return response;
}

module.exports = {
  tryS3VideoFallback,
  shouldUseFallback,
  batchCheckS3Fallback,
  extractJobIdFromArn,
  generateSearchPaths,
  handleVideoStatusCheck
};