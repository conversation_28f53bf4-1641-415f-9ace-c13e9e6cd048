import React from 'react';
import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>er, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { 
  Play, 
  Clock, 
  Zap, 
  RefreshCw, 
  Eye, 
  CheckCircle, 
  AlertCircle,
  TrendingUp,
  Database
} from 'lucide-react';

interface VideoLoadingMetrics {
  totalVideos: number;
  loadedVideos: number;
  errorVideos: number;
  videosInView: number;
  averageLoadTime: number;
  cacheHitRate: number;
  progressiveEnhancements: number;
  retryAttempts: number;
  loadStartTime: number;
  loadEndTime?: number;
  isComplete: boolean;
}

interface ProgressiveVideoLoadingStatsProps {
  metrics: VideoLoadingMetrics;
  className?: string;
  showDetailedStats?: boolean;
}

/**
 * Progressive Video Loading Stats Component - Sprint 18 Phase 2
 * 
 * Real-time performance dashboard for video progressive loading metrics.
 * Based on the successful image progressive loading stats from Sprint 17.
 * 
 * Features:
 * - Real-time video loading performance metrics
 * - Cache hit rate monitoring
 * - Progressive enhancement tracking
 * - Visual progress indicators
 * - Development-only display
 */
const ProgressiveVideoLoadingStats: React.FC<ProgressiveVideoLoadingStatsProps> = ({
  metrics,
  className,
  showDetailedStats = true,
}) => {
  // Don't render in production
  if (!import.meta.env.DEV) {
    return null;
  }

  const {
    totalVideos,
    loadedVideos,
    errorVideos,
    videosInView,
    averageLoadTime,
    cacheHitRate,
    progressiveEnhancements,
    retryAttempts,
    loadStartTime,
    loadEndTime,
    isComplete,
  } = metrics;

  const completionRate = totalVideos > 0 ? ((loadedVideos + errorVideos) / totalVideos) * 100 : 0;
  const successRate = (loadedVideos + errorVideos) > 0 ? (loadedVideos / (loadedVideos + errorVideos)) * 100 : 0;
  const totalLoadTime = loadEndTime ? loadEndTime - loadStartTime : Date.now() - loadStartTime;

  const formatTime = (ms: number) => {
    if (ms < 1000) return `${ms}ms`;
    return `${(ms / 1000).toFixed(1)}s`;
  };

  const formatPercentage = (value: number) => `${value.toFixed(1)}%`;

  return (
    <Card className={className}>
      <CardHeader className="pb-3">
        <CardTitle className="text-sm font-medium flex items-center gap-2">
          <Play className="h-4 w-4 text-blue-500" />
          Video Loading Performance
          {isComplete && (
            <Badge variant="outline" className="text-green-600 border-green-600">
              Complete
            </Badge>
          )}
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        {/* Overall Progress */}
        <div className="space-y-2">
          <div className="flex justify-between text-sm">
            <span>Overall Progress</span>
            <span>{loadedVideos + errorVideos}/{totalVideos}</span>
          </div>
          <Progress value={completionRate} className="h-2" />
        </div>

        {/* Key Metrics Grid */}
        <div className="grid grid-cols-2 gap-3">
          {/* Success Rate */}
          <div className="flex items-center gap-2">
            <CheckCircle className="h-4 w-4 text-green-500" />
            <div>
              <div className="text-xs text-muted-foreground">Success Rate</div>
              <div className="text-sm font-medium">{formatPercentage(successRate)}</div>
            </div>
          </div>

          {/* Cache Hit Rate */}
          <div className="flex items-center gap-2">
            <Database className="h-4 w-4 text-blue-500" />
            <div>
              <div className="text-xs text-muted-foreground">Cache Hit Rate</div>
              <div className="text-sm font-medium">{formatPercentage(cacheHitRate)}</div>
            </div>
          </div>

          {/* Average Load Time */}
          <div className="flex items-center gap-2">
            <Clock className="h-4 w-4 text-orange-500" />
            <div>
              <div className="text-xs text-muted-foreground">Avg Load Time</div>
              <div className="text-sm font-medium">{formatTime(averageLoadTime)}</div>
            </div>
          </div>

          {/* Videos in View */}
          <div className="flex items-center gap-2">
            <Eye className="h-4 w-4 text-purple-500" />
            <div>
              <div className="text-xs text-muted-foreground">In Viewport</div>
              <div className="text-sm font-medium">{videosInView}</div>
            </div>
          </div>
        </div>

        {/* Detailed Stats */}
        {showDetailedStats && (
          <div className="space-y-3 pt-2 border-t">
            <div className="text-xs font-medium text-muted-foreground">Detailed Metrics</div>
            
            <div className="grid grid-cols-2 gap-3 text-xs">
              {/* Progressive Enhancements */}
              <div className="flex items-center gap-2">
                <TrendingUp className="h-3 w-3 text-green-500" />
                <span>Enhancements: {progressiveEnhancements}</span>
              </div>

              {/* Retry Attempts */}
              <div className="flex items-center gap-2">
                <RefreshCw className="h-3 w-3 text-yellow-500" />
                <span>Retries: {retryAttempts}</span>
              </div>

              {/* Error Count */}
              <div className="flex items-center gap-2">
                <AlertCircle className="h-3 w-3 text-red-500" />
                <span>Errors: {errorVideos}</span>
              </div>

              {/* Total Time */}
              <div className="flex items-center gap-2">
                <Zap className="h-3 w-3 text-blue-500" />
                <span>Total: {formatTime(totalLoadTime)}</span>
              </div>
            </div>

            {/* Performance Insights */}
            {isComplete && (
              <div className="space-y-1 pt-2 border-t">
                <div className="text-xs font-medium text-muted-foreground">Performance Insights</div>
                <div className="text-xs space-y-1">
                  {cacheHitRate > 70 && (
                    <div className="text-green-600">✓ Excellent cache performance</div>
                  )}
                  {successRate > 95 && (
                    <div className="text-green-600">✓ High success rate</div>
                  )}
                  {averageLoadTime < 1000 && (
                    <div className="text-green-600">✓ Fast loading times</div>
                  )}
                  {progressiveEnhancements > totalVideos * 0.3 && (
                    <div className="text-blue-600">ℹ High progressive enhancement usage</div>
                  )}
                  {retryAttempts > totalVideos * 0.1 && (
                    <div className="text-yellow-600">⚠ Consider optimizing retry logic</div>
                  )}
                  {errorVideos > totalVideos * 0.05 && (
                    <div className="text-red-600">⚠ High error rate detected</div>
                  )}
                </div>
              </div>
            )}
          </div>
        )}
      </CardContent>
    </Card>
  );
};

export default ProgressiveVideoLoadingStats;
