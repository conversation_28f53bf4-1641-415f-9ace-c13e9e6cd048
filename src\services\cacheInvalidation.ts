import { presignedUrlCache } from '@/services/presignedUrlCache';
import { QueryClient } from '@tanstack/react-query';

export interface CacheInvalidationOptions {
  invalidateQueries?: boolean;
  clearSessionStorage?: boolean;
  pattern?: RegExp;
  enableLogging?: boolean;
}

/**
 * Cache invalidation strategies for different scenarios
 */
export class CacheInvalidationService {
  private queryClient: QueryClient;
  private enableLogging: boolean;

  constructor(queryClient: QueryClient, enableLogging = import.meta.env.DEV) {
    this.queryClient = queryClient;
    this.enableLogging = enableLogging;
  }

  /**
   * Invalidate cache when an image is deleted
   */
  async onImageDeleted(s3_key: string, options: CacheInvalidationOptions = {}): Promise<void> {
    const { invalidateQueries = true, enableLogging = this.enableLogging } = options;

    if (enableLogging) {
      console.log(`[CacheInvalidation] Image deleted: ${s3_key}`);
    }

    // Remove from presigned URL cache
    presignedUrlCache.delete(s3_key);

    // Invalidate React Query cache
    if (invalidateQueries) {
      await this.queryClient.invalidateQueries({
        predicate: (query) => {
          const queryKey = query.queryKey;
          return queryKey[0] === 'presignedUrls' && 
                 typeof queryKey[1] === 'string' && 
                 queryKey[1].includes(s3_key);
        },
      });

      // Also invalidate image history queries
      await this.queryClient.invalidateQueries({
        queryKey: ['imageHistory'],
      });
    }
  }

  /**
   * Invalidate cache when multiple images are deleted
   */
  async onImagesDeleted(s3_keys: string[], options: CacheInvalidationOptions = {}): Promise<void> {
    const { invalidateQueries = true, enableLogging = this.enableLogging } = options;

    if (enableLogging) {
      console.log(`[CacheInvalidation] Images deleted: ${s3_keys.length} items`);
    }

    // Remove from presigned URL cache
    s3_keys.forEach(s3_key => {
      presignedUrlCache.delete(s3_key);
    });

    // Invalidate React Query cache
    if (invalidateQueries) {
      await this.queryClient.invalidateQueries({
        predicate: (query) => {
          const queryKey = query.queryKey;
          return queryKey[0] === 'presignedUrls' && 
                 typeof queryKey[1] === 'string' && 
                 s3_keys.some(key => queryKey[1].includes(key));
        },
      });

      // Also invalidate image history queries
      await this.queryClient.invalidateQueries({
        queryKey: ['imageHistory'],
      });
    }
  }

  /**
   * Invalidate cache when an image is updated/regenerated
   */
  async onImageUpdated(s3_key: string, options: CacheInvalidationOptions = {}): Promise<void> {
    const { invalidateQueries = true, enableLogging = this.enableLogging } = options;

    if (enableLogging) {
      console.log(`[CacheInvalidation] Image updated: ${s3_key}`);
    }

    // Remove from presigned URL cache to force refresh
    presignedUrlCache.delete(s3_key);

    // Invalidate React Query cache
    if (invalidateQueries) {
      await this.queryClient.invalidateQueries({
        predicate: (query) => {
          const queryKey = query.queryKey;
          return queryKey[0] === 'presignedUrls' && 
                 typeof queryKey[1] === 'string' && 
                 queryKey[1].includes(s3_key);
        },
      });
    }
  }

  /**
   * Invalidate cache for a specific user (e.g., when user logs out)
   */
  async onUserChanged(userId: string, options: CacheInvalidationOptions = {}): Promise<void> {
    const { 
      invalidateQueries = true, 
      clearSessionStorage = true, 
      enableLogging = this.enableLogging 
    } = options;

    if (enableLogging) {
      console.log(`[CacheInvalidation] User changed: ${userId}`);
    }

    // Clear all cache for user change
    presignedUrlCache.clear();

    // Clear session storage if requested
    if (clearSessionStorage && typeof window !== 'undefined') {
      try {
        sessionStorage.removeItem('presigned-url-cache');
      } catch (error) {
        if (enableLogging) {
          console.warn('[CacheInvalidation] Failed to clear sessionStorage:', error);
        }
      }
    }

    // Invalidate all React Query cache
    if (invalidateQueries) {
      await this.queryClient.invalidateQueries();
    }
  }

  /**
   * Invalidate cache based on a pattern (e.g., all images from a specific folder)
   */
  async onPatternInvalidation(pattern: RegExp, options: CacheInvalidationOptions = {}): Promise<void> {
    const { invalidateQueries = true, enableLogging = this.enableLogging } = options;

    if (enableLogging) {
      console.log(`[CacheInvalidation] Pattern invalidation: ${pattern.source}`);
    }

    // Remove matching entries from presigned URL cache
    const invalidatedCount = presignedUrlCache.invalidatePattern(pattern);

    if (enableLogging) {
      console.log(`[CacheInvalidation] Invalidated ${invalidatedCount} cached URLs`);
    }

    // Invalidate React Query cache
    if (invalidateQueries) {
      await this.queryClient.invalidateQueries({
        predicate: (query) => {
          const queryKey = query.queryKey;
          return queryKey[0] === 'presignedUrls' && 
                 typeof queryKey[1] === 'string' && 
                 pattern.test(queryKey[1]);
        },
      });
    }
  }

  /**
   * Proactive cache refresh for URLs that are about to expire
   */
  async refreshExpiringUrls(thresholdMs: number = 10 * 60 * 1000): Promise<void> {
    const expiringUrls = presignedUrlCache.getExpiringUrls(thresholdMs);
    
    if (expiringUrls.length > 0) {
      if (this.enableLogging) {
        console.log(`[CacheInvalidation] Refreshing ${expiringUrls.length} expiring URLs`);
      }

      // Remove expiring URLs to force refresh
      expiringUrls.forEach(s3_key => {
        presignedUrlCache.delete(s3_key);
      });

      // Invalidate React Query cache for these URLs
      await this.queryClient.invalidateQueries({
        predicate: (query) => {
          const queryKey = query.queryKey;
          return queryKey[0] === 'presignedUrls' && 
                 typeof queryKey[1] === 'string' && 
                 expiringUrls.some(key => queryKey[1].includes(key));
        },
      });
    }
  }

  /**
   * Emergency cache clear (for debugging or when cache is corrupted)
   */
  async emergencyCacheClear(options: CacheInvalidationOptions = {}): Promise<void> {
    const { 
      invalidateQueries = true, 
      clearSessionStorage = true, 
      enableLogging = this.enableLogging 
    } = options;

    if (enableLogging) {
      console.log('[CacheInvalidation] Emergency cache clear initiated');
    }

    // Clear all caches
    presignedUrlCache.clear();

    // Clear session storage
    if (clearSessionStorage && typeof window !== 'undefined') {
      try {
        sessionStorage.clear();
      } catch (error) {
        if (enableLogging) {
          console.warn('[CacheInvalidation] Failed to clear sessionStorage:', error);
        }
      }
    }

    // Invalidate all React Query cache
    if (invalidateQueries) {
      await this.queryClient.clear();
    }

    if (enableLogging) {
      console.log('[CacheInvalidation] Emergency cache clear completed');
    }
  }

  /**
   * Get cache statistics for monitoring
   */
  getCacheStats(): {
    presignedUrlCache: any;
    reactQueryCache: {
      queryCount: number;
      mutationCount: number;
    };
  } {
    return {
      presignedUrlCache: presignedUrlCache.getMetrics(),
      reactQueryCache: {
        queryCount: this.queryClient.getQueryCache().getAll().length,
        mutationCount: this.queryClient.getMutationCache().getAll().length,
      },
    };
  }
}

// Utility functions for common invalidation patterns
export const createCacheInvalidationService = (queryClient: QueryClient) => {
  return new CacheInvalidationService(queryClient);
};

export const invalidateImageCache = (queryClient: QueryClient, s3_key: string) => {
  const service = new CacheInvalidationService(queryClient);
  return service.onImageDeleted(s3_key);
};

export const invalidateUserCache = (queryClient: QueryClient, userId: string) => {
  const service = new CacheInvalidationService(queryClient);
  return service.onUserChanged(userId);
};
