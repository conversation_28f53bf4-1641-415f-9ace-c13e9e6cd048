
@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    --background: 0 0% 100%;
    --foreground: 222.2 84% 4.9%;

    --card: 0 0% 100%;
    --card-foreground: 222.2 84% 4.9%;

    --popover: 0 0% 100%;
    --popover-foreground: 222.2 84% 4.9%;

    --primary: 263 84% 50%;
    --primary-foreground: 0 0% 98%;

    --secondary: 173 85% 32%;
    --secondary-foreground: 0 0% 98%;

    --muted: 210 40% 96.1%;
    --muted-foreground: 215.4 16.3% 46.9%;

    --accent: 210 40% 96.1%;
    --accent-foreground: 222.2 47.4% 11.2%;

    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 210 40% 98%;

    --border: 214.3 31.8% 91.4%;
    --input: 214.3 31.8% 91.4%;
    --ring: 263 84% 50%;

    --radius: 0.5rem;

    --sidebar-background: 0 0% 98%;
    --sidebar-foreground: 240 5.3% 26.1%;
    --sidebar-primary: 240 5.9% 10%;
    --sidebar-primary-foreground: 0 0% 98%;
    --sidebar-accent: 240 4.8% 95.9%;
    --sidebar-accent-foreground: 240 5.9% 10%;
    --sidebar-border: 220 13% 91%;
    --sidebar-ring: 217.2 91.2% 59.8%;
  }

  .dark {
    --background: 222.2 84% 4.9%;
    --foreground: 210 40% 98%;

    --card: 222.2 84% 4.9%;
    --card-foreground: 210 40% 98%;

    --popover: 222.2 84% 4.9%;
    --popover-foreground: 210 40% 98%;

    --primary: 263 84% 50%;
    --primary-foreground: 0 0% 98%;

    --secondary: 173 85% 32%;
    --secondary-foreground: 0 0% 98%;

    --muted: 217.2 32.6% 17.5%;
    --muted-foreground: 215 20.2% 65.1%;

    --accent: 217.2 32.6% 17.5%;
    --accent-foreground: 210 40% 98%;

    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 210 40% 98%;

    --border: 217.2 32.6% 17.5%;
    --input: 217.2 32.6% 17.5%;
    --ring: 263 84% 50%;

    --sidebar-background: 240 5.9% 10%;
    --sidebar-foreground: 240 4.8% 95.9%;
    --sidebar-primary: 224.3 76.3% 48%;
    --sidebar-primary-foreground: 0 0% 100%;
    --sidebar-accent: 240 3.7% 15.9%;
    --sidebar-accent-foreground: 240 4.8% 95.9%;
    --sidebar-border: 240 3.7% 15.9%;
    --sidebar-ring: 217.2 91.2% 59.8%;
  }
}

@layer base {
  * {
    @apply border-border;
  }

  body {
    @apply bg-background text-foreground;
    font-feature-settings: "rlig" 1, "calt" 1;
  }
}

@layer components {
  .hero-text-gradient {
    @apply bg-clip-text text-transparent bg-gradient-to-r from-brand-purple to-brand-teal;
  }

  .section-heading {
    @apply text-3xl md:text-4xl lg:text-5xl font-bold mb-6;
  }

  .section-subheading {
    @apply text-xl text-gray-600 max-w-3xl mx-auto mb-12;
  }

  .badge {
    @apply inline-block px-4 py-1.5 rounded-full text-sm font-medium mb-4;
  }

  .badge-purple {
    @apply bg-purple-100 text-purple-800;
  }

  .badge-teal {
    @apply bg-teal-100 text-teal-800;
  }

  .badge-indigo {
    @apply bg-indigo-100 text-indigo-800;
  }

  .feature-card {
    @apply bg-white p-8 rounded-xl border border-gray-100 hover:shadow-xl transition-all duration-300 hover:border-purple-100 group;
  }

  .feature-icon-container {
    @apply w-16 h-16 rounded-2xl flex items-center justify-center mb-6 transition-colors duration-300;
  }

  .feature-icon {
    @apply w-8 h-8 transition-transform duration-300 group-hover:scale-110;
  }

  .testimonial-card {
    @apply bg-white p-8 rounded-xl shadow-md hover:shadow-xl transition-all duration-300 border border-gray-100;
  }

  .image-glow {
    @apply relative;
  }

  .image-glow::before {
    content: '';
    @apply absolute -inset-1 bg-gradient-to-r from-brand-purple to-brand-teal rounded-xl blur-md opacity-70 -z-10;
  }

  /* Removed particle animations for better performance */

  /* Animations */
  .fade-in-up {
    animation: fadeInUp 0.8s ease-out forwards;
  }

  .fade-in-up-delay-1 {
    animation: fadeInUp 0.8s ease-out 0.2s forwards;
    opacity: 0;
  }

  .fade-in-up-delay-2 {
    animation: fadeInUp 0.8s ease-out 0.4s forwards;
    opacity: 0;
  }

  .fade-in-up-delay-3 {
    animation: fadeInUp 0.8s ease-out 0.6s forwards;
    opacity: 0;
  }

  @keyframes fadeInUp {
    from {
      opacity: 0;
      transform: translateY(20px);
    }
    to {
      opacity: 1;
      transform: translateY(0);
    }
  }

  /* Hover effects */
  .hover-lift {
    transition: transform 0.3s ease;
  }

  .hover-lift:hover {
    transform: translateY(-5px);
  }

  /* Removed background animations for better performance */

  /* Static text effects - no animations for better performance */
  .text-glow {
    text-shadow: 0 0 10px rgba(255, 255, 255, 0.2),
                 0 0 20px rgba(109, 40, 217, 0.2);
  }

  .text-glow-alt {
    text-shadow: 0 0 10px rgba(255, 255, 255, 0.2),
                 0 0 20px rgba(236, 72, 153, 0.2);
  }
}