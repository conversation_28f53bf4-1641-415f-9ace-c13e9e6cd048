/**
 * Background Removal Result Component
 * Displays processed image with transparent background and action buttons
 */

import React from 'react';
import { Button } from '@/components/ui/button';
import { Download, Trash2 } from 'lucide-react';
import { MESSAGES } from '@/constants/backgroundRemoval';

interface BackgroundRemovalResultProps {
  resultImage: string | null;
  onDownload: () => void;
  onStartOver: () => void;
}

const BackgroundRemovalResult: React.FC<BackgroundRemovalResultProps> = ({
  resultImage,
  onDownload,
  onStartOver,
}) => {
  if (!resultImage) {
    return null;
  }

  return (
    <div>
      <div 
        className="rounded-lg overflow-hidden mb-4 h-[400px] flex items-center justify-center"
        style={{
          backgroundImage: `url('/checkered-background.png'),
                           linear-gradient(45deg, #f0f0f0 25%, transparent 25%),
                           linear-gradient(-45deg, #f0f0f0 25%, transparent 25%)`,
          backgroundSize: '20px 20px, 20px 20px, 20px 20px',
          backgroundPosition: '0 0, 0 0, 10px 10px'
        }}
      >
        <img
          src={resultImage}
          alt="Result"
          className="max-w-full max-h-full object-contain"
        />
      </div>
      
      <p className="text-center text-sm text-gray-500 mb-4">
        Background removed successfully!
      </p>
      
      <div className="flex items-center justify-center gap-2">
        <Button
          size="sm"
          variant="outline"
          className="h-8 text-xs gap-1"
          onClick={onStartOver}
        >
          <Trash2 size={12} />
          Start Over
        </Button>
        <Button
          size="sm"
          className="h-8 text-xs gap-1 bg-brand-purple hover:bg-brand-purple/90"
          onClick={onDownload}
        >
          <Download size={12} />
          Download
        </Button>
      </div>
    </div>
  );
};

export default BackgroundRemovalResult;