import { Suspense, lazy } from "react";

// Dynamic imports with React.lazy() for code splitting
const HeroSection = lazy(() => import("@/components/landing/HeroSection"));
const FeaturesSection = lazy(() => import("@/components/landing/FeaturesSection"));
const VideoShowcase = lazy(() => import("@/components/landing/VideoShowcase"));
const CompanyLogos = lazy(() => import("@/components/landing/CompanyLogos"));
const TestimonialsSection = lazy(() => import("@/components/landing/TestimonialsSection"));
const RoadmapSection = lazy(() => import("@/components/landing/RoadmapSection"));
const Footer = lazy(() => import("@/components/landing/Footer"));

// Loading component for Suspense fallbacks
const SectionLoader = () => (
  <div className="flex items-center justify-center py-20">
    <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-cyan-400"></div>
  </div>
);

const Index = () => {
  // Scroll to sections
  const scrollToImageSection = () => {
    const imageSection = document.querySelector('[data-section="image-generation"]');
    if (imageSection) {
      imageSection.scrollIntoView({ behavior: 'smooth' });
    }
  };

  const scrollToVideoSection = () => {
    const videoSection = document.querySelector('[data-section="video-generation"]');
    if (videoSection) {
      videoSection.scrollIntoView({ behavior: 'smooth' });
    }
  };

  return (
    <div className="min-h-screen flex flex-col">
      {/* Single seamless background for entire page */}
      <div className="fixed inset-0 bg-gradient-to-br from-slate-900 via-gray-900 to-black -z-10"></div>

      {/* Hero Section - Load immediately */}
      <Suspense fallback={<SectionLoader />}>
        <HeroSection />
      </Suspense>

      {/* Features Section - Load with Suspense */}
      <Suspense fallback={<SectionLoader />}>
        <FeaturesSection />
      </Suspense>

      {/* Video Showcase - Load with Suspense */}
      <Suspense fallback={<SectionLoader />}>
        <div className="container relative z-20 mx-auto px-4 md:px-8">
          <VideoShowcase />
        </div>
      </Suspense>

      {/* Company Logos - Load with Suspense */}
      <Suspense fallback={<SectionLoader />}>
        <CompanyLogos />
      </Suspense>

      {/* Testimonials Section - Load with Suspense */}
      <Suspense fallback={<SectionLoader />}>
        <TestimonialsSection />
      </Suspense>

      {/* Roadmap Section - Load with Suspense */}
      <Suspense fallback={<SectionLoader />}>
        <RoadmapSection />
      </Suspense>

      {/* Footer - Load with Suspense */}
      <Suspense fallback={<SectionLoader />}>
        <Footer />
      </Suspense>
    </div>
  );
};

export default Index;
