import { ImageHistoryItem } from "@/services/imageHistoryService";
import { VideoHistoryItem } from "@/services/videoService";

export const formatDuration = (seconds: number): string => {
  if (seconds < 60) {
    return `${seconds}s`;
  } else if (seconds < 3600) {
    const minutes = Math.floor(seconds / 60);
    const remainingSeconds = seconds % 60;
    return remainingSeconds > 0 ? `${minutes}m ${remainingSeconds}s` : `${minutes}m`;
  } else {
    const hours = Math.floor(seconds / 3600);
    const minutes = Math.floor((seconds % 3600) / 60);
    return minutes > 0 ? `${hours}h ${minutes}m` : `${hours}h`;
  }
};

export const getMediaName = (
  item: ImageHistoryItem | VideoHistoryItem,
  mediaType: 'image' | 'video'
): string => {
  if ('prompt' in item && item.prompt) {
    return item.prompt.substring(0, 50) + (item.prompt.length > 50 ? '...' : '');
  }
  return `${mediaType === 'image' ? 'Image' : 'Video'} ${item.id}`;
};