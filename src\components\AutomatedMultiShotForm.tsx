import React, { useState } from "react";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Slider } from "@/components/ui/slider";
import { Badge } from "@/components/ui/badge";
import { toast } from "sonner";
import { Clock, Wand2 } from "lucide-react";
import {
  DURATION_LIMITS,
  PROMPT_LIMITS,
  DEFAULT_VALUES,
  ERROR_MESSAGES,
  UI_TEXT,
  calculateEstimatedTime,
  generateRandomSeed,
} from "@/constants/multiShotVideo";

interface AutomatedMultiShotFormProps {
  onGenerate: (prompt: string, durationSeconds: number, seed?: number) => void;
  isGenerating: boolean;
}

const AutomatedMultiShotForm: React.FC<AutomatedMultiShotFormProps> = ({ 
  onGenerate, 
  isGenerating 
}) => {
  const [prompt, setPrompt] = useState("");
  const [durationSeconds, setDurationSeconds] = useState<number>(DURATION_LIMITS.DEFAULT_AUTOMATED);
  const [seed, setSeed] = useState<number>(generateRandomSeed());

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    if (prompt.trim().length < PROMPT_LIMITS.AUTOMATED_MIN) {
      toast.error(ERROR_MESSAGES.PROMPT_TOO_SHORT);
      return;
    }
    onGenerate(prompt, durationSeconds, seed);
  };

  const handleGenerateRandomSeed = () => {
    setSeed(generateRandomSeed());
  };

  const estimatedTime = calculateEstimatedTime(durationSeconds, true);

  // If generating, show progress UI instead of the form
  if (isGenerating) {
    return (
      <div className="bg-blue-50 dark:bg-blue-950/20 p-8 rounded-lg border-2 border-blue-200 dark:border-blue-800">
        <div className="text-center space-y-4">
          <div className="flex justify-center">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600" />
          </div>
          <div>
            <h3 className="text-lg font-semibold text-blue-900 dark:text-blue-100 mb-2">
              {UI_TEXT.GENERATING_AUTOMATED}
            </h3>
            <p className="text-sm text-blue-700 dark:text-blue-300 mb-4">
              {UI_TEXT.GENERATING_AUTOMATED_DESCRIPTION.replace('{duration}', durationSeconds.toString())}
            </p>
            <div className="flex items-center justify-center gap-2 text-sm">
              <Clock className="h-4 w-4 text-blue-600" />
              <span className="font-medium">{UI_TEXT.ESTIMATED_TIME}</span>
              <Badge variant="secondary" className="bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200">
                {estimatedTime.min}s - {estimatedTime.max}s
              </Badge>
            </div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <form onSubmit={handleSubmit} className="space-y-4">
      {/* Story Description */}
      <div>
        <label className="block text-sm font-medium mb-2">
          {UI_TEXT.STORY_DESCRIPTION_LABEL}
        </label>
        <Textarea
          placeholder={UI_TEXT.STORY_DESCRIPTION_PLACEHOLDER}
          value={prompt}
          onChange={(e) => setPrompt(e.target.value)}
          className="min-h-[100px] resize-none"
          maxLength={PROMPT_LIMITS.AUTOMATED_MAX}
        />
        <div className="flex justify-between text-xs text-gray-500 mt-1">
          <span>{UI_TEXT.STORY_DESCRIPTION_HELP}</span>
          <span className={prompt.length > PROMPT_LIMITS.WARNING_THRESHOLD_AUTOMATED ? "text-red-500" : ""}>
            {prompt.length}/{PROMPT_LIMITS.AUTOMATED_MAX}
          </span>
        </div>
      </div>

      {/* Duration and Seed Row */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        {/* Duration */}
        <div>
          <label className="block text-sm font-medium mb-2">
            {UI_TEXT.DURATION_LABEL}
          </label>
          <div className="space-y-2">
            <Slider
              value={[durationSeconds]}
              onValueChange={(value) => setDurationSeconds(value[0])}
              min={DURATION_LIMITS.MIN}
              max={DURATION_LIMITS.MAX}
              step={DURATION_LIMITS.STEP}
              className="w-full"
            />
            <div className="flex justify-between text-xs text-gray-500">
              <span>{DURATION_LIMITS.MIN}s</span>
              <span className="font-medium">{durationSeconds}s</span>
              <span>{DURATION_LIMITS.MAX}s</span>
            </div>
          </div>
        </div>

        {/* Seed */}
        <div>
          <label className="block text-sm font-medium mb-2">
            {UI_TEXT.SEED_LABEL}
          </label>
          <div className="flex gap-2">
            <Input
              type="number"
              placeholder={UI_TEXT.SEED_PLACEHOLDER}
              value={seed}
              onChange={(e) => setSeed(parseInt(e.target.value) || 0)}
            />
            <Button
              type="button"
              variant="outline"
              onClick={handleGenerateRandomSeed}
              className="px-3"
            >
              {UI_TEXT.RANDOM}
            </Button>
          </div>
        </div>
      </div>

      {/* Processing Time and Submit */}
      <div className="flex items-center justify-between gap-4 pt-2">
        <div className="flex items-center gap-2 text-sm">
          <Clock className="h-4 w-4 text-blue-600" />
          <span className="font-medium">{UI_TEXT.ESTIMATED_TIME}</span>
          <Badge variant="secondary">
            {estimatedTime.min}s - {estimatedTime.max}s
          </Badge>
        </div>

        <Button
          type="submit"
          className="px-8 bg-blue-600 hover:bg-blue-700"
          disabled={prompt.trim().length < PROMPT_LIMITS.AUTOMATED_MIN}
        >
          <Wand2 className="h-4 w-4 mr-2" />
          {UI_TEXT.GENERATE_BUTTON}
        </Button>
      </div>
    </form>
  );
};

export default AutomatedMultiShotForm;