import React, { useState } from "react";
import DashboardSidebar from "@/components/DashboardSidebar";
import {
  <PERSON><PERSON>,
  ArrowLeft,
  Info,
  Download,
  Trash2
} from "lucide-react";
import { But<PERSON> } from "@/components/ui/button";
import { <PERSON> } from "react-router-dom";
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from "@/components/ui/tooltip";
import { Ta<PERSON>, TabsList, TabsTrigger, TabsContent } from "@/components/ui/tabs";
import { useImageVariation } from "@/hooks/useImageVariation";
import { useImageVariationUpload } from "@/hooks/useImageVariationUpload";
import ImageVariationUpload from "@/components/ImageVariationUpload";
import ImageVariationSettings from "@/components/ImageVariationSettings";
import ImageVariationResult from "@/components/ImageVariationResult";
import ImageVariationUseCases from "@/components/ImageVariationUseCases";
import { UI_TEXT, DEFAULT_VALUES } from "@/constants/imageVariation";

const ImageVariationPage = () => {
  // State for form inputs
  const [activeTab, setActiveTab] = useState("upload");
  const [variationStrength, setVariationStrength] = useState<number>(DEFAULT_VALUES.VARIATION_STRENGTH);
  const [prompt, setPrompt] = useState("");
  const [negativePrompt, setNegativePrompt] = useState("");
  const [resultImage, setResultImage] = useState<string | null>(null);

  // Custom hooks
  const {
    sourceImage,
    isUploading,
    uploadError,
    fileInputRef,
    handleFileUpload,
    triggerFileInput,
    clearImage,
    resetUpload
  } = useImageVariationUpload();

  const {
    processImageVariation,
    isProcessing,
    error: processingError
  } = useImageVariation({
    onSuccess: (image) => {
      setResultImage(image);
      setActiveTab("result");
    },
    onError: (error) => {
      console.error('Image variation failed:', error);
    }
  });

  // Handle image variation generation
  const handleGenerate = async () => {
    if (!sourceImage) return;
    
    const result = await processImageVariation(
      sourceImage,
      variationStrength,
      prompt,
      negativePrompt
    );
  };

  // Reset the entire process
  const resetProcess = () => {
    resetUpload();
    setResultImage(null);
    setActiveTab("upload");
    setVariationStrength(DEFAULT_VALUES.VARIATION_STRENGTH);
    setPrompt("");
    setNegativePrompt("");
  };

  return (
    <>
      <DashboardSidebar />

      <main className="ml-64 min-h-screen bg-white dark:bg-gray-900 overflow-auto">
        {/* Main content area */}
        <div className="container mx-auto px-4 py-6">
          {/* Tool header */}
          <div className="flex items-center justify-between mb-4">
            <div className="flex items-center gap-2">
              <Link
                to="/advanced-image-tools"
                className="flex items-center text-gray-500 hover:text-gray-700 transition-colors"
              >
                <ArrowLeft size={16} />
              </Link>
              <div className="h-6 w-6 rounded-full bg-blue-100 dark:bg-blue-900/30 flex items-center justify-center">
                <Copy size={14} className="text-brand-purple" />
              </div>
              <h2 className="text-sm font-medium text-brand-purple">{UI_TEXT.TITLE}</h2>
              <TooltipProvider>
                <Tooltip>
                  <TooltipTrigger asChild>
                    <Button variant="ghost" size="sm" className="h-6 w-6 rounded-full p-0 ml-1">
                      <Info size={12} className="text-gray-400" />
                    </Button>
                  </TooltipTrigger>
                  <TooltipContent side="right" className="max-w-sm">
                    <div className="text-xs">
                      <p>{UI_TEXT.TOOLTIP}</p>
                    </div>
                  </TooltipContent>
                </Tooltip>
              </TooltipProvider>
            </div>

            {/* Action buttons based on current state */}
            <div className="flex items-center gap-2">
              {activeTab === "result" && (
                <>
                  <Button
                    size="sm"
                    variant="outline"
                    className="h-8 text-xs gap-1"
                    onClick={resetProcess}
                  >
                    <Trash2 size={12} />
                    {UI_TEXT.BUTTONS.START_OVER}
                  </Button>
                  <Button
                    size="sm"
                    className="h-8 text-xs gap-1 bg-brand-purple hover:bg-brand-purple/90"
                    onClick={() => {
                      if (resultImage) {
                        const link = document.createElement('a');
                        link.href = resultImage;
                        link.download = `image-variation-${Date.now()}.png`;
                        document.body.appendChild(link);
                        link.click();
                        document.body.removeChild(link);
                      }
                    }}
                  >
                    <Download size={12} />
                    {UI_TEXT.BUTTONS.DOWNLOAD}
                  </Button>
                </>
              )}
            </div>
          </div>

          {/* Main tool area */}
          <div className="mb-6">
            <Tabs value={activeTab} onValueChange={setActiveTab}>
              <TabsList className="grid grid-cols-2 w-full max-w-md mx-auto mb-4">
                <TabsTrigger value="upload" disabled={activeTab === "result"}>
                  {UI_TEXT.TABS.UPLOAD_SETTINGS}
                </TabsTrigger>
                <TabsTrigger value="result" disabled={!resultImage}>
                  {UI_TEXT.TABS.RESULT}
                </TabsTrigger>
              </TabsList>

              <TabsContent value="upload" className="m-0">
                <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                  {/* Left column: Upload section */}
                  <ImageVariationUpload
                    sourceImage={sourceImage}
                    isUploading={isUploading}
                    uploadError={uploadError}
                    fileInputRef={fileInputRef}
                    onFileUpload={handleFileUpload}
                    onTriggerFileInput={triggerFileInput}
                    onClearImage={clearImage}
                  />

                  {/* Right column: Settings section */}
                  <ImageVariationSettings
                    sourceImage={sourceImage}
                    variationStrength={variationStrength}
                    prompt={prompt}
                    negativePrompt={negativePrompt}
                    isProcessing={isProcessing}
                    onVariationStrengthChange={setVariationStrength}
                    onPromptChange={setPrompt}
                    onNegativePromptChange={setNegativePrompt}
                    onGenerate={handleGenerate}
                  />
                </div>
              </TabsContent>

              <TabsContent value="result" className="m-0">
                <ImageVariationResult
                  resultImage={resultImage}
                  onReset={resetProcess}
                />
              </TabsContent>
            </Tabs>
          </div>

          {/* Use cases */}
          <ImageVariationUseCases />
        </div>
      </main>
    </>
  );
};

export default ImageVariationPage;
