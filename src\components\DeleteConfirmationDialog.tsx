import React, { useState } from 'react';
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from '@/components/ui/alert-dialog';
import { Button } from '@/components/ui/button';
import { Loader2, Trash2, AlertTriangle, Image, Video } from 'lucide-react';
import { toast } from 'sonner';

interface DeleteConfirmationDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  onConfirm: () => Promise<void>;
  mediaType: 'image' | 'video';
  mediaName: string;
  mediaId: string;
}

export const DeleteConfirmationDialog: React.FC<DeleteConfirmationDialogProps> = ({
  open,
  onOpenChange,
  onConfirm,
  mediaType,
  mediaName,
  mediaId,
}) => {
  const [isDeleting, setIsDeleting] = useState(false);

  const handleConfirm = async () => {
    setIsDeleting(true);
    try {
      await onConfirm();
      onOpenChange(false);
      toast.success(`${mediaType === 'image' ? 'Image' : 'Video'} deleted successfully`);
    } catch (error) {
      toast.error(`Failed to delete ${mediaType}. Please try again.`);
      if (import.meta.env.DEV) {
        console.error(`Delete ${mediaType} error:`, error);
      }
    } finally {
      setIsDeleting(false);
    }
  };

  const handleCancel = () => {
    if (!isDeleting) {
      onOpenChange(false);
    }
  };

  return (
    <AlertDialog open={open} onOpenChange={onOpenChange}>
      <AlertDialogContent className="max-w-md">
        <AlertDialogHeader>
          <div className="flex items-center gap-3">
            <div className="p-2 bg-red-100 dark:bg-red-900/20 rounded-full">
              <AlertTriangle className="h-5 w-5 text-red-600" />
            </div>
            <AlertDialogTitle className="text-lg font-semibold">
              Delete {mediaType === 'image' ? 'Image' : 'Video'}
            </AlertDialogTitle>
          </div>
        </AlertDialogHeader>
        
        <AlertDialogDescription className="space-y-4">
          <div className="flex items-start gap-3 p-3 bg-gray-50 dark:bg-gray-800 rounded-lg">
            <div className="p-1.5 bg-white dark:bg-gray-700 rounded">
              {mediaType === 'image' ? (
                <Image className="h-4 w-4 text-gray-600" />
              ) : (
                <Video className="h-4 w-4 text-gray-600" />
              )}
            </div>
            <div className="flex-1 min-w-0">
              <p className="text-sm font-medium text-gray-900 dark:text-gray-100">
                {mediaType === 'image' ? 'Image' : 'Video'} Details
              </p>
              <p className="text-xs text-gray-500 dark:text-gray-400 truncate">
                ID: {mediaId}
              </p>
              {mediaName && (
                <p className="text-xs text-gray-500 dark:text-gray-400 truncate">
                  Name: {mediaName}
                </p>
              )}
            </div>
          </div>
          
          <div className="space-y-2">
            <p className="text-sm text-gray-700 dark:text-gray-300">
              <strong>This action cannot be undone.</strong> This will permanently delete:
            </p>
            <ul className="text-sm text-gray-600 dark:text-gray-400 space-y-1 ml-4">
              <li>• The {mediaType} file from cloud storage</li>
              <li>• All associated metadata and history</li>
              <li>• Any generated variations or derivatives</li>
            </ul>
          </div>
          
          <div className="p-3 bg-amber-50 dark:bg-amber-900/20 border border-amber-200 dark:border-amber-800 rounded-lg">
            <p className="text-sm text-amber-800 dark:text-amber-200">
              <strong>Warning:</strong> If this {mediaType} is being used elsewhere in your projects, 
              those references will become invalid.
            </p>
          </div>
        </AlertDialogDescription>

        <AlertDialogFooter className="gap-2">
          <AlertDialogCancel 
            onClick={handleCancel}
            disabled={isDeleting}
            className="flex-1"
          >
            Cancel
          </AlertDialogCancel>
          <AlertDialogAction
            onClick={handleConfirm}
            disabled={isDeleting}
            className="flex-1 bg-red-600 hover:bg-red-700 text-white"
          >
            {isDeleting ? (
              <>
                <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                Deleting...
              </>
            ) : (
              <>
                <Trash2 className="h-4 w-4 mr-2" />
                Delete Permanently
              </>
            )}
          </AlertDialogAction>
        </AlertDialogFooter>
      </AlertDialogContent>
    </AlertDialog>
  );
};

export default DeleteConfirmationDialog;