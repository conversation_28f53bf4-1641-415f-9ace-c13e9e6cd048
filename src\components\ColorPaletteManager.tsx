import React from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Plus, X } from 'lucide-react';
import { useColorPalette, ColorItem } from '@/hooks/useColorPalette';
import { COLOR_GUIDED_MESSAGES } from '@/constants/colorGuided';

interface ColorPaletteManagerProps {
  colors: ColorItem[];
  onColorChange: (id: number, newColor: string) => void;
  onAddColor: () => void;
  onRemoveColor: (id: number) => void;
  canAddColor: boolean;
  canRemoveColor: boolean;
}

const ColorPaletteManager: React.FC<ColorPaletteManagerProps> = ({
  colors,
  onColorChange,
  onAddColor,
  onRemoveColor,
  canAddColor,
  canRemoveColor
}) => {
  return (
    <div className="space-y-4">
      <h3 className="text-md font-semibold text-gray-700 dark:text-gray-200">
        1. Choose Your Colors
      </h3>
      
      <div className="space-y-3 mb-4">
        {colors.map((colorItem) => (
          <div key={colorItem.id} className="flex items-center gap-2">
            <div
              className="h-8 w-8 rounded-md border border-gray-200 dark:border-gray-700 flex-shrink-0"
              style={{ backgroundColor: colorItem.color }}
            ></div>
            <Input
              type="color"
              value={colorItem.color}
              onChange={(e) => onColorChange(colorItem.id, e.target.value)}
              className="h-8 w-16 p-1"
            />
            <Input
              type="text"
              value={colorItem.color.toUpperCase()}
              onChange={(e) => onColorChange(colorItem.id, e.target.value)}
              className="h-8 text-xs flex-grow"
              maxLength={7}
            />
            <Button
              variant="ghost"
              size="sm"
              className="h-8 w-8 p-0 text-gray-500 hover:text-red-500"
              onClick={() => onRemoveColor(colorItem.id)}
              disabled={!canRemoveColor}
            >
              <X size={14} />
            </Button>
          </div>
        ))}
      </div>
      
      <Button
        variant="outline"
        size="sm"
        className="text-xs gap-1 w-full"
        onClick={onAddColor}
        disabled={!canAddColor}
      >
        <Plus size={12} />
        Add Color
      </Button>
      
      <div className="mt-1 text-xs text-gray-500">
        <p>{COLOR_GUIDED_MESSAGES.INFO.COLOR_PALETTE_HELP}</p>
      </div>
    </div>
  );
};

export default ColorPaletteManager;