import { useState } from 'react';
import { toast } from 'sonner';
import { useQueryClient } from '@tanstack/react-query';
import { useAuth } from '@/contexts/AuthContext';
import { generateColorGuidedImage } from '@/services/bedrockService';
import { saveImageHistory, ImageHistoryItem } from '@/services/imageHistoryService';
import { uploadImageToS3 } from '@/services/s3Service';
import { 
  COLOR_GUIDED_MESSAGES, 
  COLOR_GUIDED_GENERATION_OPTIONS,
  COLOR_GUIDED_DEFAULTS 
} from '@/constants/colorGuided';

export interface UseColorGuidedGenerationProps {
  onSuccess?: (image: string) => void;
}

export const useColorGuidedGeneration = ({ onSuccess }: UseColorGuidedGenerationProps = {}) => {
  const { user } = useAuth();
  const queryClient = useQueryClient();
  const [isProcessing, setIsProcessing] = useState(false);
  const [resultImage, setResultImage] = useState<string | null>(null);
  const [error, setError] = useState<string | null>(null);

  const saveColorGuidedToHistory = async (
    image: string,
    prompt: string,
    colors: string[],
    s3Url?: string,
    s3Key?: string
  ) => {
    if (!user?.id) return;

    try {
      let finalS3Url = s3Url;
      let finalS3Key = s3Key;

      // If no S3 URL provided, upload to S3
      if (!finalS3Url || !finalS3Key) {
        const s3Result = await uploadImageToS3({
          image: image.split(',')[1],
          userId: user.id,
          imageType: 'color-guided',
          filename: COLOR_GUIDED_GENERATION_OPTIONS.generateFileName()
        });

        if (s3Result.success && s3Result.url && s3Result.key) {
          finalS3Url = s3Result.url;
          finalS3Key = s3Result.key;
        } else {
          throw new Error('Failed to upload to S3');
        }
      }

      // Create history item
      const historyItem = COLOR_GUIDED_GENERATION_OPTIONS.createHistoryItem(
        user.id,
        prompt,
        colors,
        finalS3Key!,
        finalS3Url!
      );

      // Save to history
      await saveImageHistory(historyItem);

      // Invalidate queries to refresh Dashboard
      await queryClient.invalidateQueries({ queryKey: ['imageHistory', user.id] });

      toast.success(COLOR_GUIDED_MESSAGES.SUCCESS.IMAGE_GENERATED);
    } catch (saveError) {
      if (import.meta.env.DEV) {
        console.error('Failed to save color-guided image to history:', saveError);
      }
      toast.warning(COLOR_GUIDED_MESSAGES.WARNING.SAVE_FAILED);
      
      // Still try to invalidate queries even if save failed
      try {
        await queryClient.invalidateQueries({ queryKey: ['imageHistory', user.id] });
      } catch (invalidateError) {
        if (import.meta.env.DEV) {
          console.error('Failed to invalidate queries after save error:', invalidateError);
        }
      }
    }
  };

  const generateImage = async (
    prompt: string,
    colors: string[],
    negativePrompt?: string,
    referenceImage?: string
  ) => {
    if (!user?.id) {
      toast.error(COLOR_GUIDED_MESSAGES.ERROR.LOGIN_REQUIRED);
      return;
    }

    setIsProcessing(true);
    setError(null);

    try {
      // Prepare generation options
      const options: any = {
        ...COLOR_GUIDED_GENERATION_OPTIONS.getDefaultOptions(),
        colors: colors,
        negativePrompt: negativePrompt || COLOR_GUIDED_DEFAULTS.DEFAULT_NEGATIVE_PROMPT
      };

      // Add reference image if provided
      if (referenceImage) {
        options.referenceImage = referenceImage;
      }

      // Generate the image
      const result = await generateColorGuidedImage(prompt, options, user.id);

      if (result.success && result.image) {
        setResultImage(result.image);
        setError(null);

        // Call success callback if provided
        if (onSuccess) {
          onSuccess(result.image);
        }

        // Save to history
        await saveColorGuidedToHistory(
          result.image,
          prompt,
          colors,
          result.s3Url,
          result.s3Key
        );
      } else {
        const errorMessage = result.error || COLOR_GUIDED_MESSAGES.ERROR.GENERATION_FAILED;
        setError(errorMessage);
        toast.error(errorMessage);
      }
    } catch (error) {
      const errorMessage = COLOR_GUIDED_MESSAGES.ERROR.UNEXPECTED_ERROR;
      setError(errorMessage);
      toast.error(errorMessage);
      
      if (import.meta.env.DEV) {
        console.error('Color-guided generation error:', error);
      }
    } finally {
      setIsProcessing(false);
    }
  };

  const reset = () => {
    setResultImage(null);
    setError(null);
    setIsProcessing(false);
  };

  return {
    // State
    isProcessing,
    resultImage,
    error,
    
    // Actions
    generateImage,
    reset
  };
};