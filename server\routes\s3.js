/**
 * S3 management routes for VibeNecto
 * Handles all S3-related API endpoints including upload, download,
 * presigned URL generation, and deletion operations
 */

const express = require('express');
const { logger } = require('../utils/logger');
const { asyncHandler } = require('../middleware/errorHandler');
const {
  uploadLimiter,
  presignedUrlLimiter,
  generalLimiter
} = require('../middleware/rateLimiter');

// Import AWS service modules
const {
  uploadImageToS3,
  generatePresignedUrl,
  deleteFromS3
} = require('../services/s3-service');

const router = express.Router();

/**
 * Upload an image to S3
 */
router.post('/upload',
  uploadLimiter, // Apply upload limiter
  asyncHandler(async (req, res) => { // Added asyncHandler
  logger.info('S3 upload request received', { requestId: req.requestId });
  const { image, userId, imageType, filename } = req.body;

  if (!image) {
    return res.status(400).json({
      success: false,
      error: 'Image is required'
    });
  }

  if (!userId) {
    return res.status(400).json({
      success: false,
      error: 'User ID is required'
    });
  }

  try {
    // Extract the base64 data from the data URL if needed
    const base64Data = image.startsWith('data:')
      ? image.split(',')[1]
      : image;

    // Upload to S3
    const result = await uploadImageToS3({
      image: base64Data,
      userId,
      imageType: imageType || 'standard',
      filename
    });

    return res.json(result);
  } catch (error) {
    logger.error('S3 upload failed', { error: error.message, requestId: req.requestId });
    return res.status(500).json({
      success: false,
      error: error.message || 'Failed to upload to S3'
    });
  }
}));

/**
 * Generate a presigned URL for an S3 object
 */
router.post('/presigned-url',
  presignedUrlLimiter,
  asyncHandler(async (req, res) => { // Added asyncHandler
  logger.info('Presigned URL request received', { requestId: req.requestId });
  const { key, expirySeconds } = req.body;

  if (!key) {
    return res.status(400).json({
      success: false,
      error: 'S3 key is required'
    });
  }

  try {
    const result = await generatePresignedUrl({
      key,
      expirySeconds: expirySeconds || 3600
    });

    return res.json(result);
  } catch (error) {
    logger.error('Presigned URL generation failed', { error: error.message, requestId: req.requestId });
    return res.status(500).json({
      success: false,
      error: error.message || 'Failed to generate presigned URL'
    });
  }
}));

/**
 * Delete an object from S3
 */
router.post('/delete',
  generalLimiter, // Or a more specific S3 operation limiter if created
  asyncHandler(async (req, res) => { // Added asyncHandler
  logger.info('S3 delete request received', { requestId: req.requestId });
  const { key } = req.body;

  if (!key) {
    return res.status(400).json({
      success: false,
      error: 'S3 key is required'
    });
  }

  try {
    const result = await deleteFromS3({ key });
    return res.json(result);
  } catch (error) {
    logger.error('S3 delete failed', { error: error.message, requestId: req.requestId });
    return res.status(500).json({
      success: false,
      error: error.message || 'Failed to delete from S3'
    });
  }
}));

/**
 * Debug endpoint to inspect S3 bucket contents
 */
router.get('/debug/contents', async (req, res) => {
  try {
    const { debugS3Contents } = require('../services/s3-service');
    const { prefix } = req.query;
    
    const result = await debugS3Contents({ prefix });
    
    res.json(result);
  } catch (error) {
    logger.error('Error in debug S3 contents endpoint', {
      error: error.message,
      requestId: req.requestId
    });
    res.status(500).json({
      success: false,
      error: error.message
    });
  }
});

module.exports = router;