/**
 * Background Removal Preview Component
 * Displays source image preview and action buttons
 */

import React from 'react';
import { Button } from '@/components/ui/button';
import { X, Check, RefreshCw, ImageIcon } from 'lucide-react';
import { MESSAGES } from '@/constants/backgroundRemoval';

interface BackgroundRemovalPreviewProps {
  sourceImage: string | null;
  isProcessing: boolean;
  onProcess: () => void;
  onReset: () => void;
}

const BackgroundRemovalPreview: React.FC<BackgroundRemovalPreviewProps> = ({
  sourceImage,
  isProcessing,
  onProcess,
  onReset,
}) => {
  return (
    <div className="md:col-span-2">
      <h3 className="text-sm font-medium mb-3">Preview</h3>
      
      <div className="rounded-lg overflow-hidden bg-gray-100 dark:bg-gray-800 mb-3 flex items-center justify-center h-[400px]">
        {sourceImage ? (
          <img
            src={sourceImage}
            alt="Source"
            className="max-w-full max-h-full object-contain"
          />
        ) : (
          <div className="text-center p-6">
            <ImageIcon size={32} className="mx-auto mb-2 text-gray-400" />
            <p className="text-gray-500 text-sm">{MESSAGES.INFO.NO_IMAGE_SELECTED}</p>
            <p className="text-xs text-gray-400 mt-2">{MESSAGES.INFO.UPLOAD_INSTRUCTIONS}</p>
          </div>
        )}
      </div>
      
      {sourceImage && (
        <div className="flex items-center justify-center gap-2 mt-2">
          <Button
            size="sm"
            variant="outline"
            className="h-8 text-xs gap-1"
            onClick={onReset}
            disabled={isProcessing}
          >
            <X size={12} />
            Cancel
          </Button>
          <Button
            size="sm"
            className="h-8 text-xs gap-1 bg-brand-purple hover:bg-brand-purple/90"
            onClick={onProcess}
            disabled={isProcessing}
          >
            {isProcessing ? (
              <>
                <RefreshCw size={12} className="animate-spin" />
                {MESSAGES.INFO.PROCESSING}
              </>
            ) : (
              <>
                <Check size={12} />
                Remove Background
              </>
            )}
          </Button>
        </div>
      )}
    </div>
  );
};

export default BackgroundRemovalPreview;