import React from 'react';
import { History, Volume2 } from 'lucide-react';
import DashboardSidebar from '@/components/DashboardSidebar';
import VoiceHistoryGallery from '@/components/VoiceHistoryGallery';

const VoiceHistory: React.FC = () => {
  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900">
      <DashboardSidebar />
      
      <div className="ml-64 p-8">
        <div className="max-w-6xl mx-auto">
          {/* Header */}
          <div className="mb-8">
            <h1 className="text-3xl font-bold text-gray-900 dark:text-white flex items-center gap-3">
              <History className="h-8 w-8 text-brand-purple" />
              Voice History
            </h1>
            <p className="text-gray-600 dark:text-gray-400 mt-2">
              View, play, and manage your generated voices
            </p>
          </div>

          {/* Voice History Gallery */}
          <VoiceHistoryGallery />
        </div>
      </div>
    </div>
  );
};

export default VoiceHistory;