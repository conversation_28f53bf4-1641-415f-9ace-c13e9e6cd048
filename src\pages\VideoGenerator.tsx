import React, { useState } from "react";
import DashboardSidebar from "@/components/DashboardSidebar";
import VideoGeneratorHeader from "@/components/VideoGeneratorHeader";
import VideoGeneratorTabs from "@/components/VideoGeneratorTabs";
import VideoUseCases from "@/components/VideoUseCases";
import { useVideoGeneration } from "@/hooks/useVideoGeneration";
import { useVideoActions } from "@/hooks/useVideoActions";

const VideoGenerator = () => {
  const [activeTab, setActiveTab] = useState("generate");
  
  // Use custom hooks for state management and actions
  const {
    activeJobs,
    completedVideos,
    handleVideoGenerated,
    handleVideoComplete,
    handleVideoError,
  } = useVideoGeneration();

  const {
    handleVideoDownload,
  } = useVideoActions();

  // Handle tab switching and video generation flow
  const handleVideoGeneratedWithTabSwitch = (jobId: string, videoId: string, estimatedCompletionTime: string) => {
    handleVideoGenerated(jobId, videoId, estimatedCompletionTime);
    // Automatically switch to Result tab when generation starts
    setActiveTab("result");
  };

  const handleGenerateNew = () => {
    setActiveTab("generate");
  };

  return (
    <>
      <DashboardSidebar />

      <main className="ml-64 h-screen bg-white dark:bg-gray-900 overflow-hidden flex flex-col">
        {/* Main content area */}
        <div className="container mx-auto px-4 py-4 flex-1 flex flex-col">
          {/* Tool header */}
          <VideoGeneratorHeader />

          {/* Main tool area with tabs */}
          <VideoGeneratorTabs
            activeTab={activeTab}
            onTabChange={setActiveTab}
            activeJobs={activeJobs}
            completedVideos={completedVideos}
            onVideoGenerated={handleVideoGeneratedWithTabSwitch}
            onVideoComplete={handleVideoComplete}
            onVideoError={handleVideoError}
            onDownload={handleVideoDownload}
            onGenerateNew={handleGenerateNew}
          />

          {/* Use cases section */}
          <VideoUseCases />
        </div>
      </main>
    </>
  );
};

export default VideoGenerator;
