/**
 * Express server for VibeNecto - Refactored Version
 * Handles API requests for image and video generation using AWS Bedrock SDK
 * Phase 4: Production-ready with comprehensive monitoring, caching, and security
 * Refactor #8: Modular route organization for better maintainability
 */

// Load environment variables from .env file
require('dotenv').config();

const express = require('express');
const cors = require('cors');
const helmet = require('helmet');
const fs = require('fs');
const path = require('path');
const http = require('http');
const https = require('https');
const { createClient } = require('@supabase/supabase-js');

// SSL Certificate configuration
let httpsOptions = {};
const CERT_PATH = process.env.CERT_PATH || '/etc/letsencrypt/live/vibenecto.com';

// Check if SSL certificates exist
try {
  httpsOptions = {
    key: fs.readFileSync(path.join(CERT_PATH, 'privkey.pem')),
    cert: fs.readFileSync(path.join(CERT_PATH, 'cert.pem')),
    ca: fs.readFileSync(path.join(CERT_PATH, 'chain.pem'))
  };
  console.log('SSL certificates loaded successfully');
} catch (error) {
  console.warn('SSL certificates not found or not accessible:', error.message);
  console.warn('Running in HTTP-only mode');
}

// Phase 4: Import production utilities and middleware
const { logger } = require('./utils/logger');
const { TIME_INTERVALS } = require('./constants');
const { performStartupValidation } = require('./utils/startup-validation');
const {
  errorHandler,
  notFoundHandler,
  requestIdMiddleware,
  requestLogger,
  asyncHandler
} = require('./middleware/errorHandler');
const { authMiddleware } = require('./middleware/auth');
const {
  generalLimiter,
  videoGenerationLimiter,
  imageGenerationLimiter,
  authLimiter,
  uploadLimiter,
  videoHistoryLimiter,
  presignedUrlLimiter
} = require('./middleware/rateLimiter');
const { metricsCollector, startMetricsCollection } = require('./utils/metrics');
const { cacheHelpers, startCacheCleanup, cacheMiddleware } = require('./utils/cache');

// Import utility functions for Phase 3 & 4 completion
const {
  createErrorResponse,
  createSuccessResponse,
  handleGenerationError,
  handleS3Error,
  handleDatabaseError,
  handleAuthError,
  handleValidationError,
  handleRateLimitError,
  handleUnexpectedError
} = require('./utils/errorHandlers');

const {
  validateRequiredFields,
  validateUserAuth,
  validateImageData,
  validateTextContent,
  validateColors,
  validatePrompt,
  validateNumericRange,
  validateFileSize,
  validateUsageLimit
} = require('./utils/validators');

const {
  uploadImageWithFallback,
  uploadVoiceWithFallback,
  deleteFileWithHandling,
  generatePresignedUrlSafe,
  generatePresignedUrlsBatch,
  handleS3Operation
} = require('./utils/s3Helpers');

const {
  cleanupDatabaseRecords,
  cleanupFailedVideoJobs,
  cleanupOldFailedVideos,
  cleanupTemporaryResources,
  runComprehensiveCleanup,
  scheduleCleanupOperations
} = require('./utils/cleanup');

// Import route modules
const imagesRoutes = require('./routes/images');
const videosRoutes = require('./routes/videos');
const voicesRoutes = require('./routes/voices');
const s3Routes = require('./routes/s3');
const adminRoutes = require('./routes/admin');

// Initialize Supabase client for server-side operations
const supabase = createClient(
  process.env.SUPABASE_URL,
  process.env.SUPABASE_SERVICE_KEY
);

const app = express();
const PORT = process.env.PORT || 3001;

// Phase 4: Production middleware setup
// Security middleware
app.use(helmet({
  contentSecurityPolicy: {
    directives: {
      defaultSrc: ["'self'"],
      styleSrc: ["'self'", "'unsafe-inline'"],
      scriptSrc: ["'self'"],
      imgSrc: ["'self'", "data:", "https:"],
      connectSrc: ["'self'", "https:"],
      fontSrc: ["'self'"],
      objectSrc: ["'none'"],
      mediaSrc: ["'self'", "https://vibenecto-images-prod.s3.us-east-1.amazonaws.com", "https://*.s3.us-east-1.amazonaws.com"],
      frameSrc: ["'none'"],
    },
  },
  crossOriginEmbedderPolicy: false
}));

// CORS configuration
app.use(cors({
  origin: ['http://localhost:8080', 'https://vibenecto.com', 'http://vibenecto.com'],
  methods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'],
  allowedHeaders: ['Content-Type', 'Authorization']
}));

// Request tracking and logging
app.use(requestIdMiddleware);
app.use(authMiddleware);
app.use(requestLogger);

// Body parsing with size limits
const { FILE_SIZE_LIMITS } = require('./constants');
app.use(express.json({ limit: FILE_SIZE_LIMITS.REQUEST_BODY_LIMIT }));
app.use(express.urlencoded({ extended: true, limit: FILE_SIZE_LIMITS.REQUEST_BODY_LIMIT }));

// General rate limiting
app.use(generalLimiter);

// Start production services
startMetricsCollection();
startCacheCleanup();

logger.info('VibeNecto server starting up', {
  environment: process.env.NODE_ENV || 'development',
  port: PORT,
  timestamp: new Date().toISOString()
});

// Mount route modules
app.use('/api', imagesRoutes);
app.use('/api', videosRoutes);
app.use('/api', voicesRoutes);
app.use('/api/s3', s3Routes);
app.use('/', adminRoutes);

// Superadmin routes - Sprint 7: Backend Infrastructure
const createSuperadminRoutes = require('./routes/superadmin');
const superadminRoutes = createSuperadminRoutes(supabase);
app.use('/api/superadmin', superadminRoutes);

// Add HTTP to HTTPS redirect middleware
app.use((req, res, next) => {
  // Check if we're behind a proxy that sets X-Forwarded-Proto
  if (req.headers['x-forwarded-proto'] === 'http') {
    return res.redirect(`https://${req.headers.host}${req.url}`);
  }

  // For direct connections, check the protocol
  if (!req.secure && process.env.NODE_ENV === 'production' && Object.keys(httpsOptions).length > 0) {
    return res.redirect(`https://${req.headers.host}${req.url}`);
  }

  next();
});

// Important: All API routes are defined above this line
// API routes should be processed before static file serving

// Create a special route for API 404s
app.all('/api/*', (req, res) => {
  console.log(`API route not found: ${req.method} ${req.url}`);
  res.status(404).json({
    success: false,
    error: `API endpoint not found: ${req.method} ${req.url}`
  });
});

// Serve static files from the React frontend app
app.use(express.static(path.join(__dirname, '../dist')));

// For any other routes, send back the index.html file
// This should come AFTER all API routes are defined
app.get('*', (req, res) => {
  res.sendFile(path.join(__dirname, '../dist/index.html'));
});

/**
 * Phase 4: Error Handling and 404 Middleware
 */

// 404 handler for unmatched routes
app.use(notFoundHandler);

// Global error handler (must be last)
app.use(errorHandler);

// Use consolidated cleanup functions from utils/cleanup.js
async function performVideoCleanup() {
  try {
    logger.info('Starting scheduled video cleanup operations');

    // Use the comprehensive cleanup function from utils
    const cleanupResult = await runComprehensiveCleanup(supabase, {
      requestId: 'scheduled-cleanup',
      includeFailedVideoJobs: true,
      includeOldFailedVideos: true
    });

    if (cleanupResult.success) {
      logger.info('Scheduled cleanup completed successfully', {
        results: cleanupResult.results
      });
    } else {
      logger.error('Scheduled cleanup failed', {
        error: cleanupResult.error
      });
    }
  } catch (error) {
    logger.error('Error in scheduled cleanup process', {
      error: error.message,
      stack: error.stack
    });
  }
}

// Schedule cleanup operations using utility function
scheduleCleanupOperations(supabase, {
  cleanupInterval: TIME_INTERVALS.CLEANUP_INTERVAL,
  initialDelay: TIME_INTERVALS.INITIAL_CLEANUP_DELAY,
  customCleanupFunction: performVideoCleanup
});

// Graceful shutdown handling
process.on('SIGTERM', () => {
  logger.info('SIGTERM received, shutting down gracefully');
  process.exit(0);
});

process.on('SIGINT', () => {
  logger.info('SIGINT received, shutting down gracefully');
  process.exit(0);
});

process.on('uncaughtException', (error) => {
  logger.error('Uncaught Exception', { error: error.message, stack: error.stack });
  process.exit(1);
});

process.on('unhandledRejection', (reason, promise) => {
  logger.error('Unhandled Rejection', { reason, promise });
  process.exit(1);
});

// Start the server with validation
async function startServer() {
  try {
    // Perform startup validation
    const validationPassed = await performStartupValidation();
    if (!validationPassed) {
      logger.error('Server startup validation failed. Exiting...');
      process.exit(1);
    }

    // Create HTTP server
    const httpServer = http.createServer(app);

    // Start HTTP server
    httpServer.listen(PORT, () => {
      console.log(`HTTP server running on port ${PORT}`);
      console.log(`API endpoints:`);
      console.log(`- POST /api/generate-image: Generate an image using AWS Bedrock SDK`);
      console.log(`- POST /api/remove-background: Remove background from an image`);
      console.log(`- POST /api/color-guided-generation: Generate a color-guided image`);
      console.log(`- POST /api/image-variation: Generate image variations`);
      console.log(`- POST /api/s3/upload: Upload an image to S3`);
      console.log(`- POST /api/s3/presigned-url: Generate a presigned URL for an S3 object`);
      console.log(`- POST /api/s3/delete: Delete an object from S3`);
      console.log(`- POST /api/generate-video: Generate a video using AWS Bedrock Nova Reel`);
      console.log(`- GET /api/video-status/:jobId: Check video generation status`);
      console.log(`- GET /api/video-history: Get user's video generation history`);
      console.log(`- DELETE /api/video/:videoId: Delete a video`);
      console.log(`- POST /api/generate-voice: Generate voice from text using AWS Polly`);
      console.log(`- GET /api/voice-history: Get user's voice generation history`);
      console.log(`- DELETE /api/voice/:voiceId: Delete a voice file`);
      console.log(`- GET /api/voice-usage/:userId: Get current voice usage statistics`);
      console.log(`- GET /api/superadmin/stats: Get system statistics (superadmin only)`);
      console.log(`- GET /api/superadmin/users: Get user management data (superadmin only)`);
      console.log(`- GET /api/superadmin/activity: Get system activity logs (superadmin only)`);
      console.log(`- GET /api/superadmin/health: Get system health status (superadmin only)`);
      console.log(`\nServing frontend static files from: ${path.join(__dirname, '../dist')}`);
    });

    // Create and start HTTPS server if certificates are available
    if (Object.keys(httpsOptions).length > 0) {
      const httpsServer = https.createServer(httpsOptions, app);
      // Use a higher port (8443) that doesn't require root privileges
      const HTTPS_PORT = process.env.HTTPS_PORT || 8443;

      try {
        httpsServer.listen(HTTPS_PORT, () => {
          console.log(`HTTPS server running on port ${HTTPS_PORT}`);
          console.log(`Secure API endpoints available at https://vibenecto.com/api/...`);
          console.log(`NOTE: You need to set up port forwarding from 443 to ${HTTPS_PORT} using:`);
          console.log(`sudo iptables -t nat -A PREROUTING -p tcp --dport 443 -j REDIRECT --to-port ${HTTPS_PORT}`);
          console.log(`sudo netfilter-persistent save`);
        });
      } catch (error) {
        console.error('Failed to start HTTPS server:', error.message);
        console.error(`Check if port ${HTTPS_PORT} is already in use`);
      }
    } else {
      console.log('HTTPS server not started due to missing certificates');
    }

    // Handle server errors
    httpServer.on('error', (error) => {
      logger.error('HTTP Server error', { error: error.message, stack: error.stack });
    });

    return httpServer;
  } catch (error) {
    logger.error('Failed to start server', { error: error.message, stack: error.stack });
    process.exit(1);
  }
}

// Start the server
startServer();

module.exports = app;