import React from "react";
import { <PERSON> } from "react-router-dom";
import { But<PERSON> } from "@/components/ui/button";
import { <PERSON><PERSON><PERSON>, TooltipContent, TooltipProvider, TooltipTrigger } from "@/components/ui/tooltip";
import { ArrowLeft, Video, Info } from "lucide-react";
import { VIDEO_GENERATOR_CONFIG } from "@/constants/videoGenerator";

const VideoGeneratorHeader: React.FC = () => {
  return (
    <div className="flex items-center mb-4">
      <div className="flex items-center gap-2">
        <Link
          to="/dashboard"
          className="flex items-center text-gray-500 hover:text-gray-700 transition-colors"
        >
          <ArrowLeft size={16} />
        </Link>
        <div className="h-6 w-6 rounded-full bg-purple-100 dark:bg-purple-900/30 flex items-center justify-center">
          <Video size={14} className="text-brand-purple" />
        </div>
        <h2 className="text-sm font-medium text-brand-purple">
          {VIDEO_GENERATOR_CONFIG.HEADER_TITLE}
        </h2>
        <TooltipProvider>
          <Tooltip>
            <TooltipTrigger asChild>
              <Button variant="ghost" size="sm" className="h-6 w-6 rounded-full p-0 ml-1">
                <Info size={12} className="text-gray-400" />
              </Button>
            </TooltipTrigger>
            <TooltipContent side="right" className="max-w-sm">
              <div className="text-xs">
                <p>{VIDEO_GENERATOR_CONFIG.HEADER_DESCRIPTION}</p>
              </div>
            </TooltipContent>
          </Tooltip>
        </TooltipProvider>
      </div>
    </div>
  );
};

export default VideoGeneratorHeader;