import React, { useState } from 'react';
import { <PERSON>, <PERSON><PERSON>ontent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Progress } from '@/components/ui/progress';
import { 
  Play, 
  Clock, 
  Database, 
  Eye, 
  CheckCircle, 
  AlertCircle,
  ChevronDown,
  ChevronUp,
  Monitor
} from 'lucide-react';

interface VideoLoadingMetrics {
  totalVideos: number;
  loadedVideos: number;
  errorVideos: number;
  videosInView: number;
  videosLazilyLoaded: number;
  averageLoadTime: number;
  cacheHitRate: number;
  progressiveEnhancements: number;
  retryAttempts: number;
  loadStartTime: number;
  loadEndTime?: number;
  isComplete: boolean;
  lazyLoadingSavings: number;
}

interface VideoLoadingDebuggerProps {
  metrics?: VideoLoadingMetrics;
  className?: string;
}

/**
 * Video Loading Debugger Component - Sprint 18 Phase 3
 *
 * Development-only component for monitoring video loading performance in real-time.
 * Enhanced for Phase 3 with lazy loading metrics and bandwidth savings tracking.
 * Provides a compact, collapsible interface for tracking progressive loading metrics.
 *
 * Features:
 * - Real-time metrics display
 * - Lazy loading performance tracking
 * - Bandwidth savings monitoring
 * - Collapsible interface
 * - Performance insights
 * - Development-only rendering
 */
const VideoLoadingDebugger: React.FC<VideoLoadingDebuggerProps> = ({
  metrics,
  className,
}) => {
  const [isExpanded, setIsExpanded] = useState(false);

  // Don't render in production
  if (!import.meta.env.DEV) {
    return null;
  }

  // Don't render if no metrics
  if (!metrics || metrics.totalVideos === 0) {
    return null;
  }

  const {
    totalVideos,
    loadedVideos,
    errorVideos,
    videosInView,
    videosLazilyLoaded,
    averageLoadTime,
    cacheHitRate,
    progressiveEnhancements,
    retryAttempts,
    isComplete,
    lazyLoadingSavings,
  } = metrics;

  const completionRate = totalVideos > 0 ? ((loadedVideos + errorVideos) / totalVideos) * 100 : 0;
  const successRate = (loadedVideos + errorVideos) > 0 ? (loadedVideos / (loadedVideos + errorVideos)) * 100 : 0;
  const lazyLoadingRate = totalVideos > 0 ? (videosLazilyLoaded / totalVideos) * 100 : 0;

  const formatTime = (ms: number) => {
    if (ms < 1000) return `${ms}ms`;
    return `${(ms / 1000).toFixed(1)}s`;
  };

  const formatPercentage = (value: number) => `${value.toFixed(1)}%`;

  const formatBytes = (bytes: number) => {
    if (bytes === 0) return '0 B';
    const k = 1024;
    const sizes = ['B', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(1)) + ' ' + sizes[i];
  };

  return (
    <Card className={`border-blue-200 bg-blue-50/50 dark:bg-blue-950/20 dark:border-blue-800 ${className}`}>
      <CardHeader className="pb-2">
        <div className="flex items-center justify-between">
          <CardTitle className="text-sm font-medium flex items-center gap-2">
            <Monitor className="h-4 w-4 text-blue-500" />
            Video Loading Debug
            {isComplete && (
              <Badge variant="outline" className="text-green-600 border-green-600 text-xs">
                Complete
              </Badge>
            )}
          </CardTitle>
          <Button
            variant="ghost"
            size="sm"
            onClick={() => setIsExpanded(!isExpanded)}
            className="h-6 w-6 p-0"
          >
            {isExpanded ? (
              <ChevronUp className="h-4 w-4" />
            ) : (
              <ChevronDown className="h-4 w-4" />
            )}
          </Button>
        </div>
      </CardHeader>
      
      <CardContent className="pt-0">
        {/* Compact View */}
        <div className="space-y-2">
          <div className="flex justify-between text-xs">
            <span>Progress: {loadedVideos + errorVideos}/{totalVideos}</span>
            <span>Lazy: {formatPercentage(lazyLoadingRate)}</span>
          </div>
          <Progress value={completionRate} className="h-1" />
          <div className="flex justify-between text-xs text-muted-foreground">
            <span>Cache: {formatPercentage(cacheHitRate)}</span>
            <span>Saved: {formatBytes(lazyLoadingSavings)}</span>
          </div>
        </div>

        {/* Expanded View */}
        {isExpanded && (
          <div className="mt-4 space-y-3">
            {/* Key Metrics */}
            <div className="grid grid-cols-2 gap-2 text-xs">
              <div className="flex items-center gap-1">
                <CheckCircle className="h-3 w-3 text-green-500" />
                <span>Success: {formatPercentage(successRate)}</span>
              </div>
              <div className="flex items-center gap-1">
                <Clock className="h-3 w-3 text-orange-500" />
                <span>Avg: {formatTime(averageLoadTime)}</span>
              </div>
              <div className="flex items-center gap-1">
                <Database className="h-3 w-3 text-blue-500" />
                <span>Cache: {formatPercentage(cacheHitRate)}</span>
              </div>
              <div className="flex items-center gap-1">
                <Eye className="h-3 w-3 text-purple-500" />
                <span>Visible: {videosInView}</span>
              </div>
            </div>

            {/* Additional Stats */}
            <div className="grid grid-cols-2 gap-2 text-xs text-muted-foreground">
              <span>Lazy Loaded: {videosLazilyLoaded}</span>
              <span>Enhancements: {progressiveEnhancements}</span>
              <span>Retries: {retryAttempts}</span>
              <span>Errors: {errorVideos}</span>
            </div>

            {/* Bandwidth Savings */}
            {lazyLoadingSavings > 0 && (
              <div className="text-xs text-green-600">
                💾 Bandwidth saved: {formatBytes(lazyLoadingSavings)}
              </div>
            )}

            {/* Performance Insights */}
            {isComplete && (
              <div className="pt-2 border-t border-blue-200 dark:border-blue-800">
                <div className="text-xs space-y-1">
                  {lazyLoadingRate > 80 && (
                    <div className="text-green-600">✓ Excellent lazy loading performance</div>
                  )}
                  {cacheHitRate > 70 && (
                    <div className="text-green-600">✓ Excellent cache performance</div>
                  )}
                  {successRate > 95 && (
                    <div className="text-green-600">✓ High success rate</div>
                  )}
                  {averageLoadTime < 1000 && (
                    <div className="text-green-600">✓ Fast loading times</div>
                  )}
                  {lazyLoadingSavings > 1024 * 1024 && (
                    <div className="text-green-600">✓ Significant bandwidth savings</div>
                  )}
                  {retryAttempts > totalVideos * 0.1 && (
                    <div className="text-yellow-600">⚠ High retry rate</div>
                  )}
                  {errorVideos > totalVideos * 0.05 && (
                    <div className="text-red-600">⚠ High error rate</div>
                  )}
                </div>
              </div>
            )}
          </div>
        )}
      </CardContent>
    </Card>
  );
};

export default VideoLoadingDebugger;
