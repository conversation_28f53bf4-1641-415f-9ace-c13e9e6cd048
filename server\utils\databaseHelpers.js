/**
 * Database helper utilities for VibeNecto
 * Provides singleton Supabase client and common database operations
 */

const { createClient } = require('@supabase/supabase-js');
const { logger } = require('./logger');
const { metricsCollector } = require('./metrics');

// Singleton Supabase client
let supabaseClient = null;

/**
 * Get or create Supabase client singleton
 */
function getSupabaseClient() {
  if (!supabaseClient) {
    supabaseClient = createClient(
      process.env.SUPABASE_URL,
      process.env.SUPABASE_SERVICE_KEY
    );
  }
  return supabaseClient;
}

/**
 * Execute database operation with metrics and error handling
 */
async function executeDbOperation(operation, operationName, requestId) {
  const startTime = Date.now();
  
  try {
    const result = await operation();
    const dbTime = Date.now() - startTime;
    
    metricsCollector.recordDatabaseOperation(dbTime, !result.error);
    
    if (result.error) {
      logger.error(`Database ${operationName} failed`, {
        error: result.error.message,
        requestId
      });
    } else {
      logger.debug(`Database ${operationName} completed`, {
        duration: dbTime,
        requestId
      });
    }
    
    return result;
  } catch (error) {
    const dbTime = Date.now() - startTime;
    metricsCollector.recordDatabaseOperation(dbTime, false);
    
    logger.error(`Database ${operationName} exception`, {
      error: error.message,
      requestId
    });
    
    throw error;
  }
}

/**
 * Save video generation record to database
 */
async function saveVideoRecord(videoData, requestId) {
  const supabase = getSupabaseClient();
  
  return executeDbOperation(
    () => supabase
      .from('video_history')
      .insert(videoData)
      .select()
      .single(),
    'video record insert',
    requestId
  );
}

/**
 * Save video shots to database
 */
async function saveVideoShots(shotRecords, requestId) {
  const supabase = getSupabaseClient();
  
  return executeDbOperation(
    () => supabase
      .from('video_shots')
      .insert(shotRecords),
    'video shots insert',
    requestId
  );
}

/**
 * Update video status and metadata
 */
async function updateVideoStatus(jobId, updateData, requestId) {
  const supabase = getSupabaseClient();
  
  return executeDbOperation(
    () => supabase
      .from('video_history')
      .update(updateData)
      .eq('job_id', jobId)
      .select()
      .single(),
    'video status update',
    requestId
  );
}

/**
 * Get video record by job ID
 */
async function getVideoByJobId(jobId, requestId) {
  const supabase = getSupabaseClient();
  
  return executeDbOperation(
    () => supabase
      .from('video_history')
      .select('*')
      .eq('job_id', jobId)
      .single(),
    'video record fetch',
    requestId
  );
}

/**
 * Get video history for user with optional filters
 */
async function getVideoHistory(userId, options = {}, requestId) {
  const supabase = getSupabaseClient();
  const { limit = 50, videoType, status } = options;
  
  let query = supabase
    .from('video_history')
    .select(`
      *,
      video_shots (
        id,
        shot_number,
        prompt,
        reference_image_s3_key,
        reference_image_format
      )
    `)
    .eq('user_id', userId)
    .order('created_at', { ascending: false })
    .limit(parseInt(limit));

  if (videoType) {
    query = query.eq('video_type', videoType);
  }

  if (status) {
    query = query.eq('status', status);
  }

  return executeDbOperation(
    () => query,
    'video history fetch',
    requestId
  );
}

/**
 * Get processing videos for user
 */
async function getProcessingVideos(userId, requestId) {
  const supabase = getSupabaseClient();
  
  return executeDbOperation(
    () => supabase
      .from('video_history')
      .select('id, job_id, user_id, status, created_at, prompt')
      .eq('user_id', userId)
      .in('status', ['processing', 'pending']),
    'processing videos fetch',
    requestId
  );
}

/**
 * Delete video record and return S3 key
 */
async function deleteVideoRecord(videoId, userId, requestId) {
  const supabase = getSupabaseClient();
  
  // First get the S3 key
  const fetchResult = await executeDbOperation(
    () => supabase
      .from('video_history')
      .select('s3_key, prompt')
      .eq('id', videoId)
      .eq('user_id', userId)
      .single(),
    'video record fetch for deletion',
    requestId
  );

  if (fetchResult.error || !fetchResult.data) {
    return { error: fetchResult.error, found: false };
  }

  // Delete the record
  const deleteResult = await executeDbOperation(
    () => supabase
      .from('video_history')
      .delete()
      .eq('id', videoId)
      .eq('user_id', userId),
    'video record deletion',
    requestId
  );

  return {
    error: deleteResult.error,
    s3Key: fetchResult.data.s3_key,
    found: true
  };
}

/**
 * Delete image record and return S3 key
 */
async function deleteImageRecord(imageId, userId, requestId) {
  const supabase = getSupabaseClient();
  
  // First get the S3 key
  const fetchResult = await executeDbOperation(
    () => supabase
      .from('image_history')
      .select('s3_key, prompt')
      .eq('id', imageId)
      .eq('user_id', userId)
      .single(),
    'image record fetch for deletion',
    requestId
  );

  if (fetchResult.error || !fetchResult.data) {
    return { error: fetchResult.error, found: false };
  }

  // Delete the record
  const deleteResult = await executeDbOperation(
    () => supabase
      .from('image_history')
      .delete()
      .eq('id', imageId)
      .eq('user_id', userId),
    'image record deletion',
    requestId
  );

  return {
    error: deleteResult.error,
    s3Key: fetchResult.data.s3_key,
    found: true
  };
}

/**
 * Get pending videos for cleanup
 */
async function getPendingVideosForCleanup(timeoutMs, requestId) {
  const supabase = getSupabaseClient();
  
  return executeDbOperation(
    () => supabase
      .from('video_history')
      .select('id, job_id, s3_key')
      .eq('status', 'pending')
      .lt('created_at', new Date(Date.now() - timeoutMs).toISOString()),
    'pending videos cleanup fetch',
    requestId
  );
}

/**
 * Delete old failed videos
 */
async function deleteOldFailedVideos(cleanupTimeMs, requestId) {
  const supabase = getSupabaseClient();
  
  return executeDbOperation(
    () => supabase
      .from('video_history')
      .delete()
      .eq('status', 'failed')
      .lt('completed_at', new Date(Date.now() - cleanupTimeMs).toISOString()),
    'old failed videos cleanup',
    requestId
  );
}

module.exports = {
  getSupabaseClient,
  executeDbOperation,
  saveVideoRecord,
  saveVideoShots,
  updateVideoStatus,
  getVideoByJobId,
  getVideoHistory,
  getProcessingVideos,
  deleteVideoRecord,
  deleteImageRecord,
  getPendingVideosForCleanup,
  deleteOldFailedVideos
};