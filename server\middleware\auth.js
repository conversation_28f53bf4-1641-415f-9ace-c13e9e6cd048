const jwt = require('jsonwebtoken');
const { logger } = require('../utils/logger');

const authMiddleware = (req, res, next) => {
  const authHeader = req.headers.authorization;

  if (authHeader && authHeader.startsWith('Bearer ')) {
    const token = authHeader.substring(7, authHeader.length); // Remove "Bearer "
    if (!process.env.SUPABASE_JWT_SECRET) {
      logger.error('SUPABASE_JWT_SECRET is not defined. Cannot verify JWT.');
      return res.status(500).json({
        success: false,
        error: 'Server configuration error: JWT secret not configured'
      });
    }
    try {
      const decoded = jwt.verify(token, process.env.SUPABASE_JWT_SECRET);
      req.user = { id: decoded.sub, ...decoded }; // Attach user info to request
      logger.debug('JWT verified successfully', { userId: req.user.id, requestId: req.requestId });
    } catch (error) {
      logger.warn('JWT verification failed', { error: error.message, tokenProvided: !!token, requestId: req.requestId });
      // Token is invalid or expired. Don't set req.user.
      // You might want to return a 401/403 here if all routes after this are protected.
      // For now, we'll let it pass and individual routes can check req.user.
    }
  } else {
    logger.debug('No JWT provided in Authorization header', { requestId: req.requestId });
  }
  next();
};

// Optional: Middleware to protect specific routes
const protectRoute = (req, res, next) => {
  if (!req.user || !req.user.id) {
    logger.warn('Protected route accessed without authentication', { path: req.path, requestId: req.requestId });
    return res.status(401).json({ error: 'Unauthorized: Authentication required' });
  }
  next();
};

module.exports = { authMiddleware, protectRoute };