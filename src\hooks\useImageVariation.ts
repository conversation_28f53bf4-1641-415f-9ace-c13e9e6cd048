import { useState } from 'react';
import { toast } from 'sonner';
import { useQueryClient } from '@tanstack/react-query';
import { useAuth } from '@/contexts/AuthContext';
import { generateImageVariation, ImageVariationOptions } from '@/services/bedrockService';
import { saveImageHistory, ImageHistoryItem } from '@/services/imageHistoryService';
import { uploadImageToS3 } from '@/services/s3Service';
import { TOAST_MESSAGES, DEFAULT_VALUES, PROCESSING } from '@/constants/imageVariation';

export interface UseImageVariationOptions {
  onSuccess?: (resultImage: string) => void;
  onError?: (error: string) => void;
}

export const useImageVariation = (options: UseImageVariationOptions = {}) => {
  const { user } = useAuth();
  const queryClient = useQueryClient();
  const [isProcessing, setIsProcessing] = useState(false);
  const [error, setError] = useState<string | null>(null);

  /**
   * Consolidated function to save image variation to history
   * Handles both primary S3 upload (from service) and fallback S3 upload
   * Eliminates the duplicate logic that was in the original component
   */
  const saveVariationToHistory = async (
    resultImage: string,
    prompt: string,
    variationStrength: number,
    negativePrompt: string,
    s3Url?: string,
    s3Key?: string
  ): Promise<void> => {
    if (!user?.id) return;

    const historyParameters = PROCESSING.getHistoryParameters(variationStrength, negativePrompt);
    
    // Try to save with provided S3 information first
    if (s3Url && s3Key) {
      try {
        const newHistoryItem: ImageHistoryItem = {
          user_id: user.id,
          prompt: prompt || DEFAULT_VALUES.PROMPT_PLACEHOLDER,
          image_type: DEFAULT_VALUES.IMAGE_TYPE,
          s3_key: s3Key,
          s3_url: s3Url,
          parameters: historyParameters
        };

        await saveImageHistory(newHistoryItem);
        await queryClient.invalidateQueries({ queryKey: ['imageHistory', user.id] });
        toast.success(TOAST_MESSAGES.SUCCESS);
        return;
      } catch (saveError) {
        if (import.meta.env.DEV) {
          console.error('Failed to save image to history:', saveError);
        }
        toast.warning(TOAST_MESSAGES.ERROR.SAVE_WARNING);
        
        // Still try to invalidate queries even if save failed
        try {
          await queryClient.invalidateQueries({ queryKey: ['imageHistory', user.id] });
        } catch (invalidateError) {
          if (import.meta.env.DEV) {
            console.error('Failed to invalidate queries after save error:', invalidateError);
          }
        }
        return;
      }
    }

    // Fallback: Upload to S3 directly if service didn't provide S3 info
    try {
      const s3Result = await uploadImageToS3({
        image: resultImage.split(',')[1],
        userId: user.id,
        imageType: 'variation',
        filename: PROCESSING.generateFilename()
      });

      if (s3Result.success && s3Result.url && s3Result.key) {
        const newHistoryItem: ImageHistoryItem = {
          user_id: user.id,
          prompt: prompt || DEFAULT_VALUES.PROMPT_PLACEHOLDER,
          image_type: DEFAULT_VALUES.IMAGE_TYPE,
          s3_key: s3Result.key,
          s3_url: s3Result.url,
          parameters: historyParameters
        };

        try {
          await saveImageHistory(newHistoryItem);
          await queryClient.invalidateQueries({ queryKey: ['imageHistory', user.id] });
          toast.success(TOAST_MESSAGES.SUCCESS);
        } catch (saveError) {
          if (import.meta.env.DEV) {
            console.error('Failed to save image to history:', saveError);
          }
          toast.warning(TOAST_MESSAGES.ERROR.SAVE_WARNING);
          
          // Still try to invalidate queries even if save failed
          try {
            await queryClient.invalidateQueries({ queryKey: ['imageHistory', user.id] });
          } catch (invalidateError) {
            if (import.meta.env.DEV) {
              console.error('Failed to invalidate queries after save error:', invalidateError);
            }
          }
        }
      } else {
        toast.warning(TOAST_MESSAGES.ERROR.S3_WARNING);
      }
    } catch (s3Error) {
      if (import.meta.env.DEV) {
        console.error('Failed to upload to S3:', s3Error);
      }
      toast.warning(TOAST_MESSAGES.ERROR.S3_WARNING);
    }
  };

  /**
   * Main function to process image variation
   * Consolidates the generation logic and history saving
   */
  const processImageVariation = async (
    sourceImage: string,
    variationStrength: number,
    prompt: string,
    negativePrompt: string
  ): Promise<string | null> => {
    if (!sourceImage || !user?.id) {
      const errorMessage = TOAST_MESSAGES.ERROR.AUTH_REQUIRED;
      toast.error(errorMessage);
      setError(errorMessage);
      options.onError?.(errorMessage);
      return null;
    }

    setIsProcessing(true);
    setError(null);

    try {
      // Extract the base64 data from the source image
      const base64Data = sourceImage.split(',')[1];

      // Prepare options for image variation
      const variationOptions: ImageVariationOptions = {
        image: base64Data,
        ...PROCESSING.getVariationOptions(variationStrength, negativePrompt)
      };

      // Call the Bedrock service to generate an image variation
      const result = await generateImageVariation(variationOptions, user.id);

      if (result.success && result.image) {
        // Save to history (handles both S3 scenarios)
        await saveVariationToHistory(
          result.image,
          prompt,
          variationStrength,
          negativePrompt,
          result.s3Url,
          result.s3Key
        );

        options.onSuccess?.(result.image);
        return result.image;
      } else {
        const errorMessage = result.error || TOAST_MESSAGES.ERROR.GENERATION_FAILED;
        toast.error(errorMessage);
        setError(errorMessage);
        options.onError?.(errorMessage);
        return null;
      }
    } catch (error) {
      const errorMessage = TOAST_MESSAGES.ERROR.PROCESSING_FAILED;
      toast.error(errorMessage);
      setError(errorMessage);
      options.onError?.(errorMessage);
      
      if (import.meta.env.DEV) {
        console.error(error);
      }
      return null;
    } finally {
      setIsProcessing(false);
    }
  };

  return {
    processImageVariation,
    isProcessing,
    error,
    clearError: () => setError(null)
  };
};