/**
 * Image processing utilities for background removal, color-guided generation, and other image operations
 * Handles canvas operations, compression, file conversions, and color extraction
 */

import { IMAGE_PROCESSING } from '@/constants/backgroundRemoval';
import { COLOR_GUIDED_LIMITS } from '@/constants/colorGuided';

/**
 * Converts a File to base64 string using canvas for better compression control
 * @param file - The image file to convert
 * @param maxDimension - Maximum dimension for resizing (optional)
 * @param quality - Compression quality for JPEG (0-1, optional)
 * @returns Promise that resolves to base64 string without data URL prefix
 */
export const fileToBase64WithCompression = async (
  file: File,
  maxDimension: number = IMAGE_PROCESSING.MAX_DIMENSION,
  quality: number = IMAGE_PROCESSING.COMPRESSION_QUALITY
): Promise<string> => {
  return new Promise((resolve, reject) => {
    const img = new Image();
    img.crossOrigin = 'anonymous';

    img.onload = () => {
      try {
        const canvas = document.createElement('canvas');
        const ctx = canvas.getContext('2d');

        if (!ctx) {
          reject(new Error('Failed to get canvas context'));
          return;
        }

        // Calculate new dimensions
        const scale = Math.min(maxDimension / img.width, maxDimension / img.height, 1);
        canvas.width = img.width * scale;
        canvas.height = img.height * scale;

        // Draw and compress
        ctx.drawImage(img, 0, 0, canvas.width, canvas.height);

        // Choose format based on file size and type
        const shouldCompress = file.size > IMAGE_PROCESSING.COMPRESSION_THRESHOLD;
        const format = shouldCompress ? 'image/jpeg' : 'image/png';
        const compressionQuality = shouldCompress ? quality : undefined;

        const dataUrl = canvas.toDataURL(format, compressionQuality);
        const base64 = dataUrl.split(',')[1];

        resolve(base64);
      } catch (error) {
        reject(error);
      }
    };

    img.onerror = () => {
      reject(new Error('Failed to load image'));
    };

    img.src = URL.createObjectURL(file);
  });
};

/**
 * Converts a blob URL or data URL to base64 string using canvas
 * @param imageUrl - The image URL (blob: or data:)
 * @returns Promise that resolves to base64 string without data URL prefix
 */
export const imageUrlToBase64 = async (imageUrl: string): Promise<string> => {
  return new Promise((resolve, reject) => {
    const img = new Image();
    img.crossOrigin = 'anonymous';

    img.onload = () => {
      try {
        const canvas = document.createElement('canvas');
        canvas.width = img.width;
        canvas.height = img.height;
        const ctx = canvas.getContext('2d');

        if (!ctx) {
          reject(new Error('Failed to get canvas context'));
          return;
        }

        ctx.drawImage(img, 0, 0);
        const dataUrl = canvas.toDataURL('image/png');
        const base64 = dataUrl.split(',')[1];

        resolve(base64);
      } catch (error) {
        reject(error);
      }
    };

    img.onerror = () => {
      reject(new Error('Failed to load image'));
    };

    img.src = imageUrl;
  });
};

/**
 * Creates a download link and triggers download for an image
 * @param imageData - Base64 image data or data URL
 * @param filename - Name for the downloaded file
 */
export const downloadImage = (imageData: string, filename: string): void => {
  try {
    const link = document.createElement('a');
    
    // Ensure imageData is a proper data URL
    const dataUrl = imageData.startsWith('data:') ? imageData : `data:image/png;base64,${imageData}`;
    
    link.href = dataUrl;
    link.download = filename;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  } catch (error) {
    throw new Error('Failed to download image');
  }
};

/**
 * Creates a preview URL from a File object
 * @param file - The image file
 * @returns Object URL for preview (remember to revoke when done)
 */
export const createPreviewUrl = (file: File): string => {
  return URL.createObjectURL(file);
};

/**
 * Revokes an object URL to free memory
 * @param url - The object URL to revoke
 */
export const revokePreviewUrl = (url: string): void => {
  if (url && url.startsWith('blob:')) {
    URL.revokeObjectURL(url);
  }
};

/**
 * Validates image file type and size
 * @param file - The file to validate
 * @param maxSize - Maximum file size in bytes
 * @param allowedTypes - Array of allowed MIME types
 * @returns Validation result with success flag and error message
 */
export const validateImageFile = (
  file: File,
  maxSize: number,
  allowedTypes: string[]
): { isValid: boolean; error?: string } => {
  if (!file.type.startsWith('image/')) {
    return { isValid: false, error: 'Please select an image file' };
  }

  if (!allowedTypes.includes(file.type)) {
    return { isValid: false, error: `Unsupported file type. Allowed: ${allowedTypes.join(', ')}` };
  }

  if (file.size > maxSize) {
    const maxSizeMB = Math.round(maxSize / (1024 * 1024));
    return { isValid: false, error: `File size exceeds ${maxSizeMB}MB limit` };
  }

  return { isValid: true };
};

/**
 * Gets image dimensions from a File object
 * @param file - The image file
 * @returns Promise that resolves to dimensions object
 */
export const getImageDimensions = async (file: File): Promise<{ width: number; height: number }> => {
  return new Promise((resolve, reject) => {
    const img = new Image();
    
    img.onload = () => {
      resolve({ width: img.width, height: img.height });
    };
    
    img.onerror = () => {
      reject(new Error('Failed to load image for dimension calculation'));
    };
    
    img.src = URL.createObjectURL(file);
  });
};

// ============================================================================
// COLOR-GUIDED GENERATION UTILITIES
// ============================================================================

/**
 * Converts a reference image to base64 with compression for color-guided generation
 * @param imageUrl - The image URL (blob: or data:)
 * @param maxDimension - Maximum dimension for compression
 * @param quality - Compression quality (0-1)
 * @returns Promise that resolves to base64 string without data URL prefix
 */
export const processReferenceImageForColorGuided = async (
  imageUrl: string,
  maxDimension: number = COLOR_GUIDED_LIMITS.MAX_DIMENSION,
  quality: number = COLOR_GUIDED_LIMITS.COMPRESSION_QUALITY
): Promise<string> => {
  return new Promise((resolve, reject) => {
    const img = new Image();
    img.crossOrigin = 'anonymous';

    img.onload = () => {
      try {
        const canvas = document.createElement('canvas');
        canvas.width = img.width;
        canvas.height = img.height;
        const ctx = canvas.getContext('2d');

        if (!ctx) {
          reject(new Error('Failed to get canvas context'));
          return;
        }

        ctx.drawImage(img, 0, 0);
        const originalBase64 = canvas.toDataURL('image/png').split(',')[1];

        // Compress if the image is too large
        if (originalBase64.length > COLOR_GUIDED_LIMITS.COMPRESSION_THRESHOLD) {
          const compressionCanvas = document.createElement('canvas');
          const maxDim = Math.max(img.width, img.height);
          const scale = maxDim > maxDimension ? maxDimension / maxDim : 1;

          compressionCanvas.width = img.width * scale;
          compressionCanvas.height = img.height * scale;

          const compressionCtx = compressionCanvas.getContext('2d');
          if (compressionCtx) {
            compressionCtx.drawImage(img, 0, 0, compressionCanvas.width, compressionCanvas.height);
            const compressedBase64 = compressionCanvas.toDataURL('image/jpeg', quality).split(',')[1];
            resolve(compressedBase64);
          } else {
            resolve(originalBase64);
          }
        } else {
          resolve(originalBase64);
        }
      } catch (error) {
        reject(error);
      }
    };

    img.onerror = () => {
      reject(new Error('Failed to load reference image'));
    };

    img.src = imageUrl;
  });
};

/**
 * Extracts dominant colors from an image
 * @param imageUrl - The image URL to extract colors from
 * @param colorCount - Number of colors to extract (default: 5)
 * @returns Promise that resolves to array of hex color strings
 */
export const extractColorsFromImage = async (
  imageUrl: string,
  colorCount: number = 5
): Promise<string[]> => {
  return new Promise((resolve, reject) => {
    const img = new Image();
    img.crossOrigin = 'anonymous';

    img.onload = () => {
      try {
        const canvas = document.createElement('canvas');
        const ctx = canvas.getContext('2d');

        if (!ctx) {
          reject(new Error('Failed to get canvas context'));
          return;
        }

        // Use a smaller canvas for color extraction to improve performance
        const maxSize = 100;
        const scale = Math.min(maxSize / img.width, maxSize / img.height);
        canvas.width = img.width * scale;
        canvas.height = img.height * scale;

        ctx.drawImage(img, 0, 0, canvas.width, canvas.height);
        const imageData = ctx.getImageData(0, 0, canvas.width, canvas.height);
        const pixels = imageData.data;

        // Simple color extraction algorithm
        const colorMap = new Map<string, number>();
        
        // Sample every 4th pixel to improve performance
        for (let i = 0; i < pixels.length; i += 16) {
          const r = pixels[i];
          const g = pixels[i + 1];
          const b = pixels[i + 2];
          const alpha = pixels[i + 3];

          // Skip transparent pixels
          if (alpha < 128) continue;

          // Quantize colors to reduce noise
          const quantizedR = Math.round(r / 32) * 32;
          const quantizedG = Math.round(g / 32) * 32;
          const quantizedB = Math.round(b / 32) * 32;

          const hex = rgbToHex(quantizedR, quantizedG, quantizedB);
          colorMap.set(hex, (colorMap.get(hex) || 0) + 1);
        }

        // Sort colors by frequency and return top colors
        const sortedColors = Array.from(colorMap.entries())
          .sort((a, b) => b[1] - a[1])
          .slice(0, colorCount)
          .map(([color]) => color);

        resolve(sortedColors);
      } catch (error) {
        reject(error);
      }
    };

    img.onerror = () => {
      reject(new Error('Failed to load image for color extraction'));
    };

    img.src = imageUrl;
  });
};

/**
 * Converts RGB values to hex color string
 * @param r - Red value (0-255)
 * @param g - Green value (0-255)
 * @param b - Blue value (0-255)
 * @returns Hex color string (e.g., "#FF5733")
 */
export const rgbToHex = (r: number, g: number, b: number): string => {
  const toHex = (n: number) => {
    const hex = Math.max(0, Math.min(255, Math.round(n))).toString(16);
    return hex.length === 1 ? '0' + hex : hex;
  };
  
  return `#${toHex(r)}${toHex(g)}${toHex(b)}`.toUpperCase();
};

/**
 * Converts hex color to RGB values
 * @param hex - Hex color string (e.g., "#FF5733" or "FF5733")
 * @returns RGB object with r, g, b values
 */
export const hexToRgb = (hex: string): { r: number; g: number; b: number } | null => {
  const cleanHex = hex.replace('#', '');
  
  if (cleanHex.length === 3) {
    // Short hex format (e.g., "F53")
    const r = parseInt(cleanHex[0] + cleanHex[0], 16);
    const g = parseInt(cleanHex[1] + cleanHex[1], 16);
    const b = parseInt(cleanHex[2] + cleanHex[2], 16);
    return { r, g, b };
  } else if (cleanHex.length === 6) {
    // Full hex format (e.g., "FF5733")
    const r = parseInt(cleanHex.substring(0, 2), 16);
    const g = parseInt(cleanHex.substring(2, 4), 16);
    const b = parseInt(cleanHex.substring(4, 6), 16);
    return { r, g, b };
  }
  
  return null;
};

/**
 * Validates if a string is a valid hex color
 * @param color - Color string to validate
 * @returns True if valid hex color, false otherwise
 */
export const isValidHexColor = (color: string): boolean => {
  return /^#([A-Fa-f0-9]{6}|[A-Fa-f0-9]{3})$/.test(color);
};

/**
 * Generates a random hex color
 * @returns Random hex color string
 */
export const generateRandomColor = (): string => {
  const letters = '0123456789ABCDEF';
  let color = '#';
  for (let i = 0; i < 6; i++) {
    color += letters[Math.floor(Math.random() * 16)];
  }
  return color;
};

/**
 * Gets the contrast ratio between two colors
 * @param color1 - First hex color
 * @param color2 - Second hex color
 * @returns Contrast ratio (1-21)
 */
export const getContrastRatio = (color1: string, color2: string): number => {
  const getLuminance = (hex: string): number => {
    const rgb = hexToRgb(hex);
    if (!rgb) return 0;
    
    const { r, g, b } = rgb;
    const [rs, gs, bs] = [r, g, b].map(c => {
      c = c / 255;
      return c <= 0.03928 ? c / 12.92 : Math.pow((c + 0.055) / 1.055, 2.4);
    });
    
    return 0.2126 * rs + 0.7152 * gs + 0.0722 * bs;
  };
  
  const lum1 = getLuminance(color1);
  const lum2 = getLuminance(color2);
  const brightest = Math.max(lum1, lum2);
  const darkest = Math.min(lum1, lum2);
  
  return (brightest + 0.05) / (darkest + 0.05);
};

/**
 * Creates a color palette with good contrast
 * @param baseColor - Base hex color to build palette from
 * @param count - Number of colors in palette
 * @returns Array of hex color strings
 */
export const createContrastingPalette = (baseColor: string, count: number = 5): string[] => {
  const palette: string[] = [baseColor];
  const baseRgb = hexToRgb(baseColor);
  
  if (!baseRgb) return [baseColor];
  
  for (let i = 1; i < count; i++) {
    const hueShift = (360 / count) * i;
    const newColor = shiftHue(baseColor, hueShift);
    palette.push(newColor);
  }
  
  return palette;
};

/**
 * Shifts the hue of a color by specified degrees
 * @param hex - Original hex color
 * @param degrees - Degrees to shift hue (-360 to 360)
 * @returns New hex color with shifted hue
 */
export const shiftHue = (hex: string, degrees: number): string => {
  const rgb = hexToRgb(hex);
  if (!rgb) return hex;
  
  const { r, g, b } = rgb;
  
  // Convert RGB to HSL
  const max = Math.max(r, g, b) / 255;
  const min = Math.min(r, g, b) / 255;
  const diff = max - min;
  const sum = max + min;
  const l = sum / 2;
  
  let h = 0;
  let s = 0;
  
  if (diff !== 0) {
    s = l < 0.5 ? diff / sum : diff / (2 - sum);
    
    switch (max) {
      case r / 255:
        h = ((g / 255 - b / 255) / diff) + (g < b ? 6 : 0);
        break;
      case g / 255:
        h = (b / 255 - r / 255) / diff + 2;
        break;
      case b / 255:
        h = (r / 255 - g / 255) / diff + 4;
        break;
    }
    h /= 6;
  }
  
  // Shift hue
  h = (h + degrees / 360) % 1;
  if (h < 0) h += 1;
  
  // Convert HSL back to RGB
  const hue2rgb = (p: number, q: number, t: number): number => {
    if (t < 0) t += 1;
    if (t > 1) t -= 1;
    if (t < 1/6) return p + (q - p) * 6 * t;
    if (t < 1/2) return q;
    if (t < 2/3) return p + (q - p) * (2/3 - t) * 6;
    return p;
  };
  
  let newR, newG, newB;
  
  if (s === 0) {
    newR = newG = newB = l; // achromatic
  } else {
    const q = l < 0.5 ? l * (1 + s) : l + s - l * s;
    const p = 2 * l - q;
    newR = hue2rgb(p, q, h + 1/3);
    newG = hue2rgb(p, q, h);
    newB = hue2rgb(p, q, h - 1/3);
  }
  
  return rgbToHex(Math.round(newR * 255), Math.round(newG * 255), Math.round(newB * 255));
};