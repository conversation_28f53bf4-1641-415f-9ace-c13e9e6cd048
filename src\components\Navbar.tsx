
import { Link, useLocation, useNavigate } from "react-router-dom";
import { useAuth } from "@/contexts/AuthContext";
import { Button } from "@/components/ui/button";
import { Loader2 } from "lucide-react";

const Navbar = () => {
  const { isAuthenticated, logout, isLoggingOut } = useAuth();
  const location = useLocation();
  const navigate = useNavigate();

  const handleLogout = () => {
    logout(() => navigate("/"));
  };

  return (
    <nav className="bg-white border-b border-gray-200 py-4 px-6 md:px-12">
      <div className="container mx-auto flex justify-between items-center">
        <div className="flex items-center">
          <Link to="/" className="flex items-center gap-2">
            <img src="/logo.png" alt="VibeNecto Logo" className="h-8 w-auto" />
            <span className="text-xl font-bold bg-clip-text text-transparent bg-gradient-to-r from-brand-purple to-brand-teal">
              VibeNecto
            </span>
          </Link>
        </div>

        <div className="flex items-center gap-4">
          {!isAuthenticated ? (
            <>
              {location.pathname !== "/signin" && (
                <Button variant="outline" asChild>
                  <Link to="/signin">Sign In</Link>
                </Button>
              )}
              {location.pathname !== "/signup" && (
                <Button asChild>
                  <Link to="/signup">Sign Up</Link>
                </Button>
              )}
            </>
          ) : (
            <>
              <Button variant="ghost" asChild>
                <Link to="/dashboard">Dashboard</Link>
              </Button>
              <Button variant="outline" onClick={handleLogout} disabled={isLoggingOut}>
                {isLoggingOut ? (
                  <>
                    <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                    Signing Out
                  </>
                ) : (
                  "Sign Out"
                )}
              </Button>
            </>
          )}
        </div>
      </div>
    </nav>
  );
};

export default Navbar;
