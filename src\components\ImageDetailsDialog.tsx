import React from "react";
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogTitle
} from "@/components/ui/dialog";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Download, Calendar, Settings, Info } from "lucide-react";
import { ImageHistoryItem } from "@/services/imageHistoryService";
import { formatDistanceToNow } from "date-fns";
import { Separator } from "@/components/ui/separator";
import { Badge } from "@/components/ui/badge";
import { ScrollArea } from "@/components/ui/scroll-area";

interface ImageDetailsDialogProps {
  image: ImageHistoryItem | null;
  imageUrl: string;
  open: boolean;
  onOpenChange: (open: boolean) => void;
  onDownload: (image: ImageHistoryItem) => void;
}

const ImageDetailsDialog: React.FC<ImageDetailsDialogProps> = ({
  image,
  imageUrl,
  open,
  onOpenChange,
  onDownload
}) => {
  if (!image) return null;

  // Format timestamp to relative time
  const formatTimestamp = (timestamp: string | undefined) => {
    if (!timestamp) return "Unknown date";
    try {
      return formatDistanceToNow(new Date(timestamp), { addSuffix: true });
    } catch (error) {
      return "Unknown date";
    }
  };

  // Format date to full date
  const formatFullDate = (timestamp: string | undefined) => {
    if (!timestamp) return "Unknown date";
    try {
      return new Date(timestamp).toLocaleString();
    } catch (error) {
      return "Unknown date";
    }
  };

  // Get image type display name
  const getImageTypeDisplay = (type: string) => {
    const types: Record<string, string> = {
      'text-to-image': 'Text to Image',
      'background-removal': 'Background Removal',
      'color-guided': 'Color Guided',
      'image-variation': 'Image Variation'
    };
    return types[type] || type;
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-5xl w-[90vw] max-h-[90vh] flex flex-col p-0 gap-0 overflow-hidden">
        <div className="flex h-full">
          {/* Image section (70%) */}
          <div className="w-[70%] h-[80vh] bg-gray-50 dark:bg-gray-900 flex items-center justify-center overflow-hidden">
            <img
              src={imageUrl}
              alt={image.prompt || "Generated image"}
              className="max-w-full max-h-full object-contain"
            />
          </div>

          {/* Metadata section (30%) */}
          <div className="w-[30%] p-4 border-l border-gray-200 dark:border-gray-800 flex flex-col h-[80vh]">
            <div className="mb-4">
              <DialogTitle className="text-lg font-medium">Image Details</DialogTitle>
            </div>

            <ScrollArea className="flex-1 pr-4">
              {/* Image type and date */}
              <div className="mb-4">
                <Badge variant="outline" className="mb-2">
                  {getImageTypeDisplay(image.image_type)}
                </Badge>
                <div className="flex items-center text-sm text-gray-500 dark:text-gray-400">
                  <Calendar size={14} className="mr-1" />
                  <span>{formatFullDate(image.created_at)}</span>
                </div>
              </div>

              <Separator className="my-3" />

              {/* Prompt */}
              {image.prompt && (
                <div className="mb-4">
                  <h4 className="text-sm font-medium mb-1 flex items-center">
                    <Info size={14} className="mr-1" /> Prompt
                  </h4>
                  <p className="text-sm text-gray-600 dark:text-gray-300">
                    {image.prompt}
                  </p>
                </div>
              )}

              {/* Parameters */}
              {image.parameters && Object.keys(image.parameters).length > 0 && (
                <div className="mb-4">
                  <h4 className="text-sm font-medium mb-2 flex items-center">
                    <Settings size={14} className="mr-1" /> Parameters
                  </h4>
                  <div className="space-y-2">
                    {Object.entries(image.parameters).map(([key, value]) => {
                      // Skip empty values or platform/style if they match image_type
                      if (!value ||
                          (key === 'platform' && value === 'Advanced Tools') ||
                          (key === 'style' && value === image.image_type)) {
                        return null;
                      }

                      return (
                        <div key={key} className="flex justify-between">
                          <span className="text-xs text-gray-500 capitalize">{key.replace(/([A-Z])/g, ' $1')}</span>
                          <span className="text-xs font-medium">
                            {typeof value === 'object'
                              ? JSON.stringify(value)
                              : String(value)}
                          </span>
                        </div>
                      );
                    })}
                  </div>
                </div>
              )}

              {/* Additional metadata */}
              {image.metadata && Object.keys(image.metadata).length > 0 && (
                <div className="mb-4">
                  <h4 className="text-sm font-medium mb-2">Additional Info</h4>
                  <div className="space-y-2">
                    {Object.entries(image.metadata).map(([key, value]) => {
                      if (key === 'legacy_id' || !value) return null;

                      return (
                        <div key={key} className="flex justify-between">
                          <span className="text-xs text-gray-500 capitalize">{key.replace(/([A-Z])/g, ' $1')}</span>
                          <span className="text-xs font-medium">
                            {typeof value === 'object'
                              ? JSON.stringify(value)
                              : String(value)}
                          </span>
                        </div>
                      );
                    })}
                  </div>
                </div>
              )}
            </ScrollArea>

            {/* Actions */}
            <div className="mt-auto pt-4 border-t border-gray-200 dark:border-gray-800">
              <Button
                className="w-full"
                onClick={() => onDownload(image)}
                variant="default"
              >
                <Download size={16} className="mr-2" /> Download Image
              </Button>
            </div>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
};

export default ImageDetailsDialog;
