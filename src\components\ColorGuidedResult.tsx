import React from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Download, Trash2 } from 'lucide-react';
import { toast } from 'sonner';
import { COLOR_GUIDED_MESSAGES, COLOR_GUIDED_GENERATION_OPTIONS } from '@/constants/colorGuided';

interface ColorGuidedResultProps {
  resultImage: string;
  onReset: () => void;
}

const ColorGuidedResult: React.FC<ColorGuidedResultProps> = ({
  resultImage,
  onReset
}) => {
  const downloadImage = () => {
    try {
      const link = document.createElement('a');
      link.href = resultImage;
      link.download = COLOR_GUIDED_GENERATION_OPTIONS.generateFileName();
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      toast.success(COLOR_GUIDED_MESSAGES.SUCCESS.IMAGE_DOWNLOADED);
    } catch (error) {
      toast.error(COLOR_GUIDED_MESSAGES.ERROR.DOWNLOAD_FAILED);
      if (import.meta.env.DEV) {
        console.error('Download failed:', error);
      }
    }
  };

  return (
    <div>
      <div className="rounded-lg overflow-hidden bg-gray-100 dark:bg-gray-800 mb-4 h-[400px] flex items-center justify-center">
        <img
          src={resultImage}
          alt="Color-guided generation result"
          className="max-w-full max-h-full object-contain"
        />
      </div>
      
      <p className="text-center text-sm text-gray-500 mb-4">
        {COLOR_GUIDED_MESSAGES.SUCCESS.IMAGE_GENERATED}
      </p>
      
      <div className="flex items-center justify-center gap-2">
        <Button
          size="sm"
          variant="outline"
          className="h-8 text-xs gap-1"
          onClick={onReset}
        >
          <Trash2 size={12} />
          Start Over
        </Button>
        <Button
          size="sm"
          className="h-8 text-xs gap-1 bg-brand-purple hover:bg-brand-purple/90"
          onClick={downloadImage}
        >
          <Download size={12} />
          Download
        </Button>
      </div>
    </div>
  );
};

export default ColorGuidedResult;