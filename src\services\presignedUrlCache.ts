interface CachedUrl {
  url: string;
  timestamp: number;
  expiresAt: number;
  s3_key: string;
  retryCount: number;
  loadTime: number;
}

interface CacheMetrics {
  hits: number;
  misses: number;
  evictions: number;
  totalRequests: number;
  averageLoadTime: number;
  cacheSize: number;
}

interface CacheOptions {
  maxSize?: number; // Maximum number of URLs to cache
  defaultTtl?: number; // Default TTL in milliseconds
  cleanupInterval?: number; // Cleanup interval in milliseconds
  enableMetrics?: boolean; // Enable cache metrics tracking
  enablePersistence?: boolean; // Enable sessionStorage persistence
}

class PresignedUrlCache {
  private cache = new Map<string, CachedUrl>();
  private metrics: CacheMetrics = {
    hits: 0,
    misses: 0,
    evictions: 0,
    totalRequests: 0,
    averageLoadTime: 0,
    cacheSize: 0,
  };
  private cleanupTimer: NodeJS.Timeout | null = null;
  private options: Required<CacheOptions>;

  constructor(options: CacheOptions = {}) {
    this.options = {
      maxSize: options.maxSize ?? 500,
      defaultTtl: options.defaultTtl ?? 45 * 60 * 1000, // 45 minutes (S3 URLs expire in 1 hour)
      cleanupInterval: options.cleanupInterval ?? 5 * 60 * 1000, // 5 minutes
      enableMetrics: options.enableMetrics ?? import.meta.env.DEV,
      enablePersistence: options.enablePersistence ?? true,
    };

    this.startCleanupTimer();
    this.loadFromSessionStorage();
  }

  /**
   * Get a cached URL if it exists and hasn't expired
   */
  get(s3_key: string): CachedUrl | null {
    this.metrics.totalRequests++;

    const cached = this.cache.get(s3_key);
    
    if (!cached) {
      this.metrics.misses++;
      return null;
    }

    // Check if expired
    if (Date.now() > cached.expiresAt) {
      this.cache.delete(s3_key);
      this.metrics.misses++;
      this.metrics.evictions++;
      this.updateMetrics();
      return null;
    }

    this.metrics.hits++;
    this.updateMetrics();

    if (this.options.enableMetrics) {
      console.log(`[PresignedUrlCache] Cache HIT for ${s3_key} (age: ${Date.now() - cached.timestamp}ms)`);
    }

    return cached;
  }

  /**
   * Set a URL in the cache with optional TTL
   */
  set(s3_key: string, url: string, ttl?: number, metadata?: Partial<Pick<CachedUrl, 'retryCount' | 'loadTime'>>): void {
    const now = Date.now();
    const expiresAt = now + (ttl ?? this.options.defaultTtl);

    // Evict oldest entries if cache is full
    if (this.cache.size >= this.options.maxSize) {
      this.evictOldest();
    }

    const cachedUrl: CachedUrl = {
      url,
      timestamp: now,
      expiresAt,
      s3_key,
      retryCount: metadata?.retryCount ?? 0,
      loadTime: metadata?.loadTime ?? 0,
    };

    this.cache.set(s3_key, cachedUrl);
    this.updateMetrics();
    this.saveToSessionStorage();

    if (this.options.enableMetrics) {
      console.log(`[PresignedUrlCache] Cached URL for ${s3_key} (TTL: ${ttl ?? this.options.defaultTtl}ms)`);
    }
  }

  /**
   * Check if a URL exists in cache and is not expired
   */
  has(s3_key: string): boolean {
    return this.get(s3_key) !== null;
  }

  /**
   * Remove a specific URL from cache
   */
  delete(s3_key: string): boolean {
    const deleted = this.cache.delete(s3_key);
    if (deleted) {
      this.updateMetrics();
      this.saveToSessionStorage();
    }
    return deleted;
  }

  /**
   * Clear all cached URLs
   */
  clear(): void {
    this.cache.clear();
    this.updateMetrics();
    this.clearSessionStorage();
    
    if (this.options.enableMetrics) {
      console.log('[PresignedUrlCache] Cache cleared');
    }
  }

  /**
   * Get cache metrics
   */
  getMetrics(): CacheMetrics {
    return { ...this.metrics };
  }

  /**
   * Get cache hit rate as percentage
   */
  getHitRate(): number {
    return this.metrics.totalRequests > 0 
      ? (this.metrics.hits / this.metrics.totalRequests) * 100 
      : 0;
  }

  /**
   * Get all cached URLs (for debugging)
   */
  getAll(): Map<string, CachedUrl> {
    return new Map(this.cache);
  }

  /**
   * Invalidate URLs that match a pattern
   */
  invalidatePattern(pattern: RegExp): number {
    let invalidated = 0;
    for (const [key] of this.cache) {
      if (pattern.test(key)) {
        this.cache.delete(key);
        invalidated++;
      }
    }
    
    if (invalidated > 0) {
      this.updateMetrics();
      this.saveToSessionStorage();
      
      if (this.options.enableMetrics) {
        console.log(`[PresignedUrlCache] Invalidated ${invalidated} URLs matching pattern`);
      }
    }
    
    return invalidated;
  }

  /**
   * Refresh a cached URL with new data
   */
  refresh(s3_key: string, url: string, ttl?: number, metadata?: Partial<Pick<CachedUrl, 'retryCount' | 'loadTime'>>): void {
    this.set(s3_key, url, ttl, metadata);
  }

  /**
   * Get URLs that are about to expire (within threshold)
   */
  getExpiringUrls(thresholdMs: number = 5 * 60 * 1000): string[] {
    const now = Date.now();
    const threshold = now + thresholdMs;
    
    return Array.from(this.cache.entries())
      .filter(([_, cached]) => cached.expiresAt <= threshold && cached.expiresAt > now)
      .map(([key]) => key);
  }

  private evictOldest(): void {
    let oldestKey: string | null = null;
    let oldestTime = Date.now();

    for (const [key, cached] of this.cache) {
      if (cached.timestamp < oldestTime) {
        oldestTime = cached.timestamp;
        oldestKey = key;
      }
    }

    if (oldestKey) {
      this.cache.delete(oldestKey);
      this.metrics.evictions++;
      
      if (this.options.enableMetrics) {
        console.log(`[PresignedUrlCache] Evicted oldest entry: ${oldestKey}`);
      }
    }
  }

  private updateMetrics(): void {
    this.metrics.cacheSize = this.cache.size;
    
    // Calculate average load time
    const loadTimes = Array.from(this.cache.values())
      .map(cached => cached.loadTime)
      .filter(time => time > 0);
    
    this.metrics.averageLoadTime = loadTimes.length > 0
      ? loadTimes.reduce((sum, time) => sum + time, 0) / loadTimes.length
      : 0;
  }

  private startCleanupTimer(): void {
    this.cleanupTimer = setInterval(() => {
      this.cleanup();
    }, this.options.cleanupInterval);
  }

  private cleanup(): void {
    const now = Date.now();
    let cleaned = 0;

    for (const [key, cached] of this.cache) {
      if (now > cached.expiresAt) {
        this.cache.delete(key);
        cleaned++;
      }
    }

    if (cleaned > 0) {
      this.metrics.evictions += cleaned;
      this.updateMetrics();
      this.saveToSessionStorage();
      
      if (this.options.enableMetrics) {
        console.log(`[PresignedUrlCache] Cleaned up ${cleaned} expired URLs`);
      }
    }
  }

  private saveToSessionStorage(): void {
    if (!this.options.enablePersistence || typeof window === 'undefined') return;

    try {
      const cacheData = Array.from(this.cache.entries());
      sessionStorage.setItem('presigned-url-cache', JSON.stringify(cacheData));
    } catch (error) {
      if (this.options.enableMetrics) {
        console.warn('[PresignedUrlCache] Failed to save to sessionStorage:', error);
      }
    }
  }

  private loadFromSessionStorage(): void {
    if (!this.options.enablePersistence || typeof window === 'undefined') return;

    try {
      const stored = sessionStorage.getItem('presigned-url-cache');
      if (stored) {
        const cacheData: [string, CachedUrl][] = JSON.parse(stored);
        const now = Date.now();
        
        // Only load non-expired entries
        for (const [key, cached] of cacheData) {
          if (now < cached.expiresAt) {
            this.cache.set(key, cached);
          }
        }
        
        this.updateMetrics();
        
        if (this.options.enableMetrics) {
          console.log(`[PresignedUrlCache] Loaded ${this.cache.size} URLs from sessionStorage`);
        }
      }
    } catch (error) {
      if (this.options.enableMetrics) {
        console.warn('[PresignedUrlCache] Failed to load from sessionStorage:', error);
      }
    }
  }

  private clearSessionStorage(): void {
    if (!this.options.enablePersistence || typeof window === 'undefined') return;

    try {
      sessionStorage.removeItem('presigned-url-cache');
    } catch (error) {
      if (this.options.enableMetrics) {
        console.warn('[PresignedUrlCache] Failed to clear sessionStorage:', error);
      }
    }
  }

  /**
   * Cleanup resources
   */
  destroy(): void {
    if (this.cleanupTimer) {
      clearInterval(this.cleanupTimer);
      this.cleanupTimer = null;
    }
    this.clear();
  }
}

// Global cache instance
export const presignedUrlCache = new PresignedUrlCache({
  maxSize: 500,
  defaultTtl: 45 * 60 * 1000, // 45 minutes
  cleanupInterval: 5 * 60 * 1000, // 5 minutes
  enableMetrics: import.meta.env.DEV,
  enablePersistence: true,
});

export default PresignedUrlCache;
