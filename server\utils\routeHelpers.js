/**
 * Common route helper utilities for VibeNecto
 * Extracts repetitive patterns from route handlers
 */

const { logger } = require('./logger');
const { metricsCollector } = require('./metrics');
const { uploadImageToS3 } = require('../services/s3-service');

/**
 * Standard response formatter
 */
const formatResponse = {
  success: (data) => ({
    success: true,
    ...data
  }),
  
  error: (message, details = null) => ({
    success: false,
    error: message,
    ...(details && { details })
  }),
  
  warning: (data, warning) => ({
    success: true,
    ...data,
    warning
  })
};

/**
 * Handle S3 upload with consistent error handling and logging
 */
async function handleS3Upload({ image, userId, imageType, requestId }) {
  if (!userId) {
    logger.warn('S3 upload skipped - no user ID provided', { requestId });
    return { success: true, skipped: true };
  }

  logger.info('Uploading image to S3', { userId, imageType, requestId });
  const s3StartTime = Date.now();

  try {
    const s3Result = await uploadImageToS3({
      image,
      userId,
      imageType
    });

    const s3Time = Date.now() - s3StartTime;
    metricsCollector.recordS3Operation('upload', s3Time, s3Result.success);

    if (!s3Result.success) {
      logger.warn('S3 upload failed, continuing anyway', {
        error: s3Result.error,
        userId,
        requestId
      });
      
      return {
        success: false,
        error: s3Result.error,
        continueAnyway: true
      };
    }

    logger.info('S3 upload completed successfully', {
      uploadTime: s3Time,
      userId,
      requestId
    });

    return {
      success: true,
      url: s3Result.url,
      key: s3Result.key
    };
  } catch (error) {
    const s3Time = Date.now() - s3StartTime;
    metricsCollector.recordS3Operation('upload', s3Time, false);
    
    logger.error('S3 upload exception', {
      error: error.message,
      userId,
      requestId
    });
    
    return {
      success: false,
      error: error.message,
      continueAnyway: true
    };
  }
}

/**
 * Handle Bedrock service calls with consistent error handling
 */
async function handleBedrockCall({ serviceFunction, params, operationType, startTime, requestId }) {
  logger.info(`Starting ${operationType}`, {
    ...params,
    // Sanitize sensitive data
    prompt: params.prompt?.substring(0, 100),
    image: params.image ? '[base64_data]' : undefined,
    requestId
  });

  try {
    const result = await serviceFunction(params);

    if (!result.success) {
      const processingTime = Date.now() - startTime;
      
      logger.error(`${operationType} failed`, {
        error: result.error,
        processingTime,
        requestId
      });

      return {
        success: false,
        error: result.error,
        processingTime
      };
    }

    logger.info(`${operationType} completed successfully`, { requestId });
    return {
      success: true,
      data: result,
      processingTime: Date.now() - startTime
    };
  } catch (error) {
    const processingTime = Date.now() - startTime;
    
    logger.error(`${operationType} exception`, {
      error: error.message,
      stack: error.stack,
      processingTime,
      requestId
    });

    throw error; // Re-throw for asyncHandler
  }
}

/**
 * Validate authentication before processing
 */
function validateAuthentication(userId, requestId) {
  if (!userId) {
    logger.warn('Operation attempted without authentication', { requestId });
    return {
      valid: false,
      response: {
        status: 401,
        body: formatResponse.error('Authentication required. User ID is missing.')
      }
    };
  }
  return { valid: true };
}

/**
 * Record metrics for image generation
 */
function recordImageMetrics(quality, success, processingTime) {
  metricsCollector.recordImageGeneration(
    quality || 'standard',
    success,
    processingTime
  );
}

/**
 * Build image response with S3 data
 */
function buildImageResponse(bedrockResult, s3Result) {
  const response = {
    success: true,
    image: bedrockResult.image
  };

  if (s3Result && s3Result.success) {
    response.s3Url = s3Result.url;
    response.s3Key = s3Result.key;
  } else if (s3Result && s3Result.continueAnyway) {
    response.warning = `Image generated successfully but failed to upload to S3: ${s3Result.error}`;
  }

  return response;
}

/**
 * Standard error response handler
 */
function handleRouteError(error, operationType, processingTime, userId, requestId) {
  logger.error(`${operationType} error`, {
    error: error.message,
    stack: error.stack,
    processingTime,
    userId,
    requestId
  });

  // Let asyncHandler handle the error
  throw error;
}

module.exports = {
  formatResponse,
  handleS3Upload,
  handleBedrockCall,
  validateAuthentication,
  recordImageMetrics,
  buildImageResponse,
  handleRouteError
};