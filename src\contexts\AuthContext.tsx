
import React, { createContext, useContext, useState, useEffect } from 'react';
import { toast } from "sonner";
import { supabase } from '@/lib/supabase';
import { User as SupabaseUser, Session } from '@supabase/supabase-js';

interface User {
  id: string;
  email: string;
  name: string;
  createdAt?: string; // Added to store user creation date
  role?: string; // User role (user, superadmin)
  isSuperadmin?: boolean; // Superadmin flag
}

interface AuthContextType {
  user: User | null;
  isAuthenticated: boolean;
  isLoading: boolean;
  isLoggingOut: boolean;
  login: (email: string, password: string) => Promise<void>;
  signup: (email: string, password: string, name: string) => Promise<void>;
  logout: (callback?: () => void) => Promise<void>;
  resetPassword: (email: string) => Promise<void>;
  updateProfile: (data: { name?: string; email?: string }) => Promise<void>;
  updateUserPassword: (newPassword: string) => Promise<void>;
  checkSuperadminAccess: () => boolean;
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

export const useAuth = () => {
  const context = useContext(AuthContext);
  if (!context) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
};

export const AuthProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const [user, setUser] = useState<User | null>(null);
  const [isLoading, setIsLoading] = useState<boolean>(true);
  const [isLoggingOut, setIsLoggingOut] = useState<boolean>(false);
  const [session, setSession] = useState<Session | null>(null);

  // Initialize the auth state based on Supabase session
  useEffect(() => {
    const initializeAuth = async () => {
      setIsLoading(true);

      // Get the current session
      const { data: { session } } = await supabase.auth.getSession();
      setSession(session);

      if (session?.user) {
        await fetchUserProfile(session.user);
      } else {
        setUser(null);
      }

      setIsLoading(false);

      // Set up auth state listener
      const { data: { subscription } } = supabase.auth.onAuthStateChange(
        async (event, session) => {
          // Auth state changed - using structured logging would be better here
          // console.log('Auth state changed:', event);
          setSession(session);

          if (event === 'SIGNED_IN' && session?.user) {
            await fetchUserProfile(session.user);
          } else if (event === 'SIGNED_OUT') {
            // Ensure user state is cleared
            setUser(null);
            setIsLoggingOut(false);
          }
        }
      );

      // Cleanup subscription on unmount
      return () => {
        subscription.unsubscribe();
      };
    };

    initializeAuth();
  }, []);

  // Fetch user profile from the profiles table
  const fetchUserProfile = async (supabaseUser: SupabaseUser) => {
    try {
      const { data, error } = await supabase
        .from('profiles')
        .select('name, email, role, is_superadmin')
        .eq('id', supabaseUser.id)
        .single();

      if (error) {
        // Error fetching user profile - should use proper error logging
        // console.error('Error fetching user profile:', error);
        return;
      }

      if (data) {
        setUser({
          id: supabaseUser.id,
          email: data.email || supabaseUser.email || '',
          name: data.name || '',
          createdAt: supabaseUser.created_at, // Store createdAt
          role: data.role || 'user',
          isSuperadmin: data.is_superadmin || false,
        });
      } else {
        // If no profile exists yet, create one with basic info
        const newProfile = {
          id: supabaseUser.id,
          email: supabaseUser.email || '',
          name: supabaseUser.email ? supabaseUser.email.split('@')[0] : '',
          createdAt: supabaseUser.created_at, // Store createdAt for new profiles too
          role: 'user',
          isSuperadmin: false,
        };

        const { error: insertError } = await supabase
          .from('profiles')
          .insert(newProfile);

        if (insertError) {
          // Error creating user profile - should use proper error logging
          // console.error('Error creating user profile:', insertError);
        } else {
          setUser(newProfile);
        }
      }
    } catch (error) {
      // Error in fetchUserProfile - should use proper error logging
      // console.error('Error in fetchUserProfile:', error);
    }
  };

  // Sign in with email and password
  const login = async (email: string, password: string) => {
    setIsLoading(true);
    try {
      const { data, error } = await supabase.auth.signInWithPassword({
        email,
        password,
      });

      if (error) {
        throw error;
      }

      toast.success("Logged in successfully!");
    } catch (error: any) {
      toast.error(error.message || "Login failed. Please check your credentials.");
      throw error;
    } finally {
      setIsLoading(false);
    }
  };

  // Sign up with email, password, and name
  const signup = async (email: string, password: string, name: string) => {
    setIsLoading(true);
    try {
      // Create the user in Supabase Auth
      const { data, error } = await supabase.auth.signUp({
        email,
        password,
        options: {
          data: {
            name,
          },
        },
      });

      if (error) {
        throw error;
      }

      // If email confirmation is required
      if (data.user && !data.user.confirmed_at) {
        toast.success("Please check your email to confirm your account!");
      } else {
        toast.success("Account created successfully!");
      }
    } catch (error: any) {
      toast.error(error.message || "Signup failed. Please try again.");
      throw error;
    } finally {
      setIsLoading(false);
    }
  };

  // Sign out
  const logout = async (callback?: () => void) => {
    setIsLoggingOut(true);
    try {
      const { error } = await supabase.auth.signOut();
      if (error) {
        throw error;
      }

      // Set user to null immediately to update UI
      setUser(null);

      toast.success("Logged out successfully!");

      // Add a small delay to ensure state updates before navigation
      setTimeout(() => {
        if (callback) {
          callback();
        }
        setIsLoggingOut(false);
      }, 300);
    } catch (error: any) {
      setIsLoggingOut(false);
      toast.error(error.message || "Failed to log out.");
      throw error;
    }
  };

  // Reset password
  const resetPassword = async (email: string) => {
    try {
      const { error } = await supabase.auth.resetPasswordForEmail(email, {
        redirectTo: `${window.location.origin}/reset-password`,
      });

      if (error) {
        throw error;
      }

      toast.success("Password reset email sent. Please check your inbox.");
    } catch (error: any) {
      toast.error(error.message || "Failed to send reset email.");
      throw error;
    }
  };

  // Update user profile
  const updateProfile = async (data: { name?: string; email?: string }) => {
    if (!user) return;

    try {
      const updates = {
        id: user.id,
        ...data,
        updated_at: new Date().toISOString(),
      };

      const { error } = await supabase
        .from('profiles')
        .update(updates)
        .eq('id', user.id);

      if (error) {
        throw error;
      }

      // Update local user state
      setUser({
        ...user,
        ...data,
        createdAt: user.createdAt, // Preserve createdAt during profile update
      });

      toast.success("Profile updated successfully!");
    } catch (error: any) {
      toast.error(error.message || "Failed to update profile.");
      throw error;
    }
  };

  // Update user password
  const updateUserPassword = async (newPassword: string) => {
    if (!user) {
      toast.error("You must be logged in to update your password.");
      throw new Error("User not authenticated");
    }

    try {
      const { error } = await supabase.auth.updateUser({
        password: newPassword,
      });

      if (error) {
        throw error;
      }

      toast.success("Password updated successfully!");
    } catch (error: any) {
      toast.error(error.message || "Failed to update password.");
      throw error;
    }
  };

  // Check if current user has superadmin access
  const checkSuperadminAccess = (): boolean => {
    if (!user) return false;
    return user.email === '<EMAIL>' &&
           user.isSuperadmin === true &&
           user.role === 'superadmin';
  };

  const value = {
    user,
    isAuthenticated: !!user,
    isLoading,
    isLoggingOut,
    login,
    signup,
    logout,
    resetPassword,
    updateProfile,
    updateUserPassword, // Add new function to context
    checkSuperadminAccess,
  };

  return (
    <AuthContext.Provider value={value}>
      {children}
    </AuthContext.Provider>
  );
};
