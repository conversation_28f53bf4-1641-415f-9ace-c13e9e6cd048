import { VideoHistoryItem } from "@/services/videoService";
import { CompletedVideo } from "@/hooks/useVideoGeneration";

/**
 * Create a mock VideoHistoryItem from completed video data
 * Used when we need to display video details but don't have full history data
 */
export const createMockVideoItem = (
  completedVideo: CompletedVideo,
  userId: string
): VideoHistoryItem => {
  return {
    id: completedVideo.videoId,
    created_at: new Date().toISOString(),
    user_id: userId,
    job_id: completedVideo.jobId,
    prompt: 'Generated video', // We don't have the original prompt here
    video_type: 'text-to-video',
    status: 'completed',
    duration_seconds: 6,
    s3_key: '',
    s3_url: completedVideo.videoUrl,
    presigned_url: completedVideo.videoUrl,
    parameters: {
      taskType: 'TEXT_VIDEO',
      options: {},
    },
    reference_images: [],
    estimated_completion_at: '',
    completed_at: new Date().toISOString(),
  };
};

/**
 * Handle video download with proper cleanup
 */
export const downloadVideo = (videoUrl: string, filename?: string): void => {
  const link = document.createElement('a');
  link.href = videoUrl;
  link.download = filename || `video-${Date.now()}.mp4`;
  link.style.display = 'none';
  
  try {
    document.body.appendChild(link);
    link.click();
  } finally {
    // Ensure cleanup even if click fails
    if (document.body.contains(link)) {
      document.body.removeChild(link);
    }
  }
};

/**
 * Invalidate video-related React Query caches
 * Centralized function to ensure consistent cache invalidation
 */
export const invalidateVideoQueries = async (
  queryClient: any,
  userId?: string,
  delay: number = 1000
): Promise<void> => {
  // Use setTimeout to ensure database transaction is committed
  return new Promise((resolve) => {
    setTimeout(async () => {
      try {
        await queryClient.invalidateQueries({ queryKey: ['videoHistory'] });
        if (userId) {
          await queryClient.invalidateQueries({ queryKey: ['videoHistory', userId] });
        }
        // Force refetch to ensure immediate update
        await queryClient.refetchQueries({ queryKey: ['videoHistory'] });
        if (userId) {
          await queryClient.refetchQueries({ queryKey: ['videoHistory', userId] });
        }
        resolve();
      } catch (error) {
        // Silently handle query invalidation errors to prevent UI from getting stuck
        if (import.meta.env.DEV) {
          console.warn("Query invalidation failed:", error);
        }
        resolve();
      }
    }, delay);
  });
};

/**
 * Format video duration for display
 */
export const formatVideoDuration = (seconds: number): string => {
  if (seconds < 60) {
    return `${seconds}s`;
  }
  const minutes = Math.floor(seconds / 60);
  const remainingSeconds = seconds % 60;
  return remainingSeconds > 0 ? `${minutes}m ${remainingSeconds}s` : `${minutes}m`;
};

/**
 * Get video file extension from URL
 */
export const getVideoFileExtension = (url: string): string => {
  const urlParts = url.split('.');
  const extension = urlParts[urlParts.length - 1]?.split('?')[0];
  return extension || 'mp4';
};