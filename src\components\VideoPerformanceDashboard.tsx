import React, { useState, useEffect } from 'react';
import { <PERSON>, Card<PERSON>ontent, Card<PERSON><PERSON>er, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Progress } from '@/components/ui/progress';
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON>List, TabsTrigger } from '@/components/ui/tabs';
import { 
  BarChart3, 
  TrendingUp, 
  Zap, 
  Database, 
  Clock, 
  Wifi, 
  Eye,
  Download,
  RefreshCw,
  AlertTriangle,
  CheckCircle,
  Activity,
  Monitor,
  Smartphone,
  Laptop
} from 'lucide-react';

interface PerformanceMetrics {
  // Loading Performance
  totalVideos: number;
  loadedVideos: number;
  errorVideos: number;
  averageLoadTime: number;
  
  // Lazy Loading
  videosInView: number;
  videosLazilyLoaded: number;
  lazyLoadingSavings: number;
  
  // Caching Performance
  cacheHitRate: number;
  progressiveEnhancements: number;
  retryAttempts: number;
  
  // Network & Device
  connectionType?: string;
  deviceType?: 'mobile' | 'tablet' | 'desktop';
  deviceMemory?: number;
  
  // Time-based metrics
  loadStartTime: number;
  loadEndTime?: number;
  isComplete: boolean;
}

interface VideoPerformanceDashboardProps {
  metrics: PerformanceMetrics;
  className?: string;
  showRecommendations?: boolean;
  onOptimizationApply?: (optimization: string) => void;
}

/**
 * Video Performance Dashboard - Sprint 18 Phase 4
 * 
 * Comprehensive performance monitoring dashboard that provides real-time insights,
 * analytics, and optimization recommendations for video loading performance.
 * 
 * Features:
 * - Real-time performance metrics visualization
 * - Device and network condition analysis
 * - Performance insights and recommendations
 * - Historical performance tracking
 * - Optimization suggestions with one-click apply
 * - Export capabilities for performance reports
 */
const VideoPerformanceDashboard: React.FC<VideoPerformanceDashboardProps> = ({
  metrics,
  className,
  showRecommendations = true,
  onOptimizationApply,
}) => {
  const [selectedTab, setSelectedTab] = useState('overview');
  const [performanceGrade, setPerformanceGrade] = useState<'A' | 'B' | 'C' | 'D' | 'F'>('C');
  const [recommendations, setRecommendations] = useState<Array<{
    id: string;
    title: string;
    description: string;
    impact: 'high' | 'medium' | 'low';
    effort: 'low' | 'medium' | 'high';
    category: 'caching' | 'lazy-loading' | 'network' | 'device';
  }>>([]);

  // Don't render in production
  if (!import.meta.env.DEV) {
    return null;
  }

  // Calculate performance metrics
  const completionRate = metrics.totalVideos > 0 ? ((metrics.loadedVideos + metrics.errorVideos) / metrics.totalVideos) * 100 : 0;
  const successRate = (metrics.loadedVideos + metrics.errorVideos) > 0 ? (metrics.loadedVideos / (metrics.loadedVideos + metrics.errorVideos)) * 100 : 0;
  const lazyLoadingRate = metrics.totalVideos > 0 ? (metrics.videosLazilyLoaded / metrics.totalVideos) * 100 : 0;
  const totalLoadTime = metrics.loadEndTime ? metrics.loadEndTime - metrics.loadStartTime : Date.now() - metrics.loadStartTime;

  // Calculate performance grade
  useEffect(() => {
    let score = 0;
    
    // Success rate (30%)
    if (successRate >= 95) score += 30;
    else if (successRate >= 90) score += 25;
    else if (successRate >= 80) score += 20;
    else if (successRate >= 70) score += 15;
    else score += 10;
    
    // Cache hit rate (25%)
    if (metrics.cacheHitRate >= 80) score += 25;
    else if (metrics.cacheHitRate >= 70) score += 20;
    else if (metrics.cacheHitRate >= 60) score += 15;
    else if (metrics.cacheHitRate >= 50) score += 10;
    else score += 5;
    
    // Average load time (25%)
    if (metrics.averageLoadTime <= 500) score += 25;
    else if (metrics.averageLoadTime <= 1000) score += 20;
    else if (metrics.averageLoadTime <= 2000) score += 15;
    else if (metrics.averageLoadTime <= 3000) score += 10;
    else score += 5;
    
    // Lazy loading efficiency (20%)
    if (lazyLoadingRate >= 80) score += 20;
    else if (lazyLoadingRate >= 60) score += 15;
    else if (lazyLoadingRate >= 40) score += 10;
    else if (lazyLoadingRate >= 20) score += 5;
    else score += 0;
    
    if (score >= 90) setPerformanceGrade('A');
    else if (score >= 80) setPerformanceGrade('B');
    else if (score >= 70) setPerformanceGrade('C');
    else if (score >= 60) setPerformanceGrade('D');
    else setPerformanceGrade('F');
  }, [successRate, metrics.cacheHitRate, metrics.averageLoadTime, lazyLoadingRate]);

  // Generate recommendations
  useEffect(() => {
    const newRecommendations = [];

    if (metrics.cacheHitRate < 70) {
      newRecommendations.push({
        id: 'improve-caching',
        title: 'Improve Cache Hit Rate',
        description: 'Cache hit rate is below optimal. Consider increasing cache TTL or implementing smarter cache warming.',
        impact: 'high' as const,
        effort: 'medium' as const,
        category: 'caching' as const,
      });
    }

    if (lazyLoadingRate < 60) {
      newRecommendations.push({
        id: 'optimize-lazy-loading',
        title: 'Optimize Lazy Loading',
        description: 'Many videos are loading immediately. Adjust root margins and thresholds for better lazy loading.',
        impact: 'medium' as const,
        effort: 'low' as const,
        category: 'lazy-loading' as const,
      });
    }

    if (metrics.averageLoadTime > 2000) {
      newRecommendations.push({
        id: 'reduce-load-time',
        title: 'Reduce Load Times',
        description: 'Average load time is high. Consider video compression or CDN optimization.',
        impact: 'high' as const,
        effort: 'high' as const,
        category: 'network' as const,
      });
    }

    if (metrics.retryAttempts > metrics.totalVideos * 0.1) {
      newRecommendations.push({
        id: 'reduce-retries',
        title: 'Reduce Retry Attempts',
        description: 'High retry rate indicates network or server issues. Investigate error patterns.',
        impact: 'medium' as const,
        effort: 'medium' as const,
        category: 'network' as const,
      });
    }

    setRecommendations(newRecommendations);
  }, [metrics, lazyLoadingRate]);

  const formatTime = (ms: number) => {
    if (ms < 1000) return `${ms}ms`;
    return `${(ms / 1000).toFixed(1)}s`;
  };

  const formatBytes = (bytes: number) => {
    if (bytes === 0) return '0 B';
    const k = 1024;
    const sizes = ['B', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(1)) + ' ' + sizes[i];
  };

  const formatPercentage = (value: number) => `${value.toFixed(1)}%`;

  const getGradeColor = (grade: string) => {
    switch (grade) {
      case 'A': return 'text-green-600 border-green-600';
      case 'B': return 'text-blue-600 border-blue-600';
      case 'C': return 'text-yellow-600 border-yellow-600';
      case 'D': return 'text-orange-600 border-orange-600';
      case 'F': return 'text-red-600 border-red-600';
      default: return 'text-gray-600 border-gray-600';
    }
  };

  const getDeviceIcon = () => {
    switch (metrics.deviceType) {
      case 'mobile': return <Smartphone className="h-4 w-4" />;
      case 'tablet': return <Monitor className="h-4 w-4" />;
      case 'desktop': return <Laptop className="h-4 w-4" />;
      default: return <Monitor className="h-4 w-4" />;
    }
  };

  return (
    <div className={`space-y-6 ${className}`}>
      {/* Header with Performance Grade */}
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-3">
          <BarChart3 className="h-6 w-6 text-blue-500" />
          <h2 className="text-xl font-semibold">Video Performance Dashboard</h2>
          <Badge variant="outline" className="text-blue-600 border-blue-600">
            Development
          </Badge>
        </div>
        <div className="flex items-center gap-3">
          <span className="text-sm text-muted-foreground">Performance Grade:</span>
          <Badge variant="outline" className={`text-lg font-bold ${getGradeColor(performanceGrade)}`}>
            {performanceGrade}
          </Badge>
        </div>
      </div>

      <Tabs value={selectedTab} onValueChange={setSelectedTab}>
        <TabsList className="grid w-full grid-cols-4">
          <TabsTrigger value="overview">Overview</TabsTrigger>
          <TabsTrigger value="performance">Performance</TabsTrigger>
          <TabsTrigger value="insights">Insights</TabsTrigger>
          <TabsTrigger value="recommendations">Recommendations</TabsTrigger>
        </TabsList>

        {/* Overview Tab */}
        <TabsContent value="overview" className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
            {/* Loading Progress */}
            <Card>
              <CardHeader className="pb-2">
                <CardTitle className="text-sm flex items-center gap-2">
                  <Activity className="h-4 w-4 text-blue-500" />
                  Loading Progress
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-2">
                  <div className="flex justify-between text-sm">
                    <span>Completed</span>
                    <span>{metrics.loadedVideos + metrics.errorVideos}/{metrics.totalVideos}</span>
                  </div>
                  <Progress value={completionRate} className="h-2" />
                  <div className="text-xs text-muted-foreground">
                    {formatPercentage(completionRate)} complete
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Success Rate */}
            <Card>
              <CardHeader className="pb-2">
                <CardTitle className="text-sm flex items-center gap-2">
                  <CheckCircle className="h-4 w-4 text-green-500" />
                  Success Rate
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold text-green-600">
                  {formatPercentage(successRate)}
                </div>
                <div className="text-xs text-muted-foreground">
                  {metrics.loadedVideos} successful, {metrics.errorVideos} errors
                </div>
              </CardContent>
            </Card>

            {/* Cache Performance */}
            <Card>
              <CardHeader className="pb-2">
                <CardTitle className="text-sm flex items-center gap-2">
                  <Database className="h-4 w-4 text-purple-500" />
                  Cache Hit Rate
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold text-purple-600">
                  {formatPercentage(metrics.cacheHitRate)}
                </div>
                <div className="text-xs text-muted-foreground">
                  {metrics.progressiveEnhancements} enhancements
                </div>
              </CardContent>
            </Card>

            {/* Bandwidth Savings */}
            <Card>
              <CardHeader className="pb-2">
                <CardTitle className="text-sm flex items-center gap-2">
                  <Download className="h-4 w-4 text-orange-500" />
                  Bandwidth Saved
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold text-orange-600">
                  {formatBytes(metrics.lazyLoadingSavings)}
                </div>
                <div className="text-xs text-muted-foreground">
                  {formatPercentage(lazyLoadingRate)} lazy loaded
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Device & Network Info */}
          <Card>
            <CardHeader className="pb-3">
              <CardTitle className="text-sm">Environment Information</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
                <div className="flex items-center gap-2">
                  {getDeviceIcon()}
                  <span>Device: {metrics.deviceType || 'Unknown'}</span>
                </div>
                <div className="flex items-center gap-2">
                  <Wifi className="h-4 w-4" />
                  <span>Network: {metrics.connectionType || 'Unknown'}</span>
                </div>
                <div className="flex items-center gap-2">
                  <Monitor className="h-4 w-4" />
                  <span>Memory: {metrics.deviceMemory || 'Unknown'}GB</span>
                </div>
                <div className="flex items-center gap-2">
                  <Clock className="h-4 w-4" />
                  <span>Total Time: {formatTime(totalLoadTime)}</span>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        {/* Performance Tab */}
        <TabsContent value="performance" className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            {/* Load Time Distribution */}
            <Card>
              <CardHeader className="pb-3">
                <CardTitle className="text-sm">Load Time Performance</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  <div className="flex justify-between">
                    <span className="text-sm">Average Load Time</span>
                    <span className="font-medium">{formatTime(metrics.averageLoadTime)}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-sm">Videos in Viewport</span>
                    <span className="font-medium">{metrics.videosInView}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-sm">Retry Attempts</span>
                    <span className="font-medium">{metrics.retryAttempts}</span>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Lazy Loading Efficiency */}
            <Card>
              <CardHeader className="pb-3">
                <CardTitle className="text-sm">Lazy Loading Efficiency</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  <div className="flex justify-between">
                    <span className="text-sm">Lazy Loaded</span>
                    <span className="font-medium">{metrics.videosLazilyLoaded}/{metrics.totalVideos}</span>
                  </div>
                  <Progress value={lazyLoadingRate} className="h-2" />
                  <div className="text-xs text-muted-foreground">
                    {formatPercentage(lazyLoadingRate)} efficiency
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        {/* Insights Tab */}
        <TabsContent value="insights" className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <Card>
              <CardHeader className="pb-3">
                <CardTitle className="text-sm flex items-center gap-2">
                  <TrendingUp className="h-4 w-4 text-green-500" />
                  Performance Insights
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-2">
                {metrics.cacheHitRate > 80 && (
                  <div className="flex items-center gap-2 text-green-600 text-sm">
                    <CheckCircle className="h-4 w-4" />
                    Excellent cache performance
                  </div>
                )}
                {successRate > 95 && (
                  <div className="flex items-center gap-2 text-green-600 text-sm">
                    <CheckCircle className="h-4 w-4" />
                    High success rate
                  </div>
                )}
                {metrics.averageLoadTime < 1000 && (
                  <div className="flex items-center gap-2 text-green-600 text-sm">
                    <CheckCircle className="h-4 w-4" />
                    Fast loading times
                  </div>
                )}
                {lazyLoadingRate > 70 && (
                  <div className="flex items-center gap-2 text-green-600 text-sm">
                    <CheckCircle className="h-4 w-4" />
                    Effective lazy loading
                  </div>
                )}
                {metrics.lazyLoadingSavings > 10 * 1024 * 1024 && (
                  <div className="flex items-center gap-2 text-green-600 text-sm">
                    <CheckCircle className="h-4 w-4" />
                    Significant bandwidth savings
                  </div>
                )}
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="pb-3">
                <CardTitle className="text-sm flex items-center gap-2">
                  <AlertTriangle className="h-4 w-4 text-yellow-500" />
                  Areas for Improvement
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-2">
                {metrics.cacheHitRate < 70 && (
                  <div className="flex items-center gap-2 text-yellow-600 text-sm">
                    <AlertTriangle className="h-4 w-4" />
                    Cache hit rate could be improved
                  </div>
                )}
                {metrics.retryAttempts > metrics.totalVideos * 0.1 && (
                  <div className="flex items-center gap-2 text-yellow-600 text-sm">
                    <AlertTriangle className="h-4 w-4" />
                    High retry rate detected
                  </div>
                )}
                {metrics.averageLoadTime > 2000 && (
                  <div className="flex items-center gap-2 text-yellow-600 text-sm">
                    <AlertTriangle className="h-4 w-4" />
                    Load times could be faster
                  </div>
                )}
                {lazyLoadingRate < 50 && (
                  <div className="flex items-center gap-2 text-yellow-600 text-sm">
                    <AlertTriangle className="h-4 w-4" />
                    Lazy loading efficiency is low
                  </div>
                )}
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        {/* Recommendations Tab */}
        <TabsContent value="recommendations" className="space-y-4">
          {showRecommendations && recommendations.length > 0 ? (
            <div className="space-y-3">
              {recommendations.map((rec) => (
                <Card key={rec.id}>
                  <CardHeader className="pb-3">
                    <div className="flex items-center justify-between">
                      <CardTitle className="text-sm">{rec.title}</CardTitle>
                      <div className="flex items-center gap-2">
                        <Badge variant="outline" className={
                          rec.impact === 'high' ? 'text-red-600 border-red-600' :
                          rec.impact === 'medium' ? 'text-yellow-600 border-yellow-600' :
                          'text-green-600 border-green-600'
                        }>
                          {rec.impact} impact
                        </Badge>
                        <Badge variant="outline" className={
                          rec.effort === 'high' ? 'text-red-600 border-red-600' :
                          rec.effort === 'medium' ? 'text-yellow-600 border-yellow-600' :
                          'text-green-600 border-green-600'
                        }>
                          {rec.effort} effort
                        </Badge>
                      </div>
                    </div>
                  </CardHeader>
                  <CardContent>
                    <p className="text-sm text-muted-foreground mb-3">{rec.description}</p>
                    {onOptimizationApply && (
                      <Button
                        size="sm"
                        variant="outline"
                        onClick={() => onOptimizationApply(rec.id)}
                        className="text-xs"
                      >
                        <Zap className="h-3 w-3 mr-1" />
                        Apply Optimization
                      </Button>
                    )}
                  </CardContent>
                </Card>
              ))}
            </div>
          ) : (
            <Card>
              <CardContent className="pt-6">
                <div className="text-center text-muted-foreground">
                  <CheckCircle className="h-8 w-8 mx-auto mb-2 text-green-500" />
                  <p>No recommendations at this time.</p>
                  <p className="text-sm">Your video loading performance is optimal!</p>
                </div>
              </CardContent>
            </Card>
          )}
        </TabsContent>
      </Tabs>
    </div>
  );
};

export default VideoPerformanceDashboard;
