import React from 'react';
import { Card, CardContent } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from '@/components/ui/tooltip';
import { Download, Copy, Maximize2 } from 'lucide-react';

interface ImagePreviewProps {
  generatedImage: string;
  warning: string | null;
  isFullscreen: boolean;
  onDownload: () => void;
  onCopy: () => void;
  onToggleFullscreen: () => void;
}

export const ImagePreview: React.FC<ImagePreviewProps> = ({
  generatedImage,
  warning,
  isFullscreen,
  onDownload,
  onCopy,
  onToggleFullscreen,
}) => {
  return (
    <Card className={`w-full max-w-2xl shadow-2xl rounded-xl overflow-hidden ${isFullscreen ? 'fixed inset-0 z-50 w-screen h-screen max-w-none rounded-none flex items-center justify-center bg-black/80 backdrop-blur-sm' : ''}`}>
      <CardContent className={`p-0 relative ${isFullscreen ? 'w-auto h-auto max-w-[90vw] max-h-[90vh]' : 'aspect-[auto_1024/1024]'}`}>
        <img
          src={generatedImage}
          alt="Generated Art"
          className={`object-contain ${isFullscreen ? 'max-w-full max-h-full w-auto h-auto' : 'w-full h-full'}`}
        />
        <div className={`absolute top-3 right-3 flex gap-2 ${isFullscreen ? 'p-2 bg-black/50 rounded-lg' : ''}`}>
          <TooltipProvider>
            <Tooltip>
              <TooltipTrigger asChild>
                <Button 
                  variant="ghost" 
                  size="icon" 
                  onClick={onToggleFullscreen} 
                  className="text-white bg-black/30 hover:bg-black/50 hover:text-white"
                >
                  <Maximize2 size={18} />
                </Button>
              </TooltipTrigger>
              <TooltipContent>{isFullscreen ? 'Exit Fullscreen' : 'Fullscreen'}</TooltipContent>
            </Tooltip>
            <Tooltip>
              <TooltipTrigger asChild>
                <Button 
                  variant="ghost" 
                  size="icon" 
                  onClick={onDownload} 
                  className="text-white bg-black/30 hover:bg-black/50 hover:text-white"
                >
                  <Download size={18} />
                </Button>
              </TooltipTrigger>
              <TooltipContent>Download Image</TooltipContent>
            </Tooltip>
            <Tooltip>
              <TooltipTrigger asChild>
                <Button 
                  variant="ghost" 
                  size="icon" 
                  onClick={onCopy} 
                  className="text-white bg-black/30 hover:bg-black/50 hover:text-white"
                >
                  <Copy size={18} />
                </Button>
              </TooltipTrigger>
              <TooltipContent>Copy Image</TooltipContent>
            </Tooltip>
          </TooltipProvider>
        </div>
        {warning && (
          <div className="absolute bottom-0 left-0 right-0 bg-yellow-500/90 text-yellow-900 p-2 text-xs text-center">
            <strong>Warning:</strong> {warning}
          </div>
        )}
      </CardContent>
    </Card>
  );
};