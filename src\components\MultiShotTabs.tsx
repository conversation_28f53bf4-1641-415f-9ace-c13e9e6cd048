import React from "react";
import { But<PERSON> } from "@/components/ui/button";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from "@/components/ui/tabs";
import VideoStatusTracker from "@/components/VideoStatusTracker";
import VideoTemplateSelector from "@/components/VideoTemplateSelector";
import AutomatedMultiShotForm from "@/components/AutomatedMultiShotForm";
import ManualStoryboardBuilder from "@/components/ManualStoryboardBuilder";
import MultiShotResult from "@/components/MultiShotResult";
import { VideoShot } from "@/services/videoService";
import { Wand2, Layers, Play } from "lucide-react";
import { TAB_VALUES, UI_TEXT, GENERATION_METHODS } from "@/constants/multiShotVideo";

interface ActiveJob {
  jobId: string;
  videoId: string;
  estimatedCompletionTime: string;
}

interface CompletedVideo {
  videoUrl: string;
  videoId: string;
  jobId: string;
}

interface MultiShotTabsProps {
  activeTab: string;
  onTabChange: (tab: string) => void;
  generationMethod: "automated" | "manual";
  onGenerationMethodChange: (method: "automated" | "manual") => void;
  activeJobs: ActiveJob[];
  completedVideos: CompletedVideo[];
  isGenerating: boolean;
  userId: string;
  onVideoComplete: (videoUrl: string, videoId: string, jobId: string) => void;
  onVideoError: (error: string, jobId: string) => void;
  onAutomatedGeneration: (prompt: string, durationSeconds: number, seed?: number) => void;
  onTemplateSelect: (template: any) => void;
  onShotGeneration: (shots: VideoShot[]) => void;
  onVideoDownload: (videoUrl: string, filename?: string) => void;
  onGenerateNew: () => void;
  onViewVideo: () => void;
}

const MultiShotTabs: React.FC<MultiShotTabsProps> = ({
  activeTab,
  onTabChange,
  generationMethod,
  onGenerationMethodChange,
  activeJobs,
  completedVideos,
  isGenerating,
  userId,
  onVideoComplete,
  onVideoError,
  onAutomatedGeneration,
  onTemplateSelect,
  onShotGeneration,
  onVideoDownload,
  onGenerateNew,
  onViewVideo,
}) => {
  return (
    <Tabs value={activeTab} onValueChange={onTabChange}>
      <TabsList className="grid grid-cols-3 w-full max-w-lg mx-auto mb-6">
        <TabsTrigger value={TAB_VALUES.GENERATE}>
          Generate Video
        </TabsTrigger>
        <TabsTrigger value={TAB_VALUES.TEMPLATES}>
          Templates
        </TabsTrigger>
        <TabsTrigger value={TAB_VALUES.RESULT} disabled={activeJobs.length === 0 && completedVideos.length === 0}>
          Result
        </TabsTrigger>
      </TabsList>

      <TabsContent value={TAB_VALUES.GENERATE} className="m-0">
        {/* Video Generation Options */}
        <div className="max-w-4xl mx-auto">
          {activeJobs.length > 0 ? (
            // Active Jobs Display (VideoStatusTracker)
            <div className="mt-6 bg-white dark:bg-gray-800 rounded-lg p-6 shadow-sm">
              <h2 className="text-xl font-semibold text-center mb-4">Generating Video...</h2>
              <div className="space-y-4">
                {activeJobs.map((job) => (
                  <VideoStatusTracker
                    key={job.jobId}
                    jobId={job.jobId}
                    userId={userId}
                    estimatedCompletionTime={job.estimatedCompletionTime}
                    onComplete={(videoUrl, videoId) => onVideoComplete(videoUrl, videoId, job.jobId)}
                    onError={(error) => onVideoError(error, job.jobId)}
                  />
                ))}
              </div>
            </div>
          ) : (
            // Form section (Choose Generation Method and Forms)
            <div className="bg-white dark:bg-gray-800 rounded-lg p-6 shadow-sm">
              <h3 className="text-lg font-medium mb-4 text-center">Choose Generation Method</h3>

              {/* Horizontal buttons */}
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6">
                <Button
                  variant={generationMethod === GENERATION_METHODS.AUTOMATED ? "default" : "outline"}
                  className="h-20 flex flex-col gap-2"
                  onClick={() => onGenerationMethodChange(GENERATION_METHODS.AUTOMATED)}
                >
                  <Wand2 size={20} className="text-blue-600" />
                  <div className="text-center">
                    <div className="font-medium">{UI_TEXT.AUTOMATED_TITLE}</div>
                    <div className="text-xs text-gray-500">{UI_TEXT.AUTOMATED_SUBTITLE}</div>
                  </div>
                </Button>

                <Button
                  variant={generationMethod === GENERATION_METHODS.MANUAL ? "default" : "outline"}
                  className="h-20 flex flex-col gap-2"
                  onClick={() => onGenerationMethodChange(GENERATION_METHODS.MANUAL)}
                >
                  <Layers size={20} className="text-green-600" />
                  <div className="text-center">
                    <div className="font-medium">{UI_TEXT.MANUAL_TITLE}</div>
                    <div className="text-xs text-gray-500">{UI_TEXT.MANUAL_SUBTITLE}</div>
                  </div>
                </Button>
              </div>

              {/* Automated Multi-Shot Form */}
              {generationMethod === GENERATION_METHODS.AUTOMATED && (
                <AutomatedMultiShotForm
                  onGenerate={onAutomatedGeneration}
                  isGenerating={isGenerating}
                />
              )}

              {/* Manual Storyboard Builder */}
              {generationMethod === GENERATION_METHODS.MANUAL && (
                <ManualStoryboardBuilder
                  onGenerate={onShotGeneration}
                  isGenerating={isGenerating}
                />
              )}
            </div>
          )}

          {/* Completed Videos Preview */}
          {completedVideos.length > 0 && activeJobs.length === 0 && (
            <div className="mt-6 bg-white dark:bg-gray-800 rounded-lg p-6 shadow-sm">
              <div className="flex items-center justify-between mb-4">
                <h2 className="text-xl font-semibold">{UI_TEXT.LATEST_VIDEO_READY}</h2>
                <Button
                  size="sm"
                  className="bg-brand-purple hover:bg-brand-purple/90"
                  onClick={onViewVideo}
                >
                  <Play size={16} className="mr-2" />
                  {UI_TEXT.VIEW_VIDEO}
                </Button>
              </div>
              <p className="text-gray-600 dark:text-gray-400 text-center">
                {UI_TEXT.VIDEO_READY_MESSAGE}
              </p>
            </div>
          )}
        </div>
      </TabsContent>

      <TabsContent value={TAB_VALUES.TEMPLATES} className="m-0">
        <div className="max-w-4xl mx-auto">
          <div className="bg-white dark:bg-gray-800 rounded-lg p-6 shadow-sm">
            <h3 className="text-lg font-medium mb-4 text-center">Video Templates</h3>
            <VideoTemplateSelector
              onTemplateSelect={onTemplateSelect}
              className="w-full"
            />
          </div>
        </div>
      </TabsContent>

      <TabsContent value={TAB_VALUES.RESULT} className="m-0">
        {completedVideos.length > 0 && (
          <MultiShotResult
            videoUrl={completedVideos[completedVideos.length - 1].videoUrl}
            videoId={completedVideos[completedVideos.length - 1].videoId}
            onDownload={onVideoDownload}
            onGenerateNew={onGenerateNew}
          />
        )}
      </TabsContent>
    </Tabs>
  );
};

export default MultiShotTabs;