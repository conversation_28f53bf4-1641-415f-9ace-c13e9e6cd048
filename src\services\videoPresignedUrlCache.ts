/**
 * Video Presigned URL Cache Service - Sprint 18 Phase 1
 * 
 * Specialized caching system for video presigned URLs with optimizations
 * for video-specific requirements like larger file sizes and longer processing times.
 * 
 * Based on the successful image presigned URL cache but adapted for video content.
 */

export interface CachedVideoUrl {
  url: string;
  timestamp: number;
  expiresAt: number;
  s3_key: string;
  retryCount: number;
  loadTime: number;
  videoType?: 'text-to-video' | 'multi-shot-auto' | 'multi-shot-manual';
  duration?: number; // Video duration in seconds
  fileSize?: number; // Video file size in bytes (if available)
}

export interface VideoCacheOptions {
  maxSize: number;
  defaultTtl: number; // Time to live in milliseconds
  cleanupInterval: number;
  enableMetrics: boolean;
  enablePersistence: boolean;
  maxVideoFileSize?: number; // Maximum video file size to cache (in bytes)
}

export interface VideoCacheMetrics {
  hits: number;
  misses: number;
  totalRequests: number;
  evictions: number;
  cacheSize: number;
  hitRate: number;
  totalVideoSize: number; // Total size of cached videos
  averageVideoSize: number;
  lastCleanup: number;
}

class VideoPresignedUrlCache {
  private cache: Map<string, CachedVideoUrl> = new Map();
  private metrics: VideoCacheMetrics = {
    hits: 0,
    misses: 0,
    totalRequests: 0,
    evictions: 0,
    cacheSize: 0,
    hitRate: 0,
    totalVideoSize: 0,
    averageVideoSize: 0,
    lastCleanup: Date.now(),
  };
  private cleanupTimer: NodeJS.Timeout | null = null;
  private options: VideoCacheOptions;

  constructor(options: Partial<VideoCacheOptions> = {}) {
    this.options = {
      maxSize: 200, // Smaller than image cache due to larger video files
      defaultTtl: 45 * 60 * 1000, // 45 minutes (same as images)
      cleanupInterval: 5 * 60 * 1000, // 5 minutes
      enableMetrics: import.meta.env.DEV,
      enablePersistence: true,
      maxVideoFileSize: 100 * 1024 * 1024, // 100MB max per video
      ...options,
    };

    this.startCleanupTimer();
    this.loadFromSessionStorage();

    if (this.options.enableMetrics) {
      console.log('[VideoPresignedUrlCache] Initialized with options:', this.options);
    }
  }

  /**
   * Get a cached video URL if it exists and hasn't expired
   */
  get(s3_key: string): CachedVideoUrl | null {
    this.metrics.totalRequests++;

    const cached = this.cache.get(s3_key);
    
    if (!cached) {
      this.metrics.misses++;
      return null;
    }

    // Check if expired
    if (Date.now() > cached.expiresAt) {
      this.cache.delete(s3_key);
      this.metrics.misses++;
      this.metrics.evictions++;
      this.updateMetrics();
      return null;
    }

    this.metrics.hits++;
    this.updateMetrics();

    if (this.options.enableMetrics) {
      console.log(`[VideoPresignedUrlCache] Cache HIT for ${s3_key} (age: ${Date.now() - cached.timestamp}ms, type: ${cached.videoType})`);
    }

    return cached;
  }

  /**
   * Set a video URL in the cache with optional TTL and video metadata
   */
  set(
    s3_key: string, 
    url: string, 
    ttl?: number, 
    metadata?: Partial<Pick<CachedVideoUrl, 'retryCount' | 'loadTime' | 'videoType' | 'duration' | 'fileSize'>>
  ): void {
    const now = Date.now();
    const expiresAt = now + (ttl ?? this.options.defaultTtl);

    // Check if video file size exceeds limit
    if (metadata?.fileSize && metadata.fileSize > (this.options.maxVideoFileSize || Infinity)) {
      if (this.options.enableMetrics) {
        console.warn(`[VideoPresignedUrlCache] Video ${s3_key} exceeds size limit (${metadata.fileSize} bytes), not caching`);
      }
      return;
    }

    // Evict oldest entries if cache is full
    if (this.cache.size >= this.options.maxSize) {
      this.evictOldest();
    }

    const cachedVideoUrl: CachedVideoUrl = {
      url,
      timestamp: now,
      expiresAt,
      s3_key,
      retryCount: metadata?.retryCount ?? 0,
      loadTime: metadata?.loadTime ?? 0,
      videoType: metadata?.videoType,
      duration: metadata?.duration,
      fileSize: metadata?.fileSize,
    };

    this.cache.set(s3_key, cachedVideoUrl);
    this.updateMetrics();
    this.saveToSessionStorage();

    if (this.options.enableMetrics) {
      console.log(`[VideoPresignedUrlCache] Cached video URL for ${s3_key} (TTL: ${ttl ?? this.options.defaultTtl}ms, type: ${metadata?.videoType})`);
    }
  }

  /**
   * Remove a specific video URL from cache
   */
  delete(s3_key: string): boolean {
    const deleted = this.cache.delete(s3_key);
    if (deleted) {
      this.updateMetrics();
      this.saveToSessionStorage();
      
      if (this.options.enableMetrics) {
        console.log(`[VideoPresignedUrlCache] Deleted ${s3_key} from cache`);
      }
    }
    return deleted;
  }

  /**
   * Clear all cached video URLs
   */
  clear(): void {
    this.cache.clear();
    this.metrics = {
      hits: 0,
      misses: 0,
      totalRequests: 0,
      evictions: 0,
      cacheSize: 0,
      hitRate: 0,
      totalVideoSize: 0,
      averageVideoSize: 0,
      lastCleanup: Date.now(),
    };
    this.clearSessionStorage();

    if (this.options.enableMetrics) {
      console.log('[VideoPresignedUrlCache] Cache cleared');
    }
  }

  /**
   * Get current cache metrics
   */
  getMetrics(): VideoCacheMetrics {
    return { ...this.metrics };
  }

  /**
   * Invalidate cache entries for specific video types or patterns
   */
  invalidateByPattern(pattern: string | RegExp): number {
    let invalidated = 0;
    const regex = typeof pattern === 'string' ? new RegExp(pattern) : pattern;

    for (const [key] of this.cache) {
      if (regex.test(key)) {
        this.cache.delete(key);
        invalidated++;
      }
    }

    if (invalidated > 0) {
      this.updateMetrics();
      this.saveToSessionStorage();
      
      if (this.options.enableMetrics) {
        console.log(`[VideoPresignedUrlCache] Invalidated ${invalidated} entries matching pattern: ${pattern}`);
      }
    }

    return invalidated;
  }

  /**
   * Invalidate cache entries for a specific user
   */
  invalidateForUser(userId: string): number {
    return this.invalidateByPattern(`users/${userId}/videos/`);
  }

  /**
   * Get cache statistics by video type
   */
  getStatsByVideoType(): Record<string, { count: number; totalSize: number; averageSize: number }> {
    const stats: Record<string, { count: number; totalSize: number; averageSize: number }> = {};

    for (const cached of this.cache.values()) {
      const type = cached.videoType || 'unknown';
      if (!stats[type]) {
        stats[type] = { count: 0, totalSize: 0, averageSize: 0 };
      }
      
      stats[type].count++;
      if (cached.fileSize) {
        stats[type].totalSize += cached.fileSize;
      }
    }

    // Calculate averages
    for (const type in stats) {
      if (stats[type].count > 0) {
        stats[type].averageSize = stats[type].totalSize / stats[type].count;
      }
    }

    return stats;
  }

  /**
   * Evict the oldest cache entry
   */
  private evictOldest(): void {
    let oldestKey: string | null = null;
    let oldestTimestamp = Infinity;

    for (const [key, cached] of this.cache) {
      if (cached.timestamp < oldestTimestamp) {
        oldestTimestamp = cached.timestamp;
        oldestKey = key;
      }
    }

    if (oldestKey) {
      this.cache.delete(oldestKey);
      this.metrics.evictions++;
      
      if (this.options.enableMetrics) {
        console.log(`[VideoPresignedUrlCache] Evicted oldest entry: ${oldestKey}`);
      }
    }
  }

  /**
   * Update cache metrics
   */
  private updateMetrics(): void {
    this.metrics.cacheSize = this.cache.size;
    this.metrics.hitRate = this.metrics.totalRequests > 0 
      ? (this.metrics.hits / this.metrics.totalRequests) * 100 
      : 0;

    // Calculate total video size
    let totalSize = 0;
    let videoCount = 0;
    
    for (const cached of this.cache.values()) {
      if (cached.fileSize) {
        totalSize += cached.fileSize;
        videoCount++;
      }
    }
    
    this.metrics.totalVideoSize = totalSize;
    this.metrics.averageVideoSize = videoCount > 0 ? totalSize / videoCount : 0;
  }

  /**
   * Start the cleanup timer
   */
  private startCleanupTimer(): void {
    if (this.cleanupTimer) {
      clearInterval(this.cleanupTimer);
    }

    this.cleanupTimer = setInterval(() => {
      this.cleanup();
    }, this.options.cleanupInterval);
  }

  /**
   * Clean up expired entries
   */
  private cleanup(): void {
    const now = Date.now();
    let cleaned = 0;

    for (const [key, cached] of this.cache) {
      if (now > cached.expiresAt) {
        this.cache.delete(key);
        cleaned++;
      }
    }

    if (cleaned > 0) {
      this.metrics.evictions += cleaned;
      this.updateMetrics();
      this.saveToSessionStorage();
      
      if (this.options.enableMetrics) {
        console.log(`[VideoPresignedUrlCache] Cleaned up ${cleaned} expired entries`);
      }
    }

    this.metrics.lastCleanup = now;
  }

  /**
   * Save cache to sessionStorage for persistence
   */
  private saveToSessionStorage(): void {
    if (!this.options.enablePersistence || typeof window === 'undefined') return;

    try {
      const cacheData = {
        entries: Array.from(this.cache.entries()),
        metrics: this.metrics,
        timestamp: Date.now(),
      };
      
      sessionStorage.setItem('video-presigned-url-cache', JSON.stringify(cacheData));
    } catch (error) {
      if (this.options.enableMetrics) {
        console.warn('[VideoPresignedUrlCache] Failed to save to sessionStorage:', error);
      }
    }
  }

  /**
   * Load cache from sessionStorage
   */
  private loadFromSessionStorage(): void {
    if (!this.options.enablePersistence || typeof window === 'undefined') return;

    try {
      const stored = sessionStorage.getItem('video-presigned-url-cache');
      if (!stored) return;

      const cacheData = JSON.parse(stored);
      const now = Date.now();

      // Only load if data is less than 1 hour old
      if (now - cacheData.timestamp > 60 * 60 * 1000) {
        sessionStorage.removeItem('video-presigned-url-cache');
        return;
      }

      // Restore cache entries, filtering out expired ones
      for (const [key, cached] of cacheData.entries) {
        if (now < cached.expiresAt) {
          this.cache.set(key, cached);
        }
      }

      // Restore metrics
      if (cacheData.metrics) {
        this.metrics = { ...this.metrics, ...cacheData.metrics };
      }

      this.updateMetrics();

      if (this.options.enableMetrics) {
        console.log(`[VideoPresignedUrlCache] Loaded ${this.cache.size} entries from sessionStorage`);
      }
    } catch (error) {
      if (this.options.enableMetrics) {
        console.warn('[VideoPresignedUrlCache] Failed to load from sessionStorage:', error);
      }
      sessionStorage.removeItem('video-presigned-url-cache');
    }
  }

  /**
   * Clear sessionStorage
   */
  private clearSessionStorage(): void {
    if (!this.options.enablePersistence || typeof window === 'undefined') return;

    try {
      sessionStorage.removeItem('video-presigned-url-cache');
    } catch (error) {
      if (this.options.enableMetrics) {
        console.warn('[VideoPresignedUrlCache] Failed to clear sessionStorage:', error);
      }
    }
  }

  /**
   * Cleanup resources
   */
  destroy(): void {
    if (this.cleanupTimer) {
      clearInterval(this.cleanupTimer);
      this.cleanupTimer = null;
    }
    this.clear();
  }
}

// Global video cache instance
export const videoPresignedUrlCache = new VideoPresignedUrlCache({
  maxSize: 200, // Smaller than image cache due to larger video files
  defaultTtl: 45 * 60 * 1000, // 45 minutes
  cleanupInterval: 5 * 60 * 1000, // 5 minutes
  enableMetrics: import.meta.env.DEV,
  enablePersistence: true,
  maxVideoFileSize: 100 * 1024 * 1024, // 100MB max per video
});

export default VideoPresignedUrlCache;
