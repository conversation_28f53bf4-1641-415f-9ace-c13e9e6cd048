import React from 'react';
import { Skeleton } from '@/components/ui/skeleton';
import { Play } from 'lucide-react';
import { cn } from '@/lib/utils';

interface VideoSkeletonProps {
  className?: string;
  showPlayIcon?: boolean;
  aspectRatio?: 'video' | 'square' | 'wide';
}

/**
 * VideoSkeleton Component - Sprint 18 Phase 2
 * 
 * Enhanced skeleton placeholder for video loading states with video-specific styling
 * and play icon indicator. Provides immediate visual feedback while videos are loading.
 * 
 * Features:
 * - Video-specific aspect ratios
 * - Play icon indicator for video content
 * - Smooth loading animations
 * - Responsive design
 */
const VideoSkeleton: React.FC<VideoSkeletonProps> = ({
  className,
  showPlayIcon = true,
  aspectRatio = 'video',
}) => {
  const aspectRatioClasses = {
    video: 'aspect-video', // 16:9
    square: 'aspect-square', // 1:1
    wide: 'aspect-[21/9]', // Ultra-wide
  };

  return (
    <div className={cn(
      "relative overflow-hidden rounded-lg",
      aspectRatioClasses[aspectRatio],
      className
    )}>
      {/* Main skeleton background */}
      <Skeleton className="w-full h-full" />
      
      {/* Play icon overlay */}
      {showPlayIcon && (
        <div className="absolute inset-0 flex items-center justify-center">
          <div className="bg-black/20 rounded-full p-3">
            <Play className="h-8 w-8 text-white/60 fill-current" />
          </div>
        </div>
      )}
      
      {/* Shimmer effect for video content */}
      <div className="absolute inset-0 bg-gradient-to-r from-transparent via-white/10 to-transparent animate-pulse" />
      
      {/* Bottom gradient overlay (typical for video thumbnails) */}
      <div className="absolute bottom-0 left-0 right-0 h-1/3 bg-gradient-to-t from-black/30 to-transparent" />
      
      {/* Mock video controls area */}
      <div className="absolute bottom-2 left-2 right-2 flex items-center justify-between">
        <Skeleton className="h-3 w-20" />
        <Skeleton className="h-3 w-12" />
      </div>
    </div>
  );
};

export default VideoSkeleton;
