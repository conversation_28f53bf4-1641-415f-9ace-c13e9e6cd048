/**
 * Progressive Loading Service - Phase 4 Implementation
 * 
 * This service orchestrates the progressive loading strategy:
 * 1. Show images immediately with cached/fallback URLs
 * 2. Progressively enhance with fresh presigned URLs in background
 * 3. Track performance metrics and provide insights
 * 4. Handle error states and retry mechanisms
 */

import { presignedUrlCache } from './presignedUrlCache';
import { generatePresignedUrlsBatch, batchResultsToUrlMap } from './presignedUrlService';

export interface ProgressiveLoadingOptions {
  enableBackgroundEnhancement?: boolean;
  enhancementDelay?: number; // Delay before starting background enhancement
  batchSize?: number; // Number of URLs to enhance in each batch
  prioritizeVisible?: boolean; // Prioritize visible images for enhancement
  maxConcurrentEnhancements?: number;
}

export interface ProgressiveLoadingResult {
  immediateUrls: Record<string, string>; // URLs available immediately (cached/fallback)
  enhancementPromise: Promise<Record<string, string>>; // Promise for enhanced URLs
  metrics: {
    totalRequested: number;
    immediatelyAvailable: number;
    needsEnhancement: number;
    cacheHitRate: number;
  };
}

export interface ProgressiveLoadingMetrics {
  totalImages: number;
  immediatelyAvailable: number;
  enhancedInBackground: number;
  failedToEnhance: number;
  averageEnhancementTime: number;
  cacheHitRate: number;
  performanceScore: number;
}

class ProgressiveLoadingService {
  private enhancementQueue: Set<string> = new Set();
  private enhancementPromises: Map<string, Promise<string | null>> = new Map();
  private metrics: ProgressiveLoadingMetrics = {
    totalImages: 0,
    immediatelyAvailable: 0,
    enhancedInBackground: 0,
    failedToEnhance: 0,
    averageEnhancementTime: 0,
    cacheHitRate: 0,
    performanceScore: 0,
  };

  /**
   * Phase 4: Progressive loading strategy implementation
   * Returns immediate URLs and starts background enhancement
   */
  async loadImagesProgressively(
    imageItems: Array<{ s3_key: string; s3_url?: string }>,
    options: ProgressiveLoadingOptions = {}
  ): Promise<ProgressiveLoadingResult> {
    const {
      enableBackgroundEnhancement = true,
      enhancementDelay = 100,
      batchSize = 8,
      maxConcurrentEnhancements = 4,
    } = options;

    const s3_keys = imageItems.map(item => item.s3_key).filter(Boolean);
    const fallbackUrls: Record<string, string> = {};
    const immediateUrls: Record<string, string> = {};

    // Build fallback URL map
    imageItems.forEach(item => {
      if (item.s3_key && item.s3_url) {
        fallbackUrls[item.s3_key] = item.s3_url;
      }
    });

    let immediatelyAvailable = 0;
    let needsEnhancement = 0;

    // Phase 4: Check cache first for immediate availability
    s3_keys.forEach(s3_key => {
      const cached = presignedUrlCache.get(s3_key);
      if (cached && cached.url) {
        immediateUrls[s3_key] = cached.url;
        immediatelyAvailable++;
      } else {
        // Use fallback URL for immediate display
        immediateUrls[s3_key] = fallbackUrls[s3_key] || '/placeholder.svg';
        needsEnhancement++;
      }
    });

    // Update metrics
    this.updateMetrics({
      totalImages: s3_keys.length,
      immediatelyAvailable,
      needsEnhancement,
    });

    // Phase 4: Start background enhancement if enabled
    const enhancementPromise = enableBackgroundEnhancement
      ? this.enhanceInBackground(s3_keys, fallbackUrls, {
          delay: enhancementDelay,
          batchSize,
          maxConcurrent: maxConcurrentEnhancements,
        })
      : Promise.resolve({});

    const cacheHitRate = s3_keys.length > 0 ? (immediatelyAvailable / s3_keys.length) * 100 : 0;

    return {
      immediateUrls,
      enhancementPromise,
      metrics: {
        totalRequested: s3_keys.length,
        immediatelyAvailable,
        needsEnhancement,
        cacheHitRate,
      },
    };
  }

  /**
   * Enhance URLs in background without blocking UI
   */
  private async enhanceInBackground(
    s3_keys: string[],
    fallbackUrls: Record<string, string>,
    options: {
      delay: number;
      batchSize: number;
      maxConcurrent: number;
    }
  ): Promise<Record<string, string>> {
    const { delay, batchSize, maxConcurrent } = options;

    // Filter out keys that are already being enhanced or don't need enhancement
    const keysToEnhance = s3_keys.filter(s3_key => {
      const cached = presignedUrlCache.get(s3_key);
      return !cached && !this.enhancementQueue.has(s3_key);
    });

    if (keysToEnhance.length === 0) {
      return {};
    }

    // Add to enhancement queue
    keysToEnhance.forEach(s3_key => this.enhancementQueue.add(s3_key));

    // Delay enhancement to allow immediate display
    await new Promise(resolve => setTimeout(resolve, delay));

    try {
      if (import.meta.env.DEV) {
        console.log(`[ProgressiveLoading] Enhancing ${keysToEnhance.length} URLs in background`);
      }

      const startTime = performance.now();

      // Use batch processing with controlled concurrency
      const batchResult = await generatePresignedUrlsBatch(keysToEnhance, {
        timeout: 5000,
        maxRetries: 1, // Fewer retries for background enhancement
        retryDelay: 500,
        concurrencyLimit: maxConcurrent,
        fallbackUrls,
        enableMetrics: import.meta.env.DEV,
        useCache: true,
        cacheTtl: 45 * 60 * 1000,
        forceRefresh: false,
      });

      const enhancedUrls = batchResultsToUrlMap(batchResult.results);
      const enhancementTime = performance.now() - startTime;

      // Update metrics
      this.metrics.enhancedInBackground += batchResult.successCount;
      this.metrics.failedToEnhance += batchResult.failureCount;
      this.metrics.averageEnhancementTime = enhancementTime / keysToEnhance.length;

      // Remove from enhancement queue
      keysToEnhance.forEach(s3_key => this.enhancementQueue.delete(s3_key));

      if (import.meta.env.DEV) {
        console.log(`[ProgressiveLoading] Enhanced ${batchResult.successCount}/${keysToEnhance.length} URLs in ${enhancementTime.toFixed(0)}ms`);
      }

      return enhancedUrls;
    } catch (error) {
      if (import.meta.env.DEV) {
        console.error('[ProgressiveLoading] Background enhancement failed:', error);
      }

      // Remove from enhancement queue on error
      keysToEnhance.forEach(s3_key => this.enhancementQueue.delete(s3_key));
      
      return {};
    }
  }

  /**
   * Get current progressive loading metrics
   */
  getMetrics(): ProgressiveLoadingMetrics {
    const cacheMetrics = presignedUrlCache.getMetrics();
    const totalRequests = cacheMetrics.totalRequests || 1;
    const cacheHitRate = (cacheMetrics.hits / totalRequests) * 100;
    const performanceScore = Math.round(cacheHitRate);

    return {
      ...this.metrics,
      cacheHitRate,
      performanceScore,
      totalImages: totalRequests,
      immediatelyAvailable: cacheMetrics.hits,
    };
  }

  /**
   * Update internal metrics
   */
  private updateMetrics(update: Partial<ProgressiveLoadingMetrics>) {
    this.metrics = { ...this.metrics, ...update };
  }

  /**
   * Reset metrics (useful for testing or when starting fresh)
   */
  resetMetrics() {
    this.metrics = {
      totalImages: 0,
      immediatelyAvailable: 0,
      enhancedInBackground: 0,
      failedToEnhance: 0,
      averageEnhancementTime: 0,
      cacheHitRate: 0,
      performanceScore: 0,
    };
    this.enhancementQueue.clear();
    this.enhancementPromises.clear();
  }

  /**
   * Check if an image is currently being enhanced
   */
  isEnhancing(s3_key: string): boolean {
    return this.enhancementQueue.has(s3_key);
  }

  /**
   * Get enhancement queue status
   */
  getEnhancementStatus() {
    return {
      queueSize: this.enhancementQueue.size,
      activeEnhancements: Array.from(this.enhancementQueue),
    };
  }
}

// Export singleton instance
export const progressiveLoadingService = new ProgressiveLoadingService();
export default progressiveLoadingService;
