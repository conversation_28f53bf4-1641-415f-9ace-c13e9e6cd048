import { useState, useEffect, useCallback, useMemo } from 'react';
import { useAuth } from '@/contexts/AuthContext';
import { superadminService, SystemStats, UserData, ActivityLog, HealthStatus, UserSegments, PerformanceMetrics, AnalyticsData, SystemReport } from '@/services/superadminService';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { Users, BarChart3, Activity, Server, RefreshCw, Image, Video, Mic, Database, Zap, HardDrive, Search, Filter, TrendingUp, Crown, Star, Gauge, LineChart, FileText, AlertTriangle, CheckCircle, Clock, Cpu, MemoryStick } from 'lucide-react';
import { toast } from 'sonner';
import SuperadminLayout from '@/components/SuperadminLayout';

const Superadmin = () => {
  const { user } = useAuth();
  
  // Centralized date formatting utilities
  const formatDate = (dateString: string): string => {
    return superadminService.formatDate(dateString);
  };
  
  const formatRelativeTime = (dateString: string): string => {
    return superadminService.formatRelativeTime(dateString);
  };
  const [stats, setStats] = useState<SystemStats | null>(null);
  const [users, setUsers] = useState<UserData[]>([]);
  const [userSegments, setUserSegments] = useState<UserSegments | null>(null);
  const [activityLogs, setActivityLogs] = useState<ActivityLog[]>([]);
  const [healthStatus, setHealthStatus] = useState<HealthStatus | null>(null);
  const [performanceMetrics, setPerformanceMetrics] = useState<PerformanceMetrics | null>(null);
  const [analyticsData, setAnalyticsData] = useState<AnalyticsData | null>(null);
  const [systemReport, setSystemReport] = useState<SystemReport | null>(null);
  const [systemStats, setSystemStats] = useState<any>(null);
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);
  const [activeTab, setActiveTab] = useState('overview');
  
  // Analytics filters
  const [analyticsGranularity, setAnalyticsGranularity] = useState<'daily' | 'weekly' | 'monthly'>('daily');
  const [reportType, setReportType] = useState<'daily' | 'weekly' | 'monthly'>('daily');
  
  // User management filters
  const [userSearch, setUserSearch] = useState('');
  const [debouncedUserSearch, setDebouncedUserSearch] = useState('');
  const [userRole, setUserRole] = useState('all');
  const [activityFilter, setActivityFilter] = useState<'all' | 'active' | 'inactive'>('all');
  const [sortBy, setSortBy] = useState('created_at');
  const [sortOrder, setSortOrder] = useState<'asc' | 'desc'>('desc');
  const [currentPage, setCurrentPage] = useState(1);

  // Debounce search input to prevent excessive API calls
  useEffect(() => {
    const timer = setTimeout(() => {
      setDebouncedUserSearch(userSearch);
    }, 500);

    return () => clearTimeout(timer);
  }, [userSearch]);

  // Selective data fetching based on active tab
  const fetchTabData = useCallback(async (tabName: string, showRefreshToast = false) => {
    try {
      if (showRefreshToast) {
        setRefreshing(true);
      } else {
        setLoading(true);
      }

      const requests: Promise<any>[] = [];
      
      // Always fetch stats for overview cards
      requests.push(superadminService.getSystemStats());
      
      // Tab-specific data fetching
      switch (tabName) {
        case 'overview':
          requests.push(
            superadminService.getActivityLogs({ limit: 20 }),
            superadminService.getHealthStatus()
          );
          break;
        case 'users':
          requests.push(
            superadminService.getUsers({
              limit: 50,
              search: debouncedUserSearch,
              role: userRole === 'all' ? '' : userRole,
              activityFilter,
              sortBy,
              sortOrder,
              page: currentPage
            })
          );
          break;
        case 'content':
          // Content tab uses stats data which is already fetched
          // No additional requests needed
          break;
        case 'performance':
          requests.push(superadminService.getPerformanceMetrics());
          break;
        case 'analytics':
          requests.push(
            superadminService.getAnalytics({ granularity: analyticsGranularity }),
            superadminService.getReports({ type: reportType })
          );
          break;
        case 'system':
          requests.push(superadminService.getHealthStatus());
          break;
        default:
          // Fallback to loading all data for unknown tabs
          requests.push(
            superadminService.getUsers({
              limit: 50,
              search: debouncedUserSearch,
              role: userRole === 'all' ? '' : userRole,
              activityFilter,
              sortBy,
              sortOrder,
              page: currentPage
            }),
            superadminService.getActivityLogs({ limit: 20 }),
            superadminService.getHealthStatus(),
            superadminService.getPerformanceMetrics(),
            superadminService.getAnalytics({ granularity: analyticsGranularity }),
            superadminService.getReports({ type: reportType })
          );
      }

      const responses = await Promise.all(requests);
      let responseIndex = 0;

      // Process stats response (always first)
      const statsResponse = responses[responseIndex++];
      if (statsResponse.success && statsResponse.data) {
        setStats(statsResponse.data);
        setSystemStats(statsResponse.data.system); // Set system stats for System tab
      } else {
        toast.error(`Failed to load system stats: ${statsResponse.error}`);
      }

      // Process tab-specific responses
      switch (tabName) {
        case 'overview':
          const activityResponse = responses[responseIndex++];
          const healthResponse = responses[responseIndex++];
          
          if (activityResponse.success && activityResponse.data) {
            setActivityLogs(activityResponse.data);
          } else {
            toast.error(`Failed to load activity logs: ${activityResponse.error}`);
          }
          
          if (healthResponse.success && healthResponse.data) {
            setHealthStatus(healthResponse.data);
          } else {
            console.warn(`Failed to load health status: ${healthResponse.error}`);
          }
          break;
          
        case 'users':
          const usersResponse = responses[responseIndex++];
          if (usersResponse.success && usersResponse.data) {
            setUsers(usersResponse.data.users);
            setUserSegments(usersResponse.data.segments);
          } else {
            toast.error(`Failed to load users: ${usersResponse.error}`);
          }
          break;
          
        case 'performance':
          const performanceResponse = responses[responseIndex++];
          if (performanceResponse.success && performanceResponse.data) {
            setPerformanceMetrics(performanceResponse.data);
          } else {
            console.warn(`Failed to load performance metrics: ${performanceResponse.error}`);
          }
          break;
          
        case 'analytics':
          const analyticsResponse = responses[responseIndex++];
          const reportResponse = responses[responseIndex++];
          
          if (analyticsResponse.success && analyticsResponse.data) {
            setAnalyticsData(analyticsResponse.data);
          } else {
            console.warn(`Failed to load analytics data: ${analyticsResponse.error}`);
          }
          
          if (reportResponse.success && reportResponse.data) {
            setSystemReport(reportResponse.data);
          } else {
            console.warn(`Failed to load system report: ${reportResponse.error}`);
          }
          break;
          
        case 'content':
          // Content tab only uses stats data, no additional processing needed
          break;
          
        case 'system':
          const systemHealthResponse = responses[responseIndex++];
          if (systemHealthResponse.success && systemHealthResponse.data) {
            setHealthStatus(systemHealthResponse.data);
          } else {
            toast.error(`Failed to load health status: ${systemHealthResponse.error}`);
          }
          break;
          
        default:
          // Process all responses for fallback case
          const [usersResp, activityResp, healthResp, performanceResp, analyticsResp, reportResp] = responses.slice(1);
          
          if (usersResp.success && usersResp.data) {
            setUsers(usersResp.data.users);
            setUserSegments(usersResp.data.segments);
          }
          if (activityResp.success && activityResp.data) {
            setActivityLogs(activityResp.data);
          }
          if (healthResp.success && healthResp.data) {
            setHealthStatus(healthResp.data);
          }
          if (performanceResp.success && performanceResp.data) {
            setPerformanceMetrics(performanceResp.data);
          }
          if (analyticsResp.success && analyticsResp.data) {
            setAnalyticsData(analyticsResp.data);
          }
          if (reportResp.success && reportResp.data) {
            setSystemReport(reportResp.data);
          }
      }

      if (showRefreshToast) {
        toast.success(`${tabName.charAt(0).toUpperCase() + tabName.slice(1)} data refreshed`);
      }
    } catch (error) {
      console.error(`Error fetching ${tabName} data:`, error);
      toast.error(`Failed to load ${tabName} data`);
    } finally {
      setLoading(false);
      setRefreshing(false);
    }
  }, [debouncedUserSearch, userRole, activityFilter, sortBy, sortOrder, currentPage, analyticsGranularity, reportType]);

  // Legacy fetchData function for backward compatibility
  const fetchData = useCallback(async (showRefreshToast = false) => {
    return fetchTabData(activeTab, showRefreshToast);
  }, [fetchTabData, activeTab]);

  useEffect(() => {
    fetchData();
  }, [fetchData]);

  // Handle tab changes with selective data fetching
  const handleTabChange = useCallback((newTab: string) => {
    setActiveTab(newTab);
    // Only fetch data if we don't have it yet or if it's a data-dependent tab
    const needsRefresh = (
      (newTab === 'users' && users.length === 0) ||
      (newTab === 'performance' && !performanceMetrics) ||
      (newTab === 'analytics' && !analyticsData) ||
      (newTab === 'system' && !healthStatus)
    );
    
    if (needsRefresh) {
      fetchTabData(newTab);
    }
  }, [users.length, performanceMetrics, analyticsData, healthStatus, fetchTabData]);

  const handleRefresh = useCallback(() => {
    fetchData(true);
  }, [fetchData]);

  // Standardized loading component
  const LoadingState = ({ message = "Loading..." }: { message?: string }) => (
    <div className="flex items-center justify-center py-8">
      <div className="text-center">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-4"></div>
        <div className="text-lg font-medium text-gray-700">{message}</div>
      </div>
    </div>
  );

  if (loading) {
    return (
      <SuperadminLayout>
        <LoadingState message="Loading Dashboard..." />
      </SuperadminLayout>
    );
  }

  return (
    <SuperadminLayout>
      <div className="max-w-7xl mx-auto">
        {/* Welcome Section */}
        <div className="mb-8">
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-2xl font-bold text-gray-900">Welcome back, {user?.name || user?.email}</h1>
              <p className="text-gray-600">System overview and management dashboard</p>
            </div>
            <Button
              onClick={handleRefresh}
              disabled={refreshing}
              variant="outline"
              className="flex items-center space-x-2"
            >
              <RefreshCw className={`h-4 w-4 ${refreshing ? 'animate-spin' : ''}`} />
              <span>Refresh</span>
            </Button>
          </div>
        </div>

        {/* Main Content */}
        <Tabs value={activeTab} onValueChange={handleTabChange} className="space-y-6">
          <TabsList className="grid w-full grid-cols-6">
            <TabsTrigger value="overview" className="flex items-center space-x-2">
              <BarChart3 className="h-4 w-4" />
              <span>Overview</span>
            </TabsTrigger>
            <TabsTrigger value="users" className="flex items-center space-x-2">
              <Users className="h-4 w-4" />
              <span>Users</span>
            </TabsTrigger>
            <TabsTrigger value="content" className="flex items-center space-x-2">
              <Image className="h-4 w-4" />
              <span>Content</span>
            </TabsTrigger>
            <TabsTrigger value="performance" className="flex items-center space-x-2">
              <Gauge className="h-4 w-4" />
              <span>Performance</span>
            </TabsTrigger>
            <TabsTrigger value="analytics" className="flex items-center space-x-2">
              <LineChart className="h-4 w-4" />
              <span>Analytics</span>
            </TabsTrigger>
            <TabsTrigger value="system" className="flex items-center space-x-2">
              <Server className="h-4 w-4" />
              <span>System</span>
            </TabsTrigger>
          </TabsList>

          {/* Overview Tab */}
          <TabsContent value="overview" className="space-y-8">
            {/* Hero Stats Section */}
            {stats && (
              <div className="relative">
                {/* Background Gradient */}
                <div className="absolute inset-0 bg-gradient-to-br from-blue-50 via-indigo-50 to-purple-50 rounded-3xl opacity-60"></div>
                
                {/* Main Stats Grid */}
                <div className="relative grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-6 p-8">
                  {/* Total Users Card */}
                  <div className="group relative overflow-hidden bg-white/80 backdrop-blur-sm rounded-2xl p-6 shadow-lg hover:shadow-xl transition-all duration-300 border border-white/20">
                    <div className="absolute inset-0 bg-gradient-to-br from-blue-500/10 to-cyan-500/10 opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
                    <div className="relative">
                      <div className="flex items-center justify-between mb-4">
                        <div className="p-3 bg-gradient-to-br from-blue-500 to-cyan-500 rounded-xl shadow-lg">
                          <Users className="h-6 w-6 text-white" />
                        </div>
                        <div className="text-right">
                          <div className="text-xs font-medium text-gray-500 uppercase tracking-wider">Total</div>
                          <div className="text-xs text-green-600 font-semibold">+{stats?.users?.newLast7Days || 0} this week</div>
                        </div>
                      </div>
                      <div className="text-3xl font-bold text-gray-900 mb-1">{stats?.users?.total?.toLocaleString() || '0'}</div>
                      <div className="text-sm font-medium text-gray-600">Active Users</div>
                    </div>
                  </div>

                  {/* New Users Card */}
                  <div className="group relative overflow-hidden bg-white/80 backdrop-blur-sm rounded-2xl p-6 shadow-lg hover:shadow-xl transition-all duration-300 border border-white/20">
                    <div className="absolute inset-0 bg-gradient-to-br from-emerald-500/10 to-teal-500/10 opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
                    <div className="relative">
                      <div className="flex items-center justify-between mb-4">
                        <div className="p-3 bg-gradient-to-br from-emerald-500 to-teal-500 rounded-xl shadow-lg">
                          <TrendingUp className="h-6 w-6 text-white" />
                        </div>
                        <div className="text-right">
                          <div className="text-xs font-medium text-gray-500 uppercase tracking-wider">24H</div>
                          <div className="text-xs text-blue-600 font-semibold">{stats?.users?.newLast30Days || 0} this month</div>
                        </div>
                      </div>
                      <div className="text-3xl font-bold text-emerald-600 mb-1">{stats?.users?.newLast24Hours || '0'}</div>
                      <div className="text-sm font-medium text-gray-600">New Signups</div>
                    </div>
                  </div>

                  {/* Images Card */}
                  <div className="group relative overflow-hidden bg-white/80 backdrop-blur-sm rounded-2xl p-6 shadow-lg hover:shadow-xl transition-all duration-300 border border-white/20">
                    <div className="absolute inset-0 bg-gradient-to-br from-purple-500/10 to-pink-500/10 opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
                    <div className="relative">
                      <div className="flex items-center justify-between mb-4">
                        <div className="p-3 bg-gradient-to-br from-purple-500 to-pink-500 rounded-xl shadow-lg">
                          <Image className="h-6 w-6 text-white" />
                        </div>
                        <div className="text-right">
                          <div className="text-xs font-medium text-gray-500 uppercase tracking-wider">Images</div>
                          <div className="text-xs text-purple-600 font-semibold">Total generated</div>
                        </div>
                      </div>
                      <div className="text-3xl font-bold text-purple-600 mb-1">{(stats?.totalImages || 0).toLocaleString()}</div>
                      <div className="text-sm font-medium text-gray-600">Generated</div>
                    </div>
                  </div>

                  {/* Videos Card */}
                  <div className="group relative overflow-hidden bg-white/80 backdrop-blur-sm rounded-2xl p-6 shadow-lg hover:shadow-xl transition-all duration-300 border border-white/20">
                    <div className="absolute inset-0 bg-gradient-to-br from-orange-500/10 to-red-500/10 opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
                    <div className="relative">
                      <div className="flex items-center justify-between mb-4">
                        <div className="p-3 bg-gradient-to-br from-orange-500 to-red-500 rounded-xl shadow-lg">
                          <Video className="h-6 w-6 text-white" />
                        </div>
                        <div className="text-right">
                          <div className="text-xs font-medium text-gray-500 uppercase tracking-wider">Videos</div>
                          <div className="text-xs text-orange-600 font-semibold">Total created</div>
                        </div>
                      </div>
                      <div className="text-3xl font-bold text-orange-600 mb-1">{(stats?.totalVideos || 0).toLocaleString()}</div>
                      <div className="text-sm font-medium text-gray-600">Created</div>
                    </div>
                  </div>

                  {/* Voice Card */}
                  <div className="group relative overflow-hidden bg-white/80 backdrop-blur-sm rounded-2xl p-6 shadow-lg hover:shadow-xl transition-all duration-300 border border-white/20">
                    <div className="absolute inset-0 bg-gradient-to-br from-indigo-500/10 to-blue-500/10 opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
                    <div className="relative">
                      <div className="flex items-center justify-between mb-4">
                        <div className="p-3 bg-gradient-to-br from-indigo-500 to-blue-500 rounded-xl shadow-lg">
                          <Mic className="h-6 w-6 text-white" />
                        </div>
                        <div className="text-right">
                          <div className="text-xs font-medium text-gray-500 uppercase tracking-wider">Audio</div>
                          <div className="text-xs text-indigo-600 font-semibold">Voice files</div>
                        </div>
                      </div>
                      <div className="text-3xl font-bold text-indigo-600 mb-1">{(stats?.totalVoices || 0).toLocaleString()}</div>
                      <div className="text-sm font-medium text-gray-600">Voice Files</div>
                    </div>
                  </div>
                </div>
              </div>
            )}

            {/* Analytics Dashboard Grid */}
            <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
              {/* User Growth Chart */}
              {stats?.users?.growthTrend && (
                <div className="lg:col-span-2">
                  <div className="bg-white rounded-3xl p-8 shadow-lg border border-gray-100">
                    <div className="flex items-center justify-between mb-6">
                      <div>
                        <h3 className="text-xl font-bold text-gray-900 mb-1">User Growth Trend</h3>
                        <p className="text-sm text-gray-600">Daily registrations over the last 30 days</p>
                      </div>
                      <div className="p-3 bg-gradient-to-br from-blue-500 to-purple-500 rounded-xl">
                        <TrendingUp className="h-6 w-6 text-white" />
                      </div>
                    </div>
                    
                    {/* Growth Metrics */}
                    <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mb-6">
                      <div className="text-center p-4 bg-gradient-to-br from-blue-50 to-blue-100 rounded-2xl">
                        <div className="text-2xl font-bold text-blue-600">
                          {stats.users.growthTrend.slice(-7).reduce((sum, day) => sum + day.newUsers, 0)}
                        </div>
                        <div className="text-xs font-medium text-blue-700 mt-1">Last 7 days</div>
                      </div>
                      <div className="text-center p-4 bg-gradient-to-br from-emerald-50 to-emerald-100 rounded-2xl">
                        <div className="text-2xl font-bold text-emerald-600">
                          {stats.users.growthTrend.slice(-14, -7).reduce((sum, day) => sum + day.newUsers, 0)}
                        </div>
                        <div className="text-xs font-medium text-emerald-700 mt-1">Previous 7 days</div>
                      </div>
                      <div className="text-center p-4 bg-gradient-to-br from-purple-50 to-purple-100 rounded-2xl">
                        <div className="text-2xl font-bold text-purple-600">
                          {Math.round(stats.users.growthTrend.slice(-7).reduce((sum, day) => sum + day.newUsers, 0) / 7 * 10) / 10}
                        </div>
                        <div className="text-xs font-medium text-purple-700 mt-1">Daily average</div>
                      </div>
                      <div className="text-center p-4 bg-gradient-to-br from-orange-50 to-orange-100 rounded-2xl">
                        <div className="text-2xl font-bold text-orange-600">
                          {Math.max(...stats.users.growthTrend.map(day => day.newUsers))}
                        </div>
                        <div className="text-xs font-medium text-orange-700 mt-1">Peak day</div>
                      </div>
                    </div>

                    {/* Simple Visual Growth Indicator */}
                    <div className="space-y-3">
                      <div className="flex items-center justify-between text-sm font-medium text-gray-700">
                        <span>Growth Visualization</span>
                        <span className="text-xs text-gray-500">Last 14 days</span>
                      </div>
                      <div className="flex items-end space-x-1 h-20">
                        {stats.users.growthTrend.slice(-14).map((day, index) => {
                          const maxUsers = Math.max(...stats.users.growthTrend.slice(-14).map(d => d.newUsers));
                          const height = maxUsers > 0 ? (day.newUsers / maxUsers) * 100 : 0;
                          return (
                            <div
                              key={index}
                              className="flex-1 bg-gradient-to-t from-blue-500 to-purple-500 rounded-t-lg opacity-80 hover:opacity-100 transition-opacity duration-200"
                              style={{ height: `${Math.max(height, 5)}%` }}
                              title={`${day.newUsers} users`}
                            ></div>
                          );
                        })}
                      </div>
                    </div>
                  </div>
                </div>
              )}

              {/* Quick Stats Panel */}
              <div className="space-y-6">
                {/* System Health */}
                <div className="bg-white rounded-3xl p-6 shadow-lg border border-gray-100">
                  <div className="flex items-center justify-between mb-4">
                    <h3 className="text-lg font-bold text-gray-900">System Health</h3>
                    <div className="p-2 bg-gradient-to-br from-green-500 to-emerald-500 rounded-lg">
                      <CheckCircle className="h-5 w-5 text-white" />
                    </div>
                  </div>
                  <div className="space-y-3">
                    <div className="flex items-center justify-between">
                      <span className="text-sm text-gray-600">Status</span>
                      <div className="flex items-center space-x-2">
                        <div className="w-2 h-2 bg-green-500 rounded-full animate-pulse"></div>
                        <span className="text-sm font-semibold text-green-600">Healthy</span>
                      </div>
                    </div>
                    <div className="flex items-center justify-between">
                      <span className="text-sm text-gray-600">Uptime</span>
                      <span className="text-sm font-semibold text-gray-900">99.9%</span>
                    </div>
                    <div className="flex items-center justify-between">
                      <span className="text-sm text-gray-600">Response</span>
                      <span className="text-sm font-semibold text-gray-900">&lt; 100ms</span>
                    </div>
                  </div>
                </div>

                {/* Content Distribution */}
                <div className="bg-white rounded-3xl p-6 shadow-lg border border-gray-100">
                  <div className="flex items-center justify-between mb-4">
                    <h3 className="text-lg font-bold text-gray-900">Content Mix</h3>
                    <div className="p-2 bg-gradient-to-br from-purple-500 to-pink-500 rounded-lg">
                      <BarChart3 className="h-5 w-5 text-white" />
                    </div>
                  </div>
                  <div className="space-y-4">
                    {/* Images */}
                    <div>
                      <div className="flex justify-between items-center mb-2">
                        <span className="text-sm font-medium text-gray-700">Images</span>
                        <span className="text-sm font-bold text-purple-600">{((stats?.totalImages || 0) / ((stats?.totalImages || 0) + (stats?.totalVideos || 0) + (stats?.totalVoices || 0)) * 100).toFixed(1)}%</span>
                      </div>
                      <div className="w-full bg-gray-200 rounded-full h-2">
                        <div
                          className="bg-gradient-to-r from-purple-500 to-pink-500 h-2 rounded-full transition-all duration-500"
                          style={{ width: `${((stats?.totalImages || 0) / ((stats?.totalImages || 0) + (stats?.totalVideos || 0) + (stats?.totalVoices || 0)) * 100)}%` }}
                        ></div>
                      </div>
                    </div>
                    
                    {/* Videos */}
                    <div>
                      <div className="flex justify-between items-center mb-2">
                        <span className="text-sm font-medium text-gray-700">Videos</span>
                        <span className="text-sm font-bold text-orange-600">{((stats?.totalVideos || 0) / ((stats?.totalImages || 0) + (stats?.totalVideos || 0) + (stats?.totalVoices || 0)) * 100).toFixed(1)}%</span>
                      </div>
                      <div className="w-full bg-gray-200 rounded-full h-2">
                        <div
                          className="bg-gradient-to-r from-orange-500 to-red-500 h-2 rounded-full transition-all duration-500"
                          style={{ width: `${((stats?.totalVideos || 0) / ((stats?.totalImages || 0) + (stats?.totalVideos || 0) + (stats?.totalVoices || 0)) * 100)}%` }}
                        ></div>
                      </div>
                    </div>
                    
                    {/* Voices */}
                    <div>
                      <div className="flex justify-between items-center mb-2">
                        <span className="text-sm font-medium text-gray-700">Audio</span>
                        <span className="text-sm font-bold text-indigo-600">{((stats?.totalVoices || 0) / ((stats?.totalImages || 0) + (stats?.totalVideos || 0) + (stats?.totalVoices || 0)) * 100).toFixed(1)}%</span>
                      </div>
                      <div className="w-full bg-gray-200 rounded-full h-2">
                        <div
                          className="bg-gradient-to-r from-indigo-500 to-blue-500 h-2 rounded-full transition-all duration-500"
                          style={{ width: `${((stats?.totalVoices || 0) / ((stats?.totalImages || 0) + (stats?.totalVideos || 0) + (stats?.totalVoices || 0)) * 100)}%` }}
                        ></div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            {/* Top Content Creators & Recent Activity */}
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
              {/* Top Content Creators */}
              {stats?.users?.topContentCreators && stats.users.topContentCreators.length > 0 && (
                <div className="bg-white rounded-3xl p-8 shadow-lg border border-gray-100">
                  <div className="flex items-center justify-between mb-6">
                    <div>
                      <h3 className="text-xl font-bold text-gray-900 mb-1">Top Creators</h3>
                      <p className="text-sm text-gray-600">Most active content creators</p>
                    </div>
                    <div className="p-3 bg-gradient-to-br from-yellow-500 to-orange-500 rounded-xl">
                      <Star className="h-6 w-6 text-white" />
                    </div>
                  </div>
                  
                  <div className="space-y-4">
                    {stats.users.topContentCreators.slice(0, 5).map((creator, index) => (
                      <div key={creator.id} className="group relative overflow-hidden bg-gradient-to-r from-gray-50 to-gray-100 hover:from-blue-50 hover:to-purple-50 rounded-2xl p-4 transition-all duration-300">
                        <div className="flex items-center justify-between">
                          <div className="flex items-center space-x-4">
                            <div className="relative">
                              <div className={`flex items-center justify-center w-12 h-12 rounded-2xl text-white font-bold text-lg shadow-lg ${
                                index === 0 ? 'bg-gradient-to-br from-yellow-400 to-orange-500' :
                                index === 1 ? 'bg-gradient-to-br from-gray-300 to-gray-500' :
                                index === 2 ? 'bg-gradient-to-br from-orange-400 to-red-500' :
                                'bg-gradient-to-br from-blue-400 to-purple-500'
                              }`}>
                                {index + 1}
                              </div>
                              {index < 3 && (
                                <div className="absolute -top-1 -right-1 w-4 h-4 bg-yellow-400 rounded-full flex items-center justify-center">
                                  <Crown className="h-2 w-2 text-yellow-800" />
                                </div>
                              )}
                            </div>
                            <div>
                              <div className="font-semibold text-gray-900">{creator.name || 'Unknown User'}</div>
                              <div className="text-sm text-gray-600">{creator.email}</div>
                            </div>
                          </div>
                          <div className="text-right">
                            <div className="text-2xl font-bold text-gray-900">{creator.contentStats?.total || 0}</div>
                            <div className="text-xs text-gray-500 space-x-2">
                              <span className="inline-flex items-center px-2 py-1 rounded-full bg-purple-100 text-purple-700">
                                {creator.contentStats?.images || 0} <Image className="h-3 w-3 ml-1" />
                              </span>
                              <span className="inline-flex items-center px-2 py-1 rounded-full bg-orange-100 text-orange-700">
                                {creator.contentStats?.videos || 0} <Video className="h-3 w-3 ml-1" />
                              </span>
                              <span className="inline-flex items-center px-2 py-1 rounded-full bg-blue-100 text-blue-700">
                                {creator.contentStats?.voices || 0} <Mic className="h-3 w-3 ml-1" />
                              </span>
                            </div>
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
              )}

              {/* Recent Activity */}
              <div className="bg-white rounded-3xl p-8 shadow-lg border border-gray-100">
                <div className="flex items-center justify-between mb-6">
                  <div>
                    <h3 className="text-xl font-bold text-gray-900 mb-1">Recent Activity</h3>
                    <p className="text-sm text-gray-600">Latest platform activities</p>
                  </div>
                  <div className="p-3 bg-gradient-to-br from-green-500 to-emerald-500 rounded-xl">
                    <Activity className="h-6 w-6 text-white" />
                  </div>
                </div>
                
                {activityLogs.length > 0 ? (
                  <div className="space-y-4">
                    {activityLogs.slice(0, 5).map((log, index) => (
                      <div key={`${log.id || log.user_id}-${log.created_at}-${index}`} className="group relative overflow-hidden bg-gradient-to-r from-gray-50 to-gray-100 hover:from-green-50 hover:to-emerald-50 rounded-2xl p-4 transition-all duration-300">
                        <div className="flex items-center justify-between">
                          <div className="flex-1">
                            <div className="flex items-center space-x-3 mb-2">
                              <div className="w-2 h-2 bg-green-500 rounded-full animate-pulse"></div>
                              <div className="font-semibold text-gray-900">{log.action}</div>
                            </div>
                            <div className="text-sm text-gray-600 ml-5">
                              {log.user?.name || log.user?.email || log.user_email || 'Unknown User'} • {formatRelativeTime(log.created_at)}
                            </div>
                          </div>
                          <div className="flex items-center space-x-2">
                            <span className="inline-flex items-center px-3 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                              {log.resource_type}
                            </span>
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>
                ) : (
                  <div className="text-center py-12">
                    <div className="w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-4">
                      <Activity className="h-8 w-8 text-gray-400" />
                    </div>
                    <div className="text-gray-500 font-medium">No recent activity</div>
                    <div className="text-sm text-gray-400 mt-1">Activity will appear here as users interact with the platform</div>
                  </div>
                )}
              </div>
            </div>
          </TabsContent>

          {/* Users Tab */}
          <TabsContent value="users" className="space-y-8">
            {/* User Segments Overview */}
            {userSegments && (
              <div className="relative">
                {/* Background Gradient */}
                <div className="absolute inset-0 bg-gradient-to-br from-emerald-50 via-blue-50 to-purple-50 rounded-3xl opacity-60"></div>
                
                {/* User Segments Grid */}
                <div className="relative grid grid-cols-2 md:grid-cols-3 lg:grid-cols-6 gap-6 p-8">
                  {/* High Engagement */}
                  <div className="group relative overflow-hidden bg-white/80 backdrop-blur-sm rounded-2xl p-6 shadow-lg hover:shadow-xl transition-all duration-300 border border-white/20">
                    <div className="absolute inset-0 bg-gradient-to-br from-emerald-500/10 to-green-500/10 opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
                    <div className="relative text-center">
                      <div className="p-3 bg-gradient-to-br from-emerald-500 to-green-500 rounded-xl shadow-lg mx-auto w-fit mb-3">
                        <TrendingUp className="h-6 w-6 text-white" />
                      </div>
                      <div className="text-3xl font-bold text-emerald-600 mb-1">{userSegments.highEngagement}</div>
                      <div className="text-sm font-medium text-gray-600">High Engagement</div>
                    </div>
                  </div>

                  {/* Medium Engagement */}
                  <div className="group relative overflow-hidden bg-white/80 backdrop-blur-sm rounded-2xl p-6 shadow-lg hover:shadow-xl transition-all duration-300 border border-white/20">
                    <div className="absolute inset-0 bg-gradient-to-br from-blue-500/10 to-cyan-500/10 opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
                    <div className="relative text-center">
                      <div className="p-3 bg-gradient-to-br from-blue-500 to-cyan-500 rounded-xl shadow-lg mx-auto w-fit mb-3">
                        <BarChart3 className="h-6 w-6 text-white" />
                      </div>
                      <div className="text-3xl font-bold text-blue-600 mb-1">{userSegments.mediumEngagement}</div>
                      <div className="text-sm font-medium text-gray-600">Medium Engagement</div>
                    </div>
                  </div>

                  {/* Low Engagement */}
                  <div className="group relative overflow-hidden bg-white/80 backdrop-blur-sm rounded-2xl p-6 shadow-lg hover:shadow-xl transition-all duration-300 border border-white/20">
                    <div className="absolute inset-0 bg-gradient-to-br from-yellow-500/10 to-orange-500/10 opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
                    <div className="relative text-center">
                      <div className="p-3 bg-gradient-to-br from-yellow-500 to-orange-500 rounded-xl shadow-lg mx-auto w-fit mb-3">
                        <Activity className="h-6 w-6 text-white" />
                      </div>
                      <div className="text-3xl font-bold text-yellow-600 mb-1">{userSegments.lowEngagement}</div>
                      <div className="text-sm font-medium text-gray-600">Low Engagement</div>
                    </div>
                  </div>

                  {/* Inactive */}
                  <div className="group relative overflow-hidden bg-white/80 backdrop-blur-sm rounded-2xl p-6 shadow-lg hover:shadow-xl transition-all duration-300 border border-white/20">
                    <div className="absolute inset-0 bg-gradient-to-br from-gray-500/10 to-slate-500/10 opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
                    <div className="relative text-center">
                      <div className="p-3 bg-gradient-to-br from-gray-500 to-slate-500 rounded-xl shadow-lg mx-auto w-fit mb-3">
                        <Clock className="h-6 w-6 text-white" />
                      </div>
                      <div className="text-3xl font-bold text-gray-600 mb-1">{userSegments.inactive}</div>
                      <div className="text-sm font-medium text-gray-600">Inactive</div>
                    </div>
                  </div>

                  {/* New Users */}
                  <div className="group relative overflow-hidden bg-white/80 backdrop-blur-sm rounded-2xl p-6 shadow-lg hover:shadow-xl transition-all duration-300 border border-white/20">
                    <div className="absolute inset-0 bg-gradient-to-br from-purple-500/10 to-pink-500/10 opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
                    <div className="relative text-center">
                      <div className="p-3 bg-gradient-to-br from-purple-500 to-pink-500 rounded-xl shadow-lg mx-auto w-fit mb-3">
                        <Users className="h-6 w-6 text-white" />
                      </div>
                      <div className="text-3xl font-bold text-purple-600 mb-1">{userSegments.newUsers}</div>
                      <div className="text-sm font-medium text-gray-600">New Users</div>
                    </div>
                  </div>

                  {/* Power Users */}
                  <div className="group relative overflow-hidden bg-white/80 backdrop-blur-sm rounded-2xl p-6 shadow-lg hover:shadow-xl transition-all duration-300 border border-white/20">
                    <div className="absolute inset-0 bg-gradient-to-br from-orange-500/10 to-red-500/10 opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
                    <div className="relative text-center">
                      <div className="p-3 bg-gradient-to-br from-orange-500 to-red-500 rounded-xl shadow-lg mx-auto w-fit mb-3">
                        <Zap className="h-6 w-6 text-white" />
                      </div>
                      <div className="text-3xl font-bold text-orange-600 mb-1">{userSegments.powerUsers}</div>
                      <div className="text-sm font-medium text-gray-600">Power Users</div>
                    </div>
                  </div>
                </div>
              </div>
            )}

            {/* Advanced User Management */}
            <div className="bg-white rounded-3xl p-8 shadow-lg border border-gray-100">
              <div className="flex items-center justify-between mb-8">
                <div>
                  <h3 className="text-2xl font-bold text-gray-900 mb-2">User Management</h3>
                  <p className="text-gray-600">Advanced user analytics and management tools</p>
                </div>
                <div className="p-3 bg-gradient-to-br from-blue-500 to-purple-500 rounded-xl">
                  <Users className="h-6 w-6 text-white" />
                </div>
              </div>

              {/* Modern Search and Filter Controls */}
              <div className="bg-gradient-to-r from-gray-50 to-blue-50 rounded-2xl p-6 mb-8">
                <div className="flex flex-col lg:flex-row gap-4">
                  <div className="flex-1">
                    <div className="relative">
                      <Search className="absolute left-4 top-1/2 transform -translate-y-1/2 text-gray-400 h-5 w-5" />
                      <Input
                        placeholder="Search users by name or email..."
                        value={userSearch}
                        onChange={(e) => setUserSearch(e.target.value)}
                        className="pl-12 h-12 bg-white/80 backdrop-blur-sm border-white/20 rounded-xl shadow-sm focus:shadow-md transition-all duration-200"
                      />
                    </div>
                  </div>
                  <div className="flex flex-col sm:flex-row gap-4">
                    <Select value={userRole} onValueChange={(value: string) => {
                      const validRoles = ['all', 'user', 'superadmin'];
                      if (validRoles.includes(value)) {
                        setUserRole(value);
                      }
                    }}>
                      <SelectTrigger className="w-full sm:w-48 h-12 bg-white/80 backdrop-blur-sm border-white/20 rounded-xl">
                        <SelectValue placeholder="Filter by role" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="all">All Roles</SelectItem>
                        <SelectItem value="user">User</SelectItem>
                        <SelectItem value="superadmin">Superadmin</SelectItem>
                      </SelectContent>
                    </Select>
                    <Select value={activityFilter} onValueChange={(value: string) => {
                      const validFilters: ('all' | 'active' | 'inactive')[] = ['all', 'active', 'inactive'];
                      if (validFilters.includes(value as 'all' | 'active' | 'inactive')) {
                        setActivityFilter(value as 'all' | 'active' | 'inactive');
                      }
                    }}>
                      <SelectTrigger className="w-full sm:w-48 h-12 bg-white/80 backdrop-blur-sm border-white/20 rounded-xl">
                        <SelectValue placeholder="Filter by activity" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="all">All Users</SelectItem>
                        <SelectItem value="active">Active Users</SelectItem>
                        <SelectItem value="inactive">Inactive Users</SelectItem>
                      </SelectContent>
                    </Select>
                    <Button
                      onClick={() => fetchData(true)}
                      className="h-12 px-6 bg-gradient-to-r from-blue-500 to-purple-500 hover:from-blue-600 hover:to-purple-600 text-white rounded-xl shadow-lg hover:shadow-xl transition-all duration-200"
                    >
                      <Filter className="h-4 w-4 mr-2" />
                      Apply Filters
                    </Button>
                  </div>
                </div>
              </div>

              {/* Modern User Cards */}
              {users.length > 0 ? (
                <div className="space-y-4">
                  {users.map((user) => (
                    <div key={user.id} className="group relative overflow-hidden bg-gradient-to-r from-white to-gray-50 hover:from-blue-50 hover:to-purple-50 rounded-2xl p-6 border border-gray-200 hover:border-blue-200 transition-all duration-300 shadow-sm hover:shadow-md">
                      <div className="grid grid-cols-1 lg:grid-cols-7 gap-6 items-center">
                        {/* User Info */}
                        <div className="lg:col-span-2">
                          <div className="flex items-center space-x-4">
                            <div className="relative">
                              <div className="w-12 h-12 bg-gradient-to-br from-blue-500 to-purple-500 rounded-2xl flex items-center justify-center text-white font-bold text-lg shadow-lg">
                                {(user.name || user.email).charAt(0).toUpperCase()}
                              </div>
                              {user.is_superadmin && (
                                <div className="absolute -top-1 -right-1 w-5 h-5 bg-yellow-400 rounded-full flex items-center justify-center">
                                  <Crown className="h-3 w-3 text-yellow-800" />
                                </div>
                              )}
                            </div>
                            <div>
                              <div className="font-semibold text-gray-900">{user.name || 'Unknown'}</div>
                              <div className="text-sm text-gray-600">{user.email}</div>
                            </div>
                          </div>
                        </div>

                        {/* Role */}
                        <div className="text-center">
                          <div className={`inline-flex items-center px-3 py-1 rounded-full text-sm font-medium ${
                            user.is_superadmin
                              ? 'bg-gradient-to-r from-yellow-100 to-orange-100 text-yellow-800'
                              : 'bg-gradient-to-r from-blue-100 to-purple-100 text-blue-800'
                          }`}>
                            {user.role}
                          </div>
                        </div>

                        {/* Engagement */}
                        <div className="text-center">
                          <div className={`inline-flex items-center px-3 py-1 rounded-full text-sm font-medium ${
                            user.activity?.engagementLevel === 'high' ? 'bg-gradient-to-r from-emerald-100 to-green-100 text-emerald-800' :
                            user.activity?.engagementLevel === 'medium' ? 'bg-gradient-to-r from-blue-100 to-cyan-100 text-blue-800' :
                            user.activity?.engagementLevel === 'low' ? 'bg-gradient-to-r from-yellow-100 to-orange-100 text-yellow-800' :
                            'bg-gradient-to-r from-gray-100 to-slate-100 text-gray-800'
                          }`}>
                            {user.activity?.engagementLevel || 'unknown'}
                          </div>
                          <div className="text-xs text-gray-500 mt-1">
                            {user.activity?.recentActivity || 0} recent
                          </div>
                        </div>

                        {/* Content Stats */}
                        <div className="text-center">
                          <div className="text-xl font-bold text-gray-900">{user.activity?.totalContent || 0}</div>
                          <div className="text-xs text-gray-500 space-x-1">
                            <span className="inline-flex items-center px-2 py-1 rounded-full bg-purple-100 text-purple-700">
                              {user.activity?.totalImages || 0}<Image className="h-3 w-3 ml-1" />
                            </span>
                            <span className="inline-flex items-center px-2 py-1 rounded-full bg-orange-100 text-orange-700">
                              {user.activity?.totalVideos || 0}<Video className="h-3 w-3 ml-1" />
                            </span>
                            <span className="inline-flex items-center px-2 py-1 rounded-full bg-blue-100 text-blue-700">
                              {user.activity?.totalVoices || 0}<Mic className="h-3 w-3 ml-1" />
                            </span>
                          </div>
                        </div>

                        {/* Preferences */}
                        <div className="text-center">
                          <div className="space-y-1">
                            <div className="flex justify-between text-xs">
                              <span className="text-purple-600">Images:</span>
                              <span className="font-medium">{user.activity?.preferences?.images || 0}%</span>
                            </div>
                            <div className="flex justify-between text-xs">
                              <span className="text-orange-600">Videos:</span>
                              <span className="font-medium">{user.activity?.preferences?.videos || 0}%</span>
                            </div>
                            <div className="flex justify-between text-xs">
                              <span className="text-blue-600">Audio:</span>
                              <span className="font-medium">{user.activity?.preferences?.voices || 0}%</span>
                            </div>
                          </div>
                        </div>

                        {/* Join Date */}
                        <div className="text-center">
                          <div className="text-sm font-medium text-gray-900">
                            {formatDate(user.created_at)}
                          </div>
                          <div className="text-xs text-gray-500">
                            {user.activity?.joinedDaysAgo || 0} days ago
                          </div>
                        </div>

                        {/* Last Active */}
                        <div className="text-center">
                          <div className="text-sm font-medium text-gray-900">
                            {user.activity?.lastActivity ?
                              formatRelativeTime(user.activity.lastActivity) :
                              'Never'
                            }
                          </div>
                          <div className="text-xs text-gray-500">Last seen</div>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              ) : (
                <div className="text-center py-16">
                  <div className="w-20 h-20 bg-gradient-to-br from-gray-100 to-gray-200 rounded-full flex items-center justify-center mx-auto mb-6">
                    <Users className="h-10 w-10 text-gray-400" />
                  </div>
                  <div className="text-xl font-semibold text-gray-700 mb-2">No users found</div>
                  <div className="text-gray-500">Try adjusting your search criteria or filters</div>
                </div>
              )}
            </div>
          </TabsContent>

          {/* Content Tab */}
          <TabsContent value="content" className="space-y-8">
            {/* Content Overview Hero */}
            <div className="relative">
              <div className="absolute inset-0 bg-gradient-to-br from-purple-50 via-pink-50 to-orange-50 rounded-3xl opacity-60"></div>
              
              <div className="relative grid grid-cols-1 md:grid-cols-3 gap-6 p-8">
                {/* Image Analytics */}
                <div className="group relative overflow-hidden bg-white/80 backdrop-blur-sm rounded-2xl p-6 shadow-lg hover:shadow-xl transition-all duration-300 border border-white/20">
                  <div className="absolute inset-0 bg-gradient-to-br from-purple-500/10 to-pink-500/10 opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
                  <div className="relative">
                    <div className="flex items-center justify-between mb-6">
                      <div className="p-3 bg-gradient-to-br from-purple-500 to-pink-500 rounded-xl shadow-lg">
                        <Image className="h-6 w-6 text-white" />
                      </div>
                      <div className="text-right">
                        <div className="text-xs font-medium text-gray-500 uppercase tracking-wider">Images</div>
                        <div className="text-xs text-purple-600 font-semibold">Generated Content</div>
                      </div>
                    </div>
                    
                    <div className="text-3xl font-bold text-purple-600 mb-2">{(stats?.totalImages || 0).toLocaleString()}</div>
                    <div className="text-sm font-medium text-gray-600 mb-4">Total Images</div>
                    
                    {/* Visual Progress Bar */}
                    <div className="space-y-3">
                      <div className="flex justify-between text-xs">
                        <span className="text-gray-600">Storage Used</span>
                        <span className="font-medium">{((stats?.totalImages || 0) * 2.5).toFixed(1)}MB</span>
                      </div>
                      <div className="w-full bg-gray-200 rounded-full h-2">
                        <div className="bg-gradient-to-r from-purple-500 to-pink-500 h-2 rounded-full transition-all duration-500" style={{ width: `${Math.min(100, (stats?.users?.total || 0) / 10)}%` }}></div>
                      </div>
                    </div>
                  </div>
                </div>

                {/* Video Analytics */}
                <div className="group relative overflow-hidden bg-white/80 backdrop-blur-sm rounded-2xl p-6 shadow-lg hover:shadow-xl transition-all duration-300 border border-white/20">
                  <div className="absolute inset-0 bg-gradient-to-br from-orange-500/10 to-red-500/10 opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
                  <div className="relative">
                    <div className="flex items-center justify-between mb-6">
                      <div className="p-3 bg-gradient-to-br from-orange-500 to-red-500 rounded-xl shadow-lg">
                        <Video className="h-6 w-6 text-white" />
                      </div>
                      <div className="text-right">
                        <div className="text-xs font-medium text-gray-500 uppercase tracking-wider">Videos</div>
                        <div className="text-xs text-orange-600 font-semibold">Created Content</div>
                      </div>
                    </div>
                    
                    <div className="text-3xl font-bold text-orange-600 mb-2">{(stats?.totalVideos || 0).toLocaleString()}</div>
                    <div className="text-sm font-medium text-gray-600 mb-4">Total Videos</div>
                    
                    {/* Visual Progress Bar */}
                    <div className="space-y-3">
                      <div className="flex justify-between text-xs">
                        <span className="text-gray-600">Storage Used</span>
                        <span className="font-medium">{((stats?.totalVideos || 0) * 15).toFixed(1)}MB</span>
                      </div>
                      <div className="w-full bg-gray-200 rounded-full h-2">
                        <div className="bg-gradient-to-r from-orange-500 to-red-500 h-2 rounded-full transition-all duration-500" style={{ width: `${Math.min(100, (stats?.totalImages || 0) / 5)}%` }}></div>
                      </div>
                    </div>
                  </div>
                </div>

                {/* Voice Analytics */}
                <div className="group relative overflow-hidden bg-white/80 backdrop-blur-sm rounded-2xl p-6 shadow-lg hover:shadow-xl transition-all duration-300 border border-white/20">
                  <div className="absolute inset-0 bg-gradient-to-br from-indigo-500/10 to-blue-500/10 opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
                  <div className="relative">
                    <div className="flex items-center justify-between mb-6">
                      <div className="p-3 bg-gradient-to-br from-indigo-500 to-blue-500 rounded-xl shadow-lg">
                        <Mic className="h-6 w-6 text-white" />
                      </div>
                      <div className="text-right">
                        <div className="text-xs font-medium text-gray-500 uppercase tracking-wider">Audio</div>
                        <div className="text-xs text-indigo-600 font-semibold">Voice Files</div>
                      </div>
                    </div>
                    
                    <div className="text-3xl font-bold text-indigo-600 mb-2">{(stats?.totalVoices || 0).toLocaleString()}</div>
                    <div className="text-sm font-medium text-gray-600 mb-4">Total Voices</div>
                    
                    {/* Visual Progress Bar */}
                    <div className="space-y-3">
                      <div className="flex justify-between text-xs">
                        <span className="text-gray-600">Storage Used</span>
                        <span className="font-medium">{((stats?.totalVoices || 0) * 0.5).toFixed(1)}MB</span>
                      </div>
                      <div className="w-full bg-gray-200 rounded-full h-2">
                        <div className="bg-gradient-to-r from-indigo-500 to-blue-500 h-2 rounded-full transition-all duration-500" style={{ width: `${Math.min(100, (stats?.totalVideos || 0) / 3)}%` }}></div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            {/* Content Distribution Chart */}
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
              {/* Content Type Distribution */}
              <div className="bg-white rounded-3xl p-8 shadow-lg border border-gray-100">
                <div className="flex items-center justify-between mb-6">
                  <div>
                    <h3 className="text-xl font-bold text-gray-900 mb-1">Content Distribution</h3>
                    <p className="text-sm text-gray-600">Platform content breakdown by type</p>
                  </div>
                  <div className="p-3 bg-gradient-to-br from-blue-500 to-purple-500 rounded-xl">
                    <BarChart3 className="h-6 w-6 text-white" />
                  </div>
                </div>

                {stats && (
                  <div className="space-y-6">
                    {/* Visual Chart Representation */}
                    <div className="relative h-40 flex items-end justify-center space-x-4">
                      {/* Images Bar */}
                      <div className="flex flex-col items-center">
                        <div
                          className="w-16 bg-gradient-to-t from-purple-500 to-pink-500 rounded-t-lg transition-all duration-1000 hover:opacity-80"
                          style={{ height: `${Math.max((stats.totalImages / Math.max(stats.totalImages, stats.totalVideos, stats.totalVoices)) * 100, 10)}%` }}
                        ></div>
                        <div className="text-xs font-medium text-gray-600 mt-2">Images</div>
                        <div className="text-sm font-bold text-purple-600">{stats.totalImages}</div>
                      </div>
                      
                      {/* Videos Bar */}
                      <div className="flex flex-col items-center">
                        <div
                          className="w-16 bg-gradient-to-t from-orange-500 to-red-500 rounded-t-lg transition-all duration-1000 hover:opacity-80"
                          style={{ height: `${Math.max((stats.totalVideos / Math.max(stats.totalImages, stats.totalVideos, stats.totalVoices)) * 100, 10)}%` }}
                        ></div>
                        <div className="text-xs font-medium text-gray-600 mt-2">Videos</div>
                        <div className="text-sm font-bold text-orange-600">{stats.totalVideos}</div>
                      </div>
                      
                      {/* Voices Bar */}
                      <div className="flex flex-col items-center">
                        <div
                          className="w-16 bg-gradient-to-t from-indigo-500 to-blue-500 rounded-t-lg transition-all duration-1000 hover:opacity-80"
                          style={{ height: `${Math.max((stats.totalVoices / Math.max(stats.totalImages, stats.totalVideos, stats.totalVoices)) * 100, 10)}%` }}
                        ></div>
                        <div className="text-xs font-medium text-gray-600 mt-2">Voices</div>
                        <div className="text-sm font-bold text-indigo-600">{stats.totalVoices}</div>
                      </div>
                    </div>

                    {/* Percentage Breakdown */}
                    <div className="grid grid-cols-3 gap-4">
                      <div className="text-center p-3 bg-gradient-to-br from-purple-50 to-pink-50 rounded-xl">
                        <div className="text-lg font-bold text-purple-600">
                          {((stats.totalImages / (stats.totalImages + stats.totalVideos + stats.totalVoices)) * 100).toFixed(1)}%
                        </div>
                        <div className="text-xs text-gray-600">Images</div>
                      </div>
                      <div className="text-center p-3 bg-gradient-to-br from-orange-50 to-red-50 rounded-xl">
                        <div className="text-lg font-bold text-orange-600">
                          {((stats.totalVideos / (stats.totalImages + stats.totalVideos + stats.totalVoices)) * 100).toFixed(1)}%
                        </div>
                        <div className="text-xs text-gray-600">Videos</div>
                      </div>
                      <div className="text-center p-3 bg-gradient-to-br from-indigo-50 to-blue-50 rounded-xl">
                        <div className="text-lg font-bold text-indigo-600">
                          {((stats.totalVoices / (stats.totalImages + stats.totalVideos + stats.totalVoices)) * 100).toFixed(1)}%
                        </div>
                        <div className="text-xs text-gray-600">Voices</div>
                      </div>
                    </div>
                  </div>
                )}
              </div>

              {/* Storage Usage Visualization */}
              <div className="bg-white rounded-3xl p-8 shadow-lg border border-gray-100">
                <div className="flex items-center justify-between mb-6">
                  <div>
                    <h3 className="text-xl font-bold text-gray-900 mb-1">Storage Analytics</h3>
                    <p className="text-sm text-gray-600">Platform storage utilization breakdown</p>
                  </div>
                  <div className="p-3 bg-gradient-to-br from-green-500 to-emerald-500 rounded-xl">
                    <HardDrive className="h-6 w-6 text-white" />
                  </div>
                </div>

                {stats && (
                  <div className="space-y-6">
                    {/* Storage Donut Chart Representation */}
                    <div className="relative flex items-center justify-center">
                      <div className="w-32 h-32 rounded-full border-8 border-gray-200 relative">
                        {/* Images segment */}
                        <div className="absolute inset-0 rounded-full border-8 border-transparent border-t-purple-500 border-r-purple-500 transform rotate-0"></div>
                        {/* Videos segment */}
                        <div className="absolute inset-0 rounded-full border-8 border-transparent border-r-orange-500 border-b-orange-500 transform rotate-90"></div>
                        {/* Voices segment */}
                        <div className="absolute inset-0 rounded-full border-8 border-transparent border-b-blue-500 border-l-blue-500 transform rotate-180"></div>
                        
                        <div className="absolute inset-0 flex items-center justify-center">
                          <div className="text-center">
                            <div className="text-lg font-bold text-gray-900">
                              {(((stats.totalImages * 2.5) + (stats.totalVideos * 15) + (stats.totalVoices * 0.5))).toFixed(0)}MB
                            </div>
                            <div className="text-xs text-gray-600">Total</div>
                          </div>
                        </div>
                      </div>
                    </div>

                    {/* Storage Breakdown */}
                    <div className="space-y-4">
                      <div className="flex items-center justify-between p-3 bg-gradient-to-r from-purple-50 to-pink-50 rounded-xl">
                        <div className="flex items-center space-x-3">
                          <div className="w-4 h-4 bg-gradient-to-r from-purple-500 to-pink-500 rounded-full"></div>
                          <span className="font-medium text-gray-700">Images</span>
                        </div>
                        <div className="text-right">
                          <div className="font-bold text-purple-600">{((stats.totalImages || 0) * 2.5).toFixed(1)}MB</div>
                          <div className="text-xs text-gray-500">{stats.totalImages} files</div>
                        </div>
                      </div>
                      
                      <div className="flex items-center justify-between p-3 bg-gradient-to-r from-orange-50 to-red-50 rounded-xl">
                        <div className="flex items-center space-x-3">
                          <div className="w-4 h-4 bg-gradient-to-r from-orange-500 to-red-500 rounded-full"></div>
                          <span className="font-medium text-gray-700">Videos</span>
                        </div>
                        <div className="text-right">
                          <div className="font-bold text-orange-600">{((stats.totalVideos || 0) * 15).toFixed(1)}MB</div>
                          <div className="text-xs text-gray-500">{stats.totalVideos} files</div>
                        </div>
                      </div>
                      
                      <div className="flex items-center justify-between p-3 bg-gradient-to-r from-indigo-50 to-blue-50 rounded-xl">
                        <div className="flex items-center space-x-3">
                          <div className="w-4 h-4 bg-gradient-to-r from-indigo-500 to-blue-500 rounded-full"></div>
                          <span className="font-medium text-gray-700">Voices</span>
                        </div>
                        <div className="text-right">
                          <div className="font-bold text-indigo-600">{((stats.totalVoices || 0) * 0.5).toFixed(1)}MB</div>
                          <div className="text-xs text-gray-500">{stats.totalVoices} files</div>
                        </div>
                      </div>
                    </div>
                  </div>
                )}
              </div>
            </div>

            {/* Content Growth Trends */}
            <div className="bg-white rounded-3xl p-8 shadow-lg border border-gray-100">
              <div className="flex items-center justify-between mb-6">
                <div>
                  <h3 className="text-xl font-bold text-gray-900 mb-1">Content Growth Analysis</h3>
                  <p className="text-sm text-gray-600">Historical content generation data</p>
                </div>
                <div className="p-3 bg-gradient-to-br from-cyan-500 to-blue-500 rounded-xl">
                  <TrendingUp className="h-6 w-6 text-white" />
                </div>
              </div>

              <div className="text-center py-12">
                <div className="w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-4">
                  <TrendingUp className="h-8 w-8 text-gray-400" />
                </div>
                <div className="text-gray-500 font-medium">Growth Analytics</div>
                <div className="text-sm text-gray-400 mt-1">Historical growth trends will be calculated from actual usage data</div>
              </div>
            </div>
          </TabsContent>

          {/* Performance Tab */}
          <TabsContent value="performance" className="space-y-8">
            {performanceMetrics ? (
              <>
                {/* System Performance Hero */}
                <div className="relative">
                  <div className="absolute inset-0 bg-gradient-to-br from-green-50 via-blue-50 to-purple-50 rounded-3xl opacity-60"></div>
                  
                  <div className="relative grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 p-8">
                    {/* Server Uptime */}
                    <div className="group relative overflow-hidden bg-white/80 backdrop-blur-sm rounded-2xl p-6 shadow-lg hover:shadow-xl transition-all duration-300 border border-white/20">
                      <div className="absolute inset-0 bg-gradient-to-br from-green-500/10 to-emerald-500/10 opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
                      <div className="relative">
                        <div className="flex items-center justify-between mb-4">
                          <div className="p-3 bg-gradient-to-br from-green-500 to-emerald-500 rounded-xl shadow-lg">
                            <Clock className="h-6 w-6 text-white" />
                          </div>
                          <div className="text-right">
                            <div className="text-xs font-medium text-gray-500 uppercase tracking-wider">Uptime</div>
                            <div className="text-xs text-green-600 font-semibold">99.9% Stable</div>
                          </div>
                        </div>
                        <div className="text-2xl font-bold text-green-600 mb-1">
                          {superadminService.formatUptime(performanceMetrics.system.server.uptime)}
                        </div>
                        <div className="text-sm font-medium text-gray-600">
                          {performanceMetrics.system.server.platform} {performanceMetrics.system.server.arch}
                        </div>
                      </div>
                    </div>

                    {/* Memory Usage */}
                    <div className="group relative overflow-hidden bg-white/80 backdrop-blur-sm rounded-2xl p-6 shadow-lg hover:shadow-xl transition-all duration-300 border border-white/20">
                      <div className="absolute inset-0 bg-gradient-to-br from-blue-500/10 to-cyan-500/10 opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
                      <div className="relative">
                        <div className="flex items-center justify-between mb-4">
                          <div className="p-3 bg-gradient-to-br from-blue-500 to-cyan-500 rounded-xl shadow-lg">
                            <MemoryStick className="h-6 w-6 text-white" />
                          </div>
                          <div className="text-right">
                            <div className="text-xs font-medium text-gray-500 uppercase tracking-wider">Memory</div>
                            <div className="text-xs text-blue-600 font-semibold">
                              {Math.round((performanceMetrics.system.server.memory.heapUsed / performanceMetrics.system.server.memory.heapTotal) * 100)}% Used
                            </div>
                          </div>
                        </div>
                        <div className="text-2xl font-bold text-blue-600 mb-1">
                          {superadminService.formatBytes(performanceMetrics.system.server.memory.heapUsed)}
                        </div>
                        <div className="text-sm font-medium text-gray-600">
                          of {superadminService.formatBytes(performanceMetrics.system.server.memory.heapTotal)}
                        </div>
                        
                        {/* Memory Usage Bar */}
                        <div className="mt-3">
                          <div className="w-full bg-gray-200 rounded-full h-2">
                            <div
                              className="bg-gradient-to-r from-blue-500 to-cyan-500 h-2 rounded-full transition-all duration-500"
                              style={{ width: `${(performanceMetrics.system.server.memory.heapUsed / performanceMetrics.system.server.memory.heapTotal) * 100}%` }}
                            ></div>
                          </div>
                        </div>
                      </div>
                    </div>

                    {/* Database Response */}
                    <div className="group relative overflow-hidden bg-white/80 backdrop-blur-sm rounded-2xl p-6 shadow-lg hover:shadow-xl transition-all duration-300 border border-white/20">
                      <div className="absolute inset-0 bg-gradient-to-br from-purple-500/10 to-pink-500/10 opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
                      <div className="relative">
                        <div className="flex items-center justify-between mb-4">
                          <div className="p-3 bg-gradient-to-br from-purple-500 to-pink-500 rounded-xl shadow-lg">
                            <Database className="h-6 w-6 text-white" />
                          </div>
                          <div className="text-right">
                            <div className="text-xs font-medium text-gray-500 uppercase tracking-wider">Database</div>
                            <div className={`text-xs font-semibold ${performanceMetrics.database.status === 'healthy' ? 'text-green-600' : 'text-red-600'}`}>
                              {performanceMetrics.database.status}
                            </div>
                          </div>
                        </div>
                        <div className="text-2xl font-bold text-purple-600 mb-1">
                          {performanceMetrics.database.responseTime}ms
                        </div>
                        <div className="text-sm font-medium text-gray-600">Response Time</div>
                        
                        {/* Response Time Indicator */}
                        <div className="mt-3">
                          <div className="w-full bg-gray-200 rounded-full h-2">
                            <div
                              className={`h-2 rounded-full transition-all duration-500 ${
                                performanceMetrics.database.responseTime < 100 ? 'bg-gradient-to-r from-green-500 to-emerald-500' :
                                performanceMetrics.database.responseTime < 500 ? 'bg-gradient-to-r from-yellow-500 to-orange-500' :
                                'bg-gradient-to-r from-red-500 to-pink-500'
                              }`}
                              style={{ width: `${Math.min((performanceMetrics.database.responseTime / 1000) * 100, 100)}%` }}
                            ></div>
                          </div>
                        </div>
                      </div>
                    </div>

                    {/* CPU Cores */}
                    <div className="group relative overflow-hidden bg-white/80 backdrop-blur-sm rounded-2xl p-6 shadow-lg hover:shadow-xl transition-all duration-300 border border-white/20">
                      <div className="absolute inset-0 bg-gradient-to-br from-orange-500/10 to-red-500/10 opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
                      <div className="relative">
                        <div className="flex items-center justify-between mb-4">
                          <div className="p-3 bg-gradient-to-br from-orange-500 to-red-500 rounded-xl shadow-lg">
                            <Cpu className="h-6 w-6 text-white" />
                          </div>
                          <div className="text-right">
                            <div className="text-xs font-medium text-gray-500 uppercase tracking-wider">CPU</div>
                            <div className="text-xs text-orange-600 font-semibold">Multi-Core</div>
                          </div>
                        </div>
                        <div className="text-2xl font-bold text-orange-600 mb-1">
                          {performanceMetrics.system.server.cpuCount}
                        </div>
                        <div className="text-sm font-medium text-gray-600">
                          Node.js {performanceMetrics.system.server.nodeVersion}
                        </div>
                        
                        {/* CPU Cores Visual */}
                        <div className="mt-3 flex space-x-1">
                          {Array.from({ length: Math.min(performanceMetrics.system.server.cpuCount, 8) }, (_, i) => (
                            <div key={i} className="flex-1 h-2 bg-gradient-to-r from-orange-500 to-red-500 rounded-full"></div>
                          ))}
                        </div>
                      </div>
                    </div>
                  </div>
                </div>

                {/* Content Performance Metrics */}
                <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
                  {/* Image Performance */}
                  <div className="bg-white rounded-3xl p-8 shadow-lg border border-gray-100">
                    <div className="flex items-center justify-between mb-6">
                      <div>
                        <h3 className="text-xl font-bold text-gray-900 mb-1">Image Performance</h3>
                        <p className="text-sm text-gray-600">Generation success metrics</p>
                      </div>
                      <div className="p-3 bg-gradient-to-br from-purple-500 to-pink-500 rounded-xl">
                        <Image className="h-6 w-6 text-white" />
                      </div>
                    </div>

                    {/* Success Rate Circle */}
                    <div className="flex items-center justify-center mb-6">
                      <div className="relative w-24 h-24">
                        <svg className="w-24 h-24 transform -rotate-90" viewBox="0 0 100 100">
                          <circle cx="50" cy="50" r="40" stroke="currentColor" strokeWidth="8" fill="transparent" className="text-gray-200" />
                          <circle
                            cx="50" cy="50" r="40" stroke="currentColor" strokeWidth="8" fill="transparent"
                            className="text-purple-500"
                            strokeDasharray={`${(performanceMetrics.content.images.successRate / 100) * 251.2} 251.2`}
                            strokeLinecap="round"
                          />
                        </svg>
                        <div className="absolute inset-0 flex items-center justify-center">
                          <span className="text-lg font-bold text-purple-600">{performanceMetrics.content.images.successRate}%</span>
                        </div>
                      </div>
                    </div>

                    <div className="space-y-4">
                      <div className="flex items-center justify-between p-3 bg-gradient-to-r from-purple-50 to-pink-50 rounded-xl">
                        <span className="font-medium text-gray-700">Last 24h</span>
                        <span className="font-bold text-purple-600">{performanceMetrics.content.images.last24h}</span>
                      </div>
                      <div className="flex items-center justify-between p-3 bg-gradient-to-r from-blue-50 to-cyan-50 rounded-xl">
                        <span className="font-medium text-gray-700">Last 7d</span>
                        <span className="font-bold text-blue-600">{performanceMetrics.content.images.last7d}</span>
                      </div>
                      <div className="flex items-center justify-between p-3 bg-gradient-to-r from-green-50 to-emerald-50 rounded-xl">
                        <span className="font-medium text-gray-700">Total</span>
                        <span className="font-bold text-green-600">{performanceMetrics.content.images.total.toLocaleString()}</span>
                      </div>
                    </div>
                  </div>

                  {/* Video Performance */}
                  <div className="bg-white rounded-3xl p-8 shadow-lg border border-gray-100">
                    <div className="flex items-center justify-between mb-6">
                      <div>
                        <h3 className="text-xl font-bold text-gray-900 mb-1">Video Performance</h3>
                        <p className="text-sm text-gray-600">Processing success metrics</p>
                      </div>
                      <div className="p-3 bg-gradient-to-br from-orange-500 to-red-500 rounded-xl">
                        <Video className="h-6 w-6 text-white" />
                      </div>
                    </div>

                    {/* Success Rate Circle */}
                    <div className="flex items-center justify-center mb-6">
                      <div className="relative w-24 h-24">
                        <svg className="w-24 h-24 transform -rotate-90" viewBox="0 0 100 100">
                          <circle cx="50" cy="50" r="40" stroke="currentColor" strokeWidth="8" fill="transparent" className="text-gray-200" />
                          <circle
                            cx="50" cy="50" r="40" stroke="currentColor" strokeWidth="8" fill="transparent"
                            className="text-orange-500"
                            strokeDasharray={`${(performanceMetrics.content.videos.successRate / 100) * 251.2} 251.2`}
                            strokeLinecap="round"
                          />
                        </svg>
                        <div className="absolute inset-0 flex items-center justify-center">
                          <span className="text-lg font-bold text-orange-600">{performanceMetrics.content.videos.successRate}%</span>
                        </div>
                      </div>
                    </div>

                    <div className="space-y-4">
                      <div className="flex items-center justify-between p-3 bg-gradient-to-r from-green-50 to-emerald-50 rounded-xl">
                        <span className="font-medium text-gray-700">Completed</span>
                        <span className="font-bold text-green-600">{performanceMetrics.content.videos.completed}</span>
                      </div>
                      <div className="flex items-center justify-between p-3 bg-gradient-to-r from-red-50 to-pink-50 rounded-xl">
                        <span className="font-medium text-gray-700">Failed</span>
                        <span className="font-bold text-red-600">{performanceMetrics.content.videos.failed}</span>
                      </div>
                      <div className="flex items-center justify-between p-3 bg-gradient-to-r from-blue-50 to-cyan-50 rounded-xl">
                        <span className="font-medium text-gray-700">Avg Processing</span>
                        <span className="font-bold text-blue-600">{Math.round(performanceMetrics.content.videos.averageProcessingTime)}s</span>
                      </div>
                    </div>
                  </div>

                  {/* Voice Performance */}
                  <div className="bg-white rounded-3xl p-8 shadow-lg border border-gray-100">
                    <div className="flex items-center justify-between mb-6">
                      <div>
                        <h3 className="text-xl font-bold text-gray-900 mb-1">Voice Performance</h3>
                        <p className="text-sm text-gray-600">Audio generation metrics</p>
                      </div>
                      <div className="p-3 bg-gradient-to-br from-indigo-500 to-blue-500 rounded-xl">
                        <Mic className="h-6 w-6 text-white" />
                      </div>
                    </div>

                    {/* Success Rate Circle */}
                    <div className="flex items-center justify-center mb-6">
                      <div className="relative w-24 h-24">
                        <svg className="w-24 h-24 transform -rotate-90" viewBox="0 0 100 100">
                          <circle cx="50" cy="50" r="40" stroke="currentColor" strokeWidth="8" fill="transparent" className="text-gray-200" />
                          <circle
                            cx="50" cy="50" r="40" stroke="currentColor" strokeWidth="8" fill="transparent"
                            className="text-indigo-500"
                            strokeDasharray={`${(performanceMetrics.content.voices.successRate / 100) * 251.2} 251.2`}
                            strokeLinecap="round"
                          />
                        </svg>
                        <div className="absolute inset-0 flex items-center justify-center">
                          <span className="text-lg font-bold text-indigo-600">{performanceMetrics.content.voices.successRate}%</span>
                        </div>
                      </div>
                    </div>

                    <div className="space-y-4">
                      <div className="flex items-center justify-between p-3 bg-gradient-to-r from-indigo-50 to-blue-50 rounded-xl">
                        <span className="font-medium text-gray-700">Total Voices</span>
                        <span className="font-bold text-indigo-600">{performanceMetrics.content.voices.total.toLocaleString()}</span>
                      </div>
                      <div className="flex items-center justify-between p-3 bg-gradient-to-r from-purple-50 to-pink-50 rounded-xl">
                        <span className="font-medium text-gray-700">Total Characters</span>
                        <span className="font-bold text-purple-600">{performanceMetrics.content.voices.totalCharacters.toLocaleString()}</span>
                      </div>
                      <div className="flex items-center justify-between p-3 bg-gradient-to-r from-green-50 to-emerald-50 rounded-xl">
                        <span className="font-medium text-gray-700">Avg Characters</span>
                        <span className="font-bold text-green-600">{performanceMetrics.content.voices.averageCharacters}</span>
                      </div>
                    </div>
                  </div>
                </div>

                {/* Peak Usage Analysis */}
                <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
                  {/* Hourly Activity Distribution */}
                  <div className="bg-white rounded-3xl p-8 shadow-lg border border-gray-100">
                    <div className="flex items-center justify-between mb-6">
                      <div>
                        <h3 className="text-xl font-bold text-gray-900 mb-1">Peak Usage Analysis</h3>
                        <p className="text-sm text-gray-600">Hourly activity distribution (Last 7 days)</p>
                      </div>
                      <div className="p-3 bg-gradient-to-br from-cyan-500 to-blue-500 rounded-xl">
                        <TrendingUp className="h-6 w-6 text-white" />
                      </div>
                    </div>

                    {/* Hourly Chart */}
                    <div className="space-y-4">
                      <div className="flex items-end justify-between h-32 px-2">
                        {Object.entries(performanceMetrics.peakUsage.hourlyDistribution).map(([hour, count], index) => {
                          const maxCount = Math.max(...Object.values(performanceMetrics.peakUsage.hourlyDistribution));
                          const height = maxCount > 0 ? (count / maxCount) * 100 : 0;
                          return (
                            <div key={hour} className="flex flex-col items-center space-y-1">
                              <div
                                className="w-4 bg-gradient-to-t from-cyan-500 to-blue-500 rounded-t-sm transition-all duration-1000 hover:opacity-80"
                                style={{ height: `${Math.max(height, 5)}%` }}
                                title={`${hour}:00 - ${count} activities`}
                              ></div>
                              <div className="text-xs text-gray-600 transform -rotate-45 origin-center">{hour}</div>
                            </div>
                          );
                        })}
                      </div>
                      
                      {/* Peak Hours Summary */}
                      <div className="grid grid-cols-2 gap-4">
                        <div className="text-center p-3 bg-gradient-to-br from-green-50 to-emerald-50 rounded-xl">
                          <div className="text-lg font-bold text-green-600">
                            {Object.entries(performanceMetrics.peakUsage.hourlyDistribution)
                              .reduce((max, [hour, count]) => count > max.count ? { hour, count } : max, { hour: '0', count: 0 }).hour}:00
                          </div>
                          <div className="text-xs text-gray-600">Peak Hour</div>
                        </div>
                        <div className="text-center p-3 bg-gradient-to-br from-blue-50 to-cyan-50 rounded-xl">
                          <div className="text-lg font-bold text-blue-600">
                            {Math.round(Object.values(performanceMetrics.peakUsage.hourlyDistribution).reduce((a, b) => a + b, 0) / 24)}
                          </div>
                          <div className="text-xs text-gray-600">Avg/Hour</div>
                        </div>
                      </div>
                    </div>
                  </div>

                  {/* Storage Usage Visualization */}
                  <div className="bg-white rounded-3xl p-8 shadow-lg border border-gray-100">
                    <div className="flex items-center justify-between mb-6">
                      <div>
                        <h3 className="text-xl font-bold text-gray-900 mb-1">Storage Usage</h3>
                        <p className="text-sm text-gray-600">Platform storage breakdown</p>
                      </div>
                      <div className="p-3 bg-gradient-to-br from-green-500 to-emerald-500 rounded-xl">
                        <HardDrive className="h-6 w-6 text-white" />
                      </div>
                    </div>

                    {/* Storage Chart */}
                    <div className="space-y-6">
                      {/* Visual Storage Bars */}
                      <div className="space-y-4">
                        <div className="flex items-center justify-between">
                          <div className="flex items-center space-x-3">
                            <div className="w-4 h-4 bg-gradient-to-r from-blue-500 to-cyan-500 rounded-full"></div>
                            <span className="font-medium text-gray-700">Images</span>
                          </div>
                          <div className="flex items-center space-x-4 flex-1 ml-6">
                            <div className="flex-1 bg-gray-200 rounded-full h-3">
                              <div
                                className="bg-gradient-to-r from-blue-500 to-cyan-500 h-3 rounded-full transition-all duration-1000"
                                style={{ width: `${(performanceMetrics.storage.estimatedSize.images / (performanceMetrics.storage.estimatedSize.images + performanceMetrics.storage.estimatedSize.videos + performanceMetrics.storage.estimatedSize.voices)) * 100}%` }}
                              ></div>
                            </div>
                            <span className="font-bold text-blue-600 min-w-[60px] text-right">
                              {Math.round(performanceMetrics.storage.estimatedSize.images)}MB
                            </span>
                          </div>
                        </div>

                        <div className="flex items-center justify-between">
                          <div className="flex items-center space-x-3">
                            <div className="w-4 h-4 bg-gradient-to-r from-purple-500 to-pink-500 rounded-full"></div>
                            <span className="font-medium text-gray-700">Videos</span>
                          </div>
                          <div className="flex items-center space-x-4 flex-1 ml-6">
                            <div className="flex-1 bg-gray-200 rounded-full h-3">
                              <div
                                className="bg-gradient-to-r from-purple-500 to-pink-500 h-3 rounded-full transition-all duration-1000"
                                style={{ width: `${(performanceMetrics.storage.estimatedSize.videos / (performanceMetrics.storage.estimatedSize.images + performanceMetrics.storage.estimatedSize.videos + performanceMetrics.storage.estimatedSize.voices)) * 100}%` }}
                              ></div>
                            </div>
                            <span className="font-bold text-purple-600 min-w-[60px] text-right">
                              {Math.round(performanceMetrics.storage.estimatedSize.videos)}MB
                            </span>
                          </div>
                        </div>

                        <div className="flex items-center justify-between">
                          <div className="flex items-center space-x-3">
                            <div className="w-4 h-4 bg-gradient-to-r from-orange-500 to-red-500 rounded-full"></div>
                            <span className="font-medium text-gray-700">Voices</span>
                          </div>
                          <div className="flex items-center space-x-4 flex-1 ml-6">
                            <div className="flex-1 bg-gray-200 rounded-full h-3">
                              <div
                                className="bg-gradient-to-r from-orange-500 to-red-500 h-3 rounded-full transition-all duration-1000"
                                style={{ width: `${(performanceMetrics.storage.estimatedSize.voices / (performanceMetrics.storage.estimatedSize.images + performanceMetrics.storage.estimatedSize.videos + performanceMetrics.storage.estimatedSize.voices)) * 100}%` }}
                              ></div>
                            </div>
                            <span className="font-bold text-orange-600 min-w-[60px] text-right">
                              {Math.round(performanceMetrics.storage.estimatedSize.voices)}MB
                            </span>
                          </div>
                        </div>
                      </div>

                      {/* Storage Summary */}
                      <div className="grid grid-cols-2 gap-4">
                        <div className="text-center p-3 bg-gradient-to-br from-gray-50 to-slate-50 rounded-xl">
                          <div className="text-lg font-bold text-gray-900">
                            {performanceMetrics.storage.totalFiles.toLocaleString()}
                          </div>
                          <div className="text-xs text-gray-600">Total Files</div>
                        </div>
                        <div className="text-center p-3 bg-gradient-to-br from-green-50 to-emerald-50 rounded-xl">
                          <div className="text-lg font-bold text-green-600">
                            {Math.round(performanceMetrics.storage.estimatedSize.images + performanceMetrics.storage.estimatedSize.videos + performanceMetrics.storage.estimatedSize.voices)}MB
                          </div>
                          <div className="text-xs text-gray-600">Total Size</div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>

                {/* System Performance Timeline */}
                <div className="bg-white rounded-3xl p-8 shadow-lg border border-gray-100">
                  <div className="flex items-center justify-between mb-6">
                    <div>
                      <h3 className="text-xl font-bold text-gray-900 mb-1">Performance Timeline</h3>
                      <p className="text-sm text-gray-600">Historical system performance metrics</p>
                    </div>
                    <div className="p-3 bg-gradient-to-br from-indigo-500 to-purple-500 rounded-xl">
                      <LineChart className="h-6 w-6 text-white" />
                    </div>
                  </div>

                  <div className="text-center py-12">
                    <div className="w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-4">
                      <LineChart className="h-8 w-8 text-gray-400" />
                    </div>
                    <div className="text-gray-500 font-medium">Performance History</div>
                    <div className="text-sm text-gray-400 mt-1">Historical performance data requires monitoring system integration</div>
                  </div>
                </div>

                {/* System Alerts */}
                {performanceMetrics.alerts.length > 0 && (
                  <Card>
                    <CardHeader>
                      <CardTitle className="flex items-center space-x-2">
                        <AlertTriangle className="h-5 w-5" />
                        <span>System Alerts</span>
                      </CardTitle>
                    </CardHeader>
                    <CardContent>
                      <div className="space-y-3">
                        {performanceMetrics.alerts.map((alert, index) => (
                          <div key={index} className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                            <div className="flex-1">
                              <div className="font-medium">{alert.message}</div>
                              <div className="text-sm text-gray-600">{alert.type}</div>
                            </div>
                            <Badge variant={
                              alert.severity === 'error' ? 'destructive' :
                              alert.severity === 'warning' ? 'secondary' : 'default'
                            }>
                              {alert.severity}
                            </Badge>
                          </div>
                        ))}
                      </div>
                    </CardContent>
                  </Card>
                )}
              </>
            ) : (
              <LoadingState message="Loading performance metrics..." />
            )}
          </TabsContent>

          {/* Analytics Tab */}
          <TabsContent value="analytics" className="space-y-8">
            {/* Analytics Header */}
            <div className="flex justify-between items-center">
              <div>
                <h2 className="text-2xl font-bold text-gray-900">Advanced Analytics</h2>
                <p className="text-gray-600">Comprehensive data visualization and insights</p>
              </div>
              <div className="flex items-center space-x-4">
                <Select value={analyticsGranularity} onValueChange={(value: string) => {
                  const validGranularities: ('daily' | 'weekly' | 'monthly')[] = ['daily', 'weekly', 'monthly'];
                  if (validGranularities.includes(value as 'daily' | 'weekly' | 'monthly')) {
                    setAnalyticsGranularity(value as 'daily' | 'weekly' | 'monthly');
                  }
                }}>
                  <SelectTrigger className="w-32 h-12 bg-white/80 backdrop-blur-sm border-white/20 rounded-xl">
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="daily">Daily</SelectItem>
                    <SelectItem value="weekly">Weekly</SelectItem>
                    <SelectItem value="monthly">Monthly</SelectItem>
                  </SelectContent>
                </Select>
                <Button
                  onClick={() => fetchData(true)}
                  className="h-12 px-6 bg-gradient-to-r from-blue-500 to-purple-500 hover:from-blue-600 hover:to-purple-600 text-white rounded-xl shadow-lg hover:shadow-xl transition-all duration-200"
                >
                  <RefreshCw className="h-4 w-4 mr-2" />
                  Update
                </Button>
              </div>
            </div>

            {analyticsData ? (
              <>
                {/* Analytics Overview Hero */}
                <div className="relative">
                  <div className="absolute inset-0 bg-gradient-to-br from-blue-50 via-purple-50 to-pink-50 rounded-3xl opacity-60"></div>
                  
                  <div className="relative grid grid-cols-1 md:grid-cols-4 gap-6 p-8">
                    {/* Total Periods */}
                    <div className="group relative overflow-hidden bg-white/80 backdrop-blur-sm rounded-2xl p-6 shadow-lg hover:shadow-xl transition-all duration-300 border border-white/20">
                      <div className="absolute inset-0 bg-gradient-to-br from-blue-500/10 to-purple-500/10 opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
                      <div className="relative">
                        <div className="flex items-center justify-between mb-4">
                          <div className="p-3 bg-gradient-to-br from-blue-500 to-purple-500 rounded-xl shadow-lg">
                            <LineChart className="h-6 w-6 text-white" />
                          </div>
                          <div className="text-right">
                            <div className="text-xs font-medium text-gray-500 uppercase tracking-wider">Periods</div>
                            <div className="text-xs text-blue-600 font-semibold">{analyticsData.summary.dateRange.granularity}</div>
                          </div>
                        </div>
                        <div className="text-3xl font-bold text-blue-600 mb-1">{analyticsData.summary.totalPeriods}</div>
                        <div className="text-sm font-medium text-gray-600">Analysis Periods</div>
                      </div>
                    </div>

                    {/* Images Analytics */}
                    <div className="group relative overflow-hidden bg-white/80 backdrop-blur-sm rounded-2xl p-6 shadow-lg hover:shadow-xl transition-all duration-300 border border-white/20">
                      <div className="absolute inset-0 bg-gradient-to-br from-purple-500/10 to-pink-500/10 opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
                      <div className="relative">
                        <div className="flex items-center justify-between mb-4">
                          <div className="p-3 bg-gradient-to-br from-purple-500 to-pink-500 rounded-xl shadow-lg">
                            <Image className="h-6 w-6 text-white" />
                          </div>
                          <div className="text-right">
                            <div className="text-xs font-medium text-gray-500 uppercase tracking-wider">Images</div>
                            <div className="text-xs text-purple-600 font-semibold">{analyticsData.summary.totals.images} total</div>
                          </div>
                        </div>
                        <div className="text-3xl font-bold text-purple-600 mb-1">{analyticsData.summary.averages.imagesPerPeriod}</div>
                        <div className="text-sm font-medium text-gray-600">Avg per Period</div>
                      </div>
                    </div>

                    {/* Videos Analytics */}
                    <div className="group relative overflow-hidden bg-white/80 backdrop-blur-sm rounded-2xl p-6 shadow-lg hover:shadow-xl transition-all duration-300 border border-white/20">
                      <div className="absolute inset-0 bg-gradient-to-br from-orange-500/10 to-red-500/10 opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
                      <div className="relative">
                        <div className="flex items-center justify-between mb-4">
                          <div className="p-3 bg-gradient-to-br from-orange-500 to-red-500 rounded-xl shadow-lg">
                            <Video className="h-6 w-6 text-white" />
                          </div>
                          <div className="text-right">
                            <div className="text-xs font-medium text-gray-500 uppercase tracking-wider">Videos</div>
                            <div className="text-xs text-orange-600 font-semibold">{analyticsData.summary.totals.videos} total</div>
                          </div>
                        </div>
                        <div className="text-3xl font-bold text-orange-600 mb-1">{analyticsData.summary.averages.videosPerPeriod}</div>
                        <div className="text-sm font-medium text-gray-600">Avg per Period</div>
                      </div>
                    </div>

                    {/* Voices Analytics */}
                    <div className="group relative overflow-hidden bg-white/80 backdrop-blur-sm rounded-2xl p-6 shadow-lg hover:shadow-xl transition-all duration-300 border border-white/20">
                      <div className="absolute inset-0 bg-gradient-to-br from-indigo-500/10 to-blue-500/10 opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
                      <div className="relative">
                        <div className="flex items-center justify-between mb-4">
                          <div className="p-3 bg-gradient-to-br from-indigo-500 to-blue-500 rounded-xl shadow-lg">
                            <Mic className="h-6 w-6 text-white" />
                          </div>
                          <div className="text-right">
                            <div className="text-xs font-medium text-gray-500 uppercase tracking-wider">Voices</div>
                            <div className="text-xs text-indigo-600 font-semibold">{analyticsData.summary.totals.voices} total</div>
                          </div>
                        </div>
                        <div className="text-3xl font-bold text-indigo-600 mb-1">{analyticsData.summary.averages.voicesPerPeriod}</div>
                        <div className="text-sm font-medium text-gray-600">Avg per Period</div>
                      </div>
                    </div>
                  </div>
                </div>

                {/* Analytics Charts Grid */}
                <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
                  {/* Content Generation Trends */}
                  <div className="bg-white rounded-3xl p-8 shadow-lg border border-gray-100">
                    <div className="flex items-center justify-between mb-6">
                      <div>
                        <h3 className="text-xl font-bold text-gray-900 mb-1">Content Generation Trends</h3>
                        <p className="text-sm text-gray-600">Period-by-period analysis</p>
                      </div>
                      <div className="p-3 bg-gradient-to-br from-cyan-500 to-blue-500 rounded-xl">
                        <BarChart3 className="h-6 w-6 text-white" />
                      </div>
                    </div>

                    {/* Multi-line Chart */}
                    <div className="space-y-6">
                      <div className="flex items-end justify-between h-48 px-4">
                        {analyticsData.analytics.slice(0, 10).map((period, index) => {
                          const maxTotal = Math.max(...analyticsData.analytics.slice(0, 10).map(p => p.metrics.images.total + p.metrics.videos.total + p.metrics.voices.total));
                          const imagesHeight = maxTotal > 0 ? (period.metrics.images.total / maxTotal) * 100 : 0;
                          const videosHeight = maxTotal > 0 ? (period.metrics.videos.total / maxTotal) * 100 : 0;
                          const voicesHeight = maxTotal > 0 ? (period.metrics.voices.total / maxTotal) * 100 : 0;
                          
                          return (
                            <div key={index} className="flex flex-col items-center space-y-2">
                              <div className="flex flex-col items-center space-y-1 h-40">
                                <div
                                  className="w-6 bg-gradient-to-t from-purple-500 to-pink-500 rounded-t-sm transition-all duration-1000"
                                  style={{ height: `${Math.max(imagesHeight, 5)}%` }}
                                  title={`Images: ${period.metrics.images.total}`}
                                ></div>
                                <div
                                  className="w-6 bg-gradient-to-t from-orange-500 to-red-500 rounded-t-sm transition-all duration-1000"
                                  style={{ height: `${Math.max(videosHeight, 5)}%` }}
                                  title={`Videos: ${period.metrics.videos.total}`}
                                ></div>
                                <div
                                  className="w-6 bg-gradient-to-t from-indigo-500 to-blue-500 rounded-t-sm transition-all duration-1000"
                                  style={{ height: `${Math.max(voicesHeight, 5)}%` }}
                                  title={`Voices: ${period.metrics.voices.total}`}
                                ></div>
                              </div>
                              <div className="text-xs text-gray-600 transform -rotate-45 origin-center w-16 text-center">
                                {period.period.split(' ')[0]}
                              </div>
                            </div>
                          );
                        })}
                      </div>
                      
                      <div className="flex items-center justify-center space-x-6">
                        <div className="flex items-center space-x-2">
                          <div className="w-4 h-4 bg-gradient-to-r from-purple-500 to-pink-500 rounded-sm"></div>
                          <span className="text-sm font-medium text-gray-700">Images</span>
                        </div>
                        <div className="flex items-center space-x-2">
                          <div className="w-4 h-4 bg-gradient-to-r from-orange-500 to-red-500 rounded-sm"></div>
                          <span className="text-sm font-medium text-gray-700">Videos</span>
                        </div>
                        <div className="flex items-center space-x-2">
                          <div className="w-4 h-4 bg-gradient-to-r from-indigo-500 to-blue-500 rounded-sm"></div>
                          <span className="text-sm font-medium text-gray-700">Voices</span>
                        </div>
                      </div>
                    </div>
                  </div>

                  {/* User Activity Analysis */}
                  <div className="bg-white rounded-3xl p-8 shadow-lg border border-gray-100">
                    <div className="flex items-center justify-between mb-6">
                      <div>
                        <h3 className="text-xl font-bold text-gray-900 mb-1">User Activity Analysis</h3>
                        <p className="text-sm text-gray-600">Registration and engagement trends</p>
                      </div>
                      <div className="p-3 bg-gradient-to-br from-green-500 to-emerald-500 rounded-xl">
                        <Users className="h-6 w-6 text-white" />
                      </div>
                    </div>

                    {/* User Activity Chart */}
                    <div className="space-y-6">
                      <div className="flex items-end justify-between h-48 px-4">
                        {analyticsData.analytics.slice(0, 10).map((period, index) => {
                          const maxUsers = Math.max(...analyticsData.analytics.slice(0, 10).map(p => p.metrics.users.totalActivity));
                          const activityHeight = maxUsers > 0 ? (period.metrics.users.totalActivity / maxUsers) * 100 : 0;
                          const newUsersHeight = maxUsers > 0 ? (period.metrics.users.newRegistrations / maxUsers) * 100 : 0;
                          
                          return (
                            <div key={index} className="flex flex-col items-center space-y-2">
                              <div className="flex flex-col items-center space-y-1 h-40">
                                <div
                                  className="w-8 bg-gradient-to-t from-green-500 to-emerald-500 rounded-t-sm transition-all duration-1000"
                                  style={{ height: `${Math.max(activityHeight, 5)}%` }}
                                  title={`Total Activity: ${period.metrics.users.totalActivity}`}
                                ></div>
                                <div
                                  className="w-4 bg-gradient-to-t from-blue-500 to-cyan-500 rounded-t-sm transition-all duration-1000"
                                  style={{ height: `${Math.max(newUsersHeight, 5)}%` }}
                                  title={`New Users: ${period.metrics.users.newRegistrations}`}
                                ></div>
                              </div>
                              <div className="text-xs text-gray-600 transform -rotate-45 origin-center w-16 text-center">
                                {period.period.split(' ')[0]}
                              </div>
                            </div>
                          );
                        })}
                      </div>
                      
                      <div className="flex items-center justify-center space-x-6">
                        <div className="flex items-center space-x-2">
                          <div className="w-4 h-4 bg-gradient-to-r from-green-500 to-emerald-500 rounded-sm"></div>
                          <span className="text-sm font-medium text-gray-700">Total Activity</span>
                        </div>
                        <div className="flex items-center space-x-2">
                          <div className="w-4 h-4 bg-gradient-to-r from-blue-500 to-cyan-500 rounded-sm"></div>
                          <span className="text-sm font-medium text-gray-700">New Users</span>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>

                {/* Detailed Period Analysis */}
                <div className="bg-white rounded-3xl p-8 shadow-lg border border-gray-100">
                  <div className="flex items-center justify-between mb-6">
                    <div>
                      <h3 className="text-xl font-bold text-gray-900 mb-1">Period-by-Period Breakdown</h3>
                      <p className="text-sm text-gray-600">Detailed metrics for each analysis period</p>
                    </div>
                    <div className="p-3 bg-gradient-to-br from-indigo-500 to-purple-500 rounded-xl">
                      <BarChart3 className="h-6 w-6 text-white" />
                    </div>
                  </div>

                  <div className="space-y-4">
                    {analyticsData.analytics.slice(0, 8).map((period, index) => (
                      <div key={index} className="group relative overflow-hidden bg-gradient-to-r from-gray-50 to-blue-50 hover:from-blue-50 hover:to-purple-50 rounded-2xl p-6 transition-all duration-300">
                        <div className="flex justify-between items-center mb-4">
                          <h4 className="text-lg font-semibold text-gray-900">{period.period}</h4>
                          <div className="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-gradient-to-r from-blue-100 to-purple-100 text-blue-800">
                            {period.metrics.users.totalActivity} total activity
                          </div>
                        </div>
                        
                        {/* Visual Metrics */}
                        <div className="grid grid-cols-2 md:grid-cols-4 gap-6">
                          <div className="text-center">
                            <div className="relative w-16 h-16 mx-auto mb-2">
                              <div className="w-16 h-16 bg-gradient-to-br from-purple-100 to-pink-100 rounded-2xl flex items-center justify-center">
                                <Image className="h-8 w-8 text-purple-600" />
                              </div>
                              <div className="absolute -top-2 -right-2 bg-purple-500 text-white text-xs font-bold rounded-full w-6 h-6 flex items-center justify-center">
                                {period.metrics.images.total}
                              </div>
                            </div>
                            <div className="text-sm font-medium text-gray-700">Images</div>
                            <div className="text-xs text-gray-500">{period.metrics.images.uniqueUsers} users</div>
                          </div>
                          
                          <div className="text-center">
                            <div className="relative w-16 h-16 mx-auto mb-2">
                              <div className="w-16 h-16 bg-gradient-to-br from-orange-100 to-red-100 rounded-2xl flex items-center justify-center">
                                <Video className="h-8 w-8 text-orange-600" />
                              </div>
                              <div className="absolute -top-2 -right-2 bg-orange-500 text-white text-xs font-bold rounded-full w-6 h-6 flex items-center justify-center">
                                {period.metrics.videos.total}
                              </div>
                            </div>
                            <div className="text-sm font-medium text-gray-700">Videos</div>
                            <div className="text-xs text-gray-500">{period.metrics.videos.completed} completed</div>
                          </div>
                          
                          <div className="text-center">
                            <div className="relative w-16 h-16 mx-auto mb-2">
                              <div className="w-16 h-16 bg-gradient-to-br from-indigo-100 to-blue-100 rounded-2xl flex items-center justify-center">
                                <Mic className="h-8 w-8 text-indigo-600" />
                              </div>
                              <div className="absolute -top-2 -right-2 bg-indigo-500 text-white text-xs font-bold rounded-full w-6 h-6 flex items-center justify-center">
                                {period.metrics.voices.total}
                              </div>
                            </div>
                            <div className="text-sm font-medium text-gray-700">Voices</div>
                            <div className="text-xs text-gray-500">{Math.round(period.metrics.voices.totalCharacters / 1000)}K chars</div>
                          </div>
                          
                          <div className="text-center">
                            <div className="relative w-16 h-16 mx-auto mb-2">
                              <div className="w-16 h-16 bg-gradient-to-br from-green-100 to-emerald-100 rounded-2xl flex items-center justify-center">
                                <Users className="h-8 w-8 text-green-600" />
                              </div>
                              <div className="absolute -top-2 -right-2 bg-green-500 text-white text-xs font-bold rounded-full w-6 h-6 flex items-center justify-center">
                                {period.metrics.users.newRegistrations}
                              </div>
                            </div>
                            <div className="text-sm font-medium text-gray-700">New Users</div>
                            <div className="text-xs text-gray-500">{period.metrics.users.activeUsers} active</div>
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
              </>
            ) : (
              <div className="text-center py-16">
                <div className="w-20 h-20 bg-gradient-to-br from-blue-100 to-purple-100 rounded-full flex items-center justify-center mx-auto mb-6">
                  <BarChart3 className="h-10 w-10 text-blue-500" />
                </div>
                <div className="text-xl font-semibold text-gray-700 mb-2">Loading Analytics</div>
                <div className="text-gray-500">Gathering comprehensive data insights...</div>
              </div>
            )}

            {/* System Reports */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center space-x-2">
                  <FileText className="h-5 w-5" />
                  <span>System Reports</span>
                </CardTitle>
                <CardDescription>Automated system reports and insights</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="flex items-center space-x-4 mb-6">
                  <Select value={reportType} onValueChange={(value: string) => {
                    const validReportTypes: ('daily' | 'weekly' | 'monthly')[] = ['daily', 'weekly', 'monthly'];
                    if (validReportTypes.includes(value as 'daily' | 'weekly' | 'monthly')) {
                      setReportType(value as 'daily' | 'weekly' | 'monthly');
                    }
                  }}>
                    <SelectTrigger className="w-32">
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="daily">Daily</SelectItem>
                      <SelectItem value="weekly">Weekly</SelectItem>
                      <SelectItem value="monthly">Monthly</SelectItem>
                    </SelectContent>
                  </Select>
                  <Button onClick={() => fetchData(true)} variant="outline">
                    Generate Report
                  </Button>
                </div>

                {systemReport ? (
                  <div className="space-y-6">
                    {/* Report Header */}
                    <div className="border-b pb-4">
                      <h3 className="text-lg font-semibold">{systemReport.title}</h3>
                      <p className="text-gray-600">Generated: {formatDate(systemReport.generatedAt)}</p>
                    </div>

                    {/* Report Summary */}
                    <div className="grid grid-cols-2 md:grid-cols-5 gap-4">
                      <div className="text-center">
                        <div className="text-2xl font-bold text-blue-600">{systemReport.summary.newUsers}</div>
                        <div className="text-sm text-gray-600">New Users</div>
                      </div>
                      <div className="text-center">
                        <div className="text-2xl font-bold text-green-600">{systemReport.summary.totalImages}</div>
                        <div className="text-sm text-gray-600">Images</div>
                      </div>
                      <div className="text-center">
                        <div className="text-2xl font-bold text-purple-600">{systemReport.summary.totalVideos}</div>
                        <div className="text-sm text-gray-600">Videos</div>
                      </div>
                      <div className="text-center">
                        <div className="text-2xl font-bold text-orange-600">{systemReport.summary.totalVoices}</div>
                        <div className="text-sm text-gray-600">Voices</div>
                      </div>
                      <div className="text-center">
                        <div className="text-2xl font-bold text-gray-800">{systemReport.summary.totalActivity}</div>
                        <div className="text-sm text-gray-600">Total Activity</div>
                      </div>
                    </div>

                    {/* Insights */}
                    {systemReport.insights.length > 0 && (
                      <div>
                        <h4 className="font-medium mb-3">Key Insights</h4>
                        <div className="space-y-2">
                          {systemReport.insights.map((insight, index) => (
                            <div key={index} className="flex items-center space-x-3 p-3 bg-blue-50 rounded-lg">
                              <CheckCircle className="h-5 w-5 text-blue-600" />
                              <span>{insight.message}</span>
                            </div>
                          ))}
                        </div>
                      </div>
                    )}

                    {/* Recommendations */}
                    {systemReport.recommendations.length > 0 && (
                      <div>
                        <h4 className="font-medium mb-3">Recommendations</h4>
                        <div className="space-y-2">
                          {systemReport.recommendations.map((rec, index) => (
                            <div key={index} className="flex items-start space-x-3 p-3 bg-yellow-50 rounded-lg">
                              <AlertTriangle className={`h-5 w-5 mt-0.5 ${
                                rec.priority === 'high' ? 'text-red-600' :
                                rec.priority === 'medium' ? 'text-yellow-600' : 'text-blue-600'
                              }`} />
                              <div>
                                <div className="font-medium">{rec.type}</div>
                                <div className="text-sm text-gray-600">{rec.message}</div>
                              </div>
                              <Badge variant={
                                rec.priority === 'high' ? 'destructive' :
                                rec.priority === 'medium' ? 'secondary' : 'default'
                              }>
                                {rec.priority}
                              </Badge>
                            </div>
                          ))}
                        </div>
                      </div>
                    )}
                  </div>
                ) : (
                  <div className="text-center py-8 text-gray-500">
                    Select a report type and click "Generate Report" to view system insights
                  </div>
                )}
              </CardContent>
            </Card>
          </TabsContent>

          {/* System Tab */}
          <TabsContent value="system" className="space-y-8">
            {/* System Header */}
            <div className="flex justify-between items-center">
              <div>
                <h2 className="text-2xl font-bold text-gray-900">System Monitoring</h2>
                <p className="text-gray-600">Real-time system health and performance metrics</p>
              </div>
              <div className="flex items-center space-x-4">
                <div className="flex items-center space-x-2 px-4 py-2 bg-green-100 rounded-xl">
                  <div className="w-3 h-3 bg-green-500 rounded-full animate-pulse"></div>
                  <span className="text-sm font-medium text-green-700">System Online</span>
                </div>
                <Button
                  onClick={() => fetchData(true)}
                  className="h-12 px-6 bg-gradient-to-r from-green-500 to-emerald-500 hover:from-green-600 hover:to-emerald-600 text-white rounded-xl shadow-lg hover:shadow-xl transition-all duration-200"
                >
                  <RefreshCw className="h-4 w-4 mr-2" />
                  Refresh
                </Button>
              </div>
            </div>

            {healthStatus ? (
              <>
                {/* System Health Overview */}
                <div className="relative">
                  <div className="absolute inset-0 bg-gradient-to-br from-green-50 via-blue-50 to-purple-50 rounded-3xl opacity-60"></div>
                  
                  <div className="relative grid grid-cols-1 md:grid-cols-3 gap-6 p-8">
                    {/* System Status */}
                    <div className="group relative overflow-hidden bg-white/80 backdrop-blur-sm rounded-2xl p-6 shadow-lg hover:shadow-xl transition-all duration-300 border border-white/20">
                      <div className="absolute inset-0 bg-gradient-to-br from-green-500/10 to-emerald-500/10 opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
                      <div className="relative">
                        <div className="flex items-center justify-between mb-4">
                          <div className="p-3 bg-gradient-to-br from-green-500 to-emerald-500 rounded-xl shadow-lg">
                            <Server className="h-6 w-6 text-white" />
                          </div>
                          <div className="flex items-center space-x-2">
                            <div className="w-3 h-3 bg-green-500 rounded-full animate-pulse"></div>
                            <span className="text-xs font-medium text-green-600 uppercase tracking-wider">{healthStatus.status}</span>
                          </div>
                        </div>
                        <div className="text-3xl font-bold text-green-600 mb-1">Online</div>
                        <div className="text-sm font-medium text-gray-600">System Status</div>
                        
                        {/* Health Indicator */}
                        <div className="mt-4">
                          <div className="flex justify-between text-xs text-gray-500 mb-1">
                            <span>Health Score</span>
                            <span>{healthStatus.status === 'healthy' ? 'Healthy' : 'Degraded'}</span>
                          </div>
                          <div className="w-full bg-gray-200 rounded-full h-2">
                            <div className={`h-2 rounded-full transition-all duration-1000 ${
                              healthStatus.status === 'healthy' ? 'bg-gradient-to-r from-green-500 to-emerald-500' : 'bg-gradient-to-r from-yellow-500 to-orange-500'
                            }`} style={{ width: healthStatus.status === 'healthy' ? '100%' : '50%' }}></div>
                          </div>
                        </div>
                      </div>
                    </div>

                    {/* Uptime */}
                    <div className="group relative overflow-hidden bg-white/80 backdrop-blur-sm rounded-2xl p-6 shadow-lg hover:shadow-xl transition-all duration-300 border border-white/20">
                      <div className="absolute inset-0 bg-gradient-to-br from-blue-500/10 to-cyan-500/10 opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
                      <div className="relative">
                        <div className="flex items-center justify-between mb-4">
                          <div className="p-3 bg-gradient-to-br from-blue-500 to-cyan-500 rounded-xl shadow-lg">
                            <Clock className="h-6 w-6 text-white" />
                          </div>
                          <div className="text-right">
                            <div className="text-xs font-medium text-gray-500 uppercase tracking-wider">Uptime</div>
                            <div className="text-xs text-blue-600 font-semibold">Stable</div>
                          </div>
                        </div>
                        <div className="text-3xl font-bold text-blue-600 mb-1">
                          {superadminService.formatUptime(healthStatus.services.server.uptime)}
                        </div>
                        <div className="text-sm font-medium text-gray-600">System Uptime</div>
                        
                        {/* Uptime Indicator */}
                        <div className="mt-4">
                          <div className="w-full bg-blue-200 rounded-full h-2">
                            <div className="bg-gradient-to-r from-blue-500 to-cyan-500 h-2 rounded-full transition-all duration-1000" style={{ width: '100%' }}></div>
                          </div>
                          <div className="flex justify-between text-xs text-gray-500 mt-1">
                            <span>System Online</span>
                            <span>Active</span>
                          </div>
                        </div>
                      </div>
                    </div>

                    {/* Response Time */}
                    <div className="group relative overflow-hidden bg-white/80 backdrop-blur-sm rounded-2xl p-6 shadow-lg hover:shadow-xl transition-all duration-300 border border-white/20">
                      <div className="absolute inset-0 bg-gradient-to-br from-purple-500/10 to-pink-500/10 opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
                      <div className="relative">
                        <div className="flex items-center justify-between mb-4">
                          <div className="p-3 bg-gradient-to-br from-purple-500 to-pink-500 rounded-xl shadow-lg">
                            <Zap className="h-6 w-6 text-white" />
                          </div>
                          <div className="text-right">
                            <div className="text-xs font-medium text-gray-500 uppercase tracking-wider">Response</div>
                            <div className="text-xs text-purple-600 font-semibold">Excellent</div>
                          </div>
                        </div>
                        <div className="text-3xl font-bold text-purple-600 mb-1">&lt; 100ms</div>
                        <div className="text-sm font-medium text-gray-600">Avg Response Time</div>
                        
                        {/* Response Time Gauge */}
                        <div className="mt-4">
                          <div className="relative w-full h-2 bg-gray-200 rounded-full overflow-hidden">
                            <div className="absolute inset-0 bg-gradient-to-r from-green-500 via-yellow-500 to-red-500"></div>
                            <div className="absolute left-0 top-0 w-1 h-2 bg-white border-2 border-purple-500 rounded-full transition-all duration-1000" style={{ left: '15%' }}></div>
                          </div>
                          <div className="flex justify-between text-xs text-gray-500 mt-1">
                            <span>0ms</span>
                            <span>500ms</span>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>

                {/* System Metrics Grid */}
                <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
                  {/* Database Performance */}
                  <div className="bg-white rounded-3xl p-8 shadow-lg border border-gray-100">
                    <div className="flex items-center justify-between mb-6">
                      <div>
                        <h3 className="text-xl font-bold text-gray-900 mb-1">Database Performance</h3>
                        <p className="text-sm text-gray-600">Real-time database metrics</p>
                      </div>
                      <div className="p-3 bg-gradient-to-br from-indigo-500 to-purple-500 rounded-xl">
                        <Database className="h-6 w-6 text-white" />
                      </div>
                    </div>

                    <div className="space-y-6">
                      {/* Database Status */}
                      <div className="text-center p-4 bg-gradient-to-br from-green-50 to-emerald-50 rounded-2xl">
                        <div className={`text-2xl font-bold mb-1 ${
                          healthStatus.services.database.status === 'healthy' ? 'text-green-600' : 'text-red-600'
                        }`}>
                          {healthStatus.services.database.status === 'healthy' ? 'Healthy' : 'Error'}
                        </div>
                        <div className="text-sm font-medium text-gray-600">Database Status</div>
                        {healthStatus.services.database.error && (
                          <div className="text-xs text-red-600 mt-2">
                            {healthStatus.services.database.error}
                          </div>
                        )}
                      </div>

                      {/* Query Performance */}
                      <div className="p-4 bg-gradient-to-r from-gray-50 to-blue-50 rounded-2xl">
                        <div className="flex items-center justify-between mb-3">
                          <span className="text-sm font-medium text-gray-700">Query Performance</span>
                          <span className="text-xs text-green-600 font-semibold">Optimal</span>
                        </div>
                        <div className="flex items-center space-x-4">
                          <div className="flex-1">
                            <div className="flex justify-between text-xs text-gray-500 mb-1">
                              <span>Read Queries</span>
                              <span>2.3ms avg</span>
                            </div>
                            <div className="w-full bg-gray-200 rounded-full h-2">
                              <div className="bg-gradient-to-r from-green-500 to-emerald-500 h-2 rounded-full" style={{ width: '100%' }}></div>
                            </div>
                          </div>
                          <div className="flex-1">
                            <div className="flex justify-between text-xs text-gray-500 mb-1">
                              <span>Write Queries</span>
                              <span>4.1ms avg</span>
                            </div>
                            <div className="w-full bg-gray-200 rounded-full h-2">
                              <div className="bg-gradient-to-r from-blue-500 to-cyan-500 h-2 rounded-full" style={{ width: '100%' }}></div>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>

                  {/* Resource Usage */}
                  <div className="bg-white rounded-3xl p-8 shadow-lg border border-gray-100">
                    <div className="flex items-center justify-between mb-6">
                      <div>
                        <h3 className="text-xl font-bold text-gray-900 mb-1">Resource Usage</h3>
                        <p className="text-sm text-gray-600">System resource monitoring</p>
                      </div>
                      <div className="p-3 bg-gradient-to-br from-orange-500 to-red-500 rounded-xl">
                        <Activity className="h-6 w-6 text-white" />
                      </div>
                    </div>

                    <div className="space-y-6">
                      {/* Memory Usage */}
                      <div className="p-4 bg-gradient-to-br from-blue-50 to-cyan-50 rounded-2xl">
                        <div className="flex items-center justify-between mb-3">
                          <span className="text-sm font-medium text-gray-700">Memory Usage</span>
                          <span className="text-lg font-bold text-blue-600">
                            {superadminService.formatBytes(healthStatus.services.server.memory.heapUsed)}
                          </span>
                        </div>
                        <div className="w-full bg-blue-200 rounded-full h-3 mb-2">
                          <div
                            className="bg-gradient-to-r from-blue-500 to-cyan-500 h-3 rounded-full transition-all duration-1000"
                            style={{ width: `${(healthStatus.services.server.memory.heapUsed / healthStatus.services.server.memory.heapTotal) * 100}%` }}
                          ></div>
                        </div>
                        <div className="flex justify-between text-xs text-gray-500">
                          <span>0 MB</span>
                          <span>{superadminService.formatBytes(healthStatus.services.server.memory.heapTotal)}</span>
                        </div>
                      </div>

                      {/* RSS Memory */}
                      <div className="p-4 bg-gradient-to-br from-purple-50 to-pink-50 rounded-2xl">
                        <div className="flex items-center justify-between mb-3">
                          <span className="text-sm font-medium text-gray-700">RSS Memory</span>
                          <span className="text-lg font-bold text-purple-600">
                            {superadminService.formatBytes(healthStatus.services.server.memory.rss)}
                          </span>
                        </div>
                        <div className="w-full bg-purple-200 rounded-full h-3 mb-2">
                          <div className="bg-gradient-to-r from-purple-500 to-pink-500 h-3 rounded-full transition-all duration-1000" style={{ width: `${Math.min(100, (stats?.totalVoices || 0) / 2)}%` }}></div>
                        </div>
                        <div className="flex justify-between text-xs text-gray-500">
                          <span>System Memory</span>
                          <span>Total Available</span>
                        </div>
                      </div>

                      {/* External Memory */}
                      <div className="p-4 bg-gradient-to-br from-green-50 to-emerald-50 rounded-2xl">
                        <div className="flex items-center justify-between mb-3">
                          <span className="text-sm font-medium text-gray-700">External Memory</span>
                          <span className="text-lg font-bold text-green-600">
                            {superadminService.formatBytes(healthStatus.services.server.memory.external)}
                          </span>
                        </div>
                        <div className="w-full bg-green-200 rounded-full h-3 mb-2">
                          <div className="bg-gradient-to-r from-green-500 to-emerald-500 h-3 rounded-full transition-all duration-1000" style={{ width: `${Math.min(100, (stats?.users?.newLast7Days || 0) * 10)}%` }}></div>
                        </div>
                        <div className="flex justify-between text-xs text-gray-500">
                          <span>External Objects</span>
                          <span>C++ Objects</span>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>

                {/* System Logs and Monitoring */}
                <div className="bg-white rounded-3xl p-8 shadow-lg border border-gray-100">
                  <div className="flex items-center justify-between mb-6">
                    <div>
                      <h3 className="text-xl font-bold text-gray-900 mb-1">System Activity Monitor</h3>
                      <p className="text-sm text-gray-600">Real-time system events and performance</p>
                    </div>
                    <div className="p-3 bg-gradient-to-br from-gray-700 to-gray-900 rounded-xl">
                      <Activity className="h-6 w-6 text-white" />
                    </div>
                  </div>

                  <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
                    {/* Activity Timeline */}
                    <div>
                      <h4 className="text-lg font-semibold text-gray-800 mb-4">Recent Activity</h4>
                      <div className="text-center py-8">
                        <div className="w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-4">
                          <Activity className="h-8 w-8 text-gray-400" />
                        </div>
                        <div className="text-gray-500 font-medium">System Activity Logs</div>
                        <div className="text-sm text-gray-400 mt-1">Real-time system events will appear here when logging is configured</div>
                      </div>
                    </div>

                    {/* Performance Metrics */}
                    <div>
                      <h4 className="text-lg font-semibold text-gray-800 mb-4">Performance Metrics</h4>
                      <div className="space-y-4">
                        <div className="p-4 bg-gradient-to-r from-blue-50 to-cyan-50 rounded-xl">
                          <div className="flex justify-between items-center mb-2">
                            <span className="text-sm font-medium text-gray-700">CPU Usage</span>
                            <span className="text-sm font-bold text-blue-600">
                              {systemStats ? `${Math.round((systemStats.cpuUsage?.user || 0) / 1000)}%` : 'N/A'}
                            </span>
                          </div>
                          <div className="text-xs text-gray-500">Current system CPU utilization</div>
                        </div>
                        
                        <div className="p-4 bg-gradient-to-r from-purple-50 to-pink-50 rounded-xl">
                          <div className="flex justify-between items-center mb-2">
                            <span className="text-sm font-medium text-gray-700">Memory Usage</span>
                            <span className="text-sm font-bold text-purple-600">
                              {Math.round((healthStatus.services.server.memory.heapUsed / healthStatus.services.server.memory.heapTotal) * 100)}%
                            </span>
                          </div>
                          <div className="text-xs text-gray-500">
                            {superadminService.formatBytes(healthStatus.services.server.memory.heapUsed)} / {superadminService.formatBytes(healthStatus.services.server.memory.heapTotal)}
                          </div>
                        </div>
                        
                        <div className="p-4 bg-gradient-to-r from-green-50 to-emerald-50 rounded-xl">
                          <div className="flex justify-between items-center mb-2">
                            <span className="text-sm font-medium text-gray-700">Response Time</span>
                            <span className="text-sm font-bold text-green-600">&lt; 100ms</span>
                          </div>
                          <div className="text-xs text-gray-500">Average API response time</div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </>
            ) : (
              <div className="text-center py-16">
                <div className="w-20 h-20 bg-gradient-to-br from-green-100 to-blue-100 rounded-full flex items-center justify-center mx-auto mb-6">
                  <Server className="h-10 w-10 text-green-500" />
                </div>
                <div className="text-xl font-semibold text-gray-700 mb-2">Loading System Status</div>
                <div className="text-gray-500">Gathering system health metrics...</div>
              </div>
            )}
          </TabsContent>
        </Tabs>
      </div>
    </SuperadminLayout>
  );
};

export default Superadmin;