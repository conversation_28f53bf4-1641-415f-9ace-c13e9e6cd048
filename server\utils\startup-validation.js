/**
 * Startup validation utilities for VibeNecto server
 * Validates environment variables and configuration before server starts
 */

const { logger } = require('./logger');

/**
 * Validates all required environment variables
 * @returns {Object} - Validation result with success status and missing variables
 */
function validateEnvironmentVariables() {
  const requiredVars = [
    'AWS_REGION',
    'AWS_ACCESS_KEY_ID',
    'AWS_SECRET_ACCESS_KEY',
    'AWS_S3_BUCKET_NAME',
    'SUPABASE_URL',
    'SUPABASE_SERVICE_KEY',
    'SUPABASE_JWT_SECRET'
  ];

  const missing = [];
  const warnings = [];

  // Check required variables
  for (const varName of requiredVars) {
    if (!process.env[varName]) {
      missing.push(varName);
    }
  }

  // Check optional but recommended variables
  if (!process.env.STRIPE_SECRET_KEY) {
    warnings.push('STRIPE_SECRET_KEY - Payment processing will not work');
  }

  if (!process.env.STRIPE_WEBHOOK_SECRET) {
    warnings.push('STRIPE_WEBHOOK_SECRET - Webhook verification will not work');
  }

  if (process.env.NODE_ENV === 'production' && !process.env.ALLOWED_ORIGINS) {
    warnings.push('ALLOWED_ORIGINS - Using default production CORS origins');
  }

  return {
    success: missing.length === 0,
    missing,
    warnings
  };
}

/**
 * Validates AWS configuration
 * @returns {Promise<Object>} - Validation result
 */
async function validateAWSConfiguration() {
  try {
    const { S3Client, HeadBucketCommand } = require('@aws-sdk/client-s3');
    const s3Client = new S3Client({ region: process.env.AWS_REGION });

    // Test S3 bucket access
    await s3Client.send(new HeadBucketCommand({
      Bucket: process.env.AWS_S3_BUCKET_NAME
    }));

    return { success: true };
  } catch (error) {
    return {
      success: false,
      error: `AWS S3 configuration error: ${error.message}`
    };
  }
}

/**
 * Validates Supabase configuration
 * @returns {Promise<Object>} - Validation result
 */
async function validateSupabaseConfiguration() {
  try {
    const { createClient } = require('@supabase/supabase-js');
    const supabase = createClient(
      process.env.SUPABASE_URL,
      process.env.SUPABASE_SERVICE_KEY
    );

    // Test database connection
    const { error } = await supabase
      .from('profiles')
      .select('count')
      .limit(1);

    if (error) {
      return {
        success: false,
        error: `Supabase database error: ${error.message}`
      };
    }

    return { success: true };
  } catch (error) {
    return {
      success: false,
      error: `Supabase configuration error: ${error.message}`
    };
  }
}

/**
 * Performs comprehensive startup validation
 * @returns {Promise<boolean>} - True if all validations pass
 */
async function performStartupValidation() {
  logger.info('Starting server validation checks...');

  // Validate environment variables
  const envValidation = validateEnvironmentVariables();
  if (!envValidation.success) {
    logger.error('Missing required environment variables', {
      missing: envValidation.missing
    });
    return false;
  }

  // Log warnings for optional variables
  if (envValidation.warnings.length > 0) {
    logger.warn('Optional environment variables missing', {
      warnings: envValidation.warnings
    });
  }

  // In development, skip AWS and Supabase validation if they fail
  const isDevelopment = process.env.NODE_ENV !== 'production';

  // Validate AWS configuration
  logger.info('Validating AWS configuration...');
  const awsValidation = await validateAWSConfiguration();
  if (!awsValidation.success) {
    if (isDevelopment) {
      logger.warn('AWS validation failed in development mode - continuing anyway', { error: awsValidation.error });
    } else {
      logger.error('AWS validation failed', { error: awsValidation.error });
      return false;
    }
  }

  // Validate Supabase configuration
  logger.info('Validating Supabase configuration...');
  const supabaseValidation = await validateSupabaseConfiguration();
  if (!supabaseValidation.success) {
    if (isDevelopment) {
      logger.warn('Supabase validation failed in development mode - continuing anyway', { error: supabaseValidation.error });
    } else {
      logger.error('Supabase validation failed', { error: supabaseValidation.error });
      return false;
    }
  }

  logger.info('All startup validation checks passed successfully');
  return true;
}

module.exports = {
  validateEnvironmentVariables,
  validateAWSConfiguration,
  validateSupabaseConfiguration,
  performStartupValidation
};