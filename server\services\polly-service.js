/**
 * AWS Polly service utilities for VibeNecto
 * Handles voice generation using AWS Polly
 */

// Load environment variables from .env file
require('dotenv').config();

// Import logger
const { logger } = require('../utils/logger');

const {
  PollyClient,
  SynthesizeSpeechCommand
} = require('@aws-sdk/client-polly');

// Initialize AWS Polly client
const pollyClient = new PollyClient({
  region: process.env.AWS_REGION || 'us-east-1'
});

/**
 * Helper function to convert a stream to buffer
 * @param {Stream} stream - The stream to convert
 * @returns {Promise<Buffer>} - The buffer containing the stream data
 */
async function streamToBuffer(stream) {
  const chunks = [];
  return new Promise((resolve, reject) => {
    stream.on('data', (chunk) => chunks.push(chunk));
    stream.on('error', reject);
    stream.on('end', () => resolve(Buffer.concat(chunks)));
  });
}

/**
 * Generates speech from text using AWS Polly
 * @param {Object} params - Parameters for voice generation
 * @param {string} params.text - Text to convert to speech
 * @param {string} params.voiceId - Voice ID to use for synthesis
 * @param {string} params.languageCode - Language code for the voice
 * @param {string} params.engine - Voice engine (standard or neural)
 * @param {string} params.outputFormat - Output audio format
 * @param {string} params.sampleRate - Audio sample rate
 * @param {number} params.speechRate - Speaking rate (0.2 to 2.0)
 * @param {string} params.pitch - Pitch adjustment
 * @param {string} params.volume - Volume adjustment
 * @returns {Promise<Object>} - The generated audio data
 */
async function generateVoiceWithPolly(params) {
  const {
    text,
    voiceId,
    languageCode = 'en-US',
    engine = 'standard',
    outputFormat = 'mp3',
    sampleRate = '22050',
    speechRate = 1.0,
    pitch = '+0%',
    volume = '+0dB'
  } = params;

  // Validate required parameters
  if (!text || !voiceId) {
    return {
      success: false,
      error: 'Text and voice ID are required for voice generation'
    };
  }

  // Validate text length (AWS Polly limit is 200,000 characters)
  if (text.length > 200000) {
    return {
      success: false,
      error: 'Text exceeds maximum length of 200,000 characters'
    };
  }

  try {
    // Construct SSML for speech customization if needed
    let speechText = text;
    if (speechRate !== 1.0 || pitch !== '+0%' || volume !== '+0dB') {
      speechText = `<speak><prosody rate="${speechRate}" pitch="${pitch}" volume="${volume}">${text}</prosody></speak>`;
    }

    // Create the command to synthesize speech
    const command = new SynthesizeSpeechCommand({
      Text: speechText,
      TextType: speechText.includes('<speak>') ? 'ssml' : 'text',
      VoiceId: voiceId,
      LanguageCode: languageCode,
      Engine: engine,
      OutputFormat: outputFormat,
      SampleRate: sampleRate
    });

    // Synthesize the speech
    logger.info('Synthesizing speech with Polly', {
      voiceId,
      languageCode,
      engine,
      outputFormat,
      sampleRate,
      textLength: text.length
    });

    const response = await pollyClient.send(command);

    // Convert the audio stream to buffer
    const audioBuffer = await streamToBuffer(response.AudioStream);

    // Convert buffer to base64 for consistent handling
    const base64Audio = audioBuffer.toString('base64');

    return {
      success: true,
      audio: base64Audio,
      contentType: response.ContentType,
      requestCharacters: response.RequestCharacters
    };
  } catch (error) {
    logger.error('Error generating voice with Polly', {
      error: error.message,
      stack: error.stack,
      voiceId,
      languageCode
    });
    return {
      success: false,
      error: error.message || 'Failed to generate voice with Polly'
    };
  }
}

module.exports = {
  generateVoiceWithPolly
};