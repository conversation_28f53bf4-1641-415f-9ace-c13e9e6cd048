import { createClient } from '@supabase/supabase-js';

// These environment variables will need to be set in your .env file
const supabaseUrl = import.meta.env.VITE_SUPABASE_URL || '';
const supabaseAnonKey = import.meta.env.VITE_SUPABASE_ANON_KEY || '';

if (!supabaseUrl || !supabaseAnonKey) {
  throw new Error('Missing Supabase environment variables. Please check your .env file and ensure VITE_SUPABASE_URL and VITE_SUPABASE_ANON_KEY are set.');
}

export const supabase = createClient(supabaseUrl, supabaseAnonKey);

// Types for database
export type Tables = {
  profiles: {
    Row: {
      id: string;
      created_at: string;
      updated_at: string;
      email: string;
      name: string;
      avatar_url: string | null;
    };
    Insert: {
      id: string;
      email: string;
      name: string;
      avatar_url?: string | null;
    };
    Update: {
      email?: string;
      name?: string;
      avatar_url?: string | null;
      updated_at?: string;
    };
  };
  subscription_plans: {
    Row: {
      id: string;
      created_at: string;
      updated_at: string;
      name: string;
      description: string | null;
      price: number;
      currency: string;
      interval: string;
      standard_image_limit: number | null;
      premium_image_limit: number;
      advanced_tools_access: boolean;
      stripe_price_id: string | null;
    };
    Insert: {
      id?: string;
      created_at?: string;
      updated_at?: string;
      name: string;
      description?: string | null;
      price: number;
      currency?: string;
      interval: string;
      standard_image_limit?: number | null;
      premium_image_limit: number;
      advanced_tools_access?: boolean;
      stripe_price_id?: string | null;
    };
    Update: {
      name?: string;
      description?: string | null;
      price?: number;
      currency?: string;
      interval?: string;
      standard_image_limit?: number | null;
      premium_image_limit?: number;
      advanced_tools_access?: boolean;
      stripe_price_id?: string | null;
      updated_at?: string;
    };
  };
  user_subscriptions: {
    Row: {
      id: string;
      created_at: string;
      updated_at: string;
      user_id: string;
      plan_id: string;
      status: string;
      current_period_start: string;
      current_period_end: string;
      cancel_at_period_end: boolean;
      stripe_subscription_id: string | null;
      stripe_customer_id: string | null;
    };
    Insert: {
      id?: string;
      created_at?: string;
      updated_at?: string;
      user_id: string;
      plan_id: string;
      status: string;
      current_period_start: string;
      current_period_end: string;
      cancel_at_period_end?: boolean;
      stripe_subscription_id?: string | null;
      stripe_customer_id?: string | null;
    };
    Update: {
      user_id?: string;
      plan_id?: string;
      status?: string;
      current_period_start?: string;
      current_period_end?: string;
      cancel_at_period_end?: boolean;
      stripe_subscription_id?: string | null;
      stripe_customer_id?: string | null;
      updated_at?: string;
    };
  };
  usage_tracking: {
    Row: {
      id: string;
      created_at: string;
      user_id: string;
      image_type: string;
      count: number;
      reset_at: string;
    };
    Insert: {
      id?: string;
      created_at?: string;
      user_id: string;
      image_type: string;
      count?: number;
      reset_at: string;
    };
    Update: {
      user_id?: string;
      image_type?: string;
      count?: number;
      reset_at?: string;
    };
  };
};
