/**
 * Voice generation routes for VibeNecto
 * Handles all voice-related API endpoints including generation, history management,
 * usage tracking, and deletion
 */

const express = require('express');
const { logger } = require('../utils/logger');
const { asyncHandler } = require('../middleware/errorHandler');
const {
  generalLimiter
} = require('../middleware/rateLimiter');
const {
  validateVoiceGeneration
} = require('../middleware/validation');

// Import AWS service modules
const {
  generateVoiceWithPolly
} = require('../services/polly-service');
const {
  uploadVoiceToS3,
  deleteVoiceFromS3,
  generatePresignedUrl
} = require('../services/s3-service');

const router = express.Router();

/**
 * Generate voice from text using AWS Polly
 */
router.post('/generate-voice',
  validateVoiceGeneration, // Validation first
  generalLimiter, // Rate limiter (using general limiter for now)
  asyncHandler(async (req, res) => {
    const { createClient } = require('@supabase/supabase-js');
    
    // Initialize Supabase client
    const supabase = createClient(
      process.env.SUPABASE_URL,
      process.env.SUPABASE_SERVICE_KEY
    );

    const startTime = Date.now();
    logger.info('Voice generation request received', {
      userId: req.body.userId,
      textLength: req.body.text?.length,
      voiceId: req.body.voiceId,
      requestId: req.requestId
    });

    const {
      text,
      textType = 'text',
      voiceId,
      languageCode = 'en-US',
      engine = 'standard',
      outputFormat = 'mp3',
      sampleRate = '22050',
      speechRate = 1.0,
      pitch = '+0%',
      volume = '+0dB',
      userId
    } = req.body;

    try {
      // Check monthly usage limit (10,000 characters for free tier)
      const currentMonth = new Date().toISOString().slice(0, 7); // YYYY-MM format
      const { data: usageData } = await supabase
        .rpc('get_current_voice_usage', { p_user_id: userId });

      const currentUsage = usageData && usageData.length > 0 ? usageData[0].characters_used : 0;
      const monthlyLimit = 10000; // Free tier limit

      if (currentUsage + text.length > monthlyLimit) {
        logger.warn('Voice generation monthly limit exceeded', {
          userId,
          currentUsage,
          requestedCharacters: text.length,
          monthlyLimit,
          requestId: req.requestId
        });

        return res.status(429).json({
          success: false,
          error: `Monthly character limit exceeded. Used: ${currentUsage}/${monthlyLimit} characters.`,
          usage: {
            used: currentUsage,
            limit: monthlyLimit,
            remaining: Math.max(0, monthlyLimit - currentUsage)
          }
        });
      }

      // Generate voice using AWS Polly
      logger.info('Starting voice generation with Polly', {
        voiceId,
        languageCode,
        engine,
        outputFormat,
        textLength: text.length,
        userId,
        requestId: req.requestId
      });

      const pollyResult = await generateVoiceWithPolly({
        text,
        voiceId,
        languageCode,
        engine,
        outputFormat,
        sampleRate,
        speechRate,
        pitch,
        volume
      });

      if (!pollyResult.success) {
        const processingTime = Date.now() - startTime;
        logger.error('Voice generation failed', {
          error: pollyResult.error,
          processingTime,
          userId,
          requestId: req.requestId
        });

        return res.status(500).json({
          success: false,
          error: pollyResult.error
        });
      }

      // Upload the audio to S3
      logger.info('Uploading voice to S3', { userId, requestId: req.requestId });
      const s3StartTime = Date.now();

      const s3Result = await uploadVoiceToS3({
        audio: pollyResult.audio,
        userId,
        contentType: pollyResult.contentType
      });

      const s3Time = Date.now() - s3StartTime;

      if (!s3Result.success) {
        logger.warn('S3 upload failed, returning audio anyway', {
          error: s3Result.error,
          userId,
          requestId: req.requestId
        });

        // Still return the audio even if S3 upload fails
        return res.json({
          success: true,
          audio: pollyResult.audio,
          contentType: pollyResult.contentType,
          warning: 'Voice generated successfully but failed to upload to S3: ' + s3Result.error
        });
      }

      // Save voice generation record to database
      const voiceRecord = {
        user_id: userId,
        text_content: text,
        voice_id: voiceId,
        language_code: languageCode,
        engine: engine,
        parameters: {
          textType,
          outputFormat,
          sampleRate,
          speechRate,
          pitch,
          volume
        },
        s3_key: s3Result.key,
        s3_url: s3Result.url,
        character_count: text.length,
        request_characters: pollyResult.requestCharacters || text.length,
        content_type: pollyResult.contentType,
        status: 'completed'
      };

      const dbStartTime = Date.now();
      const { data: savedVoice, error: dbError } = await supabase
        .from('voice_history')
        .insert(voiceRecord)
        .select()
        .single();

      const dbTime = Date.now() - dbStartTime;

      if (dbError) {
        logger.error('Error saving voice record to database', {
          error: dbError.message,
          userId,
          requestId: req.requestId
        });
        // Continue anyway, voice was generated successfully
      } else {
        logger.info('Voice record saved to database', {
          voiceId: savedVoice.id,
          userId,
          requestId: req.requestId
        });

        // Update usage tracking
        try {
          await supabase.rpc('update_voice_usage', {
            p_user_id: userId,
            p_characters: text.length,
            p_voices: 1
          });
        } catch (usageError) {
          logger.warn('Failed to update voice usage tracking', {
            error: usageError.message,
            userId,
            requestId: req.requestId
          });
        }
      }

      const totalProcessingTime = Date.now() - startTime;
      logger.info('Voice generation completed successfully', {
        processingTime: totalProcessingTime,
        s3UploadTime: s3Time,
        dbTime,
        userId,
        requestId: req.requestId
      });

      // Generate presigned URL for immediate access
      const presignedResult = await generatePresignedUrl({
        key: s3Result.key,
        expirySeconds: 3600
      });

      return res.json({
        success: true,
        voiceId: savedVoice?.id,
        audio: pollyResult.audio, // Include base64 for immediate playback
        presignedUrl: presignedResult.success ? presignedResult.url : null,
        s3Key: s3Result.key,
        contentType: pollyResult.contentType,
        characterCount: text.length,
        processingTime: totalProcessingTime
      });

    } catch (error) {
      const processingTime = Date.now() - startTime;
      logger.error('Voice generation error', {
        error: error.message,
        stack: error.stack,
        processingTime,
        userId,
        requestId: req.requestId
      });

      throw error; // Let asyncHandler handle it
    }
  })
);

/**
 * Get voice generation history for a user
 */
router.get('/voice-history',
  generalLimiter,
  asyncHandler(async (req, res) => {
    const { createClient } = require('@supabase/supabase-js');
    
    // Initialize Supabase client
    const supabase = createClient(
      process.env.SUPABASE_URL,
      process.env.SUPABASE_SERVICE_KEY
    );

    logger.info('Voice history request received', { userId: req.query.userId, requestId: req.requestId });
    const { userId, limit = 50, voiceId, languageCode } = req.query;

    if (!userId) {
      return res.status(400).json({
        success: false,
        error: 'User ID is required'
      });
    }

    try {
      // Build the query
      let query = supabase
        .from('voice_history')
        .select('*')
        .eq('user_id', userId)
        .order('created_at', { ascending: false })
        .limit(parseInt(limit));

      // Apply filters if provided
      if (voiceId) {
        query = query.eq('voice_id', voiceId);
      }

      if (languageCode) {
        query = query.eq('language_code', languageCode);
      }

      const { data: voices, error: dbError } = await query;

      if (dbError) {
        logger.error('Failed to fetch voice history', {
          error: dbError.message,
          userId: req.query.userId,
          requestId: req.requestId
        });
        return res.status(500).json({
          success: false,
          error: 'Failed to fetch voice history'
        });
      }

      // Generate presigned URLs for audio files
      const voicesWithUrls = await Promise.all(
        voices.map(async (voice) => {
          if (voice.s3_key) {
            try {
              const presignedResult = await generatePresignedUrl({
                key: voice.s3_key,
                expirySeconds: 3600 // 1 hour
              });

              if (presignedResult.success) {
                voice.presigned_url = presignedResult.url;
              }
            } catch (error) {
              logger.warn('Failed to generate presigned URL for voice', {
                voiceId: voice.id,
                requestId: req.requestId
              });
            }
          }
          return voice;
        })
      );

      return res.json({
        success: true,
        voices: voicesWithUrls
      });
    } catch (error) {
      logger.error('Server error in voice history fetch', {
        error: error.message,
        requestId: req.requestId
      });
      return res.status(500).json({
        success: false,
        error: error.message || 'An unexpected error occurred'
      });
    }
  })
);

/**
 * Delete a voice file
 */
router.delete('/:voiceId',
  asyncHandler(async (req, res) => {
    const { createClient } = require('@supabase/supabase-js');
    
    // Initialize Supabase client
    const supabase = createClient(
      process.env.SUPABASE_URL,
      process.env.SUPABASE_SERVICE_KEY
    );

    logger.info('Voice deletion request received', { voiceId: req.params.voiceId, requestId: req.requestId });
    const { voiceId } = req.params;
    const { userId } = req.query;

    if (!voiceId || !userId) {
      return res.status(400).json({
        success: false,
        error: 'Voice ID and User ID are required'
      });
    }

    try {
      // Get the voice record to find the S3 key
      const { data: voice, error: fetchError } = await supabase
        .from('voice_history')
        .select('s3_key')
        .eq('id', voiceId)
        .eq('user_id', userId)
        .single();

      if (fetchError || !voice) {
        return res.status(404).json({
          success: false,
          error: 'Voice not found or access denied'
        });
      }

      // Delete the voice file from S3 if it exists
      if (voice.s3_key) {
        const s3Result = await deleteVoiceFromS3({ key: voice.s3_key });
        if (!s3Result.success) {
          logger.warn('Failed to delete voice from S3', {
            error: s3Result.error,
            voiceId: req.params.voiceId,
            requestId: req.requestId
          });
        }
      }

      // Delete the voice record from the database
      const { error: deleteError } = await supabase
        .from('voice_history')
        .delete()
        .eq('id', voiceId)
        .eq('user_id', userId);

      if (deleteError) {
        logger.error('Failed to delete voice record', {
          error: deleteError.message,
          voiceId: req.params.voiceId,
          requestId: req.requestId
        });
        return res.status(500).json({
          success: false,
          error: 'Failed to delete voice record'
        });
      }

      return res.json({
        success: true,
        message: 'Voice deleted successfully'
      });
    } catch (error) {
      logger.error('Server error in voice deletion', {
        error: error.message,
        voiceId: req.params.voiceId,
        requestId: req.requestId
      });
      return res.status(500).json({
        success: false,
        error: error.message || 'An unexpected error occurred'
      });
    }
  })
);

/**
 * Get current voice usage for a user
 */
router.get('/voice-usage/:userId',
  asyncHandler(async (req, res) => {
    const { createClient } = require('@supabase/supabase-js');
    
    // Initialize Supabase client
    const supabase = createClient(
      process.env.SUPABASE_URL,
      process.env.SUPABASE_SERVICE_KEY
    );

    const { userId } = req.params;
    
    if (!userId) {
      return res.status(400).json({
        success: false,
        error: 'User ID is required'
      });
    }

    try {
      const { data: usageData, error } = await supabase
        .rpc('get_current_voice_usage', { p_user_id: userId });

      if (error) {
        logger.error('Failed to fetch voice usage', {
          error: error.message,
          userId,
          requestId: req.requestId
        });
        return res.status(500).json({
          success: false,
          error: 'Failed to fetch voice usage'
        });
      }

      const usage = usageData && usageData.length > 0 ? usageData[0] : { characters_used: 0, voices_generated: 0 };
      const monthlyLimit = 10000; // Free tier limit

      return res.json({
        success: true,
        usage: {
          charactersUsed: usage.characters_used,
          voicesGenerated: usage.voices_generated,
          monthlyLimit,
          remaining: Math.max(0, monthlyLimit - usage.characters_used),
          resetDate: new Date(new Date().getFullYear(), new Date().getMonth() + 1, 1).toISOString()
        }
      });
    } catch (error) {
      logger.error('Server error in voice usage fetch', {
        error: error.message,
        userId,
        requestId: req.requestId
      });
      return res.status(500).json({
        success: false,
        error: error.message || 'An unexpected error occurred'
      });
    }
  })
);

module.exports = router;