import React from "react";
import { Card, CardContent } from "@/components/ui/card";
import { Skeleton } from "@/components/ui/skeleton";
import {
  Image,
  Video,
  Mic,
  Clock,
  TrendingUp,
} from "lucide-react";
import { DashboardStatsCardsProps } from "@/types/dashboard";
import { formatDuration } from "@/utils/dashboardUtils";

const DashboardStatsCards: React.FC<DashboardStatsCardsProps> = ({ stats, isLoading }) => {
  if (isLoading) {
    return (
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-4">
        {[...Array(5)].map((_, i) => (
          <Card key={i}>
            <CardContent className="p-6">
              <Skeleton className="h-8 w-3/4 mb-2" />
              <Skeleton className="h-10 w-1/2" />
            </CardContent>
          </Card>
        ))}
      </div>
    );
  }

  return (
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-4">
      <Card>
        <CardContent className="p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-muted-foreground">Total Images</p>
              <p className="text-2xl font-bold">{stats.totalImages}</p>
            </div>
            <Image className="h-8 w-8 text-muted-foreground" />
          </div>
        </CardContent>
      </Card>

      <Card>
        <CardContent className="p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-muted-foreground">Total Videos</p>
              <p className="text-2xl font-bold">{stats.totalVideos}</p>
            </div>
            <Video className="h-8 w-8 text-muted-foreground" />
          </div>
        </CardContent>
      </Card>

      <Card>
        <CardContent className="p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-muted-foreground">Videos Processing</p>
              <p className="text-2xl font-bold text-blue-600">{stats.processingVideos}</p>
            </div>
            <Clock className="h-8 w-8 text-blue-600" />
          </div>
        </CardContent>
      </Card>

      <Card>
        <CardContent className="p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-muted-foreground">Total Duration</p>
              <p className="text-2xl font-bold">{formatDuration(stats.totalDuration)}</p>
            </div>
            <TrendingUp className="h-8 w-8 text-muted-foreground" />
          </div>
        </CardContent>
      </Card>

      <Card>
        <CardContent className="p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-muted-foreground">Voice Usage</p>
              <p className="text-2xl font-bold">{stats.usagePercentage}%</p>
              <p className="text-xs text-muted-foreground mt-1">
                {stats.voiceUsage ? `${stats.voiceUsage.charactersUsed.toLocaleString()} / ${stats.voiceUsage.monthlyLimit.toLocaleString()}` : 'Loading...'}
              </p>
            </div>
            <Mic className="h-8 w-8 text-muted-foreground" />
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

export default DashboardStatsCards;