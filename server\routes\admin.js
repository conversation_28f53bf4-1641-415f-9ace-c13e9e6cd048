/**
 * Admin and utility routes for VibeNecto
 * Handles health checks, metrics, system monitoring, and administrative operations
 */

const express = require('express');
const { logger } = require('../utils/logger');
const { asyncHandler } = require('../middleware/errorHandler');
const { cacheMiddleware } = require('../utils/cache');

const router = express.Router();

/**
 * Basic health check
 */
router.get('/health', (req, res) => {
  res.json({
    status: 'healthy',
    timestamp: new Date().toISOString(),
    uptime: Math.round(process.uptime()),
    version: process.env.npm_package_version || '2.0.0'
  });
});

/**
 * Detailed health check
 */
router.get('/health/detailed', asyncHandler(async (req, res) => {
  const { createClient } = require('@supabase/supabase-js');
  
  // Initialize Supabase client
  const supabase = createClient(
    process.env.SUPABASE_URL,
    process.env.SUPABASE_SERVICE_KEY
  );

  const startTime = Date.now();
  const health = {
    status: 'healthy',
    timestamp: new Date().toISOString(),
    uptime: Math.round(process.uptime()),
    version: process.env.npm_package_version || '2.0.0',
    services: {}
  };

  // Check database connectivity
  try {
    const { data, error } = await supabase
      .from('profiles')
      .select('count')
      .limit(1);

    health.services.database = {
      status: error ? 'unhealthy' : 'healthy',
      responseTime: Date.now() - startTime,
      error: error?.message
    };
  } catch (error) {
    health.services.database = {
      status: 'unhealthy',
      responseTime: Date.now() - startTime,
      error: error.message
    };
  }

  // Check AWS services
  try {
    const { S3Client, HeadBucketCommand } = require('@aws-sdk/client-s3');
    const s3Client = new S3Client({ region: 'us-east-1' });

    const s3StartTime = Date.now();
    await s3Client.send(new HeadBucketCommand({
      Bucket: process.env.AWS_S3_BUCKET_NAME
    }));

    health.services.s3 = {
      status: 'healthy',
      responseTime: Date.now() - s3StartTime
    };
  } catch (error) {
    health.services.s3 = {
      status: 'unhealthy',
      responseTime: Date.now() - startTime,
      error: error.message
    };
  }

  // Add system metrics
  const memUsage = process.memoryUsage();
  health.system = {
    memory: {
      used: Math.round(memUsage.heapUsed / 1024 / 1024),
      total: Math.round(memUsage.heapTotal / 1024 / 1024),
      external: Math.round(memUsage.external / 1024 / 1024)
    },
    cpu: process.cpuUsage(),
    loadAverage: process.platform !== 'win32' ? require('os').loadavg() : [0, 0, 0]
  };

  // Determine overall health
  const unhealthyServices = Object.values(health.services)
    .filter(service => service.status === 'unhealthy');

  if (unhealthyServices.length > 0) {
    health.status = 'degraded';
    res.status(503);
  }

  res.json(health);
}));

/**
 * Metrics endpoint
 */
router.get('/metrics', cacheMiddleware(60), (req, res) => {
  const { getMetricsSummary } = require('../utils/metrics');
  res.json(getMetricsSummary());
});

module.exports = router;