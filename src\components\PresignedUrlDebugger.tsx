import React from 'react';
import { <PERSON>, CardContent, Card<PERSON>eader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { Clock, Zap, AlertTriangle, CheckCircle, RefreshCw } from 'lucide-react';

interface PresignedUrlMetrics {
  totalUrls: number;
  successCount: number;
  failureCount: number;
  totalTime: number;
  averageTime: number;
  retryCount: number;
  isLoading: boolean;
}

interface PresignedUrlDebuggerProps {
  metrics: PresignedUrlMetrics;
  className?: string;
}

const PresignedUrlDebugger: React.FC<PresignedUrlDebuggerProps> = ({
  metrics,
  className,
}) => {
  // Only show in development
  if (!import.meta.env.DEV) {
    return null;
  }

  const { 
    totalUrls, 
    successCount, 
    failureCount, 
    totalTime, 
    averageTime, 
    retryCount, 
    isLoading 
  } = metrics;

  const successPercentage = totalUrls > 0 ? (successCount / totalUrls) * 100 : 0;
  const failurePercentage = totalUrls > 0 ? (failureCount / totalUrls) * 100 : 0;

  const getPerformanceColor = (time: number) => {
    if (time < 1000) return 'text-green-600';
    if (time < 3000) return 'text-yellow-600';
    return 'text-red-600';
  };

  const getPerformanceBadge = (time: number) => {
    if (time < 1000) return { variant: 'default' as const, label: 'Fast' };
    if (time < 3000) return { variant: 'secondary' as const, label: 'Good' };
    return { variant: 'destructive' as const, label: 'Slow' };
  };

  const getEfficiencyBadge = (successRate: number) => {
    if (successRate >= 95) return { variant: 'default' as const, label: 'Excellent' };
    if (successRate >= 80) return { variant: 'secondary' as const, label: 'Good' };
    if (successRate >= 60) return { variant: 'outline' as const, label: 'Fair' };
    return { variant: 'destructive' as const, label: 'Poor' };
  };

  if (totalUrls === 0 && !isLoading) {
    return null;
  }

  return (
    <Card className={`fixed bottom-4 left-4 w-80 z-50 bg-white/95 backdrop-blur-sm border shadow-lg ${className}`}>
      <CardHeader className="pb-3">
        <CardTitle className="text-sm font-medium flex items-center gap-2">
          <Zap className="h-4 w-4" />
          Presigned URL Performance
          {isLoading && <RefreshCw className="h-3 w-3 animate-spin" />}
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-3">
        {/* Loading State */}
        {isLoading && (
          <div className="space-y-2">
            <div className="flex justify-between text-xs">
              <span>Generating URLs...</span>
              <span className="text-brand-purple">Processing</span>
            </div>
            <Progress value={undefined} className="h-2" />
          </div>
        )}

        {/* Results when not loading */}
        {!isLoading && totalUrls > 0 && (
          <>
            {/* Success Rate Progress */}
            <div className="space-y-2">
              <div className="flex justify-between text-xs">
                <span>Success Rate</span>
                <span>{successPercentage.toFixed(1)}%</span>
              </div>
              <Progress value={successPercentage} className="h-2" />
            </div>

            {/* Statistics Grid */}
            <div className="grid grid-cols-2 gap-3 text-xs">
              <div className="space-y-1">
                <div className="flex items-center gap-1">
                  <CheckCircle className="h-3 w-3 text-green-600" />
                  <span>Success: {successCount}</span>
                </div>
                <div className="flex items-center gap-1">
                  <AlertTriangle className="h-3 w-3 text-red-600" />
                  <span>Failed: {failureCount}</span>
                </div>
              </div>
              <div className="space-y-1">
                <div className="flex items-center gap-1">
                  <RefreshCw className="h-3 w-3 text-blue-600" />
                  <span>Retries: {retryCount}</span>
                </div>
                <div className="flex items-center gap-1">
                  <Clock className="h-3 w-3 text-gray-600" />
                  <span>Total: {totalUrls}</span>
                </div>
              </div>
            </div>

            {/* Performance Metrics */}
            <div className="space-y-2 pt-2 border-t">
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-2">
                  <Clock className="h-3 w-3" />
                  <span className="text-xs">Total Time:</span>
                </div>
                <div className="flex items-center gap-2">
                  <span className={`text-xs font-medium ${getPerformanceColor(totalTime)}`}>
                    {totalTime.toFixed(0)}ms
                  </span>
                  <Badge 
                    variant={getPerformanceBadge(totalTime).variant}
                    className="text-xs px-1 py-0"
                  >
                    {getPerformanceBadge(totalTime).label}
                  </Badge>
                </div>
              </div>

              <div className="flex items-center justify-between">
                <div className="flex items-center gap-2">
                  <Zap className="h-3 w-3" />
                  <span className="text-xs">Avg Time:</span>
                </div>
                <div className="flex items-center gap-2">
                  <span className={`text-xs font-medium ${getPerformanceColor(averageTime)}`}>
                    {averageTime.toFixed(0)}ms
                  </span>
                  <Badge 
                    variant={getEfficiencyBadge(successPercentage).variant}
                    className="text-xs px-1 py-0"
                  >
                    {getEfficiencyBadge(successPercentage).label}
                  </Badge>
                </div>
              </div>
            </div>

            {/* Performance Tips */}
            <div className="text-xs text-gray-600 pt-2 border-t">
              {failurePercentage > 20 && (
                <div className="text-red-600">⚠️ High failure rate ({failurePercentage.toFixed(1)}%)</div>
              )}
              {totalTime > 5000 && (
                <div className="text-yellow-600">⚡ Consider reducing batch size</div>
              )}
              {retryCount > totalUrls * 0.5 && (
                <div className="text-orange-600">🔄 High retry count - check network</div>
              )}
              {successPercentage === 100 && totalTime < 2000 && (
                <div className="text-green-600">✅ Excellent performance!</div>
              )}
              {successPercentage >= 95 && failureCount === 0 && (
                <div className="text-green-600">🚀 All URLs generated successfully!</div>
              )}
            </div>

            {/* Parallel Processing Info */}
            <div className="text-xs text-gray-500 pt-1 border-t">
              Parallel processing: {Math.min(8, totalUrls)} concurrent requests
            </div>
          </>
        )}
      </CardContent>
    </Card>
  );
};

export default PresignedUrlDebugger;
