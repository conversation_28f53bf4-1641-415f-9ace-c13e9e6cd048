import React, { useState, useEffect } from 'react';
import { useAuth } from '@/contexts/AuthContext';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Input } from '@/components/ui/input';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Skeleton } from '@/components/ui/skeleton';
import {
  Play,
  Pause,
  Download,
  Trash2,
  Search,
  Filter,
  Volume2,
  Calendar,
  User,
  Globe,
  Settings,
  AlertCircle,
  Loader2
} from 'lucide-react';
import { toast } from 'sonner';
import {
  getVoiceHistory,
  deleteVoice,
  getVoiceById,
  formatCharacterCount,
  VOICES_BY_LANGUAGE,
  type VoiceHistoryItem
} from '@/services/voiceService';
import { VoiceDeleteDialog } from '@/components/VoiceDeleteDialog';
import { VoiceDetailsDialog } from '@/components/VoiceDetailsDialog';

interface VoicePlayerProps {
  voice: VoiceHistoryItem;
  isPlaying: boolean;
  onPlay: () => void;
  onPause: () => void;
}

const VoicePlayer: React.FC<VoicePlayerProps> = ({ voice, isPlaying, onPlay, onPause }) => {
  const handleTogglePlay = () => {
    if (isPlaying) {
      onPause();
    } else {
      onPlay();
    }
  };

  return (
    <Button
      onClick={handleTogglePlay}
      variant="outline"
      size="sm"
      className="flex items-center gap-1 text-xs h-6 px-2"
      disabled={!voice.presigned_url}
    >
      {isPlaying ? <Pause className="h-3 w-3" /> : <Play className="h-3 w-3" />}
      {isPlaying ? 'Pause' : 'Play'}
    </Button>
  );
};

interface VoiceCardProps {
  voice: VoiceHistoryItem;
  isPlaying: boolean;
  onPlay: () => void;
  onPause: () => void;
  onDelete: () => void;
  onClick: () => void;
}

const VoiceCard: React.FC<VoiceCardProps> = ({ voice, isPlaying, onPlay, onPause, onDelete, onClick }) => {
  const voiceInfo = getVoiceById(voice.voice_id);
  const createdDate = new Date(voice.created_at);

  const handleDownload = (e: React.MouseEvent) => {
    e.stopPropagation();
    if (voice.presigned_url) {
      const a = document.createElement('a');
      a.href = voice.presigned_url;
      a.download = `voice-${voice.id}.mp3`;
      document.body.appendChild(a);
      a.click();
      document.body.removeChild(a);
      toast.success('Download started');
    } else {
      toast.error('Download URL not available');
    }
  };

  const handlePlayClick = (e: React.MouseEvent) => {
    e.stopPropagation();
    if (isPlaying) {
      onPause();
    } else {
      onPlay();
    }
  };

  const handleDeleteClick = (e: React.MouseEvent) => {
    e.stopPropagation();
    onDelete();
  };

  return (
    <Card className="hover:shadow-md transition-shadow cursor-pointer" onClick={onClick}>
      <CardHeader className="pb-2 px-4 pt-4">
        <div className="flex items-start justify-between">
          <div className="flex-1 min-w-0">
            <CardTitle className="text-sm truncate leading-tight">
              {voice.text_content.substring(0, 35)}
              {voice.text_content.length > 35 && '...'}
            </CardTitle>
            <CardDescription className="flex items-center gap-1 mt-1 text-xs">
              <Calendar className="h-3 w-3" />
              {createdDate.toLocaleDateString()}
            </CardDescription>
          </div>
          <Badge variant={voice.status === 'completed' ? 'default' : 'secondary'} className="text-xs">
            {voice.status}
          </Badge>
        </div>
      </CardHeader>
      
      <CardContent className="space-y-2 px-4 pb-4">
        {/* Compact Voice Details */}
        <div className="space-y-1">
          <div className="flex items-center justify-between text-xs text-gray-600 dark:text-gray-400">
            <div className="flex items-center gap-1 truncate">
              <User className="h-3 w-3 flex-shrink-0" />
              <span className="truncate">{voiceInfo?.name || voice.voice_id}</span>
            </div>
            <div className="flex items-center gap-1 flex-shrink-0">
              <Volume2 className="h-3 w-3" />
              <span>{formatCharacterCount(voice.character_count)}</span>
            </div>
          </div>
          <div className="flex items-center gap-1 text-xs text-gray-500 dark:text-gray-500">
            <Globe className="h-3 w-3 flex-shrink-0" />
            <span className="truncate">{VOICES_BY_LANGUAGE[voice.language_code]?.languageName || voice.language_code}</span>
          </div>
        </div>

        {/* Action Buttons */}
        <div className="flex items-center justify-between pt-1">
          <div className="flex items-center gap-1">
            <div onClick={(e) => e.stopPropagation()}>
              <VoicePlayer
                voice={voice}
                isPlaying={isPlaying}
                onPlay={onPlay}
                onPause={onPause}
              />
            </div>
            <Button
              onClick={handleDownload}
              variant="outline"
              size="sm"
              className="flex items-center gap-1 text-xs h-6 px-2"
              disabled={!voice.presigned_url}
            >
              <Download className="h-3 w-3" />
              Download
            </Button>
          </div>
          <Button
            onClick={handleDeleteClick}
            variant="outline"
            size="sm"
            className="text-red-600 hover:text-red-700 hover:bg-red-50 h-6 w-6 p-0"
          >
            <Trash2 className="h-3 w-3" />
          </Button>
        </div>
      </CardContent>
    </Card>
  );
};

interface VoiceHistoryGalleryProps {
  className?: string;
}

const VoiceHistoryGallery: React.FC<VoiceHistoryGalleryProps> = ({ className }) => {
  const { user } = useAuth();
  const [voices, setVoices] = useState<VoiceHistoryItem[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedLanguage, setSelectedLanguage] = useState<string>('all');
  const [selectedVoiceFilter, setSelectedVoiceFilter] = useState<string>('all');
  const [playingVoiceId, setPlayingVoiceId] = useState<string | null>(null);
  const [currentAudio, setCurrentAudio] = useState<HTMLAudioElement | null>(null);
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);
  const [voiceToDelete, setVoiceToDelete] = useState<VoiceHistoryItem | null>(null);
  const [deleting, setDeleting] = useState(false);
  const [detailsDialogOpen, setDetailsDialogOpen] = useState(false);
  const [selectedVoice, setSelectedVoice] = useState<VoiceHistoryItem | null>(null);

  useEffect(() => {
    if (user?.id) {
      loadVoices();
    }
  }, [user?.id]);

  const loadVoices = async () => {
    if (!user?.id) return;

    setLoading(true);
    try {
      const response = await getVoiceHistory(user.id, 50);
      if (response.success && response.voices) {
        setVoices(response.voices);
      } else {
        toast.error('Failed to load voice history');
      }
    } catch (error) {
      console.error('Error loading voices:', error);
      toast.error('Failed to load voice history');
    } finally {
      setLoading(false);
    }
  };

  const handlePlay = (voiceId: string) => {
    // Stop current audio if playing
    if (currentAudio) {
      currentAudio.pause();
      currentAudio.currentTime = 0;
      setCurrentAudio(null);
    }

    // Find the voice and create new audio
    const voice = voices.find(v => v.id === voiceId);
    if (voice?.presigned_url) {
      const audio = new Audio(voice.presigned_url);
      audio.addEventListener('ended', () => {
        setPlayingVoiceId(null);
        setCurrentAudio(null);
      });
      
      audio.play().catch(console.error);
      setCurrentAudio(audio);
      setPlayingVoiceId(voiceId);
    }
  };

  const handlePause = () => {
    if (currentAudio) {
      currentAudio.pause();
      currentAudio.currentTime = 0;
      setCurrentAudio(null);
    }
    setPlayingVoiceId(null);
  };

  // Cleanup audio on unmount
  useEffect(() => {
    return () => {
      if (currentAudio) {
        currentAudio.pause();
        currentAudio.currentTime = 0;
      }
    };
  }, [currentAudio]);

  const handleDeleteClick = (voice: VoiceHistoryItem) => {
    setVoiceToDelete(voice);
    setDeleteDialogOpen(true);
  };

  const handleDeleteConfirm = async () => {
    if (!voiceToDelete || !user?.id) return;

    setDeleting(true);
    try {
      const response = await deleteVoice(voiceToDelete.id, user.id);
      if (response.success) {
        setVoices(voices.filter(v => v.id !== voiceToDelete.id));
        toast.success('Voice deleted successfully');
      } else {
        toast.error(response.error || 'Failed to delete voice');
      }
    } catch (error) {
      console.error('Error deleting voice:', error);
      toast.error('Failed to delete voice');
    } finally {
      setDeleting(false);
      setDeleteDialogOpen(false);
      setVoiceToDelete(null);
    }
  };

  const handleVoiceClick = (voice: VoiceHistoryItem) => {
    setSelectedVoice(voice);
    setDetailsDialogOpen(true);
  };

  // Filter voices based on search and filters
  const filteredVoices = voices.filter(voice => {
    const matchesSearch = voice.text_content.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         voice.voice_id.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesLanguage = selectedLanguage === 'all' || voice.language_code === selectedLanguage;
    const matchesVoice = selectedVoiceFilter === 'all' || voice.voice_id === selectedVoiceFilter;
    
    return matchesSearch && matchesLanguage && matchesVoice;
  });

  // Get unique languages and voices from the data
  const availableLanguages = Array.from(new Set(voices.map(v => v.language_code)));
  const availableVoices = Array.from(new Set(voices.map(v => v.voice_id)));

  if (loading) {
    return (
      <div className={className}>
        <div className="space-y-4">
          {[...Array(6)].map((_, i) => (
            <Card key={i}>
              <CardHeader>
                <Skeleton className="h-6 w-3/4" />
                <Skeleton className="h-4 w-1/2" />
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  <div className="grid grid-cols-2 gap-4">
                    <Skeleton className="h-4 w-full" />
                    <Skeleton className="h-4 w-full" />
                  </div>
                  <Skeleton className="h-16 w-full" />
                  <div className="flex justify-between">
                    <div className="flex gap-2">
                      <Skeleton className="h-8 w-16" />
                      <Skeleton className="h-8 w-20" />
                    </div>
                    <Skeleton className="h-8 w-8" />
                  </div>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      </div>
    );
  }

  return (
    <div className={className}>
      {/* Search and Filters */}
      <div className="mb-6 space-y-4">
        <div className="flex flex-col sm:flex-row gap-4">
          <div className="flex-1 relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
            <Input
              placeholder="Search voices by text or voice name..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="pl-10"
            />
          </div>
          <div className="flex gap-2">
            <Select value={selectedLanguage} onValueChange={setSelectedLanguage}>
              <SelectTrigger className="w-[180px]">
                <SelectValue placeholder="All Languages" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Languages</SelectItem>
                {availableLanguages.map(lang => (
                  <SelectItem key={lang} value={lang}>
                    {VOICES_BY_LANGUAGE[lang]?.languageName || lang}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
            <Select value={selectedVoiceFilter} onValueChange={setSelectedVoiceFilter}>
              <SelectTrigger className="w-[150px]">
                <SelectValue placeholder="All Voices" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Voices</SelectItem>
                {availableVoices.map(voiceId => {
                  const voiceInfo = getVoiceById(voiceId);
                  return (
                    <SelectItem key={voiceId} value={voiceId}>
                      {voiceInfo?.name || voiceId}
                    </SelectItem>
                  );
                })}
              </SelectContent>
            </Select>
          </div>
        </div>
      </div>

      {/* Results */}
      {filteredVoices.length === 0 ? (
        <Alert>
          <AlertCircle className="h-4 w-4" />
          <AlertDescription>
            {voices.length === 0 
              ? "You haven't generated any voices yet. Create your first voice using the Voice Generator!"
              : "No voices match your current search criteria. Try adjusting your filters."
            }
          </AlertDescription>
        </Alert>
      ) : (
        <div className="space-y-4">
          <div className="flex items-center justify-between">
            <p className="text-sm text-gray-600 dark:text-gray-400">
              Showing {filteredVoices.length} of {voices.length} voices
            </p>
          </div>
          
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            {filteredVoices.map((voice) => (
              <VoiceCard
                key={voice.id}
                voice={voice}
                isPlaying={playingVoiceId === voice.id}
                onPlay={() => handlePlay(voice.id)}
                onPause={handlePause}
                onDelete={() => handleDeleteClick(voice)}
                onClick={() => handleVoiceClick(voice)}
              />
            ))}
          </div>
        </div>
      )}

      {/* Delete Confirmation Dialog */}
      <VoiceDeleteDialog
        open={deleteDialogOpen}
        onOpenChange={setDeleteDialogOpen}
        onConfirm={handleDeleteConfirm}
        voiceText={voiceToDelete?.text_content || ''}
        voiceId={voiceToDelete?.id || ''}
      />

      {/* Voice Details Dialog */}
      <VoiceDetailsDialog
        voice={selectedVoice}
        open={detailsDialogOpen}
        onOpenChange={setDetailsDialogOpen}
      />
    </div>
  );
};

export default VoiceHistoryGallery;