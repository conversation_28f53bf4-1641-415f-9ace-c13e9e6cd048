/**
 * Service for managing image history in Supabase
 * Handles storing, retrieving, and deleting image history records
 */

import { supabase } from '@/lib/supabase';
import { getPresignedUrl } from '@/services/s3Service'; // Import for presigned URLs
import { generatePresignedUrlsBatch, batchResultsToUrlMap } from '@/services/presignedUrlService';
import { progressiveLoadingService } from '@/services/progressiveLoadingService';

// Types for image history
export interface ImageHistoryItem {
  id?: string;
  created_at?: string;
  user_id: string;
  prompt?: string;
  image_type: 'text-to-image' | 'background-removal' | 'color-guided' | 'image-variation' | 'conditioning'; // Added 'conditioning'
  s3_key: string;
  s3_url: string;
  parameters?: {
    negativePrompt?: string;
    width?: number;
    height?: number;
    quality?: string;
    cfgScale?: number;
    seed?: number;
    platform?: string;
    style?: string;
    [key: string]: any; // Allow for additional parameters
  };
  metadata?: {
    [key: string]: any;
  };
}

/**
 * Save an image to the history
 * @param item - The image history item to save
 * @returns A promise that resolves to the saved item
 */
export const saveImageHistory = async (item: ImageHistoryItem): Promise<ImageHistoryItem> => {
  try {
    const { data, error } = await supabase
      .from('image_history')
      .insert({
        user_id: item.user_id,
        prompt: item.prompt || '',
        image_type: item.image_type,
        s3_key: item.s3_key,
        s3_url: item.s3_url,
        parameters: item.parameters || {},
        metadata: item.metadata || {}
      })
      .select()
      .single();

    if (error) {
      if (import.meta.env.DEV) {
        console.error('Error saving image history:', error);
      }
      throw new Error(error.message);
    }

    return data;
  } catch (error) {
    if (import.meta.env.DEV) {
      console.error('Failed to save image history:', error);
    }
    throw error;
  }
};

/**
 * Get image history for a user with Phase 4 Progressive Loading
 * @param userId - The ID of the user
 * @param limit - The maximum number of items to return (default: 50)
 * @param imageType - Optional filter by image type
 * @param useProgressiveLoading - Enable Phase 4 progressive loading strategy (default: true)
 * @returns A promise that resolves to an object containing history and presigned URLs
 */
export const getImageHistory = async (
  userId: string,
  limit: number = 50,
  imageType?: 'text-to-image' | 'background-removal' | 'color-guided' | 'image-variation',
  useProgressiveLoading: boolean = true
): Promise<{ history: ImageHistoryItem[]; presignedUrls: Record<string, string> }> => {
  if (import.meta.env.DEV) {
    console.log(`getImageHistory called for userId: ${userId}, limit: ${limit}, imageType: ${imageType}`);
  }
  try {
    let query = supabase
      .from('image_history')
      .select('*')
      .eq('user_id', userId)
      .order('created_at', { ascending: false })
      .limit(limit);

    if (imageType) {
      query = query.eq('image_type', imageType);
    }

    const { data: historyData, error } = await query;

    if (error) {
      if (import.meta.env.DEV) {
        console.error('Error fetching image history from Supabase:', error);
      }
      throw new Error(error.message);
    }

    const history = historyData || [];
    if (import.meta.env.DEV) {
      console.log(`Fetched ${history.length} history items from Supabase.`);
    }

    let presignedUrls: Record<string, string> = {};

    if (history.length > 0) {
      if (import.meta.env.DEV) {
        console.log(`Fetching presigned URLs for ${history.length} history items using ${useProgressiveLoading ? 'progressive loading' : 'parallel processing'}...`);
      }

      // Phase 4: Use progressive loading service if enabled
      if (useProgressiveLoading) {
        try {
          const progressiveResult = await progressiveLoadingService.loadImagesProgressively(
            history.map(item => ({ s3_key: item.s3_key, s3_url: item.s3_url })),
            {
              enableBackgroundEnhancement: true,
              enhancementDelay: 50, // Very short delay for immediate feedback
              batchSize: 8,
              maxConcurrentEnhancements: 4,
            }
          );

          // Use immediate URLs for instant display
          presignedUrls = progressiveResult.immediateUrls;

          // Start background enhancement (non-blocking)
          progressiveResult.enhancementPromise
            .then(enhancedUrls => {
              if (import.meta.env.DEV) {
                console.log(`[ImageHistory] Background enhancement completed for ${Object.keys(enhancedUrls).length} images`);
              }
              // Enhanced URLs will be available through cache for subsequent requests
            })
            .catch(error => {
              if (import.meta.env.DEV) {
                console.warn('[ImageHistory] Background enhancement failed:', error);
              }
            });

          if (import.meta.env.DEV) {
            console.log(`[ImageHistory] Progressive loading: ${progressiveResult.metrics.immediatelyAvailable}/${progressiveResult.metrics.totalRequested} images available immediately (${progressiveResult.metrics.cacheHitRate.toFixed(1)}% cache hit rate)`);
          }
        } catch (error) {
          if (import.meta.env.DEV) {
            console.error('[ImageHistory] Progressive loading failed, falling back to traditional method:', error);
          }
          // Fall back to traditional method
          useProgressiveLoading = false;
        }
      }

      // Traditional method (fallback or when progressive loading is disabled)
      if (!useProgressiveLoading) {
        // Extract s3_keys and create fallback URL map
        const s3_keys: string[] = [];
        const fallbackUrls: Record<string, string> = {};

        history.forEach(item => {
          if (item.s3_key) {
            s3_keys.push(item.s3_key);
            fallbackUrls[item.s3_key] = item.s3_url; // Use existing s3_url as fallback
          } else if (import.meta.env.DEV) {
            console.warn(`History item with id ${item.id} has no s3_key. Cannot fetch presigned URL.`);
          }
        });

        if (s3_keys.length > 0) {
          try {
            // Use parallel processing with optimized settings and caching
            const batchResult = await generatePresignedUrlsBatch(s3_keys, {
              timeout: 5000, // 5 second timeout per URL
              maxRetries: 2, // Retry failed requests twice
              retryDelay: 1000, // 1 second delay between retries
              concurrencyLimit: 8, // Process up to 8 URLs concurrently
              fallbackUrls, // Use existing s3_urls as fallbacks
              enableMetrics: import.meta.env.DEV,
              useCache: true, // Enable caching
              cacheTtl: 45 * 60 * 1000, // 45 minute cache TTL
              forceRefresh: false, // Use cached URLs when available
            });

            // Convert results to URL map
            const freshUrls = batchResultsToUrlMap(batchResult.results);

            // Merge fresh URLs with fallbacks for failed requests
            batchResult.results.forEach(result => {
              if (result.success && result.url) {
                presignedUrls[result.s3_key] = result.url;
              } else {
                // Use fallback URL for failed requests
                presignedUrls[result.s3_key] = fallbackUrls[result.s3_key] || '';
                if (import.meta.env.DEV && result.error) {
                  console.warn(`Failed to get fresh presigned URL for key: ${result.s3_key}. Using fallback. Error: ${result.error}`);
                }
              }
            });

            if (import.meta.env.DEV) {
              console.log(`Finished fetching presigned URLs. Success: ${batchResult.successCount}/${s3_keys.length}, Total time: ${batchResult.totalTime.toFixed(0)}ms, Avg: ${batchResult.averageTime.toFixed(0)}ms`);
            }
          } catch (error) {
            if (import.meta.env.DEV) {
              console.error('Batch presigned URL generation failed, using fallbacks:', error);
            }
            // Use all fallback URLs
            s3_keys.forEach(s3_key => {
              presignedUrls[s3_key] = fallbackUrls[s3_key] || '';
            });
          }
        }
      }
    } else {
      if (import.meta.env.DEV) {
        console.log("No history items found, skipping presigned URL fetching.");
      }
    }
    
    const result = { history, presignedUrls };
    if (import.meta.env.DEV) {
      console.log("getImageHistory service function returning:", JSON.stringify(Object.keys(result))); // Log keys of the result
    }
    return result;

  } catch (error) {
    if (import.meta.env.DEV) {
      console.error('Failed to fetch image history or presigned URLs:', error);
    }
    // Ensure a valid structure is returned even in case of a broader error
    return { history: [], presignedUrls: {} };
  }
};

/**
 * Delete an image from history
 * @param id - The ID of the image history item to delete
 * @param userId - The ID of the user (for security check)
 * @returns A promise that resolves when the item is deleted
 */
export const deleteImageHistory = async (id: string, userId: string): Promise<void> => {
  try {
    const { error } = await supabase
      .from('image_history')
      .delete()
      .eq('id', id)
      .eq('user_id', userId);

    if (error) {
      if (import.meta.env.DEV) {
        console.error('Error deleting image history:', error);
      }
      throw new Error(error.message);
    }
  } catch (error) {
    if (import.meta.env.DEV) {
      console.error('Failed to delete image history:', error);
    }
    throw error;
  }
};

/**
 * Delete all image history for a user
 * @param userId - The ID of the user
 * @returns A promise that resolves when all items are deleted
 */
export const deleteAllImageHistory = async (userId: string): Promise<void> => {
  try {
    const { error } = await supabase
      .from('image_history')
      .delete()
      .eq('user_id', userId);

    if (error) {
      if (import.meta.env.DEV) {
        console.error('Error deleting all image history:', error);
      }
      throw new Error(error.message);
    }
  } catch (error) {
    if (import.meta.env.DEV) {
      console.error('Failed to delete all image history:', error);
    }
    throw error;
  }
};

/**
 * Convert legacy image history format to new format
 * This is used for compatibility with the old localStorage format
 */
export const convertLegacyImageHistoryItem = (
  legacyItem: any,
  userId: string
): ImageHistoryItem => {
  // Determine image type based on legacy data
  let imageType: 'text-to-image' | 'background-removal' | 'color-guided' | 'image-variation' = 'text-to-image';
  
  if (legacyItem.type) {
    if (['background-removal', 'color-guided', 'image-variation'].includes(legacyItem.type)) {
      imageType = legacyItem.type as any;
    }
  } else if (legacyItem.parameters?.style) {
    if (['background-removal', 'color-guided', 'image-variation'].includes(legacyItem.parameters.style)) {
      imageType = legacyItem.parameters.style as any;
    }
  }

  return {
    user_id: userId,
    prompt: legacyItem.prompt || '',
    image_type: imageType,
    s3_key: legacyItem.s3Key || '',
    s3_url: legacyItem.s3Url || legacyItem.image || '',
    parameters: legacyItem.parameters || {},
    metadata: {
      legacy_id: legacyItem.id,
      timestamp: legacyItem.timestamp || new Date().toISOString()
    }
  };
};
