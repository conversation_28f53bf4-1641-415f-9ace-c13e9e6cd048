import React, { useState, useCallback } from 'react';
import { Button } from '@/components/ui/button';
import { RefreshCw, AlertTriangle } from 'lucide-react';
import { cn } from '@/lib/utils';
import LazyImage from '@/components/LazyImage';
import { useProgressiveImageUrl } from '@/hooks/useProgressiveImageUrl';

interface ImageWithRetryProps {
  s3_key: string;
  fallbackUrl: string;
  alt: string;
  className?: string;
  onError?: (e: React.SyntheticEvent<HTMLImageElement, Event>) => void;
  onClick?: (e: React.MouseEvent<HTMLImageElement>) => void;
  showRetryButton?: boolean;
  showConnectionStatus?: boolean;
  rootMargin?: string;
  threshold?: number;
  onLoadComplete?: (loadTime: number) => void;
  onLoadError?: () => void;
  onInView?: () => void;
  onOutOfView?: () => void;
}

const ImageWithRetry: React.FC<ImageWithRetryProps> = ({
  s3_key,
  fallbackUrl,
  alt,
  className,
  onError,
  onClick,
  showRetryButton = true,
  showConnectionStatus = false,
  rootMargin = '100px',
  threshold = 0.1,
  onLoadComplete,
  onLoadError,
  onInView,
  onOutOfView,
}) => {
  const [imageLoadError, setImageLoadError] = useState(false);
  const [isRetrying, setIsRetrying] = useState(false);

  const {
    url,
    isLoading: isUrlLoading,
    hasError: hasUrlError,
    isUsingFallback,
    refreshUrl,
    retryCount,
  } = useProgressiveImageUrl({
    s3_key,
    fallbackUrl,
    enableRefresh: true,
    refreshTimeout: 5000,
    maxRetries: 2,
    retryDelay: 1000,
  });

  const handleImageError = useCallback((e: React.SyntheticEvent<HTMLImageElement, Event>) => {
    setImageLoadError(true);
    onLoadError?.();
    onError?.(e);
  }, [onError, onLoadError]);

  const handleRetry = useCallback(async () => {
    setIsRetrying(true);
    setImageLoadError(false);
    
    try {
      await refreshUrl();
    } catch (error) {
      console.error('Retry failed:', error);
    } finally {
      setIsRetrying(false);
    }
  }, [refreshUrl]);

  const handleImageLoad = useCallback((loadTime: number) => {
    setImageLoadError(false);
    onLoadComplete?.(loadTime);
  }, [onLoadComplete]);

  const showError = imageLoadError || hasUrlError;
  const showLoading = (isUrlLoading || isRetrying) && (!url || url === fallbackUrl);

  return (
    <div className={cn("relative w-full h-full", className)}>
      <LazyImage
        src={url}
        alt={alt}
        className="w-full h-full object-cover transition-transform duration-300 group-hover:scale-110"
        fallbackSrc={fallbackUrl}
        rootMargin={rootMargin}
        threshold={threshold}
        onError={handleImageError}
        onClick={onClick}
        onLoadComplete={handleImageLoad}
        onLoadError={onLoadError}
        onInView={onInView}
        onOutOfView={onOutOfView}
      />

      {/* Loading Overlay - Only show when no image is available */}
      {showLoading && (
        <div className="absolute inset-0 bg-black bg-opacity-20 flex items-center justify-center">
          <div className="bg-white dark:bg-gray-800 rounded-lg p-2 shadow-lg">
            <RefreshCw className="h-4 w-4 animate-spin text-brand-purple" />
          </div>
        </div>
      )}

      {/* Error State with Retry */}
      {showError && showRetryButton && !showLoading && (
        <div className="absolute inset-0 bg-black bg-opacity-40 flex items-center justify-center">
          <div className="bg-white dark:bg-gray-800 rounded-lg p-3 shadow-lg text-center max-w-[80%]">
            <AlertTriangle className="h-6 w-6 text-red-500 mx-auto mb-2" />
            <p className="text-xs text-gray-600 dark:text-gray-400 mb-2">
              Failed to load image
            </p>
            {retryCount > 0 && (
              <p className="text-xs text-gray-500 mb-2">
                Retried {retryCount} time{retryCount !== 1 ? 's' : ''}
              </p>
            )}
            <Button
              size="sm"
              variant="outline"
              onClick={handleRetry}
              className="text-xs h-6 px-2"
            >
              <RefreshCw className="h-3 w-3 mr-1" />
              Retry
            </Button>
          </div>
        </div>
      )}
    </div>
  );
};

export default ImageWithRetry;
