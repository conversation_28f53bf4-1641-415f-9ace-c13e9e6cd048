import React, { useState, useRef } from "react";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Badge } from "@/components/ui/badge";
import { Separator } from "@/components/ui/separator";
import { cn } from "@/lib/utils";
import { toast } from "sonner";
import {
  Plus,
  Trash2,
  Upload,
  X,
  GripVertical,
  Image as ImageIcon,
  Film,
  Play,
  ArrowUp,
  ArrowDown
} from "lucide-react";
import { VideoShot } from "@/services/videoService";
import { fileToBase64 } from "@/utils/base64Utils";

interface Shot extends VideoShot {
  id: string;
  referenceImage?: string; // Base64 image data
}

interface ShotBuilderProps {
  onShotsChange?: (shots: VideoShot[]) => void;
  onGenerate?: (shots: VideoShot[]) => void;
  className?: string;
}

const ShotBuilder: React.FC<ShotBuilderProps> = ({
  onShotsChange,
  onGenerate,
  className
}) => {
  const [shots, setShots] = useState<Shot[]>([
    {
      id: '1',
      text: '',
      seed: Math.floor(Math.random() * 1000000)
    }
  ]);
  const fileInputRefs = useRef<{ [key: string]: HTMLInputElement | null }>({});

  // Update parent component when shots change
  React.useEffect(() => {
    const videoShots: VideoShot[] = shots.map(shot => ({
      text: shot.text,
      referenceImageKey: shot.referenceImageKey,
      referenceImageFormat: shot.referenceImageFormat,
      seed: shot.seed
    }));
    onShotsChange?.(videoShots);
  }, [shots, onShotsChange]);

  // Add new shot
  const addShot = () => {
    if (shots.length >= 20) {
      toast.error("Maximum 20 shots allowed");
      return;
    }

    const newShot: Shot = {
      id: Date.now().toString(),
      text: '',
      seed: Math.floor(Math.random() * 1000000)
    };
    setShots(prev => [...prev, newShot]);
  };

  // Remove shot
  const removeShot = (id: string) => {
    if (shots.length <= 1) {
      toast.error("At least one shot is required");
      return;
    }
    setShots(prev => prev.filter(shot => shot.id !== id));
  };

  // Update shot text
  const updateShotText = (id: string, text: string) => {
    setShots(prev => prev.map(shot => 
      shot.id === id ? { ...shot, text } : shot
    ));
  };

  // Update shot seed
  const updateShotSeed = (id: string, seed: number) => {
    setShots(prev => prev.map(shot => 
      shot.id === id ? { ...shot, seed } : shot
    ));
  };

  // Handle image upload for a shot
  const handleImageUpload = async (shotId: string, event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (!file) return;

    // Validate file type
    if (!file.type.startsWith('image/')) {
      toast.error("Please select a valid image file");
      return;
    }

    // Validate file size (max 10MB)
    if (file.size > 10 * 1024 * 1024) {
      toast.error("Image file size must be less than 10MB");
      return;
    }

    try {
      const base64 = await fileToBase64(file);
      const format = file.type.includes('png') ? 'png' : 'jpeg';
      
      setShots(prev => prev.map(shot => 
        shot.id === shotId ? { 
          ...shot, 
          referenceImage: base64,
          referenceImageFormat: format as 'png' | 'jpeg'
        } : shot
      ));
      
      toast.success("Reference image uploaded successfully");
    } catch (error) {
      if (import.meta.env.DEV) {
        console.error("Error uploading image:", error);
      }
      toast.error("Failed to upload image");
    }
  };

  // Remove reference image from shot
  const removeReferenceImage = (shotId: string) => {
    setShots(prev => prev.map(shot => 
      shot.id === shotId ? { 
        ...shot, 
        referenceImage: undefined,
        referenceImageKey: undefined,
        referenceImageFormat: undefined
      } : shot
    ));
    
    // Clear file input
    if (fileInputRefs.current[shotId]) {
      fileInputRefs.current[shotId]!.value = "";
    }
  };

  // Move shot up
  const moveShotUp = (index: number) => {
    if (index === 0) return;
    const newShots = [...shots];
    [newShots[index], newShots[index - 1]] = [newShots[index - 1], newShots[index]];
    setShots(newShots);
  };

  // Move shot down
  const moveShotDown = (index: number) => {
    if (index === shots.length - 1) return;
    const newShots = [...shots];
    [newShots[index], newShots[index + 1]] = [newShots[index + 1], newShots[index]];
    setShots(newShots);
  };

  // Generate random seed for shot
  const generateRandomSeed = (shotId: string) => {
    const randomSeed = Math.floor(Math.random() * 1000000);
    updateShotSeed(shotId, randomSeed);
  };

  // Handle generate button click
  const handleGenerate = () => {
    // Validate shots
    const validShots = shots.filter(shot => shot.text.trim().length > 0);
    
    if (validShots.length === 0) {
      toast.error("Please add at least one shot with a description");
      return;
    }

    const videoShots: VideoShot[] = validShots.map(shot => ({
      text: shot.text,
      referenceImageKey: shot.referenceImageKey,
      referenceImageFormat: shot.referenceImageFormat,
      seed: shot.seed
    }));

    onGenerate?.(videoShots);
  };

  return (
    <Card className={className}>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Film className="h-5 w-5" />
          Storyboard Builder
          <Badge variant="secondary">{shots.length}/20 shots</Badge>
        </CardTitle>
      </CardHeader>

      <CardContent className="space-y-4">
        {/* Shots List */}
        <div className="space-y-4 max-h-96 overflow-y-auto">
          {shots.map((shot, index) => (
            <Card key={shot.id} className="border-2 border-dashed">
              <CardHeader className="pb-3">
                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-2">
                    <GripVertical className="h-4 w-4 text-gray-400" />
                    <Badge variant="outline">Shot {index + 1}</Badge>
                  </div>
                  <div className="flex items-center gap-1">
                    <Button
                      variant="ghost"
                      size="icon"
                      className="h-6 w-6"
                      onClick={() => moveShotUp(index)}
                      disabled={index === 0}
                    >
                      <ArrowUp className="h-3 w-3" />
                    </Button>
                    <Button
                      variant="ghost"
                      size="icon"
                      className="h-6 w-6"
                      onClick={() => moveShotDown(index)}
                      disabled={index === shots.length - 1}
                    >
                      <ArrowDown className="h-3 w-3" />
                    </Button>
                    <Button
                      variant="ghost"
                      size="icon"
                      className="h-6 w-6 text-red-500 hover:text-red-700"
                      onClick={() => removeShot(shot.id)}
                      disabled={shots.length <= 1}
                    >
                      <Trash2 className="h-3 w-3" />
                    </Button>
                  </div>
                </div>
              </CardHeader>

              <CardContent className="space-y-3">
                {/* Shot Description */}
                <div className="space-y-2">
                  <label className="text-sm font-medium">Shot Description</label>
                  <Textarea
                    placeholder="Describe this shot in detail..."
                    value={shot.text}
                    onChange={(e) => updateShotText(shot.id, e.target.value)}
                    className="min-h-[80px] resize-none"
                    maxLength={512}
                  />
                  <div className="flex justify-between text-xs text-muted-foreground">
                    <span>Be specific about camera angles, actions, and visuals</span>
                    <span className={shot.text.length > 450 ? "text-red-500" : ""}>
                      {shot.text.length}/512
                    </span>
                  </div>
                </div>

                {/* Reference Image */}
                <div className="space-y-2">
                  <label className="text-sm font-medium">Reference Image (Optional)</label>
                  {!shot.referenceImage ? (
                    <div
                      className="border-2 border-dashed border-gray-300 rounded-lg p-4 text-center cursor-pointer hover:border-gray-400 transition-colors"
                      onClick={() => fileInputRefs.current[shot.id]?.click()}
                    >
                      <Upload className="h-6 w-6 mx-auto mb-2 text-gray-400" />
                      <p className="text-sm text-gray-600">Click to upload reference image</p>
                      <p className="text-xs text-gray-500 mt-1">PNG or JPEG, max 10MB</p>
                    </div>
                  ) : (
                    <div className="relative">
                      <img
                        src={shot.referenceImage}
                        alt={`Shot ${index + 1} reference`}
                        className="w-full h-24 object-cover rounded-lg"
                      />
                      <Button
                        type="button"
                        variant="destructive"
                        size="icon"
                        className="absolute top-1 right-1 h-6 w-6"
                        onClick={() => removeReferenceImage(shot.id)}
                      >
                        <X className="h-3 w-3" />
                      </Button>
                    </div>
                  )}
                  <input
                    ref={(el) => fileInputRefs.current[shot.id] = el}
                    type="file"
                    accept="image/*"
                    onChange={(e) => handleImageUpload(shot.id, e)}
                    className="hidden"
                  />
                </div>

                {/* Seed */}
                <div className="flex items-center gap-2">
                  <label className="text-sm font-medium">Seed:</label>
                  <Input
                    type="number"
                    value={shot.seed}
                    onChange={(e) => updateShotSeed(shot.id, parseInt(e.target.value) || 0)}
                    className="w-24"
                  />
                  <Button
                    type="button"
                    variant="outline"
                    size="sm"
                    onClick={() => generateRandomSeed(shot.id)}
                  >
                    Random
                  </Button>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>

        <Separator />

        {/* Actions */}
        <div className="flex justify-between">
          <Button
            variant="outline"
            onClick={addShot}
            disabled={shots.length >= 20}
          >
            <Plus className="h-4 w-4 mr-2" />
            Add Shot
          </Button>

          <Button
            onClick={handleGenerate}
            disabled={shots.every(shot => shot.text.trim().length === 0)}
          >
            <Play className="h-4 w-4 mr-2" />
            Generate Video
          </Button>
        </div>
      </CardContent>
    </Card>
  );
};

export default ShotBuilder;
