import React from "react";
import DashboardSidebar from "@/components/DashboardSidebar";
import { ImageGeneratorSidebar } from "@/components/ImageGeneratorSidebar";
import { ImagePreview } from "@/components/ImagePreview";
import { ImageGeneratorStates } from "@/components/ImageGeneratorStates";
import { useImageGeneration } from "@/hooks/useImageGeneration";
import { useImageActions } from "@/hooks/useImageActions";
import { useImageForm } from "@/hooks/useImageForm";

const ImageGenerator = () => {
  // Custom hooks for separated concerns
  const {
    isGenerating,
    generatedImage,
    error,
    warning,
    generateImageFromForm,
    clearError,
    resetGeneration,
  } = useImageGeneration();

  const {
    isFullscreen,
    handleDownload,
    handleCopyImage,
    toggleFullscreen,
  } = useImageActions();

  const {
    form,
    activeSidebarSection,
    setActiveSidebarSection,
    handleReset,
  } = useImageForm();

  // Combined handlers
  const onSubmit = async (data: any) => {
    await generateImageFromForm(data);
  };

  const onReset = () => {
    handleReset();
    resetGeneration();
  };

  const onDownload = () => {
    handleDownload(generatedImage);
  };

  const onCopy = () => {
    handleCopyImage(generatedImage);
  };

  return (
    <>
      <DashboardSidebar />
      <main className="ml-64 h-screen bg-gray-50 dark:bg-gray-900 flex">
        {/* Sidebar */}
        <ImageGeneratorSidebar
          form={form}
          activeSidebarSection={activeSidebarSection}
          setActiveSidebarSection={setActiveSidebarSection}
          isGenerating={isGenerating}
          onSubmit={onSubmit}
          onReset={onReset}
        />

        {/* Main content area */}
        <div className="flex-1 flex flex-col items-center justify-center p-6 bg-gray-100 dark:bg-gray-800/50 relative overflow-y-auto">
          {generatedImage ? (
            <ImagePreview
              generatedImage={generatedImage}
              warning={warning}
              isFullscreen={isFullscreen}
              onDownload={onDownload}
              onCopy={onCopy}
              onToggleFullscreen={toggleFullscreen}
            />
          ) : (
            <ImageGeneratorStates
              isGenerating={isGenerating}
              error={error}
              onClearError={clearError}
            />
          )}
        </div>
      </main>
    </>
  );
};

export default ImageGenerator;
