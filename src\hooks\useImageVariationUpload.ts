/**
 * Custom hook for handling image upload functionality specifically for Image Variation
 * Manages file selection, validation, preview, and state
 */

import { useState, useRef, useCallback } from 'react';
import { toast } from 'sonner';
import { FILE_CONSTRAINTS, VALIDATION, TOAST_MESSAGES } from '@/constants/imageVariation';

interface UseImageVariationUploadReturn {
  // State
  sourceImage: string | null;
  isUploading: boolean;
  uploadError: string | null;
  
  // Refs
  fileInputRef: React.RefObject<HTMLInputElement>;
  
  // Actions
  handleFileUpload: (e: React.ChangeEvent<HTMLInputElement>) => void;
  handleFileSelect: (file: File) => void;
  triggerFileInput: () => void;
  resetUpload: () => void;
  clearImage: () => void;
}

export const useImageVariationUpload = (): UseImageVariationUploadReturn => {
  const [sourceImage, setSourceImage] = useState<string | null>(null);
  const [isUploading, setIsUploading] = useState(false);
  const [uploadError, setUploadError] = useState<string | null>(null);
  
  const fileInputRef = useRef<HTMLInputElement>(null);

  // Handle file selection from input
  const handleFileUpload = useCallback((e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (file) {
      handleFileSelect(file);
    }
  }, []);

  // Handle file selection (can be used for drag-and-drop too)
  const handleFileSelect = useCallback((file: File) => {
    setIsUploading(true);
    setUploadError(null);

    try {
      // Validate file using our constants
      if (!VALIDATION.isValidFile(file)) {
        const error = VALIDATION.getFileError(file);
        setUploadError(error);
        toast.error(error || 'Invalid file');
        setIsUploading(false);
        return;
      }

      // Create FileReader to convert to data URL
      const reader = new FileReader();
      reader.onload = (event) => {
        if (event.target?.result) {
          setSourceImage(event.target.result as string);
          setUploadError(null);
        }
      };
      
      reader.onerror = () => {
        const errorMessage = 'Failed to read file';
        setUploadError(errorMessage);
        toast.error(errorMessage);
      };
      
      reader.readAsDataURL(file);

    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Failed to upload file';
      setUploadError(errorMessage);
      toast.error(errorMessage);
      
      if (import.meta.env.DEV) {
        console.error('Error handling file upload:', error);
      }
    } finally {
      setIsUploading(false);
    }
  }, []);

  // Trigger file input click
  const triggerFileInput = useCallback(() => {
    fileInputRef.current?.click();
  }, []);

  // Clear the current image
  const clearImage = useCallback(() => {
    setSourceImage(null);
    setUploadError(null);
    
    if (fileInputRef.current) {
      fileInputRef.current.value = '';
    }
  }, []);

  // Reset upload state completely
  const resetUpload = useCallback(() => {
    setSourceImage(null);
    setUploadError(null);
    setIsUploading(false);
    
    if (fileInputRef.current) {
      fileInputRef.current.value = '';
    }
  }, []);

  return {
    // State
    sourceImage,
    isUploading,
    uploadError,
    
    // Refs
    fileInputRef,
    
    // Actions
    handleFileUpload,
    handleFileSelect,
    triggerFileInput,
    resetUpload,
    clearImage,
  };
};