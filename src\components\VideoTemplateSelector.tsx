import React, { useState } from "react";
import { <PERSON>, <PERSON><PERSON>ontent, CardH<PERSON>er, CardTitle } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Separator } from "@/components/ui/separator";
import { cn } from "@/lib/utils";
import {
  Sparkles,
  ShoppingBag,
  GraduationCap,
  Heart,
  Briefcase,
  Camera,
  Music,
  Gamepad2,
  Utensils,
  Plane,
  Play,
  Clock,
  Users
} from "lucide-react";

interface VideoTemplate {
  id: string;
  name: string;
  description: string;
  category: 'marketing' | 'education' | 'social' | 'business';
  icon: React.ElementType;
  duration: string;
  shots: number;
  prompt: string;
  tags: string[];
  preview?: string;
}

interface VideoTemplateSelectorProps {
  onTemplateSelect?: (template: VideoTemplate) => void;
  className?: string;
}

const videoTemplates: VideoTemplate[] = [
  // Marketing Templates
  {
    id: 'product-showcase',
    name: 'Product Showcase',
    description: 'Professional product demonstration with multiple angles',
    category: 'marketing',
    icon: ShoppingBag,
    duration: '30s',
    shots: 5,
    prompt: 'A sleek product showcase video featuring a modern smartphone. Shot 1: Close-up of the phone on a minimalist white surface with dramatic lighting. Shot 2: Slow rotation showing all angles of the device. Shot 3: Hand elegantly picking up the phone. Shot 4: Screen lighting up showing the interface. Shot 5: Final hero shot with the phone floating in space with particles.',
    tags: ['product', 'commercial', 'tech']
  },
  {
    id: 'brand-story',
    name: 'Brand Story',
    description: 'Emotional brand narrative with lifestyle elements',
    category: 'marketing',
    icon: Heart,
    duration: '60s',
    shots: 8,
    prompt: 'An inspiring brand story video. Shot 1: Sunrise over a city skyline. Shot 2: Person waking up with determination. Shot 3: Coffee being poured in slow motion. Shot 4: Hands working on a laptop with passion. Shot 5: Team collaboration in a modern office. Shot 6: Product being crafted with care. Shot 7: Happy customers using the product. Shot 8: Brand logo with inspiring tagline.',
    tags: ['brand', 'lifestyle', 'emotional']
  },
  {
    id: 'social-media-ad',
    name: 'Social Media Ad',
    description: 'Quick, engaging content optimized for social platforms',
    category: 'social',
    icon: Camera,
    duration: '15s',
    shots: 3,
    prompt: 'A dynamic social media advertisement. Shot 1: Eye-catching product reveal with vibrant colors and motion graphics. Shot 2: Quick demonstration of key features with energetic music. Shot 3: Strong call-to-action with discount offer and brand logo.',
    tags: ['social', 'quick', 'engaging']
  },

  // Education Templates
  {
    id: 'tutorial-intro',
    name: 'Tutorial Introduction',
    description: 'Professional tutorial opening with clear structure',
    category: 'education',
    icon: GraduationCap,
    duration: '45s',
    shots: 6,
    prompt: 'An educational tutorial introduction. Shot 1: Animated title card with course name. Shot 2: Instructor greeting viewers warmly. Shot 3: Overview of what will be learned. Shot 4: Materials and tools needed. Shot 5: Step-by-step preview montage. Shot 6: Encouraging call to start learning.',
    tags: ['education', 'tutorial', 'learning']
  },
  {
    id: 'explainer-video',
    name: 'Concept Explainer',
    description: 'Clear explanation of complex topics with visuals',
    category: 'education',
    icon: Sparkles,
    duration: '90s',
    shots: 12,
    prompt: 'A comprehensive concept explainer video about artificial intelligence. Shot 1: Question "What is AI?" with curious expression. Shot 2: Brain neurons firing animation. Shot 3: Computer processing data visualization. Shot 4: Machine learning algorithm in action. Shot 5: AI applications in daily life. Shot 6: Self-driving car example. Shot 7: Medical diagnosis assistance. Shot 8: Voice assistants responding. Shot 9: Benefits and advantages. Shot 10: Potential challenges. Shot 11: Future possibilities. Shot 12: Summary and conclusion.',
    tags: ['explanation', 'complex', 'visual']
  },

  // Business Templates
  {
    id: 'company-intro',
    name: 'Company Introduction',
    description: 'Professional company overview and values presentation',
    category: 'business',
    icon: Briefcase,
    duration: '75s',
    shots: 10,
    prompt: 'A professional company introduction video. Shot 1: Modern office building exterior. Shot 2: Welcoming reception area. Shot 3: CEO introducing the company mission. Shot 4: Team members collaborating. Shot 5: Innovation lab with cutting-edge technology. Shot 6: Customer testimonials. Shot 7: Global reach visualization. Shot 8: Awards and achievements. Shot 9: Company values in action. Shot 10: Contact information and next steps.',
    tags: ['corporate', 'professional', 'introduction']
  },
  {
    id: 'team-spotlight',
    name: 'Team Spotlight',
    description: 'Showcase team members and company culture',
    category: 'business',
    icon: Users,
    duration: '60s',
    shots: 8,
    prompt: 'A team spotlight video showcasing company culture. Shot 1: Team gathering in a bright, modern workspace. Shot 2: Individual team members introducing themselves. Shot 3: Collaborative brainstorming session. Shot 4: Fun team building activities. Shot 5: Diverse skills and expertise showcase. Shot 6: Work-life balance moments. Shot 7: Team achievements and celebrations. Shot 8: Join our team call-to-action.',
    tags: ['team', 'culture', 'recruitment']
  },

  // Social Templates
  {
    id: 'lifestyle-vlog',
    name: 'Lifestyle Vlog',
    description: 'Personal lifestyle content with authentic moments',
    category: 'social',
    icon: Music,
    duration: '120s',
    shots: 15,
    prompt: 'A lifestyle vlog showing a perfect day. Shot 1: Morning routine with natural lighting. Shot 2: Healthy breakfast preparation. Shot 3: Workout session with energy. Shot 4: Commute with upbeat music. Shot 5: Work environment setup. Shot 6: Lunch break with friends. Shot 7: Afternoon productivity. Shot 8: Creative hobby time. Shot 9: Grocery shopping for dinner. Shot 10: Cooking with loved ones. Shot 11: Dinner conversation. Shot 12: Evening relaxation. Shot 13: Skincare routine. Shot 14: Reading before bed. Shot 15: Peaceful sleep preparation.',
    tags: ['lifestyle', 'personal', 'authentic']
  },
  {
    id: 'travel-adventure',
    name: 'Travel Adventure',
    description: 'Exciting travel content with stunning locations',
    category: 'social',
    icon: Plane,
    duration: '90s',
    shots: 12,
    prompt: 'An exciting travel adventure video. Shot 1: Airport departure with anticipation. Shot 2: Airplane taking off into clouds. Shot 3: Arrival at exotic destination. Shot 4: First glimpse of stunning landscape. Shot 5: Local culture and food exploration. Shot 6: Adventure activities like hiking. Shot 7: Sunset at scenic viewpoint. Shot 8: Local market discoveries. Shot 9: Traditional accommodation. Shot 10: Meeting local people. Shot 11: Memorable moments montage. Shot 12: Departure with lasting memories.',
    tags: ['travel', 'adventure', 'exploration']
  }
];

const VideoTemplateSelector: React.FC<VideoTemplateSelectorProps> = ({
  onTemplateSelect,
  className
}) => {
  const [selectedCategory, setSelectedCategory] = useState<string>('all');
  const [selectedTemplate, setSelectedTemplate] = useState<VideoTemplate | null>(null);

  const categories = [
    { id: 'all', name: 'All Templates', icon: Sparkles },
    { id: 'marketing', name: 'Marketing', icon: ShoppingBag },
    { id: 'education', name: 'Education', icon: GraduationCap },
    { id: 'business', name: 'Business', icon: Briefcase },
    { id: 'social', name: 'Social Media', icon: Camera }
  ];

  const filteredTemplates = selectedCategory === 'all' 
    ? videoTemplates 
    : videoTemplates.filter(template => template.category === selectedCategory);

  const handleTemplateSelect = (template: VideoTemplate) => {
    setSelectedTemplate(template);
    onTemplateSelect?.(template);
  };

  return (
    <Card className={className}>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Sparkles className="h-5 w-5" />
          Video Templates
        </CardTitle>
      </CardHeader>

      <CardContent className="space-y-4">
        {/* Category Filter */}
        <div className="flex flex-wrap gap-2">
          {categories.map((category) => {
            const Icon = category.icon;
            return (
              <Button
                key={category.id}
                variant={selectedCategory === category.id ? "default" : "outline"}
                size="sm"
                onClick={() => setSelectedCategory(category.id)}
                className="flex items-center gap-2"
              >
                <Icon className="h-3 w-3" />
                {category.name}
              </Button>
            );
          })}
        </div>

        <Separator />

        {/* Templates Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4 max-h-96 overflow-y-auto">
          {filteredTemplates.map((template) => {
            const Icon = template.icon;
            const isSelected = selectedTemplate?.id === template.id;
            
            return (
              <Card 
                key={template.id}
                className={cn(
                  "cursor-pointer transition-all hover:shadow-md",
                  isSelected && "ring-2 ring-primary"
                )}
                onClick={() => handleTemplateSelect(template)}
              >
                <CardHeader className="pb-2">
                  <div className="flex items-start justify-between">
                    <div className="flex items-center gap-2">
                      <div className="p-2 bg-primary/10 rounded-lg">
                        <Icon className="h-4 w-4 text-primary" />
                      </div>
                      <div>
                        <h3 className="font-medium text-sm">{template.name}</h3>
                        <p className="text-xs text-muted-foreground">{template.description}</p>
                      </div>
                    </div>
                  </div>
                </CardHeader>

                <CardContent className="pt-0">
                  <div className="space-y-2">
                    {/* Template Stats */}
                    <div className="flex items-center gap-4 text-xs text-muted-foreground">
                      <div className="flex items-center gap-1">
                        <Clock className="h-3 w-3" />
                        {template.duration}
                      </div>
                      <div className="flex items-center gap-1">
                        <Camera className="h-3 w-3" />
                        {template.shots} shots
                      </div>
                    </div>

                    {/* Tags */}
                    <div className="flex flex-wrap gap-1">
                      {template.tags.slice(0, 3).map((tag) => (
                        <Badge key={tag} variant="secondary" className="text-xs">
                          {tag}
                        </Badge>
                      ))}
                    </div>

                    {/* Use Template Button */}
                    {isSelected && (
                      <Button size="sm" className="w-full mt-2">
                        <Play className="h-3 w-3 mr-1" />
                        Use This Template
                      </Button>
                    )}
                  </div>
                </CardContent>
              </Card>
            );
          })}
        </div>

        {/* Selected Template Preview */}
        {selectedTemplate && (
          <div className="mt-4 p-4 bg-muted rounded-lg">
            <h4 className="font-medium mb-2">Template Preview: {selectedTemplate.name}</h4>
            <p className="text-sm text-muted-foreground mb-3">
              This template will create a {selectedTemplate.duration} video with {selectedTemplate.shots} shots.
            </p>
            <div className="text-xs bg-background p-3 rounded border">
              <strong>Prompt Preview:</strong>
              <p className="mt-1 line-clamp-3">{selectedTemplate.prompt}</p>
            </div>
          </div>
        )}
      </CardContent>
    </Card>
  );
};

export default VideoTemplateSelector;
