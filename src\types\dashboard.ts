import { ImageHistoryItem } from "@/services/imageHistoryService";
import { VideoHistoryItem } from "@/services/videoService";
import { VoiceHistoryItem, VoiceUsage } from "@/services/voiceService";

export interface DashboardStats {
  totalImages: number;
  totalVideos: number;
  completedVideos: number;
  processingVideos: number;
  totalDuration: number;
  totalVoices: number;
  completedVoices: number;
  totalCharacters: number;
  usagePercentage: number;
  voiceUsage: VoiceUsage | null;
}

export interface DashboardData {
  // Image data
  imageHistoryData: { history: ImageHistoryItem[]; presignedUrls: Record<string, string> } | undefined;
  images: ImageHistoryItem[];
  presignedImageUrls: Record<string, string>;
  isLoadingImages: boolean;
  isFetchingImages: boolean;
  imageError: Error | null;
  refetchImages: () => Promise<any>;

  // Video data
  videos: VideoHistoryItem[];
  isLoadingVideos: boolean;
  isFetchingVideos: boolean;
  videoError: Error | null;
  refetchVideos: () => Promise<any>;

  // Voice data
  voices: VoiceHistoryItem[];
  voiceUsage: VoiceUsage | null;
  isLoadingVoices: boolean;
  isFetchingVoices: boolean;
  isLoadingVoiceUsage: boolean;
  refetchVoices: () => Promise<any>;
  refetchVoiceUsage: () => Promise<any>;

  // Utility functions
  forceVideoFallbackCheck: (userId: string) => Promise<any>;
}

export interface DashboardActions {
  isCheckingFallback: boolean;
  handleRefreshImages: () => Promise<void>;
  handleRefreshVideos: () => Promise<void>;
  handleRefreshVoices: () => Promise<void>;
}

export interface DeleteItem {
  type: 'image' | 'video';
  item: ImageHistoryItem | VideoHistoryItem;
}

export interface DashboardState {
  selectedImage: ImageHistoryItem | null;
  selectedVideo: VideoHistoryItem | null;
  dialogOpen: boolean;
  videoDialogOpen: boolean;
  imageUrl: string;
  activeTab: string;
  deleteDialogOpen: boolean;
  itemToDelete: DeleteItem | null;
}

export interface DashboardHeaderProps {
  user: any;
}

export interface DashboardStatsCardsProps {
  stats: DashboardStats;
  isLoading: boolean;
}

export interface DashboardTabsProps {
  activeTab: string;
  setActiveTab: (tab: string) => void;
  stats: DashboardStats;
  data: DashboardData;
  actions: DashboardActions;
  onSelectImage: (image: ImageHistoryItem) => void;
  onSelectVideo: (video: VideoHistoryItem) => void;
  onDeleteImage: (image: ImageHistoryItem) => void;
  onDeleteVideo: (video: VideoHistoryItem) => void;
  onVideoMetricsUpdate?: (metrics: any) => void; // For development performance monitoring
}