import { useState } from 'react';
import { toast } from 'sonner';
import { 
  COLOR_GUIDED_DEFAULTS, 
  COLOR_GUIDED_LIMITS, 
  COLOR_GUIDED_MESSAGES,
  COLOR_GUIDED_VALIDATION 
} from '@/constants/colorGuided';

export interface ColorItem {
  id: number;
  color: string;
}

export const useColorPalette = () => {
  const [colors, setColors] = useState<ColorItem[]>([
    { id: 1, color: COLOR_GUIDED_DEFAULTS.DEFAULT_COLORS[0] },
    { id: 2, color: COLOR_GUIDED_DEFAULTS.DEFAULT_COLORS[1] },
    { id: 3, color: COLOR_GUIDED_DEFAULTS.DEFAULT_COLORS[2] }
  ]);

  const handleColorChange = (id: number, newColor: string) => {
    if (!COLOR_GUIDED_VALIDATION.isValidHexColor(newColor)) {
      return; // Silently ignore invalid colors during typing
    }
    
    setColors(colors.map(color =>
      color.id === id ? { ...color, color: newColor } : color
    ));
  };

  const addColor = (newColor: string = "#CCCCCC") => {
    if (colors.length >= COLOR_GUIDED_LIMITS.MAX_COLORS) {
      toast.error(COLOR_GUIDED_MESSAGES.ERROR.MAX_COLORS_REACHED);
      return false;
    }

    const newId = Math.max(0, ...colors.map(c => c.id)) + 1;
    setColors([...colors, { id: newId, color: newColor }]);
    return true;
  };

  const removeColor = (id: number) => {
    if (colors.length <= COLOR_GUIDED_LIMITS.MIN_COLORS) {
      toast.error(COLOR_GUIDED_MESSAGES.ERROR.MIN_COLORS_REQUIRED);
      return false;
    }
    
    setColors(colors.filter(color => color.id !== id));
    return true;
  };

  const resetColors = () => {
    setColors([
      { id: 1, color: COLOR_GUIDED_DEFAULTS.DEFAULT_COLORS[0] },
      { id: 2, color: COLOR_GUIDED_DEFAULTS.DEFAULT_COLORS[1] },
      { id: 3, color: COLOR_GUIDED_DEFAULTS.DEFAULT_COLORS[2] }
    ]);
  };

  const getColorValues = (): string[] => {
    return colors.map(c => c.color);
  };

  const isValidPalette = (): boolean => {
    return COLOR_GUIDED_VALIDATION.isValidColorCount(colors.map(c => c.color));
  };

  const canAddColor = (): boolean => {
    return colors.length < COLOR_GUIDED_LIMITS.MAX_COLORS;
  };

  const canRemoveColor = (): boolean => {
    return colors.length > COLOR_GUIDED_LIMITS.MIN_COLORS;
  };

  return {
    // State
    colors,
    
    // Actions
    handleColorChange,
    addColor,
    removeColor,
    resetColors,
    
    // Utilities
    getColorValues,
    isValidPalette,
    canAddColor,
    canRemoveColor
  };
};