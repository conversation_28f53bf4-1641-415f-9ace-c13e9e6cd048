import { useState } from 'react';
import { useForm } from "react-hook-form";
import { z } from "zod";
import { zodResolver } from "@hookform/resolvers/zod";

export const imageFormSchema = z.object({
  prompt: z.string().min(1, {
    message: "Prompt is required.",
  }).max(1000, "Prompt must be less than 1000 characters"),
  style: z.string().optional(),
  negativePrompt: z.string().max(1000, "Negative prompt must be less than 1000 characters").optional(),
  quality: z.enum(["standard", "premium"]).default("standard"),
  size: z.string().default("1024x1024"),
  cfgScale: z.number().min(0).max(10).default(7),
  seed: z.number().min(0).max(2147483647).optional(),
});

export type ImageFormValues = z.infer<typeof imageFormSchema>;

export const useImageForm = () => {
  const [activeSidebarSection, setActiveSidebarSection] = useState<"design" | "advanced">("design");

  const form = useForm<ImageFormValues>({
    resolver: zodResolver(imageFormSchema),
    defaultValues: {
      prompt: "",
      style: "",
      negativePrompt: "",
      quality: "standard",
      size: "1024x1024",
      cfgScale: 7,
      seed: 42,
    },
  });

  const handleReset = () => {
    form.reset();
    setActiveSidebarSection("design");
  };

  return {
    form,
    activeSidebarSection,
    setActiveSidebarSection,
    handleReset,
  };
};