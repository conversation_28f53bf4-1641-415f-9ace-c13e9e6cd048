/**
 * Image generation routes for VibeNecto
 * Handles all image-related API endpoints including generation, background removal,
 * color-guided generation, image variations, and conditioning
 */

const express = require('express');
const { asyncHandler } = require('../middleware/errorHandler');
const {
  imageGenerationLimiter,
  uploadLimiter
} = require('../middleware/rateLimiter');
const {
  validateImageGeneration,
  validateBackgroundRemoval,
  validateColorGuidedGeneration,
  validateImageVariation,
  validateImageConditioning,
  validateReferenceImagePreprocessing
} = require('../middleware/validation');

// Import AWS service modules
const {
  generateImageWithBedrock,
  removeBackgroundWithBedrock,
  generateColorGuidedImageWithBedrock,
  generateImageVariationWithBedrock,
  generateConditionedImageWithBedrock,
  preprocessReferenceImage
} = require('../services/bedrock-service');

// Import utility modules
const {
  formatResponse,
  handleS3Upload,
  handleBedrockCall,
  validateAuthentication,
  recordImageMetrics,
  buildImageResponse,
  handleRouteError
} = require('../utils/routeHelpers');
const { deleteImageRecord } = require('../utils/databaseHelpers');
const { deleteFromS3 } = require('../services/s3-service');

const router = express.Router();

/**
 * Generate an image using AWS Bedrock SDK
 * This endpoint uses the SDK to invoke the Bedrock model and upload to S3
 */
router.post('/generate-image',
  validateImageGeneration,
  imageGenerationLimiter,
  asyncHandler(async (req, res) => {
    const startTime = Date.now();
    const { prompt, options = {}, userId, taskType = 'TEXT_IMAGE' } = req.body;

    try {
      // Validate authentication first
      const authCheck = validateAuthentication(userId, req.requestId);
      if (!authCheck.valid) {
        recordImageMetrics(options.quality, false, Date.now() - startTime);
        return res.status(authCheck.response.status).json(authCheck.response.body);
      }

      // Handle Bedrock call
      const bedrockResult = await handleBedrockCall({
        serviceFunction: generateImageWithBedrock,
        params: { prompt, options },
        operationType: 'Image generation',
        startTime,
        requestId: req.requestId
      });

      if (!bedrockResult.success) {
        recordImageMetrics(options.quality, false, bedrockResult.processingTime);
        return res.status(500).json(formatResponse.error(bedrockResult.error));
      }

      // Handle S3 upload
      const imageType = options.quality === 'premium' ? 'premium' : 'standard';
      const s3Result = await handleS3Upload({
        image: bedrockResult.data.image,
        userId,
        imageType,
        requestId: req.requestId
      });

      // Record successful generation
      recordImageMetrics(imageType, true, bedrockResult.processingTime);

      // Build and return response
      const response = buildImageResponse(bedrockResult.data, s3Result);
      return res.json(response);

    } catch (error) {
      handleRouteError(error, 'Image generation', Date.now() - startTime, userId, req.requestId);
    }
  })
);

/**
 * Remove background from an image using AWS Bedrock SDK
 */
router.post('/remove-background',
  validateBackgroundRemoval,
  uploadLimiter,
  asyncHandler(async (req, res) => {
    const startTime = Date.now();
    const { image, userId } = req.body;

    try {
      // Handle Bedrock call
      const bedrockResult = await handleBedrockCall({
        serviceFunction: removeBackgroundWithBedrock,
        params: { image },
        operationType: 'Background removal',
        startTime,
        requestId: req.requestId
      });

      if (!bedrockResult.success) {
        return res.status(500).json(formatResponse.error(bedrockResult.error));
      }

      // Handle S3 upload (optional for this endpoint)
      const s3Result = await handleS3Upload({
        image: bedrockResult.data.image,
        userId,
        imageType: 'background-removal',
        requestId: req.requestId
      });

      // Build and return response
      const response = buildImageResponse(bedrockResult.data, s3Result);
      return res.json(response);

    } catch (error) {
      handleRouteError(error, 'Background removal', Date.now() - startTime, userId, req.requestId);
    }
  })
);

/**
 * Generate a color-guided image using AWS Bedrock SDK
 */
router.post('/color-guided-generation',
  validateColorGuidedGeneration,
  imageGenerationLimiter,
  asyncHandler(async (req, res) => {
    const startTime = Date.now();
    const { prompt, options = {}, userId } = req.body;

    try {
      // Handle Bedrock call
      const bedrockResult = await handleBedrockCall({
        serviceFunction: generateColorGuidedImageWithBedrock,
        params: { prompt, options },
        operationType: 'Color-guided generation',
        startTime,
        requestId: req.requestId
      });

      if (!bedrockResult.success) {
        return res.status(500).json(formatResponse.error(bedrockResult.error));
      }

      // Handle S3 upload (optional for this endpoint)
      const s3Result = await handleS3Upload({
        image: bedrockResult.data.image,
        userId,
        imageType: 'color-guided',
        requestId: req.requestId
      });

      // Build and return response
      const response = buildImageResponse(bedrockResult.data, s3Result);
      return res.json(response);

    } catch (error) {
      handleRouteError(error, 'Color-guided generation', Date.now() - startTime, userId, req.requestId);
    }
  })
);

/**
 * Generate image variations using AWS Bedrock SDK
 */
router.post('/image-variation',
  validateImageVariation,
  imageGenerationLimiter,
  asyncHandler(async (req, res) => {
    const startTime = Date.now();
    const { options = {}, userId } = req.body;

    try {
      // Handle Bedrock call
      const bedrockResult = await handleBedrockCall({
        serviceFunction: generateImageVariationWithBedrock,
        params: { image: options.image, options },
        operationType: 'Image variation',
        startTime,
        requestId: req.requestId
      });

      if (!bedrockResult.success) {
        return res.status(500).json(formatResponse.error(bedrockResult.error));
      }

      // Handle S3 upload (optional for this endpoint)
      const s3Result = await handleS3Upload({
        image: bedrockResult.data.image,
        userId,
        imageType: 'variation',
        requestId: req.requestId
      });

      // Build and return response
      const response = buildImageResponse(bedrockResult.data, s3Result);
      return res.json(response);

    } catch (error) {
      handleRouteError(error, 'Image variation', Date.now() - startTime, userId, req.requestId);
    }
  })
);

/**
 * Generate a conditioned image using AWS Bedrock SDK
 */
router.post('/image-conditioning',
  validateImageConditioning,
  imageGenerationLimiter,
  asyncHandler(async (req, res) => {
    const startTime = Date.now();
    const { prompt, options = {}, userId } = req.body;

    try {
      // Preprocess reference image format if needed
      if (options.referenceImage && options.referenceImage.startsWith('data:image')) {
        options.referenceImage = options.referenceImage.split(',')[1];
      }

      // Handle Bedrock call
      const bedrockResult = await handleBedrockCall({
        serviceFunction: generateConditionedImageWithBedrock,
        params: { prompt, options },
        operationType: 'Image conditioning',
        startTime,
        requestId: req.requestId
      });

      if (!bedrockResult.success) {
        return res.status(500).json(formatResponse.error(bedrockResult.error));
      }

      // Handle S3 upload (optional for this endpoint)
      const s3Result = await handleS3Upload({
        image: bedrockResult.data.image,
        userId,
        imageType: 'conditioning',
        requestId: req.requestId
      });

      // Build and return response
      const response = buildImageResponse(bedrockResult.data, s3Result);
      return res.json(response);

    } catch (error) {
      handleRouteError(error, 'Image conditioning', Date.now() - startTime, userId, req.requestId);
    }
  })
);

/**
 * Preprocess a reference image for conditioning
 */
router.post('/preprocess-reference-image',
  validateReferenceImagePreprocessing,
  uploadLimiter,
  asyncHandler(async (req, res) => {
    const startTime = Date.now();
    const { image, mode = 'CANNY' } = req.body;

    try {
      // Handle Bedrock call
      const bedrockResult = await handleBedrockCall({
        serviceFunction: preprocessReferenceImage,
        params: { image, mode },
        operationType: 'Reference image preprocessing',
        startTime,
        requestId: req.requestId
      });

      if (!bedrockResult.success) {
        return res.status(500).json(formatResponse.error(bedrockResult.error));
      }

      // Return the preprocessed image (no S3 upload needed)
      return res.json(formatResponse.success({
        image: bedrockResult.data.image
      }));

    } catch (error) {
      handleRouteError(error, 'Reference image preprocessing', Date.now() - startTime, null, req.requestId);
    }
  })
);

/**
 * Delete an image
 */
router.delete('/:imageId', asyncHandler(async (req, res) => {
  const { imageId } = req.params;
  const { userId } = req.query;

  if (!imageId || !userId) {
    return res.status(400).json(formatResponse.error('Image ID and User ID are required'));
  }

  try {
    // Delete image record and get S3 key
    const deleteResult = await deleteImageRecord(imageId, userId, req.requestId);

    if (deleteResult.error) {
      if (!deleteResult.found) {
        return res.status(404).json(formatResponse.error('Image not found or access denied'));
      }
      return res.status(500).json(formatResponse.error('Failed to delete image record', deleteResult.error.message));
    }

    // Delete from S3 if key exists
    let s3DeleteSuccess = true;
    let s3Error = null;

    if (deleteResult.s3Key) {
      try {
        const s3Result = await deleteFromS3({ key: deleteResult.s3Key });
        if (!s3Result.success) {
          s3DeleteSuccess = false;
          s3Error = s3Result.error;
        }
      } catch (s3DeleteError) {
        s3DeleteSuccess = false;
        s3Error = s3DeleteError.message;
      }
    }

    // Build response based on results
    if (s3DeleteSuccess) {
      return res.json(formatResponse.success({
        message: 'Image deleted successfully'
      }));
    } else {
      return res.json(formatResponse.warning({
        message: 'Image record deleted successfully'
      }, `S3 file deletion failed: ${s3Error}. The file may still exist in storage.`));
    }

  } catch (error) {
    handleRouteError(error, 'Image deletion', 0, userId, req.requestId);
  }
}));

module.exports = router;