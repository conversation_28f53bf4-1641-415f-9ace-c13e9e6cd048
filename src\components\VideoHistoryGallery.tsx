import React from 'react';
import { Card, CardContent } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Skeleton } from '@/components/ui/skeleton';
import { AlertTriangle, VideoOff, PlayCircle, Clock, Trash2 } from 'lucide-react';
import { VideoHistoryItem } from '@/services/videoService';
import { Link } from 'react-router-dom';
import { useAuth } from '@/contexts/AuthContext';
import { canDeleteMedia } from '@/services/deleteService';
import VideoWithRetry from '@/components/VideoWithRetry';
import VideoWithLazyLoading from '@/components/VideoWithLazyLoading';
import VideoSkeleton from '@/components/VideoSkeleton';
import { useLazyVideoLoading } from '@/hooks/useLazyVideoLoading';
import { useLazyVideoConfig } from '@/hooks/useLazyVideoConfig';
import { videoPerformanceAnalytics } from '@/services/videoPerformanceAnalytics';

interface VideoHistoryGalleryProps {
  onVideoSelect: (video: VideoHistoryItem) => void;
  onDeleteVideo?: (video: VideoHistoryItem) => void;
  isLoading: boolean;
  error: Error | null;
  videos: VideoHistoryItem[];
  onMetricsUpdate?: (metrics: any) => void; // For development performance monitoring
}

const VideoHistoryGallery: React.FC<VideoHistoryGalleryProps> = ({
  onVideoSelect,
  onDeleteVideo,
  isLoading,
  error,
  videos,
  onMetricsUpdate,
}) => {
  const { user } = useAuth();

  // Get intelligent lazy loading configuration
  const { config: lazyConfig, shouldUseLazyLoading, estimatedBandwidthSavings } = useLazyVideoConfig({
    // Detect connection type if available
    connectionType: (navigator as any)?.connection?.effectiveType || 'unknown',
    // Detect device memory if available
    deviceMemory: (navigator as any)?.deviceMemory || 4,
    // Simple low-end device detection
    isLowEndDevice: (navigator as any)?.deviceMemory < 2 || (navigator as any)?.hardwareConcurrency < 4,
    userPreferences: {
      dataSaver: false, // Could be connected to user settings
      autoPlay: false, // Gallery videos don't autoplay
      highQuality: false, // Gallery uses standard quality
    },
  });

  // Initialize video lazy loading performance tracking
  const {
    metrics,
    recordVideoLoad,
    recordVideoError,
    recordVideoInView,
    recordVideoOutOfView,
    recordProgressiveEnhancement,
    recordRetryAttempt,
    recordLazyLoadingSaving,
    setTotalVideos,
    resetMetrics,
  } = useLazyVideoLoading({
    enableMetrics: import.meta.env.DEV, // Only track in development
    onLoadComplete: (metrics) => {
      if (import.meta.env.DEV) {
        console.log('[VideoHistoryGallery] Video loading complete:', metrics);
      }
      onMetricsUpdate?.(metrics);
    },
  });

  // Track total videos for performance metrics
  React.useEffect(() => {
    if (videos.length > 0) {
      setTotalVideos(videos.length);

      // Log lazy loading configuration in development
      if (import.meta.env.DEV) {
        console.log('[VideoHistoryGallery] Lazy loading config:', {
          shouldUseLazyLoading,
          rootMargin: lazyConfig.rootMargin,
          threshold: lazyConfig.threshold,
          preloadStrategy: lazyConfig.preloadStrategy,
          estimatedBandwidthSavings: `${(estimatedBandwidthSavings / 1024 / 1024).toFixed(1)}MB per video`,
          totalVideos: videos.length,
        });
      }
    }
  }, [videos.length, setTotalVideos, shouldUseLazyLoading, lazyConfig, estimatedBandwidthSavings]);

  // Update metrics callback for development monitoring
  React.useEffect(() => {
    if (import.meta.env.DEV && onMetricsUpdate) {
      onMetricsUpdate(metrics);
    }
  }, [metrics, onMetricsUpdate]);

  const handleVideoClick = (video: VideoHistoryItem, event: React.MouseEvent) => {
    // Prevent opening video details when clicking on delete button
    if ((event.target as HTMLElement).closest('.delete-button')) {
      return;
    }
    onVideoSelect(video);
  };

  const handleDeleteClick = (video: VideoHistoryItem, event: React.MouseEvent) => {
    event.stopPropagation();
    if (onDeleteVideo) {
      onDeleteVideo(video);
    }
  };
  const getStatusColor = (status: string) => {
    if (status === 'completed') return 'text-green-500';
    if (status === 'processing' || status === 'pending') return 'text-blue-500';
    if (status === 'failed') return 'text-red-500';
    return 'text-gray-500';
  };

  const getStatusIcon = (status: string) => {
    if (status === 'completed') return <PlayCircle className="h-8 w-8 text-white" />;
    if (status === 'processing' || status === 'pending') return <Clock className="h-8 w-8 text-white animate-pulse" />;
    if (status === 'failed') return <AlertTriangle className="h-8 w-8 text-white" />;
    return null;
  };
  
  if (isLoading) {
    return (
      <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-6">
        {[...Array(8)].map((_, i) => (
          <VideoSkeleton key={i} className="rounded-lg" aspectRatio="video" />
        ))}
      </div>
    );
  }

  if (error) {
    return (
      <div className="flex flex-col items-center justify-center text-center py-10 px-4 border-2 border-dashed border-red-300 dark:border-red-700 rounded-lg">
        <AlertTriangle className="h-12 w-12 text-red-500 mb-4" />
        <h3 className="text-xl font-semibold mb-2 text-red-700 dark:text-red-400">Error Loading Videos</h3>
        <p className="text-sm text-muted-foreground mb-4">
          We encountered an issue trying to load your video history. Please try again later.
        </p>
        <p className="text-xs text-red-500 dark:text-red-600">Error: {error.message}</p>
        {/* Optionally, add a retry button that calls a passed-in refetch function from Dashboard */}
      </div>
    );
  }

  if (videos.length === 0) {
    return (
      <div className="flex flex-col items-center justify-center text-center py-10 px-4 border-2 border-dashed border-gray-300 dark:border-gray-700 rounded-lg">
        <VideoOff className="h-12 w-12 text-gray-400 dark:text-gray-500 mb-4" />
        <h3 className="text-xl font-semibold mb-2">No Videos Yet</h3>
        <p className="text-sm text-muted-foreground mb-4">
          You haven't generated any videos. Let's create your first one!
        </p>
        <Button asChild>
          <Link to="/video-generator">Create New Video</Link>
        </Button>
      </div>
    );
  }

  return (
    <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-6">
      {videos.map((video) => (
        <Card
          key={video.id || video.job_id}
          className="overflow-hidden cursor-pointer group relative aspect-video"
          onClick={(e) => handleVideoClick(video, e)}
        >
          <CardContent className="p-0">
            {/* Sprint 18 Phase 3: Lazy Loading with Progressive Enhancement */}
            {video.status === 'completed' && (video.presigned_url || video.s3_key) ? (
              <VideoWithLazyLoading
                s3_key={video.s3_key || ''}
                fallbackUrl={video.presigned_url || '/placeholder-video.svg'}
                alt={video.prompt?.substring(0, 50) || 'Generated Video'}
                className="w-full h-full"
                showRetryButton={true}
                showConnectionStatus={false} // Disabled per user request
                autoPlay={false}
                muted={true}
                loop={false}
                rootMargin={lazyConfig.recommendedRootMargin || "200px"}
                threshold={lazyConfig.recommendedThreshold || 0.1}
                videoMetadata={{
                  type: video.video_type,
                  duration: video.duration_seconds,
                  fileSize: video.file_size,
                }}
                onError={(e) => {
                  // Fallback if video fails to load
                  (e.target as HTMLVideoElement).style.display = 'none';
                }}
                onLoadComplete={() => {
                  recordVideoLoad(undefined, undefined, true); // Mark as lazy loaded
                  // Estimate bandwidth savings for videos not immediately loaded
                  if (video.file_size) {
                    recordLazyLoadingSaving(video.file_size);
                  }

                  // Track performance analytics
                  videoPerformanceAnalytics.trackEvent({
                    type: 'load_complete',
                    videoId: video.id,
                    duration: undefined, // Would be calculated from load start
                    metadata: {
                      videoType: video.video_type,
                      fileSize: video.file_size,
                      videoDuration: video.duration_seconds,
                      cacheHit: false, // Would be determined by cache check
                      connectionType: (navigator as any)?.connection?.effectiveType,
                      deviceType: window.innerWidth < 768 ? 'mobile' : window.innerWidth < 1024 ? 'tablet' : 'desktop',
                      deviceMemory: (navigator as any)?.deviceMemory,
                      viewportSize: { width: window.innerWidth, height: window.innerHeight },
                    },
                  });
                }}
                onLoadError={() => {
                  recordVideoError();

                  // Track error analytics
                  videoPerformanceAnalytics.trackEvent({
                    type: 'load_error',
                    videoId: video.id,
                    metadata: {
                      videoType: video.video_type,
                      fileSize: video.file_size,
                      videoDuration: video.duration_seconds,
                      connectionType: (navigator as any)?.connection?.effectiveType,
                      deviceType: window.innerWidth < 768 ? 'mobile' : window.innerWidth < 1024 ? 'tablet' : 'desktop',
                      deviceMemory: (navigator as any)?.deviceMemory,
                    },
                  });
                }}
                onInView={() => {
                  recordVideoInView();

                  // Track viewport entry
                  videoPerformanceAnalytics.trackEvent({
                    type: 'viewport_enter',
                    videoId: video.id,
                    metadata: {
                      videoType: video.video_type,
                      viewportSize: { width: window.innerWidth, height: window.innerHeight },
                    },
                  });
                }}
                onOutOfView={() => {
                  recordVideoOutOfView();

                  // Track viewport exit
                  videoPerformanceAnalytics.trackEvent({
                    type: 'viewport_exit',
                    videoId: video.id,
                    metadata: {
                      videoType: video.video_type,
                    },
                  });
                }}
              />
            ) : (
              <div className="w-full h-full bg-gray-200 dark:bg-gray-800 flex flex-col items-center justify-center p-4 text-center">
                <div className="p-3 bg-black/20 rounded-full mb-2">
                  {getStatusIcon(video.status)}
                </div>
                <span className={`text-sm font-semibold ${getStatusColor(video.status)} uppercase tracking-wider`}>
                  {video.status}
                </span>
                {video.status === 'processing' && (
                  <p className="text-xs text-gray-500 dark:text-gray-400 mt-1">Please wait...</p>
                )}
              </div>
            )}
            <div className="absolute inset-0 bg-gradient-to-t from-black/70 via-transparent to-transparent group-hover:opacity-100 transition-opacity duration-300 flex flex-col justify-end p-3">
              <p className="text-white text-sm font-medium line-clamp-2 leading-tight">
                {video.prompt || 'Video Generation'}
              </p>
              <p className={`text-xs ${getStatusColor(video.status)} capitalize pt-1`}>
                {video.status} {video.status === 'completed' && video.duration_seconds ? `(${video.duration_seconds}s)` : ''}
              </p>
            </div>

            {/* Delete Button */}
            {user && canDeleteMedia(video.user_id, user.id) && onDeleteVideo && (
              <div className="absolute top-2 right-2 opacity-0 group-hover:opacity-100 transition-opacity duration-300">
                <Button
                  variant="destructive"
                  size="icon"
                  className="h-8 w-8 bg-red-600/80 hover:bg-red-600 border-0 delete-button"
                  onClick={(e) => handleDeleteClick(video, e)}
                  title="Delete Video"
                >
                  <Trash2 className="h-4 w-4 text-white" />
                </Button>
              </div>
            )}
          </CardContent>
        </Card>
      ))}
    </div>
  );
};

export default VideoHistoryGallery;
