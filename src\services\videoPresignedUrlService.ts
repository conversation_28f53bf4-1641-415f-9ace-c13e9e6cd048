/**
 * Video Presigned URL Service - Sprint 18 Phase 1
 * 
 * Batch processing service for video presigned URLs with caching, retry logic,
 * and performance optimization. Based on the successful image presigned URL service
 * but adapted for video-specific requirements.
 */

import { getPresignedUrl } from '@/services/s3Service';
import { videoPresignedUrlCache } from '@/services/videoPresignedUrlCache';

export interface VideoPresignedUrlResult {
  s3_key: string;
  url?: string;
  success: boolean;
  error?: string;
  loadTime: number;
  retryCount: number;
  videoType?: 'text-to-video' | 'multi-shot-auto' | 'multi-shot-manual';
  duration?: number;
  fileSize?: number;
}

export interface VideoPresignedUrlBatchOptions {
  timeout?: number; // Timeout per URL in milliseconds
  maxRetries?: number; // Maximum retry attempts per URL
  retryDelay?: number; // Base delay between retries in milliseconds
  concurrencyLimit?: number; // Maximum concurrent requests
  fallbackUrls?: Record<string, string>; // Fallback URLs for failed requests
  enableMetrics?: boolean; // Enable performance metrics logging
  useCache?: boolean; // Enable caching
  cacheTtl?: number; // Cache TTL in milliseconds
  forceRefresh?: boolean; // Force refresh cached URLs
  videoMetadata?: Record<string, { type?: string; duration?: number; fileSize?: number }>; // Video metadata
}

export interface VideoPresignedUrlBatchResult {
  results: VideoPresignedUrlResult[];
  successCount: number;
  failureCount: number;
  totalTime: number;
  averageTime: number;
  cacheHitCount: number;
  cacheHitRate: number;
}

/**
 * Generate a single video presigned URL with timeout and retry logic
 */
const generateVideoPresignedUrlWithRetry = async (
  s3_key: string,
  options: VideoPresignedUrlBatchOptions,
  fallbackUrl?: string,
  videoMetadata?: { type?: string; duration?: number; fileSize?: number }
): Promise<VideoPresignedUrlResult> => {
  const {
    timeout = 8000, // Longer timeout for videos
    maxRetries = 2,
    retryDelay = 1000,
    useCache = true,
    cacheTtl,
    forceRefresh = false
  } = options;

  // Check cache first (unless force refresh is requested)
  if (useCache && !forceRefresh) {
    const cached = videoPresignedUrlCache.get(s3_key);
    if (cached) {
      return {
        s3_key,
        url: cached.url,
        success: true,
        loadTime: 0, // Cache hit, no load time
        retryCount: 0,
        videoType: cached.videoType,
        duration: cached.duration,
        fileSize: cached.fileSize,
      };
    }
  }

  const startTime = performance.now();
  let retryCount = 0;
  let lastError: string = '';

  for (let attempt = 0; attempt <= maxRetries; attempt++) {
    try {
      // Create timeout promise
      const timeoutPromise = new Promise<{ success: false; error: string }>((_, reject) => {
        setTimeout(() => reject({ success: false, error: 'Request timeout' }), timeout);
      });

      // Race between the actual request and timeout
      const urlPromise = getPresignedUrl({ key: s3_key });
      const result = await Promise.race([urlPromise, timeoutPromise]);

      if (result.success && result.url) {
        const loadTime = performance.now() - startTime;

        // Cache the successful result with video metadata
        if (useCache) {
          videoPresignedUrlCache.set(s3_key, result.url, cacheTtl, {
            retryCount: attempt,
            loadTime,
            videoType: videoMetadata?.type as any,
            duration: videoMetadata?.duration,
            fileSize: videoMetadata?.fileSize,
          });
        }

        return {
          s3_key,
          url: result.url,
          success: true,
          loadTime,
          retryCount: attempt,
          videoType: videoMetadata?.type as any,
          duration: videoMetadata?.duration,
          fileSize: videoMetadata?.fileSize,
        };
      } else {
        lastError = result.error || 'Unknown error';
        if (attempt < maxRetries) {
          retryCount++;
          await new Promise(resolve => setTimeout(resolve, retryDelay * (attempt + 1))); // Exponential backoff
        }
      }
    } catch (error) {
      lastError = error instanceof Error ? error.message : 'Request failed';
      if (attempt < maxRetries) {
        retryCount++;
        await new Promise(resolve => setTimeout(resolve, retryDelay * (attempt + 1)));
      }
    }
  }

  // All retries failed, return fallback URL if available
  const loadTime = performance.now() - startTime;
  return {
    s3_key,
    url: fallbackUrl,
    success: !!fallbackUrl,
    error: lastError,
    loadTime,
    retryCount,
    videoType: videoMetadata?.type as any,
    duration: videoMetadata?.duration,
    fileSize: videoMetadata?.fileSize,
  };
};

/**
 * Generate presigned URLs for multiple videos in parallel with controlled concurrency
 */
export const generateVideoPresignedUrlsBatch = async (
  s3_keys: string[],
  options: VideoPresignedUrlBatchOptions = {}
): Promise<VideoPresignedUrlBatchResult> => {
  const {
    concurrencyLimit = 6, // Slightly lower than images due to larger files
    fallbackUrls = {},
    enableMetrics = import.meta.env.DEV,
    videoMetadata = {}
  } = options;

  if (s3_keys.length === 0) {
    return {
      results: [],
      successCount: 0,
      failureCount: 0,
      totalTime: 0,
      averageTime: 0,
      cacheHitCount: 0,
      cacheHitRate: 0,
    };
  }

  const startTime = performance.now();
  let cacheHitCount = 0;

  if (enableMetrics) {
    console.log(`[VideoPresignedUrlService] Starting batch generation for ${s3_keys.length} videos with concurrency limit ${concurrencyLimit}`);
  }

  // Process URLs in batches with controlled concurrency
  const results: VideoPresignedUrlResult[] = [];
  
  for (let i = 0; i < s3_keys.length; i += concurrencyLimit) {
    const batch = s3_keys.slice(i, i + concurrencyLimit);
    
    const batchPromises = batch.map(s3_key => 
      generateVideoPresignedUrlWithRetry(
        s3_key, 
        options, 
        fallbackUrls[s3_key],
        videoMetadata[s3_key]
      )
    );

    const batchResults = await Promise.allSettled(batchPromises);
    
    for (const result of batchResults) {
      if (result.status === 'fulfilled') {
        results.push(result.value);
        if (result.value.loadTime === 0) { // Cache hit
          cacheHitCount++;
        }
      } else {
        // Handle rejected promises
        const s3_key = batch[results.length % batch.length] || 'unknown';
        results.push({
          s3_key,
          success: false,
          error: result.reason?.message || 'Promise rejected',
          loadTime: 0,
          retryCount: 0,
        });
      }
    }

    if (enableMetrics) {
      console.log(`[VideoPresignedUrlService] Completed batch ${Math.floor(i / concurrencyLimit) + 1}/${Math.ceil(s3_keys.length / concurrencyLimit)}`);
    }
  }

  const totalTime = performance.now() - startTime;
  const successCount = results.filter(r => r.success).length;
  const failureCount = results.length - successCount;
  const averageTime = results.length > 0 ? totalTime / results.length : 0;
  const cacheHitRate = results.length > 0 ? (cacheHitCount / results.length) * 100 : 0;

  if (enableMetrics) {
    console.log(`[VideoPresignedUrlService] Batch completed: ${successCount}/${results.length} successful, ${failureCount} failed, ${totalTime.toFixed(0)}ms total, ${averageTime.toFixed(0)}ms avg, ${cacheHitRate.toFixed(1)}% cache hit rate`);
  }

  return {
    results,
    successCount,
    failureCount,
    totalTime,
    averageTime,
    cacheHitCount,
    cacheHitRate,
  };
};

/**
 * Convert batch results to a simple URL map
 */
export const videoBatchResultsToUrlMap = (results: VideoPresignedUrlResult[]): Record<string, string> => {
  const urlMap: Record<string, string> = {};
  
  for (const result of results) {
    if (result.success && result.url) {
      urlMap[result.s3_key] = result.url;
    }
  }
  
  return urlMap;
};

/**
 * Preload video URLs for better performance
 */
export const preloadVideoUrls = async (
  s3_keys: string[],
  options: VideoPresignedUrlBatchOptions = {}
): Promise<void> => {
  const { enableMetrics = import.meta.env.DEV } = options;

  if (enableMetrics) {
    console.log(`[VideoPresignedUrlService] Preloading ${s3_keys.length} video URLs`);
  }

  // Generate URLs in background without waiting for results
  generateVideoPresignedUrlsBatch(s3_keys, {
    ...options,
    useCache: true,
    forceRefresh: false,
  }).catch(error => {
    if (enableMetrics) {
      console.warn('[VideoPresignedUrlService] Preload failed:', error);
    }
  });
};

/**
 * Invalidate cached video URLs for specific patterns
 */
export const invalidateVideoUrlCache = (pattern?: string | RegExp): number => {
  if (pattern) {
    return videoPresignedUrlCache.invalidateByPattern(pattern);
  } else {
    videoPresignedUrlCache.clear();
    return 0;
  }
};

/**
 * Get video cache metrics
 */
export const getVideoCacheMetrics = () => {
  return videoPresignedUrlCache.getMetrics();
};

/**
 * Invalidate video cache for a specific user
 */
export const invalidateVideoUrlCacheForUser = (userId: string): number => {
  return videoPresignedUrlCache.invalidateForUser(userId);
};

/**
 * Get video cache statistics by video type
 */
export const getVideoCacheStatsByType = () => {
  return videoPresignedUrlCache.getStatsByVideoType();
};

export default {
  generateVideoPresignedUrlsBatch,
  videoBatchResultsToUrlMap,
  preloadVideoUrls,
  invalidateVideoUrlCache,
  getVideoCacheMetrics,
  invalidateVideoUrlCacheForUser,
  getVideoCacheStatsByType,
};
