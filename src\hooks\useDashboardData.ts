import { useQuery } from "@tanstack/react-query";
import { useAuth } from "@/contexts/AuthContext";
import { getImageHistory, ImageHistoryItem } from "@/services/imageHistoryService";
import { getVideoHistory, forceVideoFallbackCheck, VideoHistoryItem } from "@/services/videoService";
import { getVoiceHistory, getVoiceUsage, VoiceHistoryItem, VoiceUsage } from "@/services/voiceService";
import { presignedUrlCache } from "@/services/presignedUrlCache";
import { videoPresignedUrlCache } from "@/services/videoPresignedUrlCache";
import { invalidateVideoCache } from "@/services/videoCacheInvalidation";

interface DashboardData {
  // Image data
  imageHistoryData: { history: ImageHistoryItem[]; presignedUrls: Record<string, string> } | undefined;
  images: ImageHistoryItem[];
  presignedImageUrls: Record<string, string>;
  isLoadingImages: boolean;
  isFetchingImages: boolean;
  imageError: Error | null;
  refetchImages: () => Promise<any>;
  invalidateImageCache: () => void;
  getCacheMetrics: () => any;

  // Video data
  videos: VideoHistoryItem[];
  isLoadingVideos: boolean;
  isFetchingVideos: boolean;
  videoError: Error | null;
  refetchVideos: () => Promise<any>;
  invalidateVideoCache: () => void;
  getVideoCacheMetrics: () => any;

  // Voice data
  voices: VoiceHistoryItem[];
  voiceUsage: VoiceUsage | null;
  isLoadingVoices: boolean;
  isFetchingVoices: boolean;
  isLoadingVoiceUsage: boolean;
  refetchVoices: () => Promise<any>;
  refetchVoiceUsage: () => Promise<any>;

  // Utility functions
  forceVideoFallbackCheck: (userId: string) => Promise<any>;
}

export const useDashboardData = (): DashboardData => {
  const { user, checkSuperadminAccess } = useAuth();

  // Cache management functions
  const invalidateImageCache = () => {
    presignedUrlCache.clear();
    if (import.meta.env.DEV) {
      console.log('[useDashboardData] Image cache invalidated');
    }
  };

  const getCacheMetrics = () => {
    return presignedUrlCache.getMetrics();
  };

  // Phase 1: Video cache management functions
  const invalidateVideoCacheFunction = () => {
    videoPresignedUrlCache.clear();
    if (import.meta.env.DEV) {
      console.log('[useDashboardData] Video cache invalidated');
    }
  };

  const getVideoCacheMetrics = () => {
    return videoPresignedUrlCache.getMetrics();
  };

  // Fetch image history for stats (skip for superadmin users)
  const { 
    data: imageHistoryData, 
    refetch: refetchImages, 
    isLoading: isLoadingImages, 
    isFetching: isFetchingImages, 
    error: imageError 
  } = useQuery<
    { history: ImageHistoryItem[]; presignedUrls: Record<string, string> },
    Error
  >({
    queryKey: ['imageHistory', user?.id],
    queryFn: async (): Promise<{ history: ImageHistoryItem[]; presignedUrls: Record<string, string> }> => {
      if (!user?.id || checkSuperadminAccess()) {
        return { history: [], presignedUrls: {} };
      }
      const serviceResult = await getImageHistory(user.id);
      // Check if serviceResult is an array (old format) or object (new format)
      if (Array.isArray(serviceResult)) {
        // This case should ideally not happen if imageHistoryService is updated,
        // but handles it defensively. Assumes presigned URLs would be empty or fetched separately.
        return { history: serviceResult, presignedUrls: {} };
      }
      // If serviceResult is an object like { history: ..., presignedUrls: ... } or null/undefined
      return serviceResult || { history: [], presignedUrls: {} };
    },
    enabled: !!user?.id && !checkSuperadminAccess(),
    refetchOnWindowFocus: false, // Disable automatic refetch on focus to prevent stuck states
    refetchOnMount: true,
    staleTime: 30 * 60 * 1000, // 30 minutes (longer since we have caching)
    gcTime: 45 * 60 * 1000, // 45 minutes (match cache TTL)
    retry: 3, // Limit retries
    retryDelay: 1000, // 1 second delay between retries
  });

  // Fetch video history for stats (skip for superadmin users) with Phase 1 Video Caching
  const {
    data: videos = [],
    refetch: refetchVideos,
    isLoading: isLoadingVideos,
    isFetching: isFetchingVideos,
    error: videoError
  } = useQuery({
    queryKey: ['videoHistory', user?.id],
    queryFn: () => checkSuperadminAccess() ? [] : getVideoHistory(user?.id || '', 50, undefined, undefined, true), // Enable video caching
    enabled: !!user?.id && !checkSuperadminAccess(),
    refetchInterval: (data) => {
      // Skip auto-refresh for superadmin users
      if (checkSuperadminAccess()) return false;
      // Only auto-refresh if there are processing videos, otherwise don't auto-refresh
      if (!data || !Array.isArray(data)) return false;
      const processingVideos = data.filter((v: any) => v.status === 'processing' || v.status === 'pending');
      return processingVideos.length > 0 ? 15000 : false; // 15 seconds if processing, otherwise no auto-refresh
    },
    refetchIntervalInBackground: true, // Allow background refresh without disrupting UI
    refetchOnWindowFocus: false, // Disable automatic refetch on focus to prevent stuck states
    refetchOnMount: true,
    staleTime: 60000, // 1 minute
    gcTime: 300000, // 5 minutes
    retry: 3, // Limit retries
    retryDelay: 1000, // 1 second delay between retries
  });

  // Fetch voice history and usage for stats (skip for superadmin users)
  const { 
    data: voices = [], 
    refetch: refetchVoices, 
    isLoading: isLoadingVoices, 
    isFetching: isFetchingVoices 
  } = useQuery({
    queryKey: ['voiceHistory', user?.id],
    queryFn: async () => {
      if (!user?.id || checkSuperadminAccess()) return [];
      const response = await getVoiceHistory(user.id, 50);
      return response.success ? response.voices || [] : [];
    },
    enabled: !!user?.id && !checkSuperadminAccess(),
    refetchOnWindowFocus: false,
    refetchOnMount: true,
    staleTime: 300000, // 5 minutes
    gcTime: 300000,
    retry: 3,
    retryDelay: 1000,
  });

  const { 
    data: voiceUsage, 
    refetch: refetchVoiceUsage, 
    isLoading: isLoadingVoiceUsage 
  } = useQuery({
    queryKey: ['voiceUsage', user?.id],
    queryFn: async () => {
      if (!user?.id || checkSuperadminAccess()) return null;
      const response = await getVoiceUsage(user.id);
      return response.success ? response.usage || null : null;
    },
    enabled: !!user?.id && !checkSuperadminAccess(),
    refetchOnWindowFocus: false,
    refetchOnMount: true,
    staleTime: 300000, // 5 minutes
    gcTime: 300000,
    retry: 3,
    retryDelay: 1000,
  });

  // Derived data
  const images = imageHistoryData?.history || [];
  const presignedImageUrls = imageHistoryData?.presignedUrls || {};

  return {
    // Image data
    imageHistoryData,
    images,
    presignedImageUrls,
    isLoadingImages,
    isFetchingImages,
    imageError: imageError as Error | null,
    refetchImages,

    // Video data
    videos,
    isLoadingVideos,
    isFetchingVideos,
    videoError: videoError as Error | null,
    refetchVideos,

    // Voice data
    voices,
    voiceUsage: voiceUsage || null,
    isLoadingVoices,
    isFetchingVoices,
    isLoadingVoiceUsage,
    refetchVoices,
    refetchVoiceUsage,

    // Utility functions
    forceVideoFallbackCheck,
    invalidateImageCache,
    getCacheMetrics,
    invalidateVideoCache: invalidateVideoCacheFunction,
    getVideoCacheMetrics,
  };
};