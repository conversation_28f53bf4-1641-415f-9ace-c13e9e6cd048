import React from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Dialog, DialogContent, DialogTrigger } from "@/components/ui/dialog";
import VideoPlayer from "@/components/VideoPlayer";
import { Play, Download, Copy, Share2, Trash2 } from "lucide-react";
import { CompletedVideo } from "@/hooks/useVideoGeneration";
import { VIDEO_GENERATOR_CONFIG } from "@/constants/videoGenerator";

interface VideoResultDisplayProps {
  completedVideos: CompletedVideo[];
  onDownload: (videoUrl: string) => void;
  onGenerateNew: () => void;
  showInGenerateTab?: boolean;
}

const VideoResultDisplay: React.FC<VideoResultDisplayProps> = ({
  completedVideos,
  onDownload,
  onGenerateNew,
  showInGenerateTab = false,
}) => {
  if (completedVideos.length === 0) {
    return null;
  }

  const latestVideo = completedVideos[completedVideos.length - 1];

  if (showInGenerateTab) {
    // Simplified version for the Generate tab
    return (
      <div className="mt-6 bg-white dark:bg-gray-800 rounded-lg p-6 shadow-sm">
        <div className="flex items-center justify-center">
          <Dialog>
            <DialogTrigger asChild>
              <Button
                size="lg"
                className="bg-brand-purple hover:bg-brand-purple/90"
              >
                <Play size={20} className="mr-2" />
                {VIDEO_GENERATOR_CONFIG.PLAY_VIDEO}
              </Button>
            </DialogTrigger>
            <DialogContent className="max-w-4xl w-[90vw] max-h-[90vh] p-0">
              <div className="aspect-video w-full">
                <VideoPlayer
                  src={latestVideo.videoUrl}
                  title={`Generated Video ${latestVideo.videoId}`}
                  autoPlay={true}
                  className="w-full h-full rounded-lg overflow-hidden"
                />
              </div>
            </DialogContent>
          </Dialog>
        </div>
      </div>
    );
  }

  // Full version for the Result tab
  return (
    <div className="max-w-4xl mx-auto">
      <div className="bg-white dark:bg-gray-800 rounded-lg p-6 shadow-sm">
        <h3 className="text-lg font-medium mb-4 text-center">Your Generated Video</h3>

        <div className="rounded-lg overflow-hidden bg-gray-100 dark:bg-gray-900 mb-4 aspect-video flex items-center justify-center">
          <div className="relative group w-full h-full">
            <VideoPlayer
              src={latestVideo.videoUrl}
              title={`Generated Video ${latestVideo.videoId}`}
              autoPlay={false}
              className="w-full h-full"
            />

            {/* Video actions overlay */}
            <div className="absolute bottom-4 left-1/2 transform -translate-x-1/2 opacity-0 group-hover:opacity-100 transition-opacity flex gap-2 bg-black/50 backdrop-blur-sm rounded-full p-1.5">
              <Button 
                variant="ghost" 
                size="icon" 
                className="h-8 w-8 text-white hover:bg-white/20"
                onClick={() => onDownload(latestVideo.videoUrl)}
              >
                <Download size={16} />
              </Button>
              <Button variant="ghost" size="icon" className="h-8 w-8 text-white hover:bg-white/20">
                <Copy size={16} />
              </Button>
              <Button variant="ghost" size="icon" className="h-8 w-8 text-white hover:bg-white/20">
                <Share2 size={16} />
              </Button>
            </div>
          </div>
        </div>

        <p className="text-center text-sm text-gray-500 mb-4">
          {VIDEO_GENERATOR_CONFIG.SUCCESS_MESSAGE}
        </p>

        <div className="flex items-center justify-center gap-3">
          <Button
            size="sm"
            variant="outline"
            className="h-9 text-sm gap-2"
            onClick={onGenerateNew}
          >
            <Trash2 size={14} />
            {VIDEO_GENERATOR_CONFIG.GENERATE_NEW}
          </Button>
          <Button
            size="sm"
            className="h-9 text-sm gap-2 bg-brand-purple hover:bg-brand-purple/90"
            onClick={() => onDownload(latestVideo.videoUrl)}
          >
            <Download size={14} />
            {VIDEO_GENERATOR_CONFIG.DOWNLOAD}
          </Button>
        </div>
      </div>
    </div>
  );
};

export default VideoResultDisplay;