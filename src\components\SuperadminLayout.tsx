import React from 'react';
import { Link, useNavigate } from 'react-router-dom';
import { useAuth } from '@/contexts/AuthContext';
import { Button } from '@/components/ui/button';
import { Crown, LogOut, User, Loader2 } from 'lucide-react';
import { Separator } from '@/components/ui/separator';

interface SuperadminLayoutProps {
  children: React.ReactNode;
}

const SuperadminLayout: React.FC<SuperadminLayoutProps> = ({ children }) => {
  const { user, logout, isLoggingOut } = useAuth();
  const navigate = useNavigate();

  const handleLogout = () => {
    logout(() => navigate("/"));
  };

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Top Navigation Bar */}
      <div className="bg-white border-b border-gray-200 px-6 py-4">
        <div className="flex items-center justify-between">
          {/* Logo and Title */}
          <div className="flex items-center space-x-4">
            <Link to="/" className="flex items-center space-x-2">
              <img src="/logo.png" alt="VibeNecto Logo" className="h-8 w-auto" />
              <span className="text-lg font-semibold bg-clip-text text-transparent bg-gradient-to-r from-brand-purple to-brand-teal">
                VibeNecto
              </span>
            </Link>
            <Separator orientation="vertical" className="h-6" />
            <div className="flex items-center space-x-2">
              <Crown className="h-6 w-6 text-yellow-500" />
              <span className="text-xl font-bold text-gray-900">Superadmin Dashboard</span>
            </div>
          </div>

          {/* User Info and Actions */}
          <div className="flex items-center space-x-4">
            {/* User Info */}
            <div className="flex items-center space-x-3 px-3 py-2 rounded-lg bg-gray-50">
              <div className="flex items-center justify-center w-8 h-8 rounded-full bg-yellow-100 text-yellow-600">
                <Crown size={16} />
              </div>
              <div className="text-sm">
                <p className="font-medium">{user?.name || 'Superadmin'}</p>
                <p className="text-gray-500">{user?.email}</p>
              </div>
            </div>

            {/* Logout Button */}
            <Button
              variant="outline"
              size="sm"
              onClick={handleLogout}
              disabled={isLoggingOut}
              className="flex items-center space-x-2"
            >
              {isLoggingOut ? (
                <>
                  <Loader2 size={16} className="animate-spin" />
                  <span>Signing Out</span>
                </>
              ) : (
                <>
                  <LogOut size={16} />
                  <span>Sign Out</span>
                </>
              )}
            </Button>
          </div>
        </div>
      </div>

      {/* Main Content */}
      <div className="p-6">
        {children}
      </div>
    </div>
  );
};

export default SuperadminLayout;