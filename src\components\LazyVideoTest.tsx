import React, { useState } from 'react';
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON>Header, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Play, Eye, EyeOff, Zap, Database } from 'lucide-react';
import LazyVideo from '@/components/LazyVideo';
import VideoWithLazyLoading from '@/components/VideoWithLazyLoading';
import { useLazyVideoConfig } from '@/hooks/useLazyVideoConfig';

/**
 * LazyVideoTest Component - Sprint 18 Phase 3
 * 
 * Development-only test component for verifying lazy video loading functionality.
 * Provides visual feedback and metrics for testing different lazy loading scenarios.
 * 
 * Features:
 * - Test different lazy loading configurations
 * - Visual indicators for loading states
 * - Performance metrics display
 * - Development-only rendering
 */
const LazyVideoTest: React.FC = () => {
  const [loadedVideos, setLoadedVideos] = useState<Set<string>>(new Set());
  const [videosInView, setVideosInView] = useState<Set<string>>(new Set());
  const [loadTimes, setLoadTimes] = useState<Record<string, number>>({});

  // Get lazy loading configuration
  const { config, shouldUseLazyLoading, estimatedBandwidthSavings } = useLazyVideoConfig({
    connectionType: 'unknown',
    deviceMemory: 4,
    isLowEndDevice: false,
    userPreferences: {
      dataSaver: false,
      autoPlay: false,
      highQuality: false,
    },
  });

  // Don't render in production
  if (!import.meta.env.DEV) {
    return null;
  }

  const handleVideoLoad = (videoId: string, loadTime?: number) => {
    setLoadedVideos(prev => new Set([...prev, videoId]));
    if (loadTime) {
      setLoadTimes(prev => ({ ...prev, [videoId]: loadTime }));
    }
  };

  const handleVideoInView = (videoId: string) => {
    setVideosInView(prev => new Set([...prev, videoId]));
  };

  const handleVideoOutOfView = (videoId: string) => {
    setVideosInView(prev => {
      const newSet = new Set(prev);
      newSet.delete(videoId);
      return newSet;
    });
  };

  const testVideos = [
    {
      id: 'test-1',
      src: '/placeholder-video.svg',
      title: 'Test Video 1',
      metadata: { type: 'text-to-video', duration: 10, fileSize: 5 * 1024 * 1024 },
    },
    {
      id: 'test-2',
      src: '/placeholder-video.svg',
      title: 'Test Video 2',
      metadata: { type: 'multi-shot-auto', duration: 15, fileSize: 8 * 1024 * 1024 },
    },
    {
      id: 'test-3',
      src: '/placeholder-video.svg',
      title: 'Test Video 3',
      metadata: { type: 'multi-shot-manual', duration: 20, fileSize: 12 * 1024 * 1024 },
    },
  ];

  const formatBytes = (bytes: number) => {
    if (bytes === 0) return '0 B';
    const k = 1024;
    const sizes = ['B', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(1)) + ' ' + sizes[i];
  };

  return (
    <div className="space-y-6 p-6 bg-blue-50/50 dark:bg-blue-950/20 rounded-lg border border-blue-200 dark:border-blue-800">
      <div className="flex items-center gap-2">
        <Zap className="h-5 w-5 text-blue-500" />
        <h3 className="text-lg font-semibold">Lazy Video Loading Test</h3>
        <Badge variant="outline" className="text-blue-600 border-blue-600">
          Development Only
        </Badge>
      </div>

      {/* Configuration Display */}
      <Card>
        <CardHeader className="pb-3">
          <CardTitle className="text-sm">Current Configuration</CardTitle>
        </CardHeader>
        <CardContent className="space-y-2">
          <div className="grid grid-cols-2 gap-4 text-sm">
            <div>
              <span className="font-medium">Lazy Loading:</span>{' '}
              {shouldUseLazyLoading ? '✅ Enabled' : '❌ Disabled'}
            </div>
            <div>
              <span className="font-medium">Root Margin:</span> {config.rootMargin}
            </div>
            <div>
              <span className="font-medium">Threshold:</span> {config.threshold}
            </div>
            <div>
              <span className="font-medium">Preload:</span> {config.preloadStrategy}
            </div>
          </div>
          <div className="text-sm text-muted-foreground">
            <span className="font-medium">Estimated Savings:</span> {formatBytes(estimatedBandwidthSavings)} per video
          </div>
        </CardContent>
      </Card>

      {/* Metrics Display */}
      <Card>
        <CardHeader className="pb-3">
          <CardTitle className="text-sm flex items-center gap-2">
            <Database className="h-4 w-4" />
            Live Metrics
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-2">
          <div className="grid grid-cols-3 gap-4 text-sm">
            <div className="flex items-center gap-2">
              <Play className="h-4 w-4 text-green-500" />
              <span>Loaded: {loadedVideos.size}/{testVideos.length}</span>
            </div>
            <div className="flex items-center gap-2">
              <Eye className="h-4 w-4 text-blue-500" />
              <span>In View: {videosInView.size}</span>
            </div>
            <div className="flex items-center gap-2">
              <EyeOff className="h-4 w-4 text-gray-500" />
              <span>Hidden: {testVideos.length - videosInView.size}</span>
            </div>
          </div>
          {Object.keys(loadTimes).length > 0 && (
            <div className="text-xs text-muted-foreground">
              <span className="font-medium">Load Times:</span>{' '}
              {Object.entries(loadTimes)
                .map(([id, time]) => `${id}: ${time.toFixed(0)}ms`)
                .join(', ')}
            </div>
          )}
        </CardContent>
      </Card>

      {/* Test Videos Grid */}
      <div className="space-y-4">
        <h4 className="text-sm font-medium">Test Videos (Scroll to test lazy loading)</h4>
        <div className="space-y-8">
          {testVideos.map((video, index) => (
            <Card key={video.id} className="overflow-hidden">
              <CardHeader className="pb-2">
                <div className="flex items-center justify-between">
                  <CardTitle className="text-sm">{video.title}</CardTitle>
                  <div className="flex items-center gap-2">
                    {loadedVideos.has(video.id) && (
                      <Badge variant="outline" className="text-green-600 border-green-600 text-xs">
                        Loaded
                      </Badge>
                    )}
                    {videosInView.has(video.id) && (
                      <Badge variant="outline" className="text-blue-600 border-blue-600 text-xs">
                        In View
                      </Badge>
                    )}
                  </div>
                </div>
              </CardHeader>
              <CardContent>
                <div className="aspect-video bg-gray-100 dark:bg-gray-800 rounded-lg overflow-hidden">
                  <VideoWithLazyLoading
                    s3_key={`test-${video.id}`}
                    fallbackUrl={video.src}
                    alt={video.title}
                    className="w-full h-full"
                    rootMargin={config.rootMargin}
                    threshold={config.threshold}
                    videoMetadata={video.metadata}
                    onLoadComplete={() => handleVideoLoad(video.id)}
                    onInView={() => handleVideoInView(video.id)}
                    onOutOfView={() => handleVideoOutOfView(video.id)}
                  />
                </div>
                <div className="mt-2 text-xs text-muted-foreground">
                  {video.metadata.type} • {video.metadata.duration}s • {formatBytes(video.metadata.fileSize || 0)}
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      </div>

      {/* Instructions */}
      <Card className="bg-yellow-50 dark:bg-yellow-950/20 border-yellow-200 dark:border-yellow-800">
        <CardContent className="pt-4">
          <div className="text-sm space-y-2">
            <p className="font-medium">Testing Instructions:</p>
            <ul className="list-disc list-inside space-y-1 text-muted-foreground">
              <li>Scroll down to see videos enter the viewport</li>
              <li>Watch the "In View" and "Loaded" badges update</li>
              <li>Check browser DevTools Network tab to see lazy loading in action</li>
              <li>Videos should only load when they approach the viewport</li>
              <li>Load times should be tracked and displayed</li>
            </ul>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

export default LazyVideoTest;
