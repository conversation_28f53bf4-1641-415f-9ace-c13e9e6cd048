
import { useEffect, useState } from "react";
import { useNavigate } from "react-router-dom";
import { useAuth } from "@/contexts/AuthContext";
import { toast } from "sonner";
import { supabase } from "@/lib/supabase";

const ProtectedRoute = ({ children }: { children: React.ReactNode }) => {
  const { isAuthenticated, isLoading, isLoggingOut } = useAuth();
  const navigate = useNavigate();
  const [isCheckingAuth, setIsCheckingAuth] = useState(false);

  useEffect(() => {
    let mounted = true;
    
    // Check if the user is authenticated
    const checkAuth = async () => {
      // Skip check if we're in the process of logging out
      if (isLoggingOut || !mounted) return;

      if (!isLoading && !isAuthenticated && mounted) {
        setIsCheckingAuth(true);

        try {
          // Double-check with Supabase directly
          const { data } = await supabase.auth.getSession();

          if (!data.session && mounted) {
            toast.error("Please sign in to access this page");
            navigate("/signin");
          }
        } catch (error) {
          if (import.meta.env.DEV) {
            if (import.meta.env.DEV) {
              if (import.meta.env.DEV) {
                console.error("Error checking authentication:", error);
              }
            }
          }
          if (mounted) {
            toast.error("Authentication error. Please sign in again.");
            navigate("/signin");
          }
        } finally {
          if (mounted) {
            setIsCheckingAuth(false);
          }
        }
      }
    };

    checkAuth();
    
    return () => {
      mounted = false;
    };
  }, [isAuthenticated, isLoading, navigate, isLoggingOut]);

  if (isLoading || isCheckingAuth) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="animate-pulse text-brand-purple font-semibold">Loading...</div>
      </div>
    );
  }

  return isAuthenticated ? <>{children}</> : null;
};

export default ProtectedRoute;
