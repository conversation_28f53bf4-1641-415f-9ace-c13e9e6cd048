import { useMemo } from "react";
import { ImageHistoryItem } from "@/services/imageHistoryService";
import { VideoHistoryItem } from "@/services/videoService";
import { VoiceHistoryItem, VoiceUsage } from "@/services/voiceService";

interface DashboardStats {
  totalImages: number;
  totalVideos: number;
  completedVideos: number;
  processingVideos: number;
  totalDuration: number;
  totalVoices: number;
  completedVoices: number;
  totalCharacters: number;
  usagePercentage: number;
  voiceUsage: VoiceUsage | null;
}

export const formatDuration = (seconds: number): string => {
  if (seconds < 60) {
    return `${seconds}s`;
  } else if (seconds < 3600) {
    const minutes = Math.floor(seconds / 60);
    const remainingSeconds = seconds % 60;
    return remainingSeconds > 0 ? `${minutes}m ${remainingSeconds}s` : `${minutes}m`;
  } else {
    const hours = Math.floor(seconds / 3600);
    const minutes = Math.floor((seconds % 3600) / 60);
    return minutes > 0 ? `${hours}h ${minutes}m` : `${hours}h`;
  }
};

interface UseDashboardStatsProps {
  images: ImageHistoryItem[];
  videos: VideoHistoryItem[];
  voices: VoiceHistoryItem[];
  voiceUsage: VoiceUsage | null;
}

export const useDashboardStats = ({
  images,
  videos,
  voices,
  voiceUsage,
}: UseDashboardStatsProps): DashboardStats => {
  const stats = useMemo(() => {
    const totalImages = images.length;
    const totalVideos = videos.length;
    const completedVideos = videos.filter(v => v.status === 'completed').length;
    const processingVideos = videos.filter(v => v.status === 'processing' || v.status === 'pending').length; // Include pending
    const totalDuration = videos
      .filter(v => v.status === 'completed')
      .reduce((sum, v) => sum + (v.duration_seconds || 0), 0); // Handle potentially undefined duration
    
    // Voice statistics
    const totalVoices = voices.length;
    const completedVoices = voices.filter(v => v.status === 'completed').length;
    const totalCharacters = voices.reduce((sum, v) => sum + (v.character_count || 0), 0);
    const usagePercentage = voiceUsage ? Math.round((voiceUsage.charactersUsed / voiceUsage.monthlyLimit) * 100) : 0;
    
    return {
      totalImages,
      totalVideos,
      completedVideos,
      processingVideos,
      totalDuration,
      totalVoices,
      completedVoices,
      totalCharacters,
      usagePercentage,
      voiceUsage
    };
  }, [images, videos, voices, voiceUsage]);

  return stats;
};