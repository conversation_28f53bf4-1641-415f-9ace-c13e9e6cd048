/**
 * Utility functions for handling base64 encoding and decoding for image processing
 * Used in AWS Bedrock Titan Image Generator integration
 *
 * Includes functions for:
 * - Converting base64 to Blob
 * - Converting base64 to data URL
 * - Converting base64 to downloadable URL
 * - Converting File/Blob to base64
 * - Converting between image formats
 */

/**
 * Converts a base64 string to a Blob object
 * @param base64 - The base64 string to convert
 * @param mimeType - The MIME type of the resulting blob (default: 'image/png')
 * @returns A Blob object representing the image
 */
export const base64ToBlob = (base64: string, mimeType: string = 'image/png'): Blob => {
  // Remove data URL prefix if present
  const base64Data = base64.startsWith('data:')
    ? base64.split(',')[1]
    : base64;

  // Convert base64 to binary
  const byteCharacters = atob(base64Data);
  const byteArrays = [];

  for (let offset = 0; offset < byteCharacters.length; offset += 512) {
    const slice = byteCharacters.slice(offset, offset + 512);

    const byteNumbers = new Array(slice.length);
    for (let i = 0; i < slice.length; i++) {
      byteNumbers[i] = slice.charCodeAt(i);
    }

    const byteArray = new Uint8Array(byteNumbers);
    byteArrays.push(byteArray);
  }

  return new Blob(byteArrays, { type: mimeType });
};

/**
 * Creates a downloadable URL from a base64 string
 * @param base64 - The base64 string to convert
 * @param mimeType - The MIME type of the resulting blob (default: 'image/png')
 * @returns A URL that can be used for downloading the image
 */
export const base64ToDownloadableUrl = (base64: string, mimeType: string = 'image/png'): string => {
  const blob = base64ToBlob(base64, mimeType);
  return URL.createObjectURL(blob);
};

/**
 * Converts a File or Blob to a base64 string
 * @param file - The File or Blob to convert
 * @returns A Promise that resolves to the base64 string
 */
export const fileToBase64 = (file: File | Blob): Promise<string> => {
  return new Promise((resolve, reject) => {
    const reader = new FileReader();
    reader.readAsDataURL(file);
    reader.onload = () => {
      if (typeof reader.result === 'string') {
        // Remove the data URL prefix (e.g., "data:image/png;base64,")
        const base64 = reader.result.split(',')[1];
        resolve(base64);
      } else {
        reject(new Error('Failed to convert file to base64'));
      }
    };
    reader.onerror = error => reject(error);
  });
};

/**
 * Creates a data URL from a base64 string
 * @param base64 - The base64 string
 * @param mimeType - The MIME type of the image (default: 'image/png')
 * @returns A data URL that can be used as an image source
 */
export const base64ToDataUrl = (base64Input: string, mimeType: string = 'image/png'): string => {
  let b64 = base64Input.trim();
  const desiredPrefix = `data:${mimeType};base64,`;

  // Repeatedly strip any data URI-like prefix (e.g., "data:image/jpeg;base64,")
  // until we get to (hopefully) the raw base64 string.
  while (b64.startsWith('data:') && b64.includes(',')) {
    b64 = b64.substring(b64.indexOf(',') + 1);
  }

  // If after stripping, b64 is empty or effectively whitespace, it's invalid.
  if (!b64.trim()) {
    // Log warning in development only
    if (import.meta.env.DEV) {
      console.warn('base64ToDataUrl: Input resulted in empty base64 string after stripping prefixes');
    }
    return ''; // Return empty string for invalid/empty base64
  }

  return desiredPrefix + b64;
};

/**
 * Converts an image from one format to another
 * @param imageUrl - The URL or data URL of the image to convert
 * @param outputFormat - The desired output format (e.g., 'png', 'jpeg', 'webp')
 * @param quality - The quality of the output image (0-1, only applies to lossy formats like JPEG)
 * @returns A Promise that resolves to a data URL of the converted image
 */
export const convertImageFormat = (
  imageUrl: string,
  outputFormat: string = 'png',
  quality: number = 0.9
): Promise<string> => {
  return new Promise((resolve, reject) => {
    const img = new Image();
    img.crossOrigin = 'anonymous';
    img.onload = () => {
      const canvas = document.createElement('canvas');
      canvas.width = img.width;
      canvas.height = img.height;
      const ctx = canvas.getContext('2d');

      if (ctx) {
        ctx.drawImage(img, 0, 0);

        const mimeType = `image/${outputFormat}`;
        const outputQuality = outputFormat === 'jpeg' || outputFormat === 'webp' ? quality : undefined;

        try {
          const dataUrl = canvas.toDataURL(mimeType, outputQuality);
          resolve(dataUrl);
        } catch (error) {
          reject(new Error(`Failed to convert image to ${outputFormat}: ${error}`));
        }
      } else {
        reject(new Error('Failed to get canvas context'));
      }
    };
    img.onerror = () => reject(new Error('Failed to load image'));
    img.src = imageUrl;
  });
};
