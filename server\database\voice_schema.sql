-- Voice Generation Database Schema
-- This file contains the database schema for voice generation features

-- Create voice_history table for storing voice generation history
CREATE TABLE IF NOT EXISTS voice_history (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id UUID NOT NULL REFERENCES profiles(id) ON DELETE CASCADE,
  text_content TEXT NOT NULL,
  voice_id VARCHAR(50) NOT NULL,
  language_code VARCHAR(10) NOT NULL DEFAULT 'en-US',
  engine VARCHAR(20) NOT NULL DEFAULT 'standard',
  parameters JSONB NOT NULL DEFAULT '{}',
  s3_key VARCHAR(255),
  s3_url VARCHAR(500),
  duration_seconds INTEGER,
  character_count INTEGER NOT NULL,
  request_characters INTEGER, -- AWS Polly billing characters
  content_type VARCHAR(50) DEFAULT 'audio/mpeg',
  status VARCHAR(20) DEFAULT 'completed',
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  completed_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create indexes for efficient querying
CREATE INDEX IF NOT EXISTS idx_voice_history_user_id ON voice_history(user_id);
CREATE INDEX IF NOT EXISTS idx_voice_history_created_at ON voice_history(created_at DESC);
CREATE INDEX IF NOT EXISTS idx_voice_history_status ON voice_history(status);
CREATE INDEX IF NOT EXISTS idx_voice_history_voice_id ON voice_history(voice_id);
CREATE INDEX IF NOT EXISTS idx_voice_history_language ON voice_history(language_code);

-- Create composite index for user queries
CREATE INDEX IF NOT EXISTS idx_voice_history_user_created ON voice_history(user_id, created_at DESC);

-- Enable Row Level Security
ALTER TABLE voice_history ENABLE ROW LEVEL SECURITY;

-- Create RLS policies for voice_history - users can only access their own records
-- PRODUCTION SAFE: Policies will fail gracefully if they already exist
DO $$
BEGIN
  -- Create SELECT policy
  BEGIN
    EXECUTE 'CREATE POLICY "Users can view their own voice history" ON voice_history
      FOR SELECT USING (auth.uid() = user_id)';
    RAISE NOTICE 'Created SELECT policy for voice_history';
  EXCEPTION
    WHEN duplicate_object THEN
      RAISE NOTICE 'SELECT policy for voice_history already exists';
  END;

  -- Create INSERT policy
  BEGIN
    EXECUTE 'CREATE POLICY "Users can insert their own voice history" ON voice_history
      FOR INSERT WITH CHECK (auth.uid() = user_id)';
    RAISE NOTICE 'Created INSERT policy for voice_history';
  EXCEPTION
    WHEN duplicate_object THEN
      RAISE NOTICE 'INSERT policy for voice_history already exists';
  END;

  -- Create UPDATE policy
  BEGIN
    EXECUTE 'CREATE POLICY "Users can update their own voice history" ON voice_history
      FOR UPDATE USING (auth.uid() = user_id)';
    RAISE NOTICE 'Created UPDATE policy for voice_history';
  EXCEPTION
    WHEN duplicate_object THEN
      RAISE NOTICE 'UPDATE policy for voice_history already exists';
  END;

  -- Create DELETE policy
  BEGIN
    EXECUTE 'CREATE POLICY "Users can delete their own voice history" ON voice_history
      FOR DELETE USING (auth.uid() = user_id)';
    RAISE NOTICE 'Created DELETE policy for voice_history';
  EXCEPTION
    WHEN duplicate_object THEN
      RAISE NOTICE 'DELETE policy for voice_history already exists';
  END;
END $$;

-- Create voice_usage_tracking table for monthly usage limits
CREATE TABLE IF NOT EXISTS voice_usage_tracking (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id UUID NOT NULL REFERENCES profiles(id) ON DELETE CASCADE,
  month_year VARCHAR(7) NOT NULL, -- Format: YYYY-MM
  characters_used INTEGER NOT NULL DEFAULT 0,
  voices_generated INTEGER NOT NULL DEFAULT 0,
  last_updated TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  UNIQUE(user_id, month_year)
);

-- Create indexes for usage tracking
CREATE INDEX IF NOT EXISTS idx_voice_usage_user_month ON voice_usage_tracking(user_id, month_year);
CREATE INDEX IF NOT EXISTS idx_voice_usage_month ON voice_usage_tracking(month_year);

-- Enable RLS for usage tracking
ALTER TABLE voice_usage_tracking ENABLE ROW LEVEL SECURITY;

-- Create RLS policies for usage tracking
-- PRODUCTION SAFE: Policies will fail gracefully if they already exist
DO $$
BEGIN
  -- Create SELECT policy for usage tracking
  BEGIN
    EXECUTE 'CREATE POLICY "Users can view their own voice usage" ON voice_usage_tracking
      FOR SELECT USING (auth.uid() = user_id)';
    RAISE NOTICE 'Created SELECT policy for voice_usage_tracking';
  EXCEPTION
    WHEN duplicate_object THEN
      RAISE NOTICE 'SELECT policy for voice_usage_tracking already exists';
  END;

  -- Create INSERT policy for usage tracking
  BEGIN
    EXECUTE 'CREATE POLICY "Users can insert their own voice usage" ON voice_usage_tracking
      FOR INSERT WITH CHECK (auth.uid() = user_id)';
    RAISE NOTICE 'Created INSERT policy for voice_usage_tracking';
  EXCEPTION
    WHEN duplicate_object THEN
      RAISE NOTICE 'INSERT policy for voice_usage_tracking already exists';
  END;

  -- Create UPDATE policy for usage tracking
  BEGIN
    EXECUTE 'CREATE POLICY "Users can update their own voice usage" ON voice_usage_tracking
      FOR UPDATE USING (auth.uid() = user_id)';
    RAISE NOTICE 'Created UPDATE policy for voice_usage_tracking';
  EXCEPTION
    WHEN duplicate_object THEN
      RAISE NOTICE 'UPDATE policy for voice_usage_tracking already exists';
  END;
END $$;

-- Function to update voice usage tracking
CREATE OR REPLACE FUNCTION update_voice_usage(
  p_user_id UUID,
  p_characters INTEGER,
  p_voices INTEGER DEFAULT 1
) RETURNS void AS $$
DECLARE
  current_month VARCHAR(7);
BEGIN
  current_month := TO_CHAR(NOW(), 'YYYY-MM');
  
  INSERT INTO voice_usage_tracking (user_id, month_year, characters_used, voices_generated)
  VALUES (p_user_id, current_month, p_characters, p_voices)
  ON CONFLICT (user_id, month_year)
  DO UPDATE SET
    characters_used = voice_usage_tracking.characters_used + p_characters,
    voices_generated = voice_usage_tracking.voices_generated + p_voices,
    last_updated = NOW();
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to get current month usage for a user
CREATE OR REPLACE FUNCTION get_current_voice_usage(p_user_id UUID)
RETURNS TABLE(characters_used INTEGER, voices_generated INTEGER) AS $$
DECLARE
  current_month VARCHAR(7);
BEGIN
  current_month := TO_CHAR(NOW(), 'YYYY-MM');
  
  RETURN QUERY
  SELECT 
    COALESCE(vut.characters_used, 0) as characters_used,
    COALESCE(vut.voices_generated, 0) as voices_generated
  FROM voice_usage_tracking vut
  WHERE vut.user_id = p_user_id AND vut.month_year = current_month
  UNION ALL
  SELECT 0, 0
  WHERE NOT EXISTS (
    SELECT 1 FROM voice_usage_tracking vut
    WHERE vut.user_id = p_user_id AND vut.month_year = current_month
  )
  LIMIT 1;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Grant necessary permissions
GRANT USAGE ON SCHEMA public TO authenticated;
GRANT ALL ON voice_history TO authenticated;
GRANT ALL ON voice_usage_tracking TO authenticated;
GRANT EXECUTE ON FUNCTION update_voice_usage(UUID, INTEGER, INTEGER) TO authenticated;
GRANT EXECUTE ON FUNCTION get_current_voice_usage(UUID) TO authenticated;

-- Add comments for documentation
COMMENT ON TABLE voice_history IS 'Stores voice generation history and metadata for all users';
COMMENT ON TABLE voice_usage_tracking IS 'Tracks monthly voice generation usage for quota management';
COMMENT ON FUNCTION update_voice_usage(UUID, INTEGER, INTEGER) IS 'Updates voice usage statistics for a user';
COMMENT ON FUNCTION get_current_voice_usage(UUID) IS 'Gets current month voice usage for a user';