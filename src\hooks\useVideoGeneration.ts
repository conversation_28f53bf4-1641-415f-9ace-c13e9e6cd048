import { useState, useCallback } from "react";
import { useAuth } from "@/contexts/AuthContext";
import { useQueryClient } from "@tanstack/react-query";

export interface ActiveJob {
  jobId: string;
  videoId: string;
  estimatedCompletionTime: string;
}

export interface CompletedVideo {
  videoUrl: string;
  videoId: string;
  jobId: string;
}

export const useVideoGeneration = () => {
  const { user } = useAuth();
  const queryClient = useQueryClient();
  
  const [activeJobs, setActiveJobs] = useState<ActiveJob[]>([]);
  const [completedVideos, setCompletedVideos] = useState<CompletedVideo[]>([]);

  // Handle new video generation
  const handleVideoGenerated = useCallback((jobId: string, videoId: string, estimatedCompletionTime: string) => {
    const newJob: ActiveJob = {
      jobId,
      videoId,
      estimatedCompletionTime
    };
    setActiveJobs(prev => [...prev, newJob]);
  }, []);

  // Handle video completion with query invalidation
  const handleVideoComplete = useCallback((videoUrl: string, videoId: string, jobId: string) => {
    // Remove from active jobs
    setActiveJobs(prev => prev.filter(job => job.jobId !== jobId));

    // Add to completed videos
    setCompletedVideos(prev => [...prev, { videoUrl, videoId, jobId }]);

    // Invalidate video history query to refresh the data immediately
    // Use setTimeout to ensure database transaction is committed
    setTimeout(() => {
      try {
        queryClient.invalidateQueries({ queryKey: ['videoHistory'] });
        if (user?.id) {
          queryClient.invalidateQueries({ queryKey: ['videoHistory', user.id] });
        }
        // Force refetch to ensure immediate update
        queryClient.refetchQueries({ queryKey: ['videoHistory'] });
        if (user?.id) {
          queryClient.refetchQueries({ queryKey: ['videoHistory', user.id] });
        }
      } catch (error) {
        // Silently handle query invalidation errors to prevent UI from getting stuck
        if (import.meta.env.DEV) {
          console.warn("Query invalidation failed:", error);
        }
      }
    }, 1000); // Wait 1 second for database transaction to complete
  }, [queryClient, user]);

  // Handle video generation error
  const handleVideoError = useCallback((error: string, jobId: string) => {
    if (import.meta.env.DEV) {
      console.error('Video generation error:', error);
    }
    // Remove from active jobs
    setActiveJobs(prev => prev.filter(job => job.jobId !== jobId));
  }, []);

  return {
    activeJobs,
    completedVideos,
    handleVideoGenerated,
    handleVideoComplete,
    handleVideoError,
  };
};