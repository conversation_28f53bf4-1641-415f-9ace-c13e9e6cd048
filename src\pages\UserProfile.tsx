import React, { useState } from "react";
import { useForm } from "react-hook-form";
import { z } from "zod";
import { zodResolver } from "@hookform/resolvers/zod";
import { Button } from "@/components/ui/button";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { useAuth } from "@/contexts/AuthContext";
import DashboardSidebar from "@/components/DashboardSidebar";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Separator } from "@/components/ui/separator";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { toast } from "sonner";
import {
  User,
  Camera,
  Shield,
  KeyRound,
  Bell,
  RefreshCw,
  ChevronRight,
  Lock,
  Smartphone,
  Mail,
  UserCircle,
  LogOut
} from "lucide-react";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Ta<PERSON>Trigger } from "@/components/ui/tabs";
import { Switch } from "@/components/ui/switch";
import { Label } from "@/components/ui/label";
import { Badge } from "@/components/ui/badge";

const profileSchema = z.object({
  name: z.string().min(2, { message: "Name must be at least 2 characters" }),
  email: z.string().email({ message: "Please enter a valid email address" }),
});

type ProfileFormValues = z.infer<typeof profileSchema>;

const passwordSchema = z.object({
  newPassword: z.string().min(8, { message: "Password must be at least 8 characters" }),
  confirmPassword: z.string().min(8, { message: "Password must be at least 8 characters" }),
}).refine((data) => data.newPassword === data.confirmPassword, {
  message: "Passwords do not match",
  path: ["confirmPassword"], // Set error on confirmPassword field
});

type PasswordFormValues = z.infer<typeof passwordSchema>;

const UserProfile = () => {
  const { user, updateProfile, logout, updateUserPassword } = useAuth();
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [isUpdatingPassword, setIsUpdatingPassword] = useState(false);
  const [activeTab, setActiveTab] = useState("account");

  const form = useForm<ProfileFormValues>({
    resolver: zodResolver(profileSchema),
    defaultValues: {
      name: user?.name || "",
      email: user?.email || "",
    },
  });

  const onSubmit = async (data: ProfileFormValues) => {
    setIsSubmitting(true);
    try {
      await updateProfile({
        name: data.name,
        email: data.email,
      });
      toast.success("Profile updated successfully");
    } catch (error) {
      if (import.meta.env.DEV) {
        console.error("Profile update failed", error);
      }
    } finally {
      setIsSubmitting(false);
    }
  };

  const passwordForm = useForm<PasswordFormValues>({
    resolver: zodResolver(passwordSchema),
    defaultValues: {
      newPassword: "",
      confirmPassword: "",
    },
  });

  const onSubmitPasswordChange = async (data: PasswordFormValues) => {
    setIsUpdatingPassword(true);
    try {
      await updateUserPassword(data.newPassword);
      toast.success("Password updated successfully!");
      passwordForm.reset();
    } catch (error) {
      // Error toast is handled by AuthContext, but you can add specific UI feedback here if needed
      if (import.meta.env.DEV) {
        console.error("Password update failed on page:", error);
      }
    } finally {
      setIsUpdatingPassword(false);
    }
  };

  return (
    <>
      <DashboardSidebar />

      <main className="ml-64 min-h-screen bg-gray-50 dark:bg-gray-900 flex flex-col">
        {/* Header */}
        <div className="sticky top-0 z-10 bg-white dark:bg-gray-900 border-b border-gray-200 dark:border-gray-800">
          <div className="px-8 py-4 flex items-center justify-between">
            <h1 className="text-2xl font-semibold">Your Profile</h1>
            {/* Buttons moved below the form */}
          </div>
        </div>

        <div className="flex-1 overflow-auto p-8">
          <div className="max-w-6xl mx-auto">
            {/* Profile Header */}
            <div className="mb-8 bg-gradient-to-r from-brand-purple/10 to-brand-teal/10 rounded-xl p-8 flex flex-col md:flex-row items-center md:items-start gap-6">
              <div className="relative group">
                <Avatar className="h-28 w-28 border-4 border-white dark:border-gray-800 shadow-md">
                  <AvatarImage src="" alt={user?.name} />
                  <AvatarFallback className="text-3xl bg-gradient-to-br from-brand-purple to-brand-teal text-white">
                    {user?.name ? user.name.charAt(0).toUpperCase() : <User />}
                  </AvatarFallback>
                </Avatar>
                {/* Avatar upload functionality removed */}
              </div>

              <div className="text-center md:text-left md:flex-1">
                <h2 className="text-2xl font-bold">{user?.name || 'User'}</h2>
                <p className="text-gray-600 dark:text-gray-400">{user?.email || '<EMAIL>'}</p>
                <div className="mt-2 flex flex-wrap justify-center md:justify-start gap-2">
                  <Badge variant="outline" className="bg-brand-purple/10 text-brand-purple border-brand-purple/20">
                    Free Plan
                  </Badge>
                  <Badge variant="outline" className="bg-gray-100 dark:bg-gray-800 text-gray-600 dark:text-gray-400">
                    Member since {user?.createdAt ? new Date(user.createdAt).toLocaleDateString() : "Details unavailable"}
                  </Badge>
                </div>
              </div>
            </div>

            {/* Tabs and Content */}
            <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
              <TabsList className="grid grid-cols-2 mb-8">
                <TabsTrigger value="account" className="data-[state=active]:bg-brand-purple data-[state=active]:text-white">
                  <UserCircle size={16} className="mr-2" />
                  Account
                </TabsTrigger>
                <TabsTrigger value="security" className="data-[state=active]:bg-brand-purple data-[state=active]:text-white">
                  <Shield size={16} className="mr-2" />
                  Security
                </TabsTrigger>
                {/* Notifications Tab Trigger Removed */}
              </TabsList>

              <TabsContent value="account" className="space-y-6">
                <Card className="overflow-hidden border-none shadow-md">
                  <CardHeader className="bg-white dark:bg-gray-800 pb-2">
                    <CardTitle className="flex items-center text-xl">
                      <UserCircle size={20} className="mr-2 text-brand-purple" />
                      Personal Information
                    </CardTitle>
                    <CardDescription>
                      Update your personal details and how others see you on the platform
                    </CardDescription>
                  </CardHeader>
                  <CardContent className="pt-6">
                    <Form {...form}>
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <FormField
                          control={form.control}
                          name="name"
                          render={({ field }) => (
                            <FormItem>
                              <FormLabel>Full Name</FormLabel>
                              <FormControl>
                                <Input
                                  placeholder="John Doe"
                                  {...field}
                                  className="h-11 rounded-lg border-gray-200 dark:border-gray-700 focus:border-brand-purple focus:ring-brand-purple/20"
                                />
                              </FormControl>
                              <FormMessage />
                            </FormItem>
                          )}
                        />

                        <FormField
                          control={form.control}
                          name="email"
                          render={({ field }) => (
                            <FormItem>
                              <FormLabel>Email Address</FormLabel>
                              <FormControl>
                                <Input
                                  type="email"
                                  placeholder="<EMAIL>"
                                  {...field}
                                  className="h-11 rounded-lg border-gray-200 dark:border-gray-700 focus:border-brand-purple focus:ring-brand-purple/20"
                                />
                              </FormControl>
                              <FormMessage />
                            </FormItem>
                          )}
                        />
                      </div>
                    </Form>
                    <div className="mt-6 flex justify-end gap-4">
                      <Button variant="outline" size="sm" onClick={() => form.reset()}>
                        <RefreshCw size={14} className="mr-1.5" />
                        Reset
                      </Button>
                      <Button size="sm" onClick={form.handleSubmit(onSubmit)} disabled={isSubmitting}>
                        {isSubmitting ? "Saving..." : "Save Changes"}
                      </Button>
                    </div>
                  </CardContent>
                </Card>

                {/* Email Preferences Card Removed */}
                {/* Account Actions Card Removed */}
              </TabsContent>

              <TabsContent value="security" className="space-y-6">
                <Card className="overflow-hidden border-none shadow-md">
                  <CardHeader className="bg-white dark:bg-gray-800 pb-2">
                    <CardTitle className="flex items-center text-xl">
                      <KeyRound size={20} className="mr-2 text-brand-purple" />
                      Password
                    </CardTitle>
                    <CardDescription>
                      Change your password to keep your account secure
                    </CardDescription>
                  </CardHeader>
                  <CardContent className="pt-6">
                    <Form {...passwordForm}>
                      <form onSubmit={passwordForm.handleSubmit(onSubmitPasswordChange)} className="space-y-6">
                        <FormField
                          control={passwordForm.control}
                          name="newPassword"
                          render={({ field }) => (
                            <FormItem>
                              <FormLabel>New Password</FormLabel>
                              <FormControl>
                                <Input
                                  type="password"
                                  placeholder="••••••••"
                                  {...field}
                                  className="h-11 rounded-lg border-gray-200 dark:border-gray-700 focus:border-brand-purple focus:ring-brand-purple/20"
                                />
                              </FormControl>
                              <FormMessage />
                            </FormItem>
                          )}
                        />
                        <FormField
                          control={passwordForm.control}
                          name="confirmPassword"
                          render={({ field }) => (
                            <FormItem>
                              <FormLabel>Confirm New Password</FormLabel>
                              <FormControl>
                                <Input
                                  type="password"
                                  placeholder="••••••••"
                                  {...field}
                                  className="h-11 rounded-lg border-gray-200 dark:border-gray-700 focus:border-brand-purple focus:ring-brand-purple/20"
                                />
                              </FormControl>
                              <FormMessage />
                            </FormItem>
                          )}
                        />
                        <div className="flex justify-end">
                          <Button type="submit" disabled={isUpdatingPassword} className="sm:w-auto w-full">
                            {isUpdatingPassword ? "Updating..." : "Update Password"}
                          </Button>
                        </div>
                      </form>
                    </Form>
                  </CardContent>
                </Card>

                {/* Two-Factor Authentication Card Removed */}

                <Card className="overflow-hidden border-none shadow-md">
                  <CardHeader className="bg-white dark:bg-gray-800 pb-2">
                    <CardTitle className="flex items-center text-xl">
                      <Lock size={20} className="mr-2 text-brand-purple" />
                      Login Sessions
                    </CardTitle>
                    <CardDescription>
                      Manage your active sessions and devices
                    </CardDescription>
                  </CardHeader>
                  <CardContent className="pt-6 space-y-4">
                    <div className="bg-gray-50 dark:bg-gray-800/50 rounded-lg p-4 flex items-center justify-between">
                      <div className="flex items-center gap-3">
                        <div className="bg-green-100 dark:bg-green-900/30 text-green-600 dark:text-green-400 p-2 rounded-full">
                          <Computer size={16} />
                        </div>
                        <div>
                          <h4 className="font-medium flex items-center gap-2">
                            Current Session
                            <Badge className="bg-green-100 text-green-600 border-green-200">Active</Badge>
                          </h4>
                          <p className="text-xs text-gray-500 dark:text-gray-400">Desktop • Current Browser • Active now</p>
                        </div>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              </TabsContent>

              {/* Notifications TabsContent Removed */}
            </Tabs>
          </div>
        </div>
      </main>
    </>
  );
};

// Computer icon component for the login sessions section
const Computer = ({ size = 24, ...props }) => (
  <svg
    xmlns="http://www.w3.org/2000/svg"
    width={size}
    height={size}
    viewBox="0 0 24 24"
    fill="none"
    stroke="currentColor"
    strokeWidth="2"
    strokeLinecap="round"
    strokeLinejoin="round"
    {...props}
  >
    <rect x="2" y="3" width="20" height="14" rx="2" />
    <line x1="8" x2="16" y1="21" y2="21" />
    <line x1="12" x2="12" y1="17" y2="21" />
  </svg>
);

export default UserProfile;
