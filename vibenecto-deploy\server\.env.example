# Server Environment Variables Example
# Copy this file to .env and fill in your actual values

# Server Configuration
PORT=3001

# AWS Configuration
AWS_REGION=us-east-1
AWS_ACCESS_KEY_ID=your_aws_access_key_id
AWS_SECRET_ACCESS_KEY=your_aws_secret_access_key

# S3 Configuration
AWS_S3_BUCKET_NAME=vibenecto-images-prod
# Remove duplicate - use AWS_S3_BUCKET_NAME consistently

# Note: S3 bucket will be used for both images and videos
# Videos will be stored in users/{userId}/videos/ subdirectory

# Supabase Configuration (for server-side operations)
SUPABASE_URL=https://your-project-id.supabase.co
SUPABASE_SERVICE_KEY=your_supabase_service_key
SUPABASE_JWT_SECRET=your_supabase_jwt_secret

# Stripe Configuration (for payment processing)
STRIPE_SECRET_KEY=your_stripe_secret_key
STRIPE_WEBHOOK_SECRET=your_stripe_webhook_secret

# Optional: Logging Configuration
LOG_LEVEL=info

# Production CORS Configuration
ALLOWED_ORIGINS=https://vibenecto.com,https://www.vibenecto.com
