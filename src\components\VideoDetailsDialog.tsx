import React, { useState } from "react";
import {
  <PERSON><PERSON>,
  <PERSON>alog<PERSON>ontent,
  <PERSON><PERSON>T<PERSON>le,
  DialogTrigger,
} from "@/components/ui/dialog";
import { But<PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Separator } from "@/components/ui/separator";
import { ScrollArea } from "@/components/ui/scroll-area";
import { toast } from "sonner";
import {
  Info,
  Download,
  Share2,
  Copy,
  RefreshCw,
  Calendar,
  Clock,
  Film,
  Settings,
  ExternalLink,
  FileVideo,
  Sparkles,
  Layers,
  Trash2
} from "lucide-react";
import { VideoHistoryItem, formatDuration, getVideoTypeDisplayName } from "@/services/videoService";
import VideoPlayer from "./VideoPlayer";

interface VideoDetailsDialogProps {
  video: VideoHistoryItem;
  trigger?: React.ReactNode;
  open?: boolean;
  onOpenChange?: (open: boolean) => void;
  onRegenerate?: (video: VideoHistoryItem) => void;
  onShare?: (video: VideoHistoryItem) => void;
  onDelete?: (video: VideoHistoryItem) => void;
}

const VideoDetailsDialog: React.FC<VideoDetailsDialogProps> = ({
  video,
  trigger,
  open,
  onOpenChange,
  onRegenerate,
  onShare,
  onDelete
}) => {
  const [isDownloading, setIsDownloading] = useState(false);

  // Format creation date to full date
  const formatFullDate = (dateString: string) => {
    if (!dateString) return "Unknown date";
    try {
      return new Date(dateString).toLocaleString();
    } catch (error) {
      return "Unknown date";
    }
  };

  // Get video type display name
  const getVideoTypeDisplay = (type: string) => {
    // Type guard to ensure we pass the correct type
    if (type === 'text-to-video' || type === 'multi-shot-auto' || type === 'multi-shot-manual') {
      return getVideoTypeDisplayName(type);
    }
    return 'Unknown';
  };

  // Get video type icon
  const getVideoTypeIcon = (type: string) => {
    switch (type) {
      case 'TEXT_VIDEO':
        return Sparkles;
      case 'MULTI_SHOT_AUTOMATED':
        return Film;
      case 'MULTI_SHOT_MANUAL':
        return Layers;
      default:
        return FileVideo;
    }
  };

  // Handle download
  const handleDownload = async () => {
    if (!video.presigned_url) {
      toast.error("Video URL not available");
      return;
    }

    setIsDownloading(true);
    try {
      const response = await fetch(video.presigned_url);
      const blob = await response.blob();

      const url = window.URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = `video-${video.id}.mp4`;
      document.body.appendChild(a);
      a.click();
      window.URL.revokeObjectURL(url);
      document.body.removeChild(a);

      toast.success("Video downloaded successfully");
    } catch (error) {
      if (import.meta.env.DEV) {
        console.error("Download error:", error);
      }
      toast.error("Failed to download video");
    } finally {
      setIsDownloading(false);
    }
  };

  // Handle copy link
  const handleCopyLink = () => {
    if (video.presigned_url) {
      navigator.clipboard.writeText(video.presigned_url);
      toast.success("Video link copied to clipboard");
    } else {
      toast.error("Video link not available");
    }
  };

  // Handle regenerate
  const handleRegenerate = () => {
    onRegenerate?.(video);
    toast.info("Starting video regeneration with same parameters");
  };

  // Handle delete
  const handleDelete = () => {
    onDelete?.(video);
  };

  const VideoTypeIcon = getVideoTypeIcon(video.video_type);

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      {trigger && (
        <DialogTrigger asChild>
          {trigger}
        </DialogTrigger>
      )}

      <DialogContent className="max-w-5xl w-[90vw] max-h-[90vh] flex flex-col p-0 gap-0 overflow-hidden">
        <div className="flex h-full">
          {/* Video section (70%) */}
          <div className="w-[70%] h-[80vh] bg-gray-50 dark:bg-gray-900 flex items-center justify-center overflow-hidden">
            {video.status === 'completed' && video.presigned_url ? (
              <div className="w-full h-full flex items-center justify-center">
                <VideoPlayer
                  src={video.presigned_url}
                  title={`Video ${video.id}`}
                  className="max-w-full max-h-full object-contain"
                />
              </div>
            ) : (
              <div className="flex flex-col items-center justify-center text-gray-500 dark:text-gray-400">
                <Film className="h-16 w-16 mb-4" />
                <p className="text-lg font-medium mb-2">
                  {video.status === 'processing' ? 'Video Processing...' :
                   video.status === 'pending' ? 'Video Pending...' :
                   video.status === 'failed' ? 'Video Generation Failed' :
                   'Video Not Available'}
                </p>
                <p className="text-sm">Status: {video.status}</p>
              </div>
            )}
          </div>

          {/* Metadata section (30%) */}
          <div className="w-[30%] p-4 border-l border-gray-200 dark:border-gray-800 flex flex-col h-[80vh]">
            <div className="mb-4">
              <DialogTitle className="text-lg font-medium">Video Details</DialogTitle>
            </div>

            <ScrollArea className="flex-1 pr-4">
              {/* Video type and date */}
              <div className="mb-4">
                <Badge variant="outline" className="mb-2">
                  {getVideoTypeDisplay(video.video_type)}
                </Badge>
                <div className="flex items-center text-sm text-gray-500 dark:text-gray-400">
                  <Calendar size={14} className="mr-1" />
                  <span>{formatFullDate(video.created_at)}</span>
                </div>
              </div>

              <Separator className="my-3" />

              {/* Status */}
              <div className="mb-4">
                <h4 className="text-sm font-medium mb-1 flex items-center">
                  <Info size={14} className="mr-1" /> Status
                </h4>
                <Badge variant="outline" className="text-xs">
                  {video.status}
                </Badge>
              </div>

              {/* Prompt */}
              {video.prompt && (
                <div className="mb-4">
                  <h4 className="text-sm font-medium mb-1 flex items-center">
                    <Info size={14} className="mr-1" /> Prompt
                  </h4>
                  <p className="text-sm text-gray-600 dark:text-gray-300">
                    {video.prompt}
                  </p>
                </div>
              )}

              {/* Parameters */}
              {video.parameters && Object.keys(video.parameters).length > 0 && (
                <div className="mb-4">
                  <h4 className="text-sm font-medium mb-2 flex items-center">
                    <Settings size={14} className="mr-1" /> Parameters
                  </h4>
                  <div className="space-y-2">
                    {video.duration_seconds && (
                      <div className="flex justify-between">
                        <span className="text-xs text-gray-500">Duration</span>
                        <span className="text-xs font-medium">{formatDuration(video.duration_seconds)}</span>
                      </div>
                    )}
                    {video.parameters.seed && (
                      <div className="flex justify-between">
                        <span className="text-xs text-gray-500">Seed</span>
                        <span className="text-xs font-medium font-mono">{video.parameters.seed}</span>
                      </div>
                    )}
                    {video.parameters.options?.durationSeconds && (
                      <div className="flex justify-between">
                        <span className="text-xs text-gray-500">Requested Duration</span>
                        <span className="text-xs font-medium">{video.parameters.options.durationSeconds}s</span>
                      </div>
                    )}
                    {video.reference_images && video.reference_images.length > 0 && (
                      <div className="flex justify-between">
                        <span className="text-xs text-gray-500">Reference Images</span>
                        <span className="text-xs font-medium">{video.reference_images.length} image(s)</span>
                      </div>
                    )}
                  </div>
                </div>
              )}

              {/* Additional Info */}
              <div className="mb-4">
                <h4 className="text-sm font-medium mb-2">Additional Info</h4>
                <div className="space-y-2">
                  {video.completed_at && (
                    <div className="flex justify-between">
                      <span className="text-xs text-gray-500">Completed</span>
                      <span className="text-xs font-medium">{formatFullDate(video.completed_at)}</span>
                    </div>
                  )}
                </div>
              </div>

              {/* Error Information */}
              {video.status === 'failed' && video.error_message && (
                <div className="mb-4">
                  <h4 className="text-sm font-medium mb-2 text-red-600 dark:text-red-400">Error</h4>
                  <p className="text-xs text-red-700 dark:text-red-300 bg-red-50 dark:bg-red-900/20 p-2 rounded">
                    {video.error_message}
                  </p>
                </div>
              )}
            </ScrollArea>

            {/* Actions */}
            <div className="mt-auto pt-4 border-t border-gray-200 dark:border-gray-800 space-y-2">
              {video.status === 'completed' && video.presigned_url && (
                <>
                  <Button
                    className="w-full"
                    onClick={handleDownload}
                    disabled={isDownloading}
                    variant="default"
                  >
                    <Download size={16} className="mr-2" />
                    {isDownloading ? "Downloading..." : "Download Video"}
                  </Button>

                  <div className="flex gap-2">
                    <Button
                      className="flex-1"
                      variant="outline"
                      onClick={handleCopyLink}
                      size="sm"
                    >
                      <Copy size={14} className="mr-1" />
                      Copy Link
                    </Button>

                    <Button
                      className="flex-1"
                      variant="outline"
                      onClick={() => onShare?.(video)}
                      size="sm"
                    >
                      <Share2 size={14} className="mr-1" />
                      Share
                    </Button>
                  </div>
                </>
              )}

              <div className="flex gap-2">
                <Button
                  className="flex-1"
                  variant="outline"
                  onClick={handleRegenerate}
                  size="sm"
                >
                  <RefreshCw size={14} className="mr-2" />
                  Regenerate
                </Button>

                {onDelete && (
                  <Button
                    variant="destructive"
                    size="sm"
                    onClick={handleDelete}
                    className="flex-1"
                  >
                    <Trash2 size={14} className="mr-2" />
                    Delete
                  </Button>
                )}
              </div>
            </div>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
};

export default VideoDetailsDialog;
