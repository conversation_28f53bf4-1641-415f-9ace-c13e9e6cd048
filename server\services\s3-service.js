/**
 * AWS S3 service utilities for VibeNecto
 * Handles file storage operations using AWS S3
 */

// Load environment variables from .env file
require('dotenv').config();

// Import logger
const { logger } = require('../utils/logger');

const {
  S3Client,
  PutObjectCommand,
  DeleteObjectCommand,
  GetObjectCommand,
  ListObjectsV2Command
} = require('@aws-sdk/client-s3');

const { getSignedUrl } = require('@aws-sdk/s3-request-presigner');

// Initialize AWS S3 client
const s3Client = new S3Client({
  region: process.env.AWS_REGION || 'us-east-1'
});

// Default bucket name - use consistent environment variable naming
const DEFAULT_BUCKET_NAME = process.env.AWS_S3_BUCKET_NAME || 'vibenecto-storage';

/**
 * Helper function to convert a stream to buffer
 * @param {Stream} stream - The stream to convert
 * @returns {Promise<Buffer>} - The buffer containing the stream data
 */
async function streamToBuffer(stream) {
  const chunks = [];
  return new Promise((resolve, reject) => {
    stream.on('data', (chunk) => chunks.push(chunk));
    stream.on('error', reject);
    stream.on('end', () => resolve(Buffer.concat(chunks)));
  });
}

/**
 * Uploads an image to S3
 * @param {Object} params - Parameters for the upload
 * @param {string} params.image - Base64 encoded image data
 * @param {string} params.userId - User ID for organizing images
 * @param {string} params.imageType - Type of image (standard, premium, etc.)
 * @param {string} params.filename - Optional filename
 * @returns {Promise<Object>} - The upload result
 */
async function uploadImageToS3(params) {
  const { image, userId, imageType = 'standard', filename = null } = params;

  try {
    // Create a unique filename if not provided
    const timestamp = Date.now();
    const uniqueFilename = filename || `${imageType}-${timestamp}.png`;

    // Create the S3 path with user ID for organization
    const s3Path = `users/${userId}/${imageType}/${uniqueFilename}`;

    // Convert base64 to binary
    const imageBuffer = Buffer.from(image, 'base64');

    // Create the command to upload the object
    const command = new PutObjectCommand({
      Bucket: DEFAULT_BUCKET_NAME,
      Key: s3Path,
      Body: imageBuffer,
      ContentType: 'image/png'
    });

    // Upload the image
    logger.info('Uploading image to S3', {
      bucket: DEFAULT_BUCKET_NAME,
      key: s3Path,
      userId,
      imageType
    });
    await s3Client.send(command);

    // Generate the S3 URL
    const region = process.env.AWS_REGION || 'us-east-1';
    const s3Url = `https://${DEFAULT_BUCKET_NAME}.s3.${region}.amazonaws.com/${s3Path}`;

    return {
      success: true,
      url: s3Url,
      key: s3Path
    };
  } catch (error) {
    logger.error('Error uploading image to S3', {
      error: error.message,
      bucket: DEFAULT_BUCKET_NAME,
      userId,
      imageType
    });
    return {
      success: false,
      error: error.message || 'Failed to upload image to S3'
    };
  }
}

/**
 * Uploads an audio file to S3
 * @param {Object} params - Parameters for the upload
 * @param {string} params.audio - Base64 encoded audio data
 * @param {string} params.userId - User ID for organizing audio files
 * @param {string} params.filename - Optional filename
 * @param {string} params.contentType - Audio content type
 * @returns {Promise<Object>} - The upload result
 */
async function uploadVoiceToS3(params) {
  const { audio, userId, filename = null, contentType = 'audio/mpeg' } = params;

  try {
    // Create a unique filename if not provided
    const timestamp = Date.now();
    const extension = contentType.includes('mpeg') ? 'mp3' : 'ogg';
    const uniqueFilename = filename || `voice-${timestamp}.${extension}`;

    // Create the S3 path with user ID for organization
    const s3Path = `users/${userId}/voices/${uniqueFilename}`;

    // Convert base64 to binary
    const audioBuffer = Buffer.from(audio, 'base64');

    // Create the command to upload the object
    const command = new PutObjectCommand({
      Bucket: DEFAULT_BUCKET_NAME,
      Key: s3Path,
      Body: audioBuffer,
      ContentType: contentType
    });

    // Upload the audio file
    logger.info('Uploading voice to S3', {
      bucket: DEFAULT_BUCKET_NAME,
      key: s3Path,
      userId,
      contentType
    });
    await s3Client.send(command);

    // Generate the S3 URL
    const region = process.env.AWS_REGION || 'us-east-1';
    const s3Url = `https://${DEFAULT_BUCKET_NAME}.s3.${region}.amazonaws.com/${s3Path}`;

    return {
      success: true,
      url: s3Url,
      key: s3Path,
      filename: uniqueFilename
    };
  } catch (error) {
    logger.error('Error uploading voice to S3', {
      error: error.message,
      bucket: DEFAULT_BUCKET_NAME,
      userId
    });
    return {
      success: false,
      error: error.message || 'Failed to upload voice to S3'
    };
  }
}

/**
 * Generates a presigned URL for an S3 object
 * @param {Object} params - Parameters for generating the URL
 * @param {string} params.key - S3 object key
 * @param {number} params.expirySeconds - Expiry time in seconds
 * @returns {Promise<Object>} - The presigned URL result
 */
async function generatePresignedUrl(params) {
  const { key, expirySeconds = 3600 } = params;

  try {
    // Create the command to get the object
    const command = new GetObjectCommand({
      Bucket: DEFAULT_BUCKET_NAME,
      Key: key
    });

    // Generate the presigned URL
    logger.info('Generating presigned URL', {
      bucket: DEFAULT_BUCKET_NAME,
      key,
      expirySeconds
    });
    const url = await getSignedUrl(s3Client, command, { expiresIn: expirySeconds });

    return {
      success: true,
      url
    };
  } catch (error) {
    logger.error('Error generating presigned URL', {
      error: error.message,
      key,
      bucket: DEFAULT_BUCKET_NAME
    });
    return {
      success: false,
      error: error.message || 'Failed to generate presigned URL'
    };
  }
}

/**
 * Deletes an object from S3
 * @param {Object} params - Parameters for the delete operation
 * @param {string} params.key - S3 object key
 * @returns {Promise<Object>} - The delete result
 */
async function deleteFromS3(params) {
  const { key } = params;

  try {
    // Create the command to delete the object
    const command = new DeleteObjectCommand({
      Bucket: DEFAULT_BUCKET_NAME,
      Key: key
    });

    // Delete the object
    logger.info('Deleting object from S3', {
      bucket: DEFAULT_BUCKET_NAME,
      key
    });
    await s3Client.send(command);

    return {
      success: true,
      message: 'Object deleted successfully'
    };
  } catch (error) {
    logger.error('Error deleting object from S3', {
      error: error.message,
      key,
      bucket: DEFAULT_BUCKET_NAME
    });
    return {
      success: false,
      error: error.message || 'Failed to delete object from S3'
    };
  }
}

/**
 * Retrieves a generated video from S3
 * @param {Object} params - Parameters for video retrieval
 * @param {string} params.s3Uri - The S3 URI of the generated video
 * @param {string} params.userId - User ID for organizing videos
 * @returns {Promise<Object>} - The video retrieval result
 */
async function getVideoFromS3(params) {
  const { s3Uri, userId } = params;

  if (!s3Uri) {
    return {
      success: false,
      error: 'S3 URI is required for video retrieval'
    };
  }
  if (!userId) {
    return {
      success: false,
      error: 'User ID is required for S3 organization'
    };
  }

  try {
    // Parse the S3 URI to extract bucket and key
    const s3UriMatch = s3Uri.match(/s3:\/\/([^\/]+)\/(.+)/);
    if (!s3UriMatch) {
      return {
        success: false,
        error: 'Invalid S3 URI format'
      };
    }

    const [, bucket, originalKey] = s3UriMatch;

    // Create a new key with user organization
    const timestamp = Date.now();
    const filename = `video-${timestamp}.mp4`;
    // Validate userId before using in S3 path
    if (!userId || userId === 'undefined' || userId === 'null') {
      return {
        success: false,
        error: 'Invalid or missing userId for S3 path generation'
      };
    }
    const newKey = `users/${userId}/videos/${filename}`;

    // Get the video from the original location
    const getCommand = new GetObjectCommand({
      Bucket: bucket,
      Key: originalKey
    });

    const getResponse = await s3Client.send(getCommand);

    // Read the video data
    const videoBuffer = await streamToBuffer(getResponse.Body);

    // Upload to the user-organized location
    const putCommand = new PutObjectCommand({
      Bucket: DEFAULT_BUCKET_NAME,
      Key: newKey,
      Body: videoBuffer,
      ContentType: 'video/mp4'
    });

    await s3Client.send(putCommand);

    // Generate the S3 URL
    const region = process.env.AWS_REGION || 'us-east-1';
    const s3Url = `https://${DEFAULT_BUCKET_NAME}.s3.${region}.amazonaws.com/${newKey}`;

    // Clean up the original file
    try {
      const deleteCommand = new DeleteObjectCommand({
        Bucket: bucket,
        Key: originalKey
      });
      await s3Client.send(deleteCommand);
    } catch (deleteError) {
      // Warning: Failed to delete original video file
    }

    return {
      success: true,
      url: s3Url,
      key: newKey,
      filename: filename
    };
  } catch (error) {
    return {
      success: false,
      error: error.message || 'Failed to retrieve video from S3'
    };
  }
}

/**
 * Deletes a video from S3
 * @param {Object} params - Parameters for video deletion
 * @param {string} params.key - S3 object key for the video
 * @returns {Promise<Object>} - The deletion result
 */
async function deleteVideoFromS3(params) {
  const { key } = params;

  if (!key) {
    return {
      success: false,
      error: 'S3 key is required for video deletion'
    };
  }

  try {
    // Create the command to delete the video
    const command = new DeleteObjectCommand({
      Bucket: DEFAULT_BUCKET_NAME,
      Key: key
    });

    await s3Client.send(command);

    return {
      success: true,
      message: 'Video deleted successfully'
    };
  } catch (error) {
    return {
      success: false,
      error: error.message || 'Failed to delete video from S3'
    };
  }
}

/**
 * Deletes a voice file from S3
 * @param {Object} params - Parameters for voice deletion
 * @param {string} params.key - S3 object key for the voice file
 * @returns {Promise<Object>} - The deletion result
 */
async function deleteVoiceFromS3(params) {
  const { key } = params;

  if (!key) {
    return {
      success: false,
      error: 'S3 key is required for voice deletion'
    };
  }

  try {
    // Create the command to delete the voice file
    const command = new DeleteObjectCommand({
      Bucket: DEFAULT_BUCKET_NAME,
      Key: key
    });

    await s3Client.send(command);

    return {
      success: true,
      message: 'Voice file deleted successfully'
    };
  } catch (error) {
    return {
      success: false,
      error: error.message || 'Failed to delete voice file from S3'
    };
  }
}

/**
 * Debug function to manually inspect S3 bucket contents
 * @param {Object} params - Parameters for S3 inspection
 * @param {string} params.prefix - S3 prefix to inspect
 * @returns {Promise<Object>} - The inspection result
 */
async function debugS3Contents(params) {
  const { prefix = 'videos/' } = params;
  
  try {
    const listCommand = new ListObjectsV2Command({
      Bucket: DEFAULT_BUCKET_NAME,
      Prefix: prefix,
      MaxKeys: 100
    });
    
    const listResponse = await s3Client.send(listCommand);
    const files = listResponse.Contents || [];
    
    return {
      success: true,
      files: files.map(f => ({
        key: f.Key,
        size: f.Size,
        lastModified: f.LastModified
      }))
    };
  } catch (error) {
    return {
      success: false,
      error: error.message
    };
  }
}

module.exports = {
  uploadImageToS3,
  uploadVoiceToS3,
  generatePresignedUrl,
  deleteFromS3,
  getVideoFromS3,
  deleteVideoFromS3,
  deleteVoiceFromS3,
  debugS3Contents
};