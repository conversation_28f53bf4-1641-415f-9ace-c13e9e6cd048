/**
 * AWS Bedrock service utilities for VibeNecto
 * Handles image and video generation using AWS Bedrock models
 */

// Load environment variables from .env file
require('dotenv').config();

// Import constants and logger
const {
  FILE_SIZE_LIMITS,
  VIDEO_LIMITS,
  NOVA_REEL_SPECS,
  PROCESSING_TIME_ESTIMATES,
} = require('../constants');
const { logger } = require('../utils/logger');

const {
  BedrockRuntimeClient,
  InvokeModelCommand,
  StartAsyncInvokeCommand,
  GetAsyncInvokeCommand
} = require('@aws-sdk/client-bedrock-runtime');

// Initialize AWS Bedrock client
const bedrockClient = new BedrockRuntimeClient({
  region: process.env.AWS_REGION || 'us-east-1'
});

// Default bucket name for S3 operations
const DEFAULT_BUCKET_NAME = process.env.AWS_S3_BUCKET_NAME || 'vibenecto-storage';

/**
 * Generates an image using AWS Bedrock Titan Image Generator
 * @param {Object} params - Parameters for image generation
 * @param {string} params.prompt - Text prompt for image generation
 * @param {Object} params.options - Additional options
 * @returns {Promise<Object>} - The generated image data
 */
async function generateImageWithBedrock(params) {
  const { prompt, options = {} } = params;

  // Construct the payload for the Titan Image Generator
  const payload = {
    taskType: "TEXT_IMAGE",
    textToImageParams: {
      text: prompt,
      // AWS requires negativeText to be at least 3 characters long
      negativeText: (options.negativePrompt && options.negativePrompt.length >= 3)
        ? options.negativePrompt
        : "blurry, low quality, distorted"
    },
    imageGenerationConfig: {
      quality: options.quality || "standard",
      numberOfImages: options.numberOfImages || 1,
      height: options.height || 1024,
      width: options.width || 1024,
      cfgScale: options.cfgScale || 8.0,
      seed: options.seed || 42
    }
  };

  try {
    // Create the command to invoke the model
    const command = new InvokeModelCommand({
      modelId: 'amazon.titan-image-generator-v2:0',
      contentType: 'application/json',
      accept: 'application/json',
      body: JSON.stringify(payload)
    });

    // Invoke the model
    logger.info('Invoking Bedrock model for image generation', {
      modelId: 'amazon.titan-image-generator-v2:0',
      taskType: payload.taskType,
      promptLength: prompt.length
    });
    const response = await bedrockClient.send(command);

    // Parse the response
    const responseBody = JSON.parse(new TextDecoder().decode(response.body));

    // Check for errors in the response
    if (responseBody.error) {
      logger.error('Error in Bedrock response', { error: responseBody.error });
      return {
        success: false,
        error: responseBody.error
      };
    }

    // Extract the base64 image
    const base64Image = responseBody.images[0];

    return {
      success: true,
      image: base64Image
    };
  } catch (error) {
    logger.error('Error generating image with Bedrock', {
      error: error.message,
      stack: error.stack
    });
    return {
      success: false,
      error: error.message || 'Failed to generate image with Bedrock'
    };
  }
}

/**
 * Removes the background from an image using AWS Bedrock
 * @param {Object} params - Parameters for background removal
 * @param {string} params.image - Base64 encoded image
 * @returns {Promise<Object>} - The result with the processed image
 */
async function removeBackgroundWithBedrock(params) {
  const { image } = params;

  // Construct the payload for background removal
  const payload = {
    taskType: "BACKGROUND_REMOVAL",
    backgroundRemovalParams: {
      image
    }
  };

  try {
    // Create the command to invoke the model
    const command = new InvokeModelCommand({
      modelId: 'amazon.titan-image-generator-v2:0',
      contentType: 'application/json',
      accept: 'application/json',
      body: JSON.stringify(payload)
    });

    // Invoke the model
    logger.info('Invoking Bedrock model for background removal');
    const response = await bedrockClient.send(command);

    // Parse the response
    const responseBody = JSON.parse(new TextDecoder().decode(response.body));

    // Check for errors in the response
    if (responseBody.error) {
      logger.error('Error in Bedrock response', { error: responseBody.error });
      return {
        success: false,
        error: responseBody.error
      };
    }

    // Extract the base64 image
    const base64Image = responseBody.images[0];

    return {
      success: true,
      image: base64Image
    };
  } catch (error) {
    logger.error('Error removing background with Bedrock', { error: error.message });
    return {
      success: false,
      error: error.message || 'Failed to remove background with Bedrock'
    };
  }
}

/**
 * Generates a color-guided image using AWS Bedrock
 * @param {Object} params - Parameters for color-guided generation
 * @param {string} params.prompt - Text prompt for image generation
 * @param {Object} params.options - Additional options
 * @returns {Promise<Object>} - The generated image data
 */
async function generateColorGuidedImageWithBedrock(params) {
  const { prompt, options = {} } = params;
  const {
    negativePrompt = "blurry, low quality, distorted",
    colors = [],
    referenceImage,
    width = 1024,
    height = 1024,
    quality = "standard",
    numberOfImages = 1,
    cfgScale = 8.0,
    seed = 42
  } = options;

  // Construct the payload for color-guided generation
  const payload = {
    taskType: "COLOR_GUIDED_GENERATION",
    colorGuidedGenerationParams: {
      text: prompt,
      negativeText: negativePrompt,
      colors
    },
    imageGenerationConfig: {
      quality,
      numberOfImages,
      height,
      width,
      cfgScale,
      seed
    }
  };

  // Add reference image if provided
  if (referenceImage) {
    payload.colorGuidedGenerationParams.referenceImage = referenceImage;
  }

  try {
    // Create the command to invoke the model
    const command = new InvokeModelCommand({
      modelId: 'amazon.titan-image-generator-v2:0',
      contentType: 'application/json',
      accept: 'application/json',
      body: JSON.stringify(payload)
    });

    // Invoke the model
    logger.info('Invoking Bedrock model for color-guided generation');
    const response = await bedrockClient.send(command);

    // Parse the response
    const responseBody = JSON.parse(new TextDecoder().decode(response.body));

    // Check for errors in the response
    if (responseBody.error) {
      logger.error('Error in Bedrock response', { error: responseBody.error });
      return {
        success: false,
        error: responseBody.error
      };
    }

    // Extract the base64 image
    const base64Image = responseBody.images[0];

    return {
      success: true,
      image: base64Image
    };
  } catch (error) {
    logger.error('Error generating color-guided image with Bedrock', { error: error.message });
    return {
      success: false,
      error: error.message || 'Failed to generate color-guided image with Bedrock'
    };
  }
}

/**
 * Generates image variations using AWS Bedrock
 * @param {Object} params - Parameters for image variation
 * @param {string} params.image - Base64 encoded source image
 * @param {Object} params.options - Additional options
 * @returns {Promise<Object>} - The generated image data
 */
async function generateImageVariationWithBedrock(params) {
  const { image, options = {} } = params;
  const {
    negativePrompt = "blurry, low quality, distorted",
    similarityStrength = 0.7,
    width = 1024,
    height = 1024,
    quality = "standard",
    numberOfImages = 1,
    cfgScale = 8.0,
    seed = 42
  } = options;

  // Construct the payload for image variation
  const payload = {
    taskType: "IMAGE_VARIATION",
    imageVariationParams: {
      images: [image],
      negativeText: negativePrompt,
      similarityStrength
    },
    imageGenerationConfig: {
      quality,
      numberOfImages,
      height,
      width,
      cfgScale,
      seed
    }
  };

  try {
    // Create the command to invoke the model
    const command = new InvokeModelCommand({
      modelId: 'amazon.titan-image-generator-v2:0',
      contentType: 'application/json',
      accept: 'application/json',
      body: JSON.stringify(payload)
    });

    // Invoke the model
    logger.info('Invoking Bedrock model for image variation');
    const response = await bedrockClient.send(command);

    // Parse the response
    const responseBody = JSON.parse(new TextDecoder().decode(response.body));

    // Check for errors in the response
    if (responseBody.error) {
      logger.error('Error in Bedrock response', { error: responseBody.error });
      return {
        success: false,
        error: responseBody.error
      };
    }

    // Extract the base64 image
    const base64Image = responseBody.images[0];

    return {
      success: true,
      image: base64Image
    };
  } catch (error) {
    logger.error('Error generating image variation with Bedrock', { error: error.message });
    return {
      success: false,
      error: error.message || 'Failed to generate image variation with Bedrock'
    };
  }
}

/**
 * Generates a conditioned image using AWS Bedrock
 * @param {Object} params - Parameters for image conditioning
 * @param {string} params.prompt - Text prompt for image generation
 * @param {Object} params.options - Additional options
 * @returns {Promise<Object>} - The generated image data
 */
async function generateConditionedImageWithBedrock(params) {
  const { prompt, options = {} } = params;
  const {
    negativePrompt = "blurry, low quality, distorted",
    referenceImage,
    conditioningMode = "CANNY", // CANNY or SEGMENTATION
    conditioningStrength = 0.7,
    width = 1024,
    height = 1024,
    quality = "standard",
    numberOfImages = 1,
    cfgScale = 8.0,
    seed = 42
  } = options;

  // Validate required parameters
  if (!referenceImage) {
    return {
      success: false,
      error: "Reference image is required for image conditioning"
    };
  }

  // Construct the payload for image conditioning according to AWS documentation
  // Based on the latest AWS Bedrock documentation for Titan Image Generator
  const randomSeed = Math.floor(Math.random() * 1000000);

  // Create a basic text-to-image request
  const payload = {
    taskType: "TEXT_IMAGE",
    textToImageParams: {
      text: prompt,
      negativeText: negativePrompt || "blurry, low quality, distorted"
    },
    imageGenerationConfig: {
      numberOfImages: 1,
      height: height || 1024,
      width: width || 1024,
      cfgScale: cfgScale || 8.0,
      seed: seed || randomSeed,
      quality: quality || "standard"
    }
  };

  // For image conditioning, we need to use a different approach
  // Let's try using the IMAGE_VARIATION task type instead
  if (referenceImage) {
    logger.info('Switching to IMAGE_VARIATION task type for conditioning');

    // Change the task type to IMAGE_VARIATION
    payload.taskType = "IMAGE_VARIATION";

    // Remove textToImageParams and add imageVariationParams
    delete payload.textToImageParams;

    payload.imageVariationParams = {
      images: [referenceImage],
      text: prompt,
      negativeText: negativePrompt || "blurry, low quality, distorted",
      similarityStrength: conditioningStrength || 0.7
    };
  }

  try {
    // Create the command to invoke the model
    const command = new InvokeModelCommand({
      modelId: 'amazon.titan-image-generator-v2:0',
      contentType: 'application/json',
      accept: 'application/json',
      body: JSON.stringify(payload)
    });

    // Log the exact payload for debugging
    logger.debug('Payload structure for debugging');
    // Create a copy of the payload for logging without the actual base64 image data
    const payloadForLogging = JSON.parse(JSON.stringify(payload));

    // Replace the base64 image data with a placeholder
    if (payloadForLogging.imageVariationParams && payloadForLogging.imageVariationParams.images) {
      payloadForLogging.imageVariationParams.images = ['BASE64_DATA_PRESENT'];
    }

    logger.debug('Payload structure', { payload: payloadForLogging });

    // Invoke the model
    logger.info('Invoking Bedrock model for image conditioning');
    const response = await bedrockClient.send(command);

    // Parse the response
    const responseBody = JSON.parse(new TextDecoder().decode(response.body));

    // Check for errors in the response
    if (responseBody.error) {
      logger.error('Error in Bedrock response', { error: responseBody.error });
      return {
        success: false,
        error: responseBody.error
      };
    }

    // Extract the base64 image
    const base64Image = responseBody.images[0];

    return {
      success: true,
      image: base64Image
    };
  } catch (error) {
    logger.error('Error generating conditioned image with Bedrock', { error: error.message });
    return {
      success: false,
      error: error.message || 'Failed to generate conditioned image with Bedrock'
    };
  }
}

/**
 * Preprocesses a reference image for conditioning
 * @param {Object} params - Parameters for preprocessing
 * @param {string} params.image - Base64 encoded image
 * @param {string} params.mode - Preprocessing mode (CANNY or SEGMENTATION)
 * @returns {Promise<Object>} - The preprocessed image data
 */
async function preprocessReferenceImage(params) {
  const { image, mode = "CANNY" } = params;

  // According to AWS documentation, preprocessing for visualization is done client-side
  // We'll return the original image with a message
  logger.info('Image preprocessing is not directly supported by the Titan model API');
  logger.info('Returning original image for client-side visualization');

  // For a real implementation, we would need to use a client-side library
  // to process the image (e.g., apply Canny edge detection or segmentation)

  return {
    success: true,
    image: `data:image/png;base64,${image}`,
    message: "Preprocessing should be done client-side. Returning original image."
  };
}

/**
 * Validates and normalizes video generation configuration
 * @param {Object} config - Video generation config to validate
 * @returns {Object} - Validated and normalized config
 */
function validateVideoConfig(config) {
  const validatedConfig = { ...config };

  // Nova Reel only supports 24 FPS
  if (validatedConfig.fps && validatedConfig.fps !== 24) {
    logger.warn('Nova Reel only supports 24 FPS, adjusting', {
      originalFps: validatedConfig.fps,
      adjustedFps: 24
    });
    validatedConfig.fps = 24;
  } else if (!validatedConfig.fps) {
    validatedConfig.fps = 24;
  }

  // Nova Reel only supports 1280x720 resolution
  if (validatedConfig.dimension && validatedConfig.dimension !== '1280x720') {
    logger.warn('Nova Reel only supports 1280x720, adjusting', {
      originalDimension: validatedConfig.dimension,
      adjustedDimension: '1280x720'
    });
    validatedConfig.dimension = '1280x720';
  } else if (!validatedConfig.dimension) {
    validatedConfig.dimension = '1280x720';
  }

  return validatedConfig;
}

/**
 * Generates a video using AWS Bedrock Nova Reel
 * @param {Object} params - Parameters for video generation
 * @param {string} params.taskType - Type of video generation (TEXT_VIDEO, MULTI_SHOT_AUTOMATED, MULTI_SHOT_MANUAL)
 * @param {string} params.prompt - Text prompt for video generation
 * @param {Object} params.options - Additional options
 * @returns {Promise<Object>} - The async job result with job ID
 */
async function generateVideoWithBedrock(params) {
  const { taskType, prompt, options = {} } = params;

  // Validate required parameters
  if (!taskType || !prompt) {
    return {
      success: false,
      error: 'Task type and prompt are required for video generation'
    };
  }

  // Validate task type
  const validTaskTypes = ['TEXT_VIDEO', 'MULTI_SHOT_AUTOMATED', 'MULTI_SHOT_MANUAL'];
  if (!validTaskTypes.includes(taskType)) {
    return {
      success: false,
      error: `Invalid task type. Must be one of: ${validTaskTypes.join(', ')}`
    };
  }

  // Validate prompt length based on task type
  if (taskType === 'TEXT_VIDEO' && prompt.length > VIDEO_LIMITS.MAX_PROMPT_LENGTH_SINGLE) {
    return {
      success: false,
      error: `Prompt for single-shot videos cannot exceed ${VIDEO_LIMITS.MAX_PROMPT_LENGTH_SINGLE} characters`
    };
  }

  if ((taskType === 'MULTI_SHOT_AUTOMATED') && prompt.length > VIDEO_LIMITS.MAX_PROMPT_LENGTH_MULTI) {
    return {
      success: false,
      error: `Prompt for multi-shot videos cannot exceed ${VIDEO_LIMITS.MAX_PROMPT_LENGTH_MULTI} characters`
    };
  }

  // Validate reference images if provided
  if (options.referenceImage) {
    try {
      // Basic base64 validation
      const base64Data = options.referenceImage.replace(/^data:image\/[a-z]+;base64,/, '');
      const buffer = Buffer.from(base64Data, 'base64');
      
      // Check if it's a reasonable size (not too small or too large)
      if (buffer.length < 1000) {
        return {
          success: false,
          error: 'Reference image appears to be too small or corrupted'
        };
      }
      
      if (buffer.length > FILE_SIZE_LIMITS.IMAGE_MAX_SIZE) {
        return {
          success: false,
          error: 'Reference image is too large (max 10MB)'
        };
      }
    } catch (error) {
      return {
        success: false,
        error: 'Invalid reference image format'
      };
    }
  }

  try {
    // Construct the payload based on task type
    let payload;

    if (taskType === 'TEXT_VIDEO') {
      // Single-shot 6-second video
      payload = {
        taskType: 'TEXT_VIDEO',
        textToVideoParams: {
          text: prompt.substring(0, VIDEO_LIMITS.MAX_PROMPT_LENGTH_SINGLE)
        },
        videoGenerationConfig: validateVideoConfig({
          durationSeconds: VIDEO_LIMITS.FIXED_SINGLE_DURATION,
          fps: options.fps || NOVA_REEL_SPECS.FRAME_RATE,
          dimension: options.dimension || NOVA_REEL_SPECS.RESOLUTION,
          seed: options.seed || Math.floor(Math.random() * 1000000)
        })
      };

      // Add reference image if provided
      if (options.referenceImage) {
        payload.textToVideoParams.images = [{
          format: options.referenceImageFormat || 'png',
          source: {
            bytes: options.referenceImage
          }
        }];
      }
    } else if (taskType === 'MULTI_SHOT_AUTOMATED') {
      // Automated multi-shot video
      const duration = Math.max(
        VIDEO_LIMITS.MIN_DURATION_MULTI,
        Math.min(VIDEO_LIMITS.MAX_DURATION_MULTI, options.durationSeconds || VIDEO_LIMITS.MIN_DURATION_MULTI)
      );
      // Ensure duration is in proper increments
      const adjustedDuration = Math.round(duration / VIDEO_LIMITS.SHOT_DURATION_INCREMENT) * VIDEO_LIMITS.SHOT_DURATION_INCREMENT;

      payload = {
        taskType: 'MULTI_SHOT_AUTOMATED',
        multiShotAutomatedParams: {
          text: prompt.substring(0, VIDEO_LIMITS.MAX_PROMPT_LENGTH_MULTI)
        },
        videoGenerationConfig: validateVideoConfig({
          durationSeconds: adjustedDuration,
          fps: options.fps || NOVA_REEL_SPECS.FRAME_RATE,
          dimension: options.dimension || NOVA_REEL_SPECS.RESOLUTION,
          seed: options.seed || Math.floor(Math.random() * 1000000)
        })
      };
    } else if (taskType === 'MULTI_SHOT_MANUAL') {
      // Manual multi-shot video with individual shots
      if (!options.shots || !Array.isArray(options.shots) || options.shots.length === 0) {
        return {
          success: false,
          error: 'Shots array is required for MULTI_SHOT_MANUAL task type'
        };
      }

      if (options.shots.length > VIDEO_LIMITS.MAX_SHOTS_MANUAL) {
        return {
          success: false,
          error: `Maximum ${VIDEO_LIMITS.MAX_SHOTS_MANUAL} shots allowed for manual multi-shot videos`
        };
      }

      const shots = options.shots.map((shot, index) => {
        const shotData = {
          text: (shot.prompt || shot.text || '').substring(0, VIDEO_LIMITS.MAX_SHOT_PROMPT_LENGTH)
        };

        // Add reference image if provided for this shot
        // Handle both referenceImage and referenceImageKey fields
        const referenceImageData = shot.referenceImage || shot.referenceImageKey;
        if (referenceImageData) {
          shotData.image = {
            format: shot.referenceImageFormat || 'png',
            source: {
              bytes: referenceImageData
            }
          };
        }

        return shotData;
      });

      payload = {
        taskType: 'MULTI_SHOT_MANUAL',
        multiShotManualParams: {
          shots: shots
        },
        videoGenerationConfig: validateVideoConfig({
          fps: options.fps || NOVA_REEL_SPECS.FRAME_RATE,
          dimension: options.dimension || NOVA_REEL_SPECS.RESOLUTION,
          seed: options.seed || Math.floor(Math.random() * 1000000)
        })
      };
    }

    // Create the command for async video generation
    // Generate a unique identifier for this job to ensure consistent S3 paths
    const jobIdentifier = `job-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
    
    const command = new StartAsyncInvokeCommand({
      modelId: NOVA_REEL_SPECS.MODEL_ID,
      modelInput: payload,
      outputDataConfig: {
        s3OutputDataConfig: {
          s3Uri: `s3://${DEFAULT_BUCKET_NAME}/videos/${jobIdentifier}/`
        }
      }
    });

    // Start the async video generation job
    logger.info('Starting async video generation with Nova Reel', {
      modelId: NOVA_REEL_SPECS.MODEL_ID,
      taskType,
      jobIdentifier,
      promptLength: prompt.length
    });

    const response = await bedrockClient.send(command);

    // Store the job identifier for consistent S3 path tracking
    logger.info('Video generation job started successfully', {
      jobArn: response.invocationArn,
      jobIdentifier,
      taskType
    });

    // Return the job ID for status tracking
    return {
      success: true,
      jobId: response.invocationArn,
      jobIdentifier: jobIdentifier, // Include our custom identifier for S3 path tracking
      estimatedCompletionTime: getEstimatedCompletionTime(taskType, options.durationSeconds),
      message: 'Video generation job started successfully'
    };
  } catch (error) {
    logger.error('Error starting video generation', {
      error: error.message,
      stack: error.stack,
      taskType,
      jobIdentifier
    });

    // Add frame rate specific error handling
    if (error.message?.includes('fps') || error.message?.includes('frame')) {
      return {
        success: false,
        error: `Frame rate configuration error. Nova Reel requires ${NOVA_REEL_SPECS.FRAME_RATE} FPS at ${NOVA_REEL_SPECS.RESOLUTION} resolution.`
      };
    }

    // Add model version specific error handling
    if (error.message?.includes('model') || error.message?.includes('version')) {
      return {
        success: false,
        error: `Model configuration error. Please ensure ${NOVA_REEL_SPECS.MODEL_ID} is available in your region.`
      };
    }

    // Add payload structure specific error handling
    if (error.message?.includes('multiShot') || error.message?.includes('taskType')) {
      return {
        success: false,
        error: 'Video generation parameters error. Please check your video type and configuration.'
      };
    }

    return {
      success: false,
      error: error.message || 'Failed to start video generation'
    };
  }
}

/**
 * Checks the status of a video generation job
 * @param {Object} params - Parameters for status check
 * @param {string} params.jobId - The job ID (invocation ARN) to check
 * @returns {Promise<Object>} - The job status result
 */
async function checkVideoGenerationStatus(params) {
  const { jobId } = params;

  if (!jobId) {
    return {
      success: false,
      error: 'Job ID is required for status check'
    };
  }

  try {
    // Create the command to get async job status
    const command = new GetAsyncInvokeCommand({
      invocationArn: jobId
    });

    // Get the job status
    logger.info('🔍 [DEBUG] Starting video status check', { jobId });
    const statusStartTime = Date.now();
    
    const response = await bedrockClient.send(command);
    const statusCheckTime = Date.now() - statusStartTime;
    
    // Log the full response for debugging
    logger.info('🔍 [DEBUG] Bedrock status response received', {
      jobId,
      status: response.status,
      statusCheckTime,
      hasOutputConfig: !!response.outputDataConfig
    });

    // Parse the response to determine status
    const status = response.status;
    logger.info('🔍 [DEBUG] Job status parsed', { status, jobId });

    const result = {
      success: true,
      status: status.toLowerCase(), // pending, processing, completed, failed
      jobId: jobId
    };

    // Add additional information based on status
    if (status === 'Completed') {
      result.outputLocation = response.outputDataConfig?.s3OutputDataConfig?.s3Uri;
      result.completedAt = response.endTime;
      const s3OutputUri = response.outputDataConfig?.s3OutputDataConfig?.s3Uri;
      logger.info('✅ [DEBUG] Job completed', { s3OutputUri, jobId });

      // If completed, try to get the actual video file from the output
      if (s3OutputUri) {
        try {
          logger.info('🔍 [DEBUG] Starting simplified S3 file discovery', { s3OutputUri, jobId });
          
          // Extract bucket and prefix from S3 URI
          const s3UriMatch = s3OutputUri.match(/s3:\/\/([^\/]+)\/(.+)/);
          if (s3UriMatch) {
            const bucket = s3UriMatch[1];
            const prefix = s3UriMatch[2];
            
            // Try the most common path first: prefix + "output.mp4"
            const commonPath = `${prefix}output.mp4`;
            result.videoS3Key = commonPath;
            logger.info('🔍 [DEBUG] Using common video path', {
              jobId,
              videoPath: commonPath
            });
          }
        } catch (error) {
          logger.warn('⚠️ [DEBUG] S3 file discovery failed', {
            jobId,
            error: error.message
          });
          // Continue without videoS3Key
        }
      }
    } else if (status === 'Failed') {
      result.error = response.failureMessage || 'Video generation failed';
      result.failedAt = response.endTime;
      logger.info('🚨 [DEBUG] Job failed', { jobId, error: result.error });
    } else if (status === 'InProgress') {
      result.status = 'processing';
      result.startedAt = response.startTime;
      logger.info('⏳ [DEBUG] Job in progress', { jobId });
    } else {
      logger.info('❓ [DEBUG] Unknown job status', { status, jobId });
    }

    return result;
  } catch (error) {
    logger.error('🚨 [DEBUG] Error checking video generation status', {
      jobId,
      error: error.message,
      stack: error.stack
    });
    return {
      success: false,
      error: error.message || 'Failed to check video generation status'
    };
  }
}

/**
 * Helper function to estimate completion time based on video type and duration
 * @param {string} taskType - The type of video generation task
 * @param {number} durationSeconds - The duration of the video in seconds
 * @returns {string} - Estimated completion time as ISO string
 */
function getEstimatedCompletionTime(taskType, durationSeconds = 6) {
  const now = new Date();
  let estimatedMinutes;

  if (taskType === 'TEXT_VIDEO') {
    // 6-second videos take approximately 90 seconds
    estimatedMinutes = 1.5;
  } else {
    // Multi-shot videos take 14-17 minutes for 2-minute videos
    // Scale based on duration
    const baseDuration = 120; // 2 minutes
    const baseTime = 15.5; // Average of 14-17 minutes
    estimatedMinutes = (durationSeconds / baseDuration) * baseTime;
    estimatedMinutes = Math.max(estimatedMinutes, 5); // Minimum 5 minutes
  }

  const estimatedCompletion = new Date(now.getTime() + estimatedMinutes * 60 * 1000);
  return estimatedCompletion.toISOString();
}

module.exports = {
  generateImageWithBedrock,
  removeBackgroundWithBedrock,
  generateColorGuidedImageWithBedrock,
  generateImageVariationWithBedrock,
  generateConditionedImageWithBedrock,
  preprocessReferenceImage,
  generateVideoWithBedrock,
  checkVideoGenerationStatus
};