import React from 'react';
import { Info } from 'lucide-react';
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from '@/components/ui/tooltip';

interface FormFieldWithTooltipProps {
  icon: React.ReactNode;
  label: string;
  tooltip: string;
  children: React.ReactNode;
}

export const FormFieldWithTooltip: React.FC<FormFieldWithTooltipProps> = ({
  icon,
  label,
  tooltip,
  children,
}) => {
  return (
    <div className="space-y-6">
      <div>
        <div className="flex items-center justify-between mb-2">
          <label className="text-sm font-medium flex items-center gap-1.5">
            {icon}
            {label}
          </label>
          <TooltipProvider>
            <Tooltip>
              <TooltipTrigger asChild>
                <Info size={14} className="text-gray-400 cursor-pointer" />
              </TooltipTrigger>
              <TooltipContent>
                <p className="w-80 text-xs">{tooltip}</p>
              </TooltipContent>
            </Tooltip>
          </TooltipProvider>
        </div>
        {children}
      </div>
    </div>
  );
};