import { useState, useCallback } from "react";
import { TAB_VALUES, GENERATION_METHODS } from "@/constants/multiShotVideo";

export const useMultiShotActions = () => {
  const [activeTab, setActiveTab] = useState<string>(TAB_VALUES.GENERATE);
  const [generationMethod, setGenerationMethod] = useState<"automated" | "manual">(GENERATION_METHODS.AUTOMATED);

  // Handle tab switching
  const handleTabChange = useCallback((tab: string) => {
    setActiveTab(tab);
  }, []);

  // Handle generation method change
  const handleGenerationMethodChange = useCallback((method: "automated" | "manual") => {
    setGenerationMethod(method);
  }, []);

  // Handle video download
  const handleVideoDownload = useCallback((videoUrl: string, filename?: string) => {
    const link = document.createElement('a');
    link.href = videoUrl;
    link.download = filename || `multi-shot-video-${Date.now()}.mp4`;
    link.style.display = 'none';
    
    try {
      document.body.appendChild(link);
      link.click();
    } finally {
      // Ensure cleanup even if click fails
      if (document.body.contains(link)) {
        document.body.removeChild(link);
      }
    }
  }, []);

  // Handle switching to result tab
  const handleViewVideo = useCallback(() => {
    setActiveTab(TAB_VALUES.RESULT);
  }, []);

  // Handle switching back to generate tab
  const handleGenerateNew = useCallback(() => {
    setActiveTab(TAB_VALUES.GENERATE);
  }, []);

  return {
    // State
    activeTab,
    generationMethod,
    
    // Actions
    handleTabChange,
    handleGenerationMethodChange,
    handleVideoDownload,
    handleViewVideo,
    handleGenerateNew,
  };
};