import { useState, useCallback } from "react";
import { useAuth } from "@/contexts/AuthContext";
import { VideoHistoryItem } from "@/services/videoService";
import { CompletedVideo } from "./useVideoGeneration";

export const useVideoActions = () => {
  const { user } = useAuth();
  const [isFullscreen, setIsFullscreen] = useState(false);
  const [selectedVideo, setSelectedVideo] = useState<VideoHistoryItem | null>(null);
  const [videoDialogOpen, setVideoDialogOpen] = useState(false);

  // Toggle fullscreen
  const toggleFullscreen = useCallback(() => {
    setIsFullscreen(prev => !prev);
  }, []);

  // Create mock VideoHistoryItem from completed video data
  const createMockVideoItem = useCallback((completedVideo: CompletedVideo): VideoHistoryItem => {
    return {
      id: completedVideo.videoId,
      created_at: new Date().toISOString(),
      user_id: user?.id || '',
      job_id: completedVideo.jobId,
      prompt: 'Generated video', // We don't have the original prompt here
      video_type: 'text-to-video',
      status: 'completed',
      duration_seconds: 6,
      s3_key: '',
      s3_url: completedVideo.videoUrl,
      presigned_url: completedVideo.videoUrl,
      parameters: {
        taskType: 'TEXT_VIDEO',
        options: {},
      },
      reference_images: [],
      estimated_completion_at: '',
      completed_at: new Date().toISOString(),
    };
  }, [user?.id]);

  // Handle opening video dialog
  const handleOpenVideoDialog = useCallback((completedVideo: CompletedVideo) => {
    const mockVideoItem = createMockVideoItem(completedVideo);
    setSelectedVideo(mockVideoItem);
    setVideoDialogOpen(true);
  }, [createMockVideoItem]);

  // Handle closing video dialog
  const handleCloseVideoDialog = useCallback(() => {
    setSelectedVideo(null);
    setVideoDialogOpen(false);
  }, []);

  // Handle video download
  const handleVideoDownload = useCallback((videoUrl: string) => {
    const link = document.createElement('a');
    link.href = videoUrl;
    link.download = `video-${Date.now()}.mp4`;
    link.style.display = 'none';
    
    try {
      document.body.appendChild(link);
      link.click();
    } finally {
      // Ensure cleanup even if click fails
      if (document.body.contains(link)) {
        document.body.removeChild(link);
      }
    }
  }, []);

  return {
    isFullscreen,
    selectedVideo,
    videoDialogOpen,
    toggleFullscreen,
    handleOpenVideoDialog,
    handleCloseVideoDialog,
    handleVideoDownload,
    createMockVideoItem,
  };
};