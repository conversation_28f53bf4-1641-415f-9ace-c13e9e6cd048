/**
 * Service for interacting with AWS S3 via server API
 * Handles image storage operations including:
 * - Uploading images to S3
 * - Generating presigned URLs for secure access
 * - Deleting images from S3
 */

import { supabase } from '@/lib/supabase';

// Types for the S3 service
export interface S3UploadOptions {
  image: string; // Base64 encoded image
  userId: string;
  imageType?: 'standard' | 'premium' | 'background-removal' | 'color-guided' | 'variation';
  filename?: string;
}

export interface S3UploadResult {
  success: boolean;
  url?: string;
  key?: string;
  error?: string;
}

export interface S3PresignedUrlOptions {
  key: string;
}

export interface S3PresignedUrlResult {
  success: boolean;
  url?: string;
  error?: string;
}

export interface S3DeleteOptions {
  key: string;
}

export interface S3DeleteResult {
  success: boolean;
  message?: string;
  error?: string;
}

/**
 * Uploads an image to S3
 * @param options - Options for the upload
 * @returns A promise that resolves to the upload result
 */
export const uploadImageToS3 = async (options: S3UploadOptions): Promise<S3UploadResult> => {
  const { image, userId, imageType = 'standard', filename } = options;

  try {
    const { data: { session } } = await supabase.auth.getSession();
    const token = session?.access_token;

    const headers: HeadersInit = {
      'Content-Type': 'application/json',
    };
    if (token) {
      headers['Authorization'] = `Bearer ${token}`;
    }

    // Call the server API to upload the image
    const apiUrl = import.meta.env.VITE_API_URL || 'http://localhost:3001';
    const response = await fetch(`${apiUrl}/api/s3/upload`, {
      method: 'POST',
      headers: headers,
      body: JSON.stringify({
        image,
        userId,
        imageType,
        filename
      })
    });

    if (!response.ok) {
      const errorData = await response.json();
      return {
        success: false,
        error: errorData.error || `Server error: ${response.status}`
      };
    }

    const data = await response.json();

    if (!data.success) {
      return {
        success: false,
        error: data.error || 'Unknown error'
      };
    }

    return {
      success: true,
      url: data.url,
      key: data.key
    };
  } catch (error) {
    if (import.meta.env.DEV) {
      console.error('S3 upload failed:', error);
    }
    return {
      success: false,
      error: error instanceof Error ? error.message : 'S3 upload failed'
    };
  }
};

/**
 * Generates a presigned URL for an S3 object
 * @param options - Options for generating the presigned URL
 * @returns A promise that resolves to the presigned URL result
 */
export const getPresignedUrl = async (options: S3PresignedUrlOptions): Promise<S3PresignedUrlResult> => {
  const { key } = options;

  try {
    const { data: { session } } = await supabase.auth.getSession();
    const token = session?.access_token;

    const headers: HeadersInit = {
      'Content-Type': 'application/json',
    };
    if (token) {
      headers['Authorization'] = `Bearer ${token}`;
    }

    // Call the server API to generate a presigned URL
    const apiUrl = import.meta.env.VITE_API_URL || 'http://localhost:3001';
    const response = await fetch(`${apiUrl}/api/s3/presigned-url`, {
      method: 'POST',
      headers: headers,
      body: JSON.stringify({ key })
    });

    if (!response.ok) {
      const errorData = await response.json();
      return {
        success: false,
        error: errorData.error || `Server error: ${response.status}`
      };
    }

    const data = await response.json();

    if (!data.success) {
      return {
        success: false,
        error: data.error || 'Unknown error'
      };
    }

    return {
      success: true,
      url: data.url
    };
  } catch (error) {
    if (import.meta.env.DEV) {
      console.error('Failed to generate presigned URL:', error);
    }
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Failed to generate presigned URL'
    };
  }
};

/**
 * Deletes an image from S3
 * @param options - Options for deleting the image
 * @returns A promise that resolves to the delete result
 */
export const deleteImageFromS3 = async (options: S3DeleteOptions): Promise<S3DeleteResult> => {
  const { key } = options;

  try {
    const { data: { session } } = await supabase.auth.getSession();
    const token = session?.access_token;

    const headers: HeadersInit = {
      'Content-Type': 'application/json',
    };
    if (token) {
      headers['Authorization'] = `Bearer ${token}`;
    }

    // Call the server API to delete the image
    const apiUrl = import.meta.env.VITE_API_URL || 'http://localhost:3001';
    const response = await fetch(`${apiUrl}/api/s3/delete`, {
      method: 'POST',
      headers: headers,
      body: JSON.stringify({ key })
    });

    if (!response.ok) {
      const errorData = await response.json();
      return {
        success: false,
        error: errorData.error || `Server error: ${response.status}`
      };
    }

    const data = await response.json();

    if (!data.success) {
      return {
        success: false,
        error: data.error || 'Unknown error'
      };
    }

    return {
      success: true,
      message: data.message
    };
  } catch (error) {
    if (import.meta.env.DEV) {
      console.error('S3 delete failed:', error);
    }
    return {
      success: false,
      error: error instanceof Error ? error.message : 'S3 delete failed'
    };
  }
};
