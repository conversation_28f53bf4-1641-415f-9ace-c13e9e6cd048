/**
 * Application constants for VibeNecto server
 * Centralizes magic numbers and configuration values
 */

// File size limits
const FILE_SIZE_LIMITS = {
  IMAGE_MAX_SIZE: 10 * 1024 * 1024, // 10MB for images
  REQUEST_BODY_LIMIT: '50mb', // Express body parser limit
};

// Time intervals (in milliseconds)
const TIME_INTERVALS = {
  VIDEO_CLEANUP_TIMEOUT: 2 * 60 * 60 * 1000, // 2 hours for pending videos
  OLD_VIDEO_CLEANUP: 7 * 24 * 60 * 60 * 1000, // 7 days for failed videos
  CLEANUP_INTERVAL: 60 * 60 * 1000, // 1 hour cleanup interval
  INITIAL_CLEANUP_DELAY: 5 * 60 * 1000, // 5 minutes initial delay
  PRESIGNED_URL_EXPIRY: 3600, // 1 hour in seconds
};

// Video generation limits
const VIDEO_LIMITS = {
  MAX_SHOTS_MANUAL: 20, // Maximum shots for manual multi-shot videos
  MAX_PROMPT_LENGTH_SINGLE: 512, // Characters for single-shot videos
  MAX_PROMPT_LENGTH_MULTI: 4000, // Characters for multi-shot videos
  MAX_SHOT_PROMPT_LENGTH: 512, // Characters per shot
  MIN_DURATION_MULTI: 12, // Minimum duration for multi-shot (seconds)
  MAX_DURATION_MULTI: 120, // Maximum duration for multi-shot (seconds)
  SHOT_DURATION_INCREMENT: 6, // Duration must be in 6-second increments
  FIXED_SINGLE_DURATION: 6, // Fixed duration for single-shot videos
};

// Nova Reel specifications
const NOVA_REEL_SPECS = {
  FRAME_RATE: 24, // Fixed frame rate
  RESOLUTION: '1280x720', // Fixed resolution
  MODEL_ID: 'amazon.nova-reel-v1:1', // Model version
};

// Image generation limits
const IMAGE_LIMITS = {
  MIN_NEGATIVE_PROMPT_LENGTH: 3, // AWS requirement
  MAX_COLORS_COLOR_GUIDED: 10, // Maximum colors for color-guided generation
  DEFAULT_CFG_SCALE: 8.0,
  DEFAULT_SEED: 42,
  DEFAULT_DIMENSIONS: {
    WIDTH: 1024,
    HEIGHT: 1024,
  },
};

// Cache and performance
const CACHE_SETTINGS = {
  VIDEO_STATUS_CACHE_TTL: 300, // 5 minutes in seconds
  METRICS_CACHE_TTL: 60, // 1 minute in seconds
};

// S3 and storage
const STORAGE_SETTINGS = {
  S3_LIST_MAX_KEYS: 100, // Maximum keys to list in S3 operations
  FALLBACK_FILE_SIZE_THRESHOLD: 100000, // 100KB threshold for video file detection
};

// Estimation times (in minutes)
const PROCESSING_TIME_ESTIMATES = {
  SINGLE_SHOT_VIDEO: 1.5, // 90 seconds
  MULTI_SHOT_BASE_TIME: 15.5, // Average for 2-minute videos
  MULTI_SHOT_BASE_DURATION: 120, // 2 minutes baseline
  MULTI_SHOT_MIN_TIME: 5, // Minimum processing time
};

// Voice generation limits and settings
const VOICE_LIMITS = {
  MAX_TEXT_LENGTH: 200000, // AWS Polly limit
  MAX_FREE_CHARACTERS_MONTHLY: 10000, // Free tier monthly limit
  SUPPORTED_FORMATS: ['mp3', 'ogg', 'pcm'],
  SUPPORTED_SAMPLE_RATES: ['8000', '16000', '22050', '24000'],
  SUPPORTED_ENGINES: ['standard', 'neural'],
  DEFAULT_SAMPLE_RATE: '22050',
  DEFAULT_FORMAT: 'mp3',
  DEFAULT_ENGINE: 'standard',
  DEFAULT_LANGUAGE: 'en-US',
  MIN_SPEECH_RATE: 0.2,
  MAX_SPEECH_RATE: 2.0,
  DEFAULT_SPEECH_RATE: 1.0,
  DEFAULT_PITCH: '+0%',
  DEFAULT_VOLUME: '+0dB'
};

module.exports = {
  FILE_SIZE_LIMITS,
  TIME_INTERVALS,
  VIDEO_LIMITS,
  NOVA_REEL_SPECS,
  IMAGE_LIMITS,
  CACHE_SETTINGS,
  STORAGE_SETTINGS,
  PROCESSING_TIME_ESTIMATES,
  VOICE_LIMITS,
};