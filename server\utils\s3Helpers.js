/**
 * S3 helper utilities for VibeNecto backend
 * Consolidates S3 fallback mechanisms and common operations
 */

const { logger } = require('./logger');
const { metricsCollector } = require('./metrics');
const {
  uploadImageToS3,
  uploadVoiceToS3,
  deleteFromS3,
  deleteVoiceFromS3,
  generatePresignedUrl
} = require('../services/s3-service');

/**
 * Upload image with fallback handling
 */
async function uploadImageWithFallback(imageData, userId, imageType, options = {}) {
  const { requestId, returnImageOnFailure = true } = options;
  const startTime = Date.now();
  
  try {
    logger.info('Uploading image to S3', { userId, imageType, requestId });
    
    const s3Result = await uploadImageToS3({
      image: imageData,
      userId,
      imageType
    });
    
    const uploadTime = Date.now() - startTime;
    metricsCollector.recordS3Operation('upload', uploadTime, s3Result.success);
    
    if (!s3Result.success) {
      logger.warn('S3 upload failed', {
        error: s3Result.error,
        userId,
        imageType,
        requestId
      });
      
      if (returnImageOnFailure) {
        return {
          success: true,
          image: imageData,
          warning: 'Image generated successfully but failed to upload to S3: ' + s3Result.error,
          s3Failed: true
        };
      }
      
      return {
        success: false,
        error: s3Result.error
      };
    }
    
    logger.info('Image uploaded to S3 successfully', {
      uploadTime,
      userId,
      imageType,
      requestId
    });
    
    return {
      success: true,
      image: imageData,
      s3Url: s3Result.url,
      s3Key: s3Result.key,
      uploadTime
    };
    
  } catch (error) {
    const uploadTime = Date.now() - startTime;
    metricsCollector.recordS3Operation('upload', uploadTime, false);
    
    logger.error('S3 upload exception', {
      error: error.message,
      userId,
      imageType,
      requestId
    });
    
    if (returnImageOnFailure) {
      return {
        success: true,
        image: imageData,
        warning: 'Image generated successfully but S3 upload failed: ' + error.message,
        s3Failed: true
      };
    }
    
    return {
      success: false,
      error: error.message
    };
  }
}

/**
 * Upload voice with fallback handling
 */
async function uploadVoiceWithFallback(audioData, userId, contentType, options = {}) {
  const { requestId, returnAudioOnFailure = true } = options;
  const startTime = Date.now();
  
  try {
    logger.info('Uploading voice to S3', { userId, contentType, requestId });
    
    const s3Result = await uploadVoiceToS3({
      audio: audioData,
      userId,
      contentType
    });
    
    const uploadTime = Date.now() - startTime;
    
    if (!s3Result.success) {
      logger.warn('S3 voice upload failed', {
        error: s3Result.error,
        userId,
        contentType,
        requestId
      });
      
      if (returnAudioOnFailure) {
        return {
          success: true,
          audio: audioData,
          contentType,
          warning: 'Voice generated successfully but failed to upload to S3: ' + s3Result.error,
          s3Failed: true
        };
      }
      
      return {
        success: false,
        error: s3Result.error
      };
    }
    
    logger.info('Voice uploaded to S3 successfully', {
      uploadTime,
      userId,
      contentType,
      requestId
    });
    
    return {
      success: true,
      audio: audioData,
      contentType,
      s3Url: s3Result.url,
      s3Key: s3Result.key,
      uploadTime
    };
    
  } catch (error) {
    logger.error('S3 voice upload exception', {
      error: error.message,
      userId,
      contentType,
      requestId
    });
    
    if (returnAudioOnFailure) {
      return {
        success: true,
        audio: audioData,
        contentType,
        warning: 'Voice generated successfully but S3 upload failed: ' + error.message,
        s3Failed: true
      };
    }
    
    return {
      success: false,
      error: error.message
    };
  }
}

/**
 * Delete file with comprehensive error handling
 */
async function deleteFileWithHandling(s3Key, fileType = 'file', options = {}) {
  const { requestId, userId, recordId } = options;
  
  if (!s3Key) {
    logger.info(`No S3 key provided for ${fileType} deletion`, {
      recordId,
      userId,
      requestId
    });
    return {
      success: true,
      message: `No ${fileType} file to delete from S3`
    };
  }
  
  try {
    let deleteResult;
    
    if (fileType === 'voice') {
      deleteResult = await deleteVoiceFromS3({ key: s3Key });
    } else {
      deleteResult = await deleteFromS3({ key: s3Key });
    }
    
    if (!deleteResult.success) {
      logger.warn(`Failed to delete ${fileType} from S3`, {
        error: deleteResult.error,
        s3Key,
        recordId,
        userId,
        requestId
      });
      
      return {
        success: false,
        error: deleteResult.error,
        warning: `${fileType} record will be deleted but S3 file may still exist`
      };
    }
    
    logger.info(`${fileType} deleted from S3 successfully`, {
      s3Key,
      recordId,
      userId,
      requestId
    });
    
    return {
      success: true,
      message: `${fileType} deleted from S3 successfully`
    };
    
  } catch (error) {
    logger.error(`Exception during S3 ${fileType} deletion`, {
      error: error.message,
      s3Key,
      recordId,
      userId,
      requestId
    });
    
    return {
      success: false,
      error: error.message,
      warning: `${fileType} record will be deleted but S3 file may still exist`
    };
  }
}

/**
 * Generate presigned URL with error handling
 */
async function generatePresignedUrlSafe(s3Key, options = {}) {
  const { 
    requestId, 
    userId, 
    expirySeconds = 3600,
    fileType = 'file'
  } = options;
  
  if (!s3Key) {
    return {
      success: false,
      error: 'S3 key is required for presigned URL generation'
    };
  }
  
  try {
    const presignedResult = await generatePresignedUrl({
      key: s3Key,
      expirySeconds
    });
    
    if (!presignedResult.success) {
      logger.warn(`Failed to generate presigned URL for ${fileType}`, {
        error: presignedResult.error,
        s3Key,
        userId,
        requestId
      });
      
      return {
        success: false,
        error: presignedResult.error
      };
    }
    
    return {
      success: true,
      url: presignedResult.url,
      expiresIn: expirySeconds
    };
    
  } catch (error) {
    logger.error(`Exception generating presigned URL for ${fileType}`, {
      error: error.message,
      s3Key,
      userId,
      requestId
    });
    
    return {
      success: false,
      error: error.message
    };
  }
}

/**
 * Batch generate presigned URLs for multiple files
 */
async function generatePresignedUrlsBatch(items, options = {}) {
  const { 
    requestId, 
    userId, 
    expirySeconds = 3600,
    fileType = 'file',
    keyField = 's3_key',
    urlField = 'presigned_url'
  } = options;
  
  if (!Array.isArray(items)) {
    return items;
  }
  
  const itemsWithUrls = await Promise.all(
    items.map(async (item) => {
      if (item[keyField]) {
        try {
          const presignedResult = await generatePresignedUrlSafe(item[keyField], {
            requestId,
            userId,
            expirySeconds,
            fileType
          });
          
          if (presignedResult.success) {
            item[urlField] = presignedResult.url;
          }
        } catch (error) {
          logger.warn(`Failed to generate presigned URL for ${fileType}`, {
            error: error.message,
            itemId: item.id,
            requestId
          });
        }
      }
      return item;
    })
  );
  
  return itemsWithUrls;
}

/**
 * Handle S3 operation with comprehensive logging and metrics
 */
async function handleS3Operation(operation, operationFn, options = {}) {
  const { 
    requestId, 
    userId, 
    operationType = 'unknown',
    logContext = {}
  } = options;
  
  const startTime = Date.now();
  
  try {
    logger.info(`Starting S3 ${operationType}`, {
      ...logContext,
      userId,
      requestId
    });
    
    const result = await operationFn();
    const operationTime = Date.now() - startTime;
    
    // Record metrics
    metricsCollector.recordS3Operation(operationType, operationTime, result.success);
    
    if (result.success) {
      logger.info(`S3 ${operationType} completed successfully`, {
        operationTime,
        ...logContext,
        userId,
        requestId
      });
    } else {
      logger.warn(`S3 ${operationType} failed`, {
        error: result.error,
        operationTime,
        ...logContext,
        userId,
        requestId
      });
    }
    
    return {
      ...result,
      operationTime
    };
    
  } catch (error) {
    const operationTime = Date.now() - startTime;
    metricsCollector.recordS3Operation(operationType, operationTime, false);
    
    logger.error(`S3 ${operationType} exception`, {
      error: error.message,
      operationTime,
      ...logContext,
      userId,
      requestId
    });
    
    return {
      success: false,
      error: error.message,
      operationTime
    };
  }
}

module.exports = {
  uploadImageWithFallback,
  uploadVoiceWithFallback,
  deleteFileWithHandling,
  generatePresignedUrlSafe,
  generatePresignedUrlsBatch,
  handleS3Operation
};