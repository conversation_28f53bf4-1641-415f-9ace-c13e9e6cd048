/**
 * Custom hook for handling image upload functionality
 * Manages file selection, validation, preview, and state
 */

import { useState, useRef, useCallback, useEffect } from 'react';
import { toast } from 'sonner';
import { 
  FILE_CONSTRAINTS, 
  MESSAGES, 
  isValidImageFile, 
  isValidFileSize 
} from '@/constants/backgroundRemoval';
import { 
  createPreviewUrl, 
  revokePreviewUrl, 
  validateImageFile,
  fileToBase64WithCompression 
} from '@/utils/imageUtils';

interface UseImageUploadReturn {
  // State
  sourceImage: string | null;
  isUploading: boolean;
  uploadError: string | null;
  
  // Refs
  fileInputRef: React.RefObject<HTMLInputElement>;
  
  // Actions
  handleFileUpload: (e: React.ChangeEvent<HTMLInputElement>) => void;
  handleFileSelect: (file: File) => void;
  triggerFileInput: () => void;
  resetUpload: () => void;
  getBase64Data: () => Promise<string | null>;
}

export const useImageUpload = (): UseImageUploadReturn => {
  const [sourceImage, setSourceImage] = useState<string | null>(null);
  const [isUploading, setIsUploading] = useState(false);
  const [uploadError, setUploadError] = useState<string | null>(null);
  const [currentFile, setCurrentFile] = useState<File | null>(null);
  
  const fileInputRef = useRef<HTMLInputElement>(null);

  // Cleanup effect to revoke object URLs
  useEffect(() => {
    return () => {
      if (sourceImage) {
        revokePreviewUrl(sourceImage);
      }
    };
  }, [sourceImage]);

  // Handle file selection from input
  const handleFileUpload = useCallback((e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (file) {
      handleFileSelect(file);
    }
  }, []);

  // Handle file selection (can be used for drag-and-drop too)
  const handleFileSelect = useCallback((file: File) => {
    setIsUploading(true);
    setUploadError(null);

    try {
      // Validate file
      const validation = validateImageFile(
        file,
        FILE_CONSTRAINTS.MAX_SIZE,
        FILE_CONSTRAINTS.SUPPORTED_FORMATS
      );

      if (!validation.isValid) {
        setUploadError(validation.error || MESSAGES.ERROR.INVALID_FILE_TYPE);
        toast.error(validation.error || MESSAGES.ERROR.INVALID_FILE_TYPE);
        setIsUploading(false);
        return;
      }

      // Revoke previous URL if exists
      if (sourceImage) {
        revokePreviewUrl(sourceImage);
      }

      // Create preview URL
      const previewUrl = createPreviewUrl(file);
      setSourceImage(previewUrl);
      setCurrentFile(file);
      setUploadError(null);

    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : MESSAGES.ERROR.UPLOAD_FAILED;
      setUploadError(errorMessage);
      toast.error(errorMessage);
      
      if (import.meta.env.DEV) {
        console.error('Error handling file upload:', error);
      }
    } finally {
      setIsUploading(false);
    }
  }, [sourceImage]);

  // Trigger file input click
  const triggerFileInput = useCallback(() => {
    fileInputRef.current?.click();
  }, []);

  // Reset upload state
  const resetUpload = useCallback(() => {
    if (sourceImage) {
      revokePreviewUrl(sourceImage);
    }
    
    setSourceImage(null);
    setCurrentFile(null);
    setUploadError(null);
    setIsUploading(false);
    
    if (fileInputRef.current) {
      fileInputRef.current.value = '';
    }
  }, [sourceImage]);

  // Get base64 data for processing
  const getBase64Data = useCallback(async (): Promise<string | null> => {
    if (!currentFile) {
      return null;
    }

    try {
      const base64Data = await fileToBase64WithCompression(currentFile);
      return base64Data;
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Failed to process image';
      setUploadError(errorMessage);
      toast.error(errorMessage);
      
      if (import.meta.env.DEV) {
        console.error('Error converting file to base64:', error);
      }
      
      return null;
    }
  }, [currentFile]);

  return {
    // State
    sourceImage,
    isUploading,
    uploadError,
    
    // Refs
    fileInputRef,
    
    // Actions
    handleFileUpload,
    handleFileSelect,
    triggerFileInput,
    resetUpload,
    getBase64Data,
  };
};