/**
 * Service for managing user subscriptions and usage tracking
 * Handles subscription-related operations including:
 * - Retrieving user subscription details
 * - Tracking image generation usage
 * - Checking usage limits
 */

import { supabase } from '@/lib/supabase';

// Types for subscription data
export interface SubscriptionPlan {
  id: string;
  name: string;
  description: string;
  price: number;
  currency: string;
  interval: string;
  standard_image_limit: number | null; // null means unlimited
  premium_image_limit: number;
  advanced_tools_access: boolean;
  stripe_price_id: string | null;
}

export interface UserSubscription {
  subscription_id: string;
  plan_id: string;
  plan_name: string;
  standard_image_limit: number | null; // null means unlimited
  premium_image_limit: number;
  advanced_tools_access: boolean;
  current_period_end: string;
  status: string;
}

export interface UsageStats {
  standard_images: {
    used: number;
    limit: number | null; // null means unlimited
    reset_at: string;
  };
  premium_images: {
    used: number;
    limit: number;
    reset_at: string;
  };
}

/**
 * Retrieves all available subscription plans
 * @returns A promise that resolves to an array of subscription plans
 */
export const getSubscriptionPlans = async (): Promise<SubscriptionPlan[]> => {
  try {
    const { data, error } = await supabase
      .from('subscription_plans')
      .select('*')
      .order('price', { ascending: true });

    if (error) {
      if (import.meta.env.DEV) {
        console.error('Error fetching subscription plans:', error);
      }
      throw new Error(error.message);
    }

    return data || [];
  } catch (error) {
    if (import.meta.env.DEV) {
      console.error('Failed to fetch subscription plans:', error);
    }
    throw error;
  }
};

/**
 * Retrieves the current user's subscription
 * @param userId - The ID of the user
 * @returns A promise that resolves to the user's subscription or null if not found
 */
export const getUserSubscription = async (userId: string): Promise<UserSubscription | null> => {
  try {
    const { data, error } = await supabase
      .rpc('get_user_subscription', { user_uuid: userId });

    if (error) {
      if (import.meta.env.DEV) {
        console.error('Error fetching user subscription:', error);
      }
      throw new Error(error.message);
    }

    return data && data.length > 0 ? data[0] : null;
  } catch (error) {
    if (import.meta.env.DEV) {
      console.error('Failed to fetch user subscription:', error);
    }
    throw error;
  }
};

/**
 * Retrieves the user's usage statistics
 * @param userId - The ID of the user
 * @returns A promise that resolves to the user's usage statistics
 */
export const getUserUsageStats = async (userId: string): Promise<UsageStats> => {
  try {
    // Get the user's subscription first to determine limits
    const subscription = await getUserSubscription(userId);
    
    // Default to free tier limits if no subscription is found
    const standardLimit = subscription?.standard_image_limit ?? 5;
    const premiumLimit = subscription?.premium_image_limit ?? 0;
    
    // Get standard image usage
    const { data: standardData, error: standardError } = await supabase
      .from('usage_tracking')
      .select('count, reset_at')
      .eq('user_id', userId)
      .eq('image_type', 'standard')
      .order('reset_at', { ascending: false })
      .limit(1);

    if (standardError) {
      if (import.meta.env.DEV) {
        console.error('Error fetching standard image usage:', standardError);
      }
      throw new Error(standardError.message);
    }

    // Get premium image usage
    const { data: premiumData, error: premiumError } = await supabase
      .from('usage_tracking')
      .select('count, reset_at')
      .eq('user_id', userId)
      .eq('image_type', 'premium')
      .order('reset_at', { ascending: false })
      .limit(1);

    if (premiumError) {
      if (import.meta.env.DEV) {
        console.error('Error fetching premium image usage:', premiumError);
      }
      throw new Error(premiumError.message);
    }

    // Calculate next reset date for free tier (weekly)
    const now = new Date();
    const dayOfWeek = now.getDay(); // 0 = Sunday, 1 = Monday, etc.
    const daysUntilReset = (7 - dayOfWeek) % 7; // Reset on Sunday
    const resetDate = new Date(now);
    resetDate.setDate(now.getDate() + daysUntilReset);
    resetDate.setHours(0, 0, 0, 0);
    
    // Calculate next reset date for premium tier (monthly)
    const nextMonth = new Date(now);
    nextMonth.setMonth(now.getMonth() + 1);
    nextMonth.setDate(1);
    nextMonth.setHours(0, 0, 0, 0);

    return {
      standard_images: {
        used: standardData && standardData.length > 0 ? standardData[0].count : 0,
        limit: standardLimit,
        reset_at: standardData && standardData.length > 0 ? standardData[0].reset_at : resetDate.toISOString()
      },
      premium_images: {
        used: premiumData && premiumData.length > 0 ? premiumData[0].count : 0,
        limit: premiumLimit,
        reset_at: premiumData && premiumData.length > 0 ? premiumData[0].reset_at : nextMonth.toISOString()
      }
    };
  } catch (error) {
    if (import.meta.env.DEV) {
      console.error('Failed to fetch user usage stats:', error);
    }
    throw error;
  }
};

/**
 * Tracks image generation usage
 * @param userId - The ID of the user
 * @param imageType - The type of image ('standard' or 'premium')
 * @returns A promise that resolves when the usage is tracked
 */
export const trackImageGeneration = async (userId: string, imageType: 'standard' | 'premium'): Promise<void> => {
  try {
    // Get the current usage record
    const { data, error } = await supabase
      .from('usage_tracking')
      .select('*')
      .eq('user_id', userId)
      .eq('image_type', imageType)
      .order('reset_at', { ascending: false })
      .limit(1);

    if (error) {
      if (import.meta.env.DEV) {
        console.error('Error fetching usage tracking:', error);
      }
      throw new Error(error.message);
    }

    const now = new Date();
    
    // Calculate reset date based on image type
    let resetDate: Date;
    if (imageType === 'standard') {
      // Weekly reset for standard images (free tier)
      const dayOfWeek = now.getDay(); // 0 = Sunday, 1 = Monday, etc.
      const daysUntilReset = (7 - dayOfWeek) % 7; // Reset on Sunday
      resetDate = new Date(now);
      resetDate.setDate(now.getDate() + daysUntilReset);
      resetDate.setHours(0, 0, 0, 0);
    } else {
      // Monthly reset for premium images
      resetDate = new Date(now);
      resetDate.setMonth(now.getMonth() + 1);
      resetDate.setDate(1);
      resetDate.setHours(0, 0, 0, 0);
    }

    if (data && data.length > 0) {
      const currentRecord = data[0];
      const currentResetDate = new Date(currentRecord.reset_at);
      
      // If the current record is still valid (reset date is in the future)
      if (currentResetDate > now) {
        // Update the count
        const { error: updateError } = await supabase
          .from('usage_tracking')
          .update({ count: currentRecord.count + 1 })
          .eq('id', currentRecord.id);

        if (updateError) {
          if (import.meta.env.DEV) {
            console.error('Error updating usage tracking:', updateError);
          }
          throw new Error(updateError.message);
        }
      } else {
        // Create a new record with reset date
        const { error: insertError } = await supabase
          .from('usage_tracking')
          .insert({
            user_id: userId,
            image_type: imageType,
            count: 1,
            reset_at: resetDate.toISOString()
          });

        if (insertError) {
          if (import.meta.env.DEV) {
            console.error('Error inserting usage tracking:', insertError);
          }
          throw new Error(insertError.message);
        }
      }
    } else {
      // No existing record, create a new one
      const { error: insertError } = await supabase
        .from('usage_tracking')
        .insert({
          user_id: userId,
          image_type: imageType,
          count: 1,
          reset_at: resetDate.toISOString()
        });

      if (insertError) {
        if (import.meta.env.DEV) {
          console.error('Error inserting usage tracking:', insertError);
        }
        throw new Error(insertError.message);
      }
    }
  } catch (error) {
    if (import.meta.env.DEV) {
      console.error('Failed to track image generation:', error);
    }
    throw error;
  }
};

/**
 * Checks if the user has reached their usage limit
 * @param userId - The ID of the user
 * @param imageType - The type of image ('standard' or 'premium')
 * @returns A promise that resolves to a boolean indicating if the limit is reached
 */
export const hasReachedUsageLimit = async (userId: string, imageType: 'standard' | 'premium'): Promise<boolean> => {
  try {
    const usageStats = await getUserUsageStats(userId);
    const subscription = await getUserSubscription(userId);
    
    if (imageType === 'standard') {
      // If standard_image_limit is null, it means unlimited
      if (subscription?.standard_image_limit === null) {
        return false;
      }
      
      return usageStats.standard_images.used >= (usageStats.standard_images.limit || 0);
    } else {
      return usageStats.premium_images.used >= usageStats.premium_images.limit;
    }
  } catch (error) {
    if (import.meta.env.DEV) {
      console.error('Failed to check usage limit:', error);
    }
    throw error;
  }
};
