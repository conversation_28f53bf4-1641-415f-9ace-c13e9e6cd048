import React from "react";
import DashboardSidebar from "@/components/DashboardSidebar";
import MultiShotHeader from "@/components/MultiShotHeader";
import MultiShotTabs from "@/components/MultiShotTabs";
import MultiShotUseCases from "@/components/MultiShotUseCases";
import { useAuth } from "@/contexts/AuthContext";
import { useMultiShotGeneration } from "@/hooks/useMultiShotGeneration";
import { useMultiShotActions } from "@/hooks/useMultiShotActions";

const MultiShotVideoGenerator = () => {
  const { user } = useAuth();
  
  // Custom hooks for state management
  const {
    activeJobs,
    completedVideos,
    isGenerating,
    handleVideoComplete,
    handleVideoError,
    handleAutomatedGeneration,
    handleTemplateSelect,
    handleShotGeneration,
  } = useMultiShotGeneration();

  const {
    activeTab,
    generationMethod,
    handleTabChange,
    handleGenerationMethodChange,
    handleVideoDownload,
    handleViewVideo,
    handleGenerateNew,
  } = useMultiShotActions();

  return (
    <>
      <DashboardSidebar />

      <main className="ml-64 h-screen bg-white dark:bg-gray-900 overflow-hidden flex flex-col">
        {/* Main content area */}
        <div className="container mx-auto px-4 py-4 flex-1 flex flex-col">
          {/* Tool header */}
          <MultiShotHeader />

          {/* Main tool area */}
          <div className="flex-1 flex flex-col">
            <MultiShotTabs
              activeTab={activeTab}
              onTabChange={handleTabChange}
              generationMethod={generationMethod}
              onGenerationMethodChange={handleGenerationMethodChange}
              activeJobs={activeJobs}
              completedVideos={completedVideos}
              isGenerating={isGenerating}
              userId={user?.id || ''}
              onVideoComplete={handleVideoComplete}
              onVideoError={handleVideoError}
              onAutomatedGeneration={handleAutomatedGeneration}
              onTemplateSelect={handleTemplateSelect}
              onShotGeneration={handleShotGeneration}
              onVideoDownload={handleVideoDownload}
              onGenerateNew={handleGenerateNew}
              onViewVideo={handleViewVideo}
            />
          </div>

          {/* Use cases - Detailed info section */}
          <MultiShotUseCases className="mt-auto" />
        </div>
      </main>
    </>
  );
};

export default MultiShotVideoGenerator;
