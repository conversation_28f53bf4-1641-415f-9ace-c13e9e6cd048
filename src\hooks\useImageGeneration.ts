import { useState } from 'react';
import { toast } from 'sonner';
import { useQueryClient } from '@tanstack/react-query';
import { useAuth } from '@/contexts/AuthContext';
import { generateImage, ImageGenerationOptions } from '@/services/bedrockService';
import { saveImageHistory, ImageHistoryItem } from '@/services/imageHistoryService';

export interface ImageGenerationState {
  isGenerating: boolean;
  generatedImage: string | null;
  error: string | null;
  warning: string | null;
}

export interface ImageFormData {
  prompt: string;
  style?: string;
  negativePrompt?: string;
  quality: 'standard' | 'premium';
  size: string;
  cfgScale: number;
  seed?: number;
}

export const useImageGeneration = () => {
  const { user } = useAuth();
  const queryClient = useQueryClient();
  
  const [state, setState] = useState<ImageGenerationState>({
    isGenerating: false,
    generatedImage: null,
    error: null,
    warning: null,
  });

  const generateImageFromForm = async (data: ImageFormData) => {
    if (import.meta.env.DEV) {
      console.log('Image generation started');
    }
    
    setState(prev => ({
      ...prev,
      isGenerating: true,
      error: null,
      warning: null,
    }));

    if (!user) {
      const errorMsg = "You must be logged in to generate images";
      setState(prev => ({
        ...prev,
        isGenerating: false,
        error: errorMsg,
      }));
      toast.error(errorMsg);
      return;
    }

    try {
      const [width, height] = data.size.split("x").map(Number);
      const options: ImageGenerationOptions = {
        negativePrompt: data.negativePrompt && data.negativePrompt.length >= 3
          ? data.negativePrompt
          : "blurry, low quality, distorted",
        width,
        height,
        quality: data.quality,
        numberOfImages: 1,
        cfgScale: data.cfgScale,
        seed: data.seed,
      };

      let enhancedPrompt = data.prompt;
      if (data.style) {
        enhancedPrompt += `, in ${data.style} style`;
      }

      const result = await generateImage(enhancedPrompt, options, user.id);

      if (result.success && result.image) {
        setState(prev => ({
          ...prev,
          generatedImage: result.image!,
          warning: result.warning || null,
        }));

        if (result.s3Url && result.s3Key) {
          try {
            const newHistoryItem: ImageHistoryItem = {
              user_id: user.id,
              prompt: enhancedPrompt,
              image_type: 'text-to-image',
              s3_key: result.s3Key,
              s3_url: result.s3Url,
              parameters: {
                negativePrompt: options.negativePrompt,
                width: options.width,
                height: options.height,
                quality: options.quality,
                cfgScale: options.cfgScale,
                seed: options.seed,
                style: data.style
              }
            };
            await saveImageHistory(newHistoryItem);
            await queryClient.invalidateQueries({ queryKey: ['imageHistory', user.id] });
          } catch (saveError) {
            if (import.meta.env.DEV) {
              console.error('Failed to save image to history:', saveError);
            }
            toast.warning("Image generated successfully, but failed to save to history");
            try {
              await queryClient.invalidateQueries({ queryKey: ['imageHistory', user.id] });
            } catch (invalidateError) {
              if (import.meta.env.DEV) {
                console.error('Failed to invalidate queries after save error:', invalidateError);
              }
            }
          }
        } else {
          toast.warning("Image generated but not saved to your history");
        }

        if (result.warning) {
          toast.warning(result.warning);
        } else {
          toast.success("Image generated successfully!");
        }
      } else {
        const errorMsg = result.error || "Failed to generate image";
        setState(prev => ({
          ...prev,
          error: errorMsg,
        }));
        toast.error(result.error || "Failed to generate image. Please try again.");
      }
    } catch (error) {
      if (import.meta.env.DEV) {
        console.error("Image generation error:", error);
      }
      const errorMsg = "An unexpected error occurred";
      setState(prev => ({
        ...prev,
        error: errorMsg,
      }));
      toast.error("An unexpected error occurred. Please try again.");
    } finally {
      if (import.meta.env.DEV) {
        console.log('Image generation process completed, resetting generating state');
      }
      setState(prev => ({
        ...prev,
        isGenerating: false,
      }));
    }
  };

  const clearError = () => {
    setState(prev => ({ ...prev, error: null }));
  };

  const resetGeneration = () => {
    setState({
      isGenerating: false,
      generatedImage: null,
      error: null,
      warning: null,
    });
  };

  return {
    ...state,
    generateImageFromForm,
    clearError,
    resetGeneration,
  };
};