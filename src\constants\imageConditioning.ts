/**
 * Constants for Image Conditioning functionality
 */

// Default values
export const DEFAULT_NEGATIVE_PROMPT = "blurry, low quality, distorted";
export const DEFAULT_CONDITIONING_STRENGTH = 0.7;
export const DEFAULT_CONDITIONING_MODE = "CANNY" as const;

// File constraints
export const MAX_FILE_SIZE = 5 * 1024 * 1024; // 5MB
export const ACCEPTED_FILE_TYPES = "image/*";

// Image generation parameters
export const IMAGE_DIMENSIONS = {
  width: 1024,
  height: 1024
} as const;

export const QUALITY_SETTING = "standard" as const;

// Conditioning modes
export const CONDITIONING_MODES = {
  CANNY: "CANNY",
  SEGMENTATION: "SEGMENTATION"
} as const;

// Strength range
export const CONDITIONING_STRENGTH_RANGE = {
  min: 0.1,
  max: 1.0,
  step: 0.1
} as const;

// Toast messages
export const TOAST_MESSAGES = {
  FILE_TOO_LARGE: "File size exceeds 5MB limit",
  ENTER_PROMPT: "Please enter a prompt",
  UPLOAD_IMAGE: "Please upload a reference image",
  DOWNLOAD_SUCCESS: "Image downloaded successfully!",
  GENERATION_SUCCESS: "Image generated successfully!",
  SAVE_WARNING: "Image generated successfully, but failed to save to history"
} as const;

// Use case examples
export const USE_CASES = [
  {
    icon: "Palette",
    title: "Style Transfer",
    description: "Keep pose, change style (e.g., photo to cartoon)."
  },
  {
    icon: "Home",
    title: "Scene Redecoration", 
    description: "Maintain layout, change theme (e.g., redecorate a room)."
  },
  {
    icon: "Edit",
    title: "Sketch to Image",
    description: "Transform drawings into detailed images."
  }
] as const;