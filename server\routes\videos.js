/**
 * Video generation routes for VibeNecto
 * Handles all video-related API endpoints including generation, status checking,
 * history management, and cleanup operations
 */

const express = require('express');
const { logger } = require('../utils/logger');
const { metricsCollector } = require('../utils/metrics');
const { cacheHelpers } = require('../utils/cache');
const { TIME_INTERVALS } = require('../constants');
const { asyncHandler } = require('../middleware/errorHandler');
const {
  videoGenerationLimiter,
  videoHistoryLimiter
} = require('../middleware/rateLimiter');
const {
  validateVideoGeneration
} = require('../middleware/validation');

// Import AWS service modules
const {
  generateVideoWithBedrock,
  checkVideoGenerationStatus
} = require('../services/bedrock-service');
const {
  deleteVideoFromS3,
  generatePresignedUrl
} = require('../services/s3-service');

// Import utility modules
const { formatResponse } = require('../utils/routeHelpers');
const {
  saveVideoRecord,
  saveVideoShots,
  updateVideoStatus,
  getVideoByJobId,
  getVideoHistory,
  getProcessingVideos,
  deleteVideoRecord,
  getPendingVideosForCleanup,
  deleteOldFailedVideos
} = require('../utils/databaseHelpers');
const {
  tryS3VideoFallback,
  shouldUseFallback,
  batchCheckS3Fallback,
  handleVideoStatusCheck
} = require('../utils/videoFallback');

const router = express.Router();

/**
 * Phase 1: Batch video presigned URL generation with caching
 * Optimized version of the sequential URL generation for better performance
 */
async function generateVideoPresignedUrlsBatch(videos, requestId) {
  if (!videos || videos.length === 0) {
    return videos;
  }

  const startTime = Date.now();
  const completedVideos = videos.filter(video => video.status === 'completed');
  const videosNeedingUrls = completedVideos.filter(video => video.s3_key);

  if (videosNeedingUrls.length === 0) {
    logger.info('No videos need presigned URLs', {
      totalVideos: videos.length,
      completedVideos: completedVideos.length,
      requestId
    });
    return videos;
  }

  logger.info('Starting batch video presigned URL generation', {
    totalVideos: videos.length,
    videosNeedingUrls: videosNeedingUrls.length,
    requestId
  });

  // Check cache first for each video
  const urlPromises = videosNeedingUrls.map(async (video) => {
    try {
      // Check cache first
      const cacheKey = `presigned_url:${video.s3_key}`;
      const cachedUrl = cacheHelpers.getPresignedUrl(video.s3_key);

      if (cachedUrl) {
        return {
          videoId: video.id,
          s3_key: video.s3_key,
          url: cachedUrl,
          fromCache: true,
          success: true
        };
      }

      // Generate fresh presigned URL
      const presignedResult = await generatePresignedUrl({
        key: video.s3_key,
        expirySeconds: 3600
      });

      if (presignedResult.success) {
        // Cache the successful result
        cacheHelpers.cachePresignedUrl(video.s3_key, presignedResult.url, 3600);

        return {
          videoId: video.id,
          s3_key: video.s3_key,
          url: presignedResult.url,
          fromCache: false,
          success: true
        };
      } else {
        return {
          videoId: video.id,
          s3_key: video.s3_key,
          error: presignedResult.error,
          fromCache: false,
          success: false
        };
      }
    } catch (error) {
      return {
        videoId: video.id,
        s3_key: video.s3_key,
        error: error.message,
        fromCache: false,
        success: false
      };
    }
  });

  // Process URLs in batches of 6 for controlled concurrency
  const batchSize = 6;
  const urlResults = [];

  for (let i = 0; i < urlPromises.length; i += batchSize) {
    const batch = urlPromises.slice(i, i + batchSize);
    const batchResults = await Promise.allSettled(batch);

    for (const result of batchResults) {
      if (result.status === 'fulfilled') {
        urlResults.push(result.value);
      } else {
        urlResults.push({
          error: result.reason?.message || 'Promise rejected',
          success: false,
          fromCache: false
        });
      }
    }
  }

  // Apply URLs to videos
  const urlMap = new Map();
  let cacheHits = 0;
  let successCount = 0;

  for (const result of urlResults) {
    if (result.success && result.url) {
      urlMap.set(result.s3_key, result.url);
      successCount++;
      if (result.fromCache) cacheHits++;
    }
  }

  // Update videos with presigned URLs
  const videosWithUrls = videos.map(video => {
    if (video.status === 'completed' && video.s3_key && urlMap.has(video.s3_key)) {
      return {
        ...video,
        presigned_url: urlMap.get(video.s3_key)
      };
    }
    return video;
  });

  const totalTime = Date.now() - startTime;
  const cacheHitRate = videosNeedingUrls.length > 0 ? (cacheHits / videosNeedingUrls.length) * 100 : 0;

  logger.info('Batch video presigned URL generation completed', {
    totalVideos: videos.length,
    videosProcessed: videosNeedingUrls.length,
    successCount,
    cacheHits,
    cacheHitRate: cacheHitRate.toFixed(1) + '%',
    totalTime: totalTime + 'ms',
    averageTime: videosNeedingUrls.length > 0 ? (totalTime / videosNeedingUrls.length).toFixed(0) + 'ms' : '0ms',
    requestId
  });

  return videosWithUrls;
}

// Helper functions for video generation
function getVideoTypeFromTaskType(taskType) {
  switch (taskType) {
    case 'TEXT_VIDEO':
      return 'text-to-video';
    case 'MULTI_SHOT_AUTOMATED':
      return 'multi-shot-auto';
    case 'MULTI_SHOT_MANUAL':
      return 'multi-shot-manual';
    default:
      return 'text-to-video';
  }
}

function getDurationFromOptions(taskType, options) {
  if (taskType === 'TEXT_VIDEO') {
    return 6; // Fixed duration for single-shot
  } else if (taskType === 'MULTI_SHOT_AUTOMATED') {
    const duration = Math.max(12, Math.min(120, options.durationSeconds || 12));
    return Math.round(duration / 6) * 6; // Ensure 6-second increments
  } else if (taskType === 'MULTI_SHOT_MANUAL') {
    return options.shots ? options.shots.length * 6 : 6; // 6 seconds per shot
  }
  return 6;
}


/**
 * Generate a video using AWS Bedrock Nova Reel
 */
router.post('/generate-video',
  validateVideoGeneration,
  videoGenerationLimiter,
  asyncHandler(async (req, res) => {
    const startTime = Date.now();
    const { videoType, prompt, duration, seed, referenceImage, shots, userId } = req.body;

    // Map frontend videoType to backend taskType
    const taskTypeMap = {
      'TEXT_VIDEO': 'TEXT_VIDEO',
      'MULTI_SHOT_AUTOMATED': 'MULTI_SHOT_AUTOMATED',
      'MULTI_SHOT_MANUAL': 'MULTI_SHOT_MANUAL'
    };

    const taskType = taskTypeMap[videoType];
    const options = { duration, seed, referenceImage, shots };

    try {
      logger.info('Video generation request received', {
        userId,
        videoType,
        prompt: prompt?.substring(0, 100),
        requestId: req.requestId
      });

      // Start the video generation job
      const bedrockResult = await generateVideoWithBedrock({
        taskType,
        prompt,
        options
      });

      if (!bedrockResult.success) {
        const processingTime = Date.now() - startTime;
        metricsCollector.recordVideoGeneration(videoType, false, processingTime);
        return res.status(500).json(formatResponse.error(bedrockResult.error));
      }

      // Save the video generation job to the database
      const videoRecord = {
        user_id: userId,
        job_id: bedrockResult.jobId,
        prompt: prompt,
        video_type: getVideoTypeFromTaskType(taskType),
        status: 'pending',
        duration_seconds: duration,
        parameters: { taskType, options, seed },
        reference_images: referenceImage ? [referenceImage] : [],
        estimated_completion_at: bedrockResult.estimatedCompletionTime,
        processing_time_seconds: 0,
        retry_count: 0
      };

      const saveResult = await saveVideoRecord(videoRecord, req.requestId);

      if (saveResult.error) {
        const processingTime = Date.now() - startTime;
        metricsCollector.recordVideoGeneration(videoType, false, processingTime);
        return res.status(500).json(formatResponse.error(
          'Failed to save video generation job to database.',
          saveResult.error.message
        ));
      }

      const savedVideo = saveResult.data;

      // Save individual shots for multi-shot manual videos
      if (videoType === 'MULTI_SHOT_MANUAL' && shots && savedVideo) {
        const shotRecords = shots.map((shot, index) => ({
          video_id: savedVideo.id,
          shot_number: shot.order || index + 1,
          prompt: shot.prompt || shot.text,
          reference_image_s3_key: shot.referenceImageKey || shot.referenceImage || null,
          reference_image_format: shot.referenceImageFormat || 'png',
          parameters: {
            seed: shot.seed || Math.floor(Math.random() * 1000000)
          }
        }));

        await saveVideoShots(shotRecords, req.requestId);
      }

      // Record successful video generation start
      const totalProcessingTime = Date.now() - startTime;
      metricsCollector.recordVideoGeneration(videoType, true, totalProcessingTime);

      // Cache the initial status
      cacheHelpers.cacheVideoStatus(bedrockResult.jobId, 'pending', 300);

      // Return the job information
      return res.json(formatResponse.success({
        jobId: bedrockResult.jobId,
        videoId: savedVideo?.id,
        estimatedCompletionTime: bedrockResult.estimatedCompletionTime,
        message: bedrockResult.message
      }));

    } catch (error) {
      const processingTime = Date.now() - startTime;
      metricsCollector.recordVideoGeneration(videoType || 'unknown', false, processingTime);

      logger.error('Video generation error', {
        error: error.message,
        stack: error.stack,
        processingTime,
        userId,
        requestId: req.requestId
      });

      throw error; // Let asyncHandler handle it
    }
  })
);

/**
 * Check the status of a video generation job with S3 fallback
 */
router.get('/video-status/:jobId',
  asyncHandler(async (req, res) => {
    const { jobId: encodedJobId } = req.params;
    const jobId = decodeURIComponent(encodedJobId);

    logger.info('🔍 [DEBUG] Video status check requested', {
      jobId,
      requestId: req.requestId
    });

    try {
      const result = await handleVideoStatusCheck(jobId, req.requestId);
      
      if (!result.success) {
        return res.status(result.error === 'Video record not found' ? 404 : 500)
          .json(formatResponse.error(result.error));
      }

      return res.json(result);

    } catch (error) {
      logger.error('Video status check error', {
        error: error.message,
        stack: error.stack,
        jobId,
        requestId: req.requestId
      });

      throw error; // Let asyncHandler handle it
    }
  })
);

/**
 * Get video generation history for a user
 */
router.get('/video-history', videoHistoryLimiter, asyncHandler(async (req, res) => {
  const { userId, limit = 50, videoType, status } = req.query;

  if (!userId) {
    return res.status(400).json(formatResponse.error('User ID is required'));
  }

  try {
    logger.info('Video history request received', { userId, requestId: req.requestId });

    // Get video history from database
    const historyResult = await getVideoHistory(userId, { limit, videoType, status }, req.requestId);

    if (historyResult.error) {
      return res.status(500).json(formatResponse.error('Failed to fetch video history'));
    }

    const videos = historyResult.data || [];

    // Phase 1: Optimized batch presigned URL generation for videos
    const videosWithUrls = await generateVideoPresignedUrlsBatch(videos, req.requestId);

    return res.json(formatResponse.success({
      videos: videosWithUrls
    }));

  } catch (error) {
    logger.error('Server error in video history fetch', {
      error: error.message,
      requestId: req.requestId
    });
    
    throw error; // Let asyncHandler handle it
  }
}));

/**
 * Delete a video
 */
router.delete('/:videoId', asyncHandler(async (req, res) => {
  const { videoId } = req.params;
  const { userId } = req.query;

  if (!videoId || !userId) {
    return res.status(400).json(formatResponse.error('Video ID and User ID are required'));
  }

  try {
    logger.info('Video deletion request received', { videoId, requestId: req.requestId });

    // Delete video record and get S3 key
    const deleteResult = await deleteVideoRecord(videoId, userId, req.requestId);

    if (deleteResult.error) {
      if (!deleteResult.found) {
        return res.status(404).json(formatResponse.error('Video not found or access denied'));
      }
      return res.status(500).json(formatResponse.error('Failed to delete video record'));
    }

    // Delete from S3 if key exists
    if (deleteResult.s3Key) {
      const s3Result = await deleteVideoFromS3({ key: deleteResult.s3Key });
      if (!s3Result.success) {
        logger.warn('Failed to delete video from S3', {
          error: s3Result.error,
          videoId,
          requestId: req.requestId
        });
      }
    }

    return res.json(formatResponse.success({
      message: 'Video deleted successfully'
    }));

  } catch (error) {
    logger.error('Server error in video deletion', {
      error: error.message,
      videoId,
      requestId: req.requestId
    });
    
    throw error; // Let asyncHandler handle it
  }
}));

/**
 * Cleanup function for failed or abandoned video generation jobs
 */
async function cleanupFailedVideoJobs() {
  try {
    // Find videos that have been pending for more than the timeout period
    const pendingResult = await getPendingVideosForCleanup(TIME_INTERVALS.VIDEO_CLEANUP_TIMEOUT, 'cleanup');

    if (pendingResult.error) {
      logger.error('Error fetching pending videos for cleanup', { error: pendingResult.error.message });
      return;
    }

    const pendingVideos = pendingResult.data || [];

    if (pendingVideos.length > 0) {
      logger.info(`Found ${pendingVideos.length} pending videos to cleanup`);

      for (const video of pendingVideos) {
        try {
          // Check if the job is actually still running
          const statusResult = await checkVideoGenerationStatus({ jobId: video.job_id });
          
          const updateData = statusResult.success
            ? {
                status: statusResult.status,
                error_message: statusResult.error || null
              }
            : {
                status: 'failed',
                error_message: 'Job status check failed during cleanup',
                completed_at: new Date().toISOString()
              };

          await updateVideoStatus(video.job_id, updateData, 'cleanup');
        } catch (error) {
          logger.error('Error during video cleanup', {
            videoId: video.id,
            jobId: video.job_id,
            error: error.message
          });
        }
      }
    }

    // Clean up old failed videos
    const deleteResult = await deleteOldFailedVideos(TIME_INTERVALS.OLD_VIDEO_CLEANUP, 'cleanup');

    if (deleteResult.error) {
      logger.error('Error deleting old failed videos', { error: deleteResult.error.message });
    } else {
      logger.info('Cleanup completed successfully');
    }
  } catch (error) {
    logger.error('Error in video cleanup process', { error: error.message });
  }
}

/**
 * Force S3 fallback check for processing videos
 * This endpoint specifically checks S3 for videos stuck in processing status
 */
router.post('/force-video-fallback-check',
  asyncHandler(async (req, res) => {
    const { userId } = req.body;
    
    if (!userId) {
      return res.status(400).json(formatResponse.error('User ID is required'));
    }

    try {
      logger.info('🔍 [DEBUG] Force S3 fallback check requested', {
        userId,
        requestId: req.requestId
      });

      // Get all processing videos for this user
      const processingResult = await getProcessingVideos(userId, req.requestId);

      if (processingResult.error) {
        return res.status(500).json(formatResponse.error('Failed to fetch processing videos'));
      }

      const processingVideos = processingResult.data || [];

      if (processingVideos.length === 0) {
        logger.info('No processing videos found for fallback check', {
          userId,
          requestId: req.requestId
        });
        return res.json(formatResponse.success({
          message: 'No processing videos found',
          recoveredVideos: []
        }));
      }

      logger.info('🔍 [DEBUG] Found processing videos for fallback check', {
        userId,
        count: processingVideos.length,
        requestId: req.requestId
      });

      // Use batch check for S3 fallback
      const batchResults = await batchCheckS3Fallback(processingVideos, req.requestId);
      const recoveredVideos = [];

      // Process results and update database
      for (const result of batchResults) {
        if (result.recovered) {
          const updateResult = await updateVideoStatus(result.video.job_id, {
            status: 'completed',
            s3_key: result.fallbackResult.s3Key,
            completed_at: new Date().toISOString()
          }, req.requestId);

          if (!updateResult.error) {
            recoveredVideos.push({
              id: result.video.id,
              jobId: result.video.job_id,
              prompt: result.video.prompt,
              s3Key: result.fallbackResult.s3Key,
              videoUrl: result.fallbackResult.videoUrl
            });
          }
        }
      }

      logger.info('🎯 [DEBUG] S3 fallback check completed', {
        userId,
        totalProcessing: processingVideos.length,
        recovered: recoveredVideos.length,
        requestId: req.requestId
      });

      return res.json(formatResponse.success({
        message: `Checked ${processingVideos.length} processing videos, recovered ${recoveredVideos.length}`,
        totalProcessing: processingVideos.length,
        recoveredVideos
      }));

    } catch (error) {
      logger.error('Error in force video fallback check', {
        error: error.message,
        userId,
        requestId: req.requestId
      });
      
      throw error; // Let asyncHandler handle it
    }
  })
);

// Export cleanup function for use in main server
router.cleanupFailedVideoJobs = cleanupFailedVideoJobs;

module.exports = router;