import { toast } from "sonner";
import { ImageHistoryItem } from "@/services/imageHistoryService";
import { VideoHistoryItem } from "@/services/videoService";
import { getPresignedUrl } from "@/services/s3Service";
import { deleteImage, deleteVideo } from "@/services/deleteService";

export const handleImageSelection = async (
  image: ImageHistoryItem,
  setSelectedImage: (image: ImageHistoryItem) => void,
  setImageUrl: (url: string) => void,
  setDialogOpen: (open: boolean) => void
) => {
  setSelectedImage(image);

  // Get a fresh presigned URL for the image
  if (image.s3_key) {
    try {
      const result = await getPresignedUrl({ key: image.s3_key });
      if (result.success && result.url) {
        setImageUrl(result.url);
      } else {
        setImageUrl(image.s3_url); // Fallback to direct S3 URL
      }
    } catch (error) {
      if (import.meta.env.DEV) {
        console.error("Failed to get presigned URL:", error);
      }
      setImageUrl(image.s3_url); // Fallback to direct S3 URL
    }
  } else {
    setImageUrl(image.s3_url);
  }

  setDialogOpen(true);
};

export const handleVideoSelection = (
  video: VideoHistoryItem,
  setSelectedVideo: (video: VideoHistoryItem) => void,
  setVideoDialogOpen: (open: boolean) => void
) => {
  setSelectedVideo(video);
  setVideoDialogOpen(true);
};

export const handleImageDownload = async (image: ImageHistoryItem) => {
  try {
    // Always get a fresh presigned URL for downloads
    if (image.s3_key) {
      const result = await getPresignedUrl({ key: image.s3_key });
      if (result.success && result.url) {
        const link = document.createElement("a");
        link.href = result.url;
        link.download = `vibenecto-image-${Date.now()}.png`;
        link.style.display = 'none';
        
        try {
          document.body.appendChild(link);
          link.click();
        } finally {
          // Ensure cleanup even if click fails
          if (document.body.contains(link)) {
            document.body.removeChild(link);
          }
        }
        toast.success("Image downloaded successfully");
        return;
      }
    }

    // Fallback to direct S3 URL if presigned URL fails
    const link = document.createElement("a");
    link.href = image.s3_url;
    link.download = `vibenecto-image-${Date.now()}.png`;
    link.style.display = 'none';
    
    try {
      document.body.appendChild(link);
      link.click();
    } finally {
      // Ensure cleanup even if click fails
      if (document.body.contains(link)) {
        document.body.removeChild(link);
      }
    }
    toast.success("Image downloaded successfully");
  } catch (error) {
    if (import.meta.env.DEV) {
      console.error('Failed to download image:', error);
    }
    toast.error('Failed to download image');
  }
};

export const handleMediaDeletion = async (
  itemToDelete: { type: 'image' | 'video'; item: ImageHistoryItem | VideoHistoryItem },
  user: any,
  queryClient: any
) => {
  if (!itemToDelete || !user) return;

  const { type, item } = itemToDelete;
  
  try {
    if (!item.id) {
      throw new Error('Item ID is missing');
    }

    if (type === 'image') {
      const result = await deleteImage(item.id, user.id);
      if (!result.success) {
        throw new Error(result.error || 'Failed to delete image');
      }
      
      // Invalidate and refetch image history
      await queryClient.invalidateQueries({ queryKey: ['imageHistory', user.id] });
      
      if (result.warning) {
        toast.warning(result.warning);
      }
    } else {
      const result = await deleteVideo(item.id, user.id);
      if (!result.success) {
        throw new Error(result.error || 'Failed to delete video');
      }
      
      // Invalidate and refetch video history
      await queryClient.invalidateQueries({ queryKey: ['videoHistory', user.id] });
      
      if (result.warning) {
        toast.warning(result.warning);
      }
    }
  } catch (error) {
    throw error; // Re-throw to be handled by DeleteConfirmationDialog
  }
};