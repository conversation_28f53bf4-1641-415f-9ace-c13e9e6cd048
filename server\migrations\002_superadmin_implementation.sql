-- Migration: 002_superadmin_implementation.sql
-- Sprint 7: Superadmin Dashboard Implementation - Backend Infrastructure
-- Description: Add role management and superadmin capabilities to profiles table
-- Date: January 2025

-- Begin transaction for atomic migration
BEGIN;

-- Check if migration has already been applied
DO $$
BEGIN
  IF EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'profiles' AND column_name = 'role') THEN
    RAISE NOTICE 'Migration 002_superadmin_implementation already applied, skipping...';
    ROLLBACK;
    RETURN;
  END IF;
END $$;

-- Add role and superadmin columns to profiles table
ALTER TABLE profiles ADD COLUMN IF NOT EXISTS role VARCHAR(20) DEFAULT 'user';
ALTER TABLE profiles ADD COLUMN IF NOT EXISTS is_superadmin BOOLEAN DEFAULT FALSE;

-- Create indexes for performance optimization
CREATE INDEX IF NOT EXISTS idx_profiles_role ON profiles(role);
CREATE INDEX IF NOT EXISTS idx_profiles_superadmin ON profiles(is_superadmin);

-- Set superadmin <NAME_EMAIL> (if exists)
UPDATE profiles 
SET role = 'superadmin', is_superadmin = TRUE 
WHERE email = '<EMAIL>';

-- Create function to automatically assign superadmin role
CREATE OR REPLACE FUNCTION set_superadmin_role()
RETURNS TRIGGER AS $$
BEGIN
  IF NEW.email = '<EMAIL>' THEN
    NEW.role = 'superadmin';
    NEW.is_superadmin = TRUE;
  END IF;
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create trigger for automatic superadmin role assignment
DROP TRIGGER IF EXISTS trigger_set_superadmin_role ON profiles;
CREATE TRIGGER trigger_set_superadmin_role
  BEFORE INSERT OR UPDATE ON profiles
  FOR EACH ROW
  EXECUTE FUNCTION set_superadmin_role();

-- Add comments for documentation
COMMENT ON COLUMN profiles.role IS 'User role: user, superadmin';
COMMENT ON COLUMN profiles.is_superadmin IS 'Boolean flag for superadmin access';
COMMENT ON FUNCTION set_superadmin_role() IS 'Automatically assigns superadmin <NAME_EMAIL>';

-- Create a record of this migration
CREATE TABLE IF NOT EXISTS schema_migrations (
  id SERIAL PRIMARY KEY,
  migration_name VARCHAR(255) NOT NULL UNIQUE,
  applied_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  description TEXT
);

-- Record this migration
INSERT INTO schema_migrations (migration_name, description) 
VALUES ('002_superadmin_implementation', 'Add role management and superadmin capabilities')
ON CONFLICT (migration_name) DO NOTHING;

-- Verify the migration was successful
DO $$
BEGIN
  -- Check if columns exist
  IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'profiles' AND column_name = 'role') THEN
    RAISE EXCEPTION 'Migration failed: Column role was not added to profiles table';
  END IF;
  
  IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'profiles' AND column_name = 'is_superadmin') THEN
    RAISE EXCEPTION 'Migration failed: Column is_superadmin was not added to profiles table';
  END IF;
  
  -- Check if indexes exist
  IF NOT EXISTS (SELECT 1 FROM pg_indexes WHERE tablename = 'profiles' AND indexname = 'idx_profiles_role') THEN
    RAISE EXCEPTION 'Migration failed: Index idx_profiles_role was not created';
  END IF;
  
  IF NOT EXISTS (SELECT 1 FROM pg_indexes WHERE tablename = 'profiles' AND indexname = 'idx_profiles_superadmin') THEN
    RAISE EXCEPTION 'Migration failed: Index idx_profiles_superadmin was not created';
  END IF;
  
  -- Check if function exists
  IF NOT EXISTS (SELECT 1 FROM pg_proc WHERE proname = 'set_superadmin_role') THEN
    RAISE EXCEPTION 'Migration failed: Function set_superadmin_role was not created';
  END IF;
  
  -- Check if trigger exists
  IF NOT EXISTS (SELECT 1 FROM pg_trigger WHERE tgname = 'trigger_set_superadmin_role') THEN
    RAISE EXCEPTION 'Migration failed: Trigger trigger_set_superadmin_role was not created';
  END IF;
  
  RAISE NOTICE 'Migration 002_superadmin_implementation completed successfully';
END $$;

-- Commit the transaction
COMMIT;

-- Display summary
SELECT 
  'Migration Summary' as status,
  COUNT(*) as total_users,
  COUNT(*) FILTER (WHERE role = 'superadmin') as superadmin_users,
  COUNT(*) FILTER (WHERE is_superadmin = true) as superadmin_flags
FROM profiles;