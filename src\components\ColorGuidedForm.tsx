import React from 'react';
import { Button } from '@/components/ui/button';
import { Label } from '@/components/ui/label';
import { Check, RefreshCw } from 'lucide-react';
import { ColorItem } from '@/hooks/useColorPalette';
import { COLOR_GUIDED_MESSAGES, COLOR_GUIDED_DEFAULTS } from '@/constants/colorGuided';

interface ColorGuidedFormProps {
  prompt: string;
  negativePrompt: string;
  colors: ColorItem[];
  isProcessing: boolean;
  onPromptChange: (value: string) => void;
  onNegativePromptChange: (value: string) => void;
  onGenerate: () => void;
}

const ColorGuidedForm: React.FC<ColorGuidedFormProps> = ({
  prompt,
  negativePrompt,
  colors,
  isProcessing,
  onPromptChange,
  onNegativePromptChange,
  onGenerate
}) => {
  const canGenerate = !isProcessing && prompt.trim().length > 0 && colors.length > 0;

  return (
    <div className="space-y-6">
      {/* Section 2: Describe Your Image */}
      <div className="space-y-4">
        <h3 className="text-md font-semibold text-gray-700 dark:text-gray-200">
          2. Describe Your Image
        </h3>
        
        <div className="flex items-center gap-2 mb-2">
          <span className="text-xs font-medium">Selected Colors:</span>
          <div className="flex gap-1">
            {colors.map((colorItem) => (
              <div
                key={colorItem.id}
                className="h-5 w-5 rounded-full border border-gray-200 dark:border-gray-700"
                style={{ backgroundColor: colorItem.color }}
                title={colorItem.color}
              ></div>
            ))}
          </div>
        </div>
        
        <div className="space-y-2">
          <Label htmlFor="prompt" className="text-sm font-medium">
            Prompt
          </Label>
          <textarea
            id="prompt"
            placeholder={COLOR_GUIDED_DEFAULTS.DEFAULT_PROMPT_PLACEHOLDER}
            value={prompt}
            onChange={(e) => onPromptChange(e.target.value)}
            className="w-full h-32 p-3 text-sm rounded-md border border-gray-200 dark:border-gray-700 bg-white dark:bg-gray-950 focus:outline-none focus:ring-2 focus:ring-brand-purple focus:border-transparent"
          />
          <p className="text-xs text-gray-500">
            {COLOR_GUIDED_MESSAGES.INFO.PROMPT_HELP}
          </p>
        </div>
      </div>
      
      {/* Section 3: Negative Prompt */}
      <div className="space-y-2">
        <h3 className="text-md font-semibold text-gray-700 dark:text-gray-200">
          3. Negative Prompt (Optional)
        </h3>
        <textarea
          id="negativePrompt"
          placeholder={COLOR_GUIDED_DEFAULTS.DEFAULT_NEGATIVE_PLACEHOLDER}
          value={negativePrompt}
          onChange={(e) => onNegativePromptChange(e.target.value)}
          className="w-full h-20 p-3 text-sm rounded-md border border-gray-200 dark:border-gray-700 bg-white dark:bg-gray-950 focus:outline-none focus:ring-2 focus:ring-brand-purple focus:border-transparent"
        />
        <div className="text-xs text-gray-500">
          <p>{COLOR_GUIDED_MESSAGES.INFO.NEGATIVE_PROMPT_HELP}</p>
        </div>
      </div>

      {/* Section 4: Generate Button */}
      <div className="pt-4">
        <Button
          className="w-full bg-brand-purple hover:bg-brand-purple/90"
          onClick={onGenerate}
          disabled={!canGenerate}
        >
          {isProcessing ? (
            <>
              <RefreshCw size={16} className="mr-2 animate-spin" />
              Generating...
            </>
          ) : (
            <>
              <Check size={16} className="mr-2" />
              Generate Image
            </>
          )}
        </Button>
      </div>
    </div>
  );
};

export default ColorGuidedForm;