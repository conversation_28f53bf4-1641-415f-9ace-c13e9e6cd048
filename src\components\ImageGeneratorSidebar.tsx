import React from 'react';
import { UseFormReturn } from 'react-hook-form';
import { Button } from '@/components/ui/button';
import { Form } from '@/components/ui/form';
import { Sparkles, RefreshCw, Palette, Settings2, Undo } from 'lucide-react';
import { ImageGeneratorForm } from './ImageGeneratorForm';
import { ImageFormValues } from '@/hooks/useImageForm';

interface ImageGeneratorSidebarProps {
  form: UseFormReturn<ImageFormValues>;
  activeSidebarSection: 'design' | 'advanced';
  setActiveSidebarSection: (section: 'design' | 'advanced') => void;
  isGenerating: boolean;
  onSubmit: (data: ImageFormValues) => void;
  onReset: () => void;
}

export const ImageGeneratorSidebar: React.FC<ImageGeneratorSidebarProps> = ({
  form,
  activeSidebarSection,
  setActiveSidebarSection,
  isGenerating,
  onSubmit,
  onReset,
}) => {
  return (
    <div className="w-80 border-r border-gray-200 dark:border-gray-800 flex flex-col h-full">
      {/* Sidebar Header */}
      <div className="p-4 border-b border-gray-200 dark:border-gray-800">
        <h2 className="text-lg font-semibold flex items-center gap-2">
          <Sparkles size={18} className="text-brand-purple" />
          Image Generation
        </h2>
      </div>

      {/* Tab Navigation */}
      <div className="flex border-b border-gray-200 dark:border-gray-800">
        <button
          className={`flex-1 py-3 px-4 text-sm font-medium flex items-center justify-center gap-1.5 ${
            activeSidebarSection === "design" 
              ? "border-b-2 border-brand-purple text-brand-purple" 
              : "text-gray-500 hover:bg-gray-100 dark:hover:bg-gray-800"
          }`}
          onClick={() => setActiveSidebarSection("design")}
        >
          <Palette size={14} />
          <span>Design</span>
        </button>
        <button
          className={`flex-1 py-3 px-4 text-sm font-medium flex items-center justify-center gap-1.5 ${
            activeSidebarSection === "advanced" 
              ? "border-b-2 border-brand-purple text-brand-purple" 
              : "text-gray-500 hover:bg-gray-100 dark:hover:bg-gray-800"
          }`}
          onClick={() => setActiveSidebarSection("advanced")}
        >
          <Settings2 size={14} />
          <span>Advanced</span>
        </button>
      </div>

      {/* Sidebar content - scrollable */}
      <div className="flex-1 overflow-y-auto p-6">
        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-8">
            <ImageGeneratorForm 
              form={form} 
              activeSidebarSection={activeSidebarSection} 
            />

            {/* Submit Button */}
            <div className="pt-6 border-t border-gray-200 dark:border-gray-800 sticky bottom-0 bg-gray-50 dark:bg-gray-900 py-4">
              <Button
                type="submit"
                className="w-full bg-brand-purple hover:bg-brand-purple/90 text-white"
                disabled={isGenerating}
              >
                {isGenerating ? (
                  <>
                    <RefreshCw size={16} className="mr-2 animate-spin" />
                    Generating...
                  </>
                ) : (
                  <>
                    <Sparkles size={16} className="mr-2" />
                    Generate Image
                  </>
                )}
              </Button>
              <Button
                type="button"
                variant="outline"
                size="sm"
                onClick={onReset}
                disabled={isGenerating}
                className="mt-2 w-full text-xs"
              >
                <Undo size={14} className="mr-1.5" />
                Reset All
              </Button>
            </div>
          </form>
        </Form>
      </div>

      {/* Sidebar footer */}
      <div className="p-4 border-t border-gray-200 dark:border-gray-800">
        <div className="text-xs text-gray-500 flex items-center justify-between">
          <span>Image Generator v1.2</span>
          <a href="#" className="text-brand-purple hover:underline">Help</a>
        </div>
      </div>
    </div>
  );
};