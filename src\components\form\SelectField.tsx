import React from 'react';
import { FormControl, FormItem, FormMessage } from '@/components/ui/form';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';

interface SelectOption {
  value: string;
  label: string;
}

interface SelectFieldProps {
  icon: React.ReactNode;
  label: string;
  placeholder: string;
  value: string;
  onChange: (value: string) => void;
  options: SelectOption[];
  description?: string;
}

export const SelectField: React.FC<SelectFieldProps> = ({
  icon,
  label,
  placeholder,
  value,
  onChange,
  options,
  description,
}) => {
  return (
    <div className="space-y-6">
      <FormItem>
        <label className="text-sm font-medium flex items-center gap-1.5 mb-2">
          {icon}
          {label}
        </label>
        <Select onValueChange={onChange} defaultValue={value}>
          <FormControl>
            <SelectTrigger className="text-sm">
              <SelectValue placeholder={placeholder} />
            </SelectTrigger>
          </FormControl>
          <SelectContent>
            {options.map(option => (
              <SelectItem key={option.value} value={option.value}>
                {option.label}
              </SelectItem>
            ))}
          </SelectContent>
        </Select>
        {description && (
          <p className="text-xs text-gray-500 mt-1">{description}</p>
        )}
        <FormMessage />
      </FormItem>
    </div>
  );
};