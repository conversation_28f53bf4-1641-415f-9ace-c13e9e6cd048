import React from 'react';
import { FormControl, FormItem, FormMessage } from '@/components/ui/form';
import { Slider } from '@/components/ui/slider';
import { Info } from 'lucide-react';
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from '@/components/ui/tooltip';

interface SliderFieldProps {
  icon: React.ReactNode;
  label: string;
  tooltip: string;
  value: number;
  onChange: (value: number) => void;
  min: number;
  max: number;
  step: number;
  leftLabel?: string;
  rightLabel?: string;
  description?: string;
}

export const SliderField: React.FC<SliderFieldProps> = ({
  icon,
  label,
  tooltip,
  value,
  onChange,
  min,
  max,
  step,
  leftLabel,
  rightLabel,
  description,
}) => {
  return (
    <div className="space-y-6">
      <FormItem>
        <div className="flex justify-between items-center mb-2">
          <div className="flex items-center gap-1.5">
            <label className="text-sm font-medium flex items-center gap-1.5">
              {icon}
              {label}
            </label>
            <TooltipProvider>
              <Tooltip>
                <TooltipTrigger asChild>
                  <Info size={14} className="text-gray-400 cursor-pointer" />
                </TooltipTrigger>
                <TooltipContent>
                  <p className="w-80 text-xs">{tooltip}</p>
                </TooltipContent>
              </Tooltip>
            </TooltipProvider>
          </div>
          <span className="text-xs text-gray-500">{value}</span>
        </div>
        <FormControl>
          <Slider
            min={min}
            max={max}
            step={step}
            value={[value]}
            onValueChange={(vals) => onChange(vals[0])}
          />
        </FormControl>
        {(leftLabel || rightLabel) && (
          <div className="flex justify-between text-xs text-gray-500 mt-1">
            <span>{leftLabel}</span>
            <span>{rightLabel}</span>
          </div>
        )}
        {description && (
          <p className="text-xs text-gray-500 mt-1">{description}</p>
        )}
        <FormMessage />
      </FormItem>
    </div>
  );
};