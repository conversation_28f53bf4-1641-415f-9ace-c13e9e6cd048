import { getPresignedUrl } from '@/services/s3Service';
import { presignedUrlCache } from '@/services/presignedUrlCache';

export interface PresignedUrlResult {
  s3_key: string;
  url: string;
  success: boolean;
  error?: string;
  loadTime: number;
  retryCount: number;
}

export interface PresignedUrlBatchOptions {
  timeout?: number; // Timeout per URL request in milliseconds
  maxRetries?: number; // Maximum retry attempts per URL
  retryDelay?: number; // Delay between retries in milliseconds
  concurrencyLimit?: number; // Maximum concurrent requests
  fallbackUrls?: Record<string, string>; // Fallback URLs for each s3_key
  enableMetrics?: boolean; // Enable performance metrics
  useCache?: boolean; // Enable cache usage
  cacheTtl?: number; // Cache TTL override in milliseconds
  forceRefresh?: boolean; // Force refresh cached URLs
}

export interface PresignedUrlBatchResult {
  results: PresignedUrlResult[];
  successCount: number;
  failureCount: number;
  totalTime: number;
  averageTime: number;
  retryCount: number;
  cacheHits: number;
  cacheMisses: number;
  cacheHitRate: number;
}

/**
 * Generate a single presigned URL with timeout and retry logic
 */
const generatePresignedUrlWithRetry = async (
  s3_key: string,
  options: PresignedUrlBatchOptions,
  fallbackUrl?: string
): Promise<PresignedUrlResult> => {
  const {
    timeout = 5000,
    maxRetries = 2,
    retryDelay = 1000,
    useCache = true,
    cacheTtl,
    forceRefresh = false
  } = options;

  // Check cache first (unless force refresh is requested)
  if (useCache && !forceRefresh) {
    const cached = presignedUrlCache.get(s3_key);
    if (cached) {
      return {
        s3_key,
        url: cached.url,
        success: true,
        loadTime: 0, // Cache hit, no load time
        retryCount: 0,
      };
    }
  }

  const startTime = performance.now();
  let retryCount = 0;
  let lastError: string = '';

  for (let attempt = 0; attempt <= maxRetries; attempt++) {
    try {
      // Create a timeout promise
      const timeoutPromise = new Promise<never>((_, reject) => {
        setTimeout(() => reject(new Error('Request timeout')), timeout);
      });

      // Race between the actual request and timeout
      const urlPromise = getPresignedUrl({ key: s3_key });
      const result = await Promise.race([urlPromise, timeoutPromise]);

      if (result.success && result.url) {
        const loadTime = performance.now() - startTime;

        // Cache the successful result
        if (useCache) {
          presignedUrlCache.set(s3_key, result.url, cacheTtl, {
            retryCount: attempt,
            loadTime,
          });
        }

        return {
          s3_key,
          url: result.url,
          success: true,
          loadTime,
          retryCount: attempt,
        };
      } else {
        lastError = result.error || 'Unknown error';
        if (attempt < maxRetries) {
          retryCount++;
          await new Promise(resolve => setTimeout(resolve, retryDelay * (attempt + 1))); // Exponential backoff
        }
      }
    } catch (error) {
      lastError = error instanceof Error ? error.message : 'Request failed';
      if (attempt < maxRetries) {
        retryCount++;
        await new Promise(resolve => setTimeout(resolve, retryDelay * (attempt + 1))); // Exponential backoff
      }
    }
  }

  // All retries failed, use fallback URL if available
  const loadTime = performance.now() - startTime;
  if (fallbackUrl) {
    // Cache the fallback URL with shorter TTL
    if (useCache) {
      presignedUrlCache.set(s3_key, fallbackUrl, Math.min(cacheTtl || 45 * 60 * 1000, 5 * 60 * 1000), {
        retryCount,
        loadTime,
      });
    }

    return {
      s3_key,
      url: fallbackUrl,
      success: true, // Consider fallback as success
      error: `Used fallback after ${maxRetries + 1} attempts: ${lastError}`,
      loadTime,
      retryCount,
    };
  }

  return {
    s3_key,
    url: '',
    success: false,
    error: lastError,
    loadTime,
    retryCount,
  };
};

/**
 * Process URLs in batches with concurrency control
 */
const processBatch = async (
  s3_keys: string[],
  options: PresignedUrlBatchOptions
): Promise<PresignedUrlResult[]> => {
  const { concurrencyLimit = 5, fallbackUrls = {} } = options;
  const results: PresignedUrlResult[] = [];

  // Process in chunks to control concurrency
  for (let i = 0; i < s3_keys.length; i += concurrencyLimit) {
    const chunk = s3_keys.slice(i, i + concurrencyLimit);
    const chunkPromises = chunk.map(s3_key =>
      generatePresignedUrlWithRetry(s3_key, options, fallbackUrls[s3_key])
    );

    try {
      const chunkResults = await Promise.allSettled(chunkPromises);
      
      chunkResults.forEach((result, index) => {
        if (result.status === 'fulfilled') {
          results.push(result.value);
        } else {
          // Handle Promise.allSettled rejection (shouldn't happen with our error handling)
          const s3_key = chunk[index];
          results.push({
            s3_key,
            url: fallbackUrls[s3_key] || '',
            success: !!fallbackUrls[s3_key],
            error: result.reason?.message || 'Promise rejected',
            loadTime: 0,
            retryCount: 0,
          });
        }
      });
    } catch (error) {
      // Fallback for unexpected errors
      chunk.forEach(s3_key => {
        results.push({
          s3_key,
          url: fallbackUrls[s3_key] || '',
          success: !!fallbackUrls[s3_key],
          error: error instanceof Error ? error.message : 'Batch processing failed',
          loadTime: 0,
          retryCount: 0,
        });
      });
    }
  }

  return results;
};

/**
 * Generate presigned URLs in parallel with comprehensive error handling
 */
export const generatePresignedUrlsBatch = async (
  s3_keys: string[],
  options: PresignedUrlBatchOptions = {}
): Promise<PresignedUrlBatchResult> => {
  const startTime = performance.now();

  if (s3_keys.length === 0) {
    return {
      results: [],
      successCount: 0,
      failureCount: 0,
      totalTime: 0,
      averageTime: 0,
      retryCount: 0,
      cacheHits: 0,
      cacheMisses: 0,
      cacheHitRate: 0,
    };
  }

  const { enableMetrics = import.meta.env.DEV, useCache = true } = options;

  // Track cache metrics
  const initialCacheMetrics = useCache ? presignedUrlCache.getMetrics() : null;

  if (enableMetrics) {
    console.log(`[PresignedUrlService] Starting batch generation for ${s3_keys.length} URLs`);
  }

  try {
    const results = await processBatch(s3_keys, options);
    const totalTime = performance.now() - startTime;
    
    const successCount = results.filter(r => r.success).length;
    const failureCount = results.length - successCount;
    const averageTime = results.length > 0
      ? results.reduce((sum, r) => sum + r.loadTime, 0) / results.length
      : 0;
    const totalRetries = results.reduce((sum, r) => sum + r.retryCount, 0);

    // Calculate cache metrics
    const finalCacheMetrics = useCache ? presignedUrlCache.getMetrics() : null;
    const cacheHits = finalCacheMetrics && initialCacheMetrics
      ? finalCacheMetrics.hits - initialCacheMetrics.hits
      : 0;
    const cacheMisses = finalCacheMetrics && initialCacheMetrics
      ? finalCacheMetrics.misses - initialCacheMetrics.misses
      : 0;
    const cacheHitRate = (cacheHits + cacheMisses) > 0
      ? (cacheHits / (cacheHits + cacheMisses)) * 100
      : 0;

    if (enableMetrics) {
      console.log(`[PresignedUrlService] Batch complete: ${successCount}/${results.length} successful, ${totalTime.toFixed(0)}ms total, ${averageTime.toFixed(0)}ms avg, ${totalRetries} retries, ${cacheHitRate.toFixed(1)}% cache hit rate`);
    }

    return {
      results,
      successCount,
      failureCount,
      totalTime,
      averageTime,
      retryCount: totalRetries,
      cacheHits,
      cacheMisses,
      cacheHitRate,
    };
  } catch (error) {
    const totalTime = performance.now() - startTime;
    
    if (enableMetrics) {
      console.error(`[PresignedUrlService] Batch failed:`, error);
    }

    // Return fallback results for all keys
    const fallbackResults: PresignedUrlResult[] = s3_keys.map(s3_key => ({
      s3_key,
      url: options.fallbackUrls?.[s3_key] || '',
      success: !!options.fallbackUrls?.[s3_key],
      error: error instanceof Error ? error.message : 'Batch generation failed',
      loadTime: 0,
      retryCount: 0,
    }));

    return {
      results: fallbackResults,
      successCount: fallbackResults.filter(r => r.success).length,
      failureCount: fallbackResults.filter(r => !r.success).length,
      totalTime,
      averageTime: 0,
      retryCount: 0,
      cacheHits: 0,
      cacheMisses: 0,
      cacheHitRate: 0,
    };
  }
};

/**
 * Convert batch results to a URL map for easy lookup
 */
export const batchResultsToUrlMap = (results: PresignedUrlResult[]): Record<string, string> => {
  const urlMap: Record<string, string> = {};
  results.forEach(result => {
    if (result.success && result.url) {
      urlMap[result.s3_key] = result.url;
    }
  });
  return urlMap;
};
