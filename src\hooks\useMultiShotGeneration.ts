import { useState, useCallback } from "react";
import { useQueryClient } from "@tanstack/react-query";
import { useAuth } from "@/contexts/AuthContext";
import { generateVideo, VideoShot, VideoGenerationOptions } from "@/services/videoService";
import { toast } from "sonner";
import {
  ERROR_MESSAGES,
  SUCCESS_MESSAGES,
  DEFAULT_VALUES,
} from "@/constants/multiShotVideo";

interface ActiveJob {
  jobId: string;
  videoId: string;
  estimatedCompletionTime: string;
}

interface CompletedVideo {
  videoUrl: string;
  videoId: string;
  jobId: string;
}

export const useMultiShotGeneration = () => {
  const { user } = useAuth();
  const queryClient = useQueryClient();
  const [activeJobs, setActiveJobs] = useState<ActiveJob[]>([]);
  const [completedVideos, setCompletedVideos] = useState<CompletedVideo[]>([]);
  const [isGenerating, setIsGenerating] = useState(false);

  // Handle new video generation
  const handleVideoGenerated = useCallback((jobId: string, videoId: string, estimatedCompletionTime: string) => {
    const newJob: ActiveJob = {
      jobId,
      videoId,
      estimatedCompletionTime
    };
    setActiveJobs(prev => [...prev, newJob]);
  }, []);

  // Handle video completion
  const handleVideoComplete = useCallback((videoUrl: string, videoId: string, jobId: string) => {
    setCompletedVideos(prev => [...prev, { videoUrl, videoId, jobId }]);
    setActiveJobs(prev => prev.filter(job => job.jobId !== jobId));

    // Invalidate video history query to refresh the list immediately
    // Use setTimeout to ensure database transaction is committed
    setTimeout(() => {
      try {
        queryClient.invalidateQueries({ queryKey: ['videoHistory'] });
        if (user?.id) {
          queryClient.invalidateQueries({ queryKey: ['videoHistory', user.id] });
        }
        // Force refetch to ensure immediate update
        queryClient.refetchQueries({ queryKey: ['videoHistory'] });
        if (user?.id) {
          queryClient.refetchQueries({ queryKey: ['videoHistory', user.id] });
        }
      } catch (error) {
        // Silently handle query invalidation errors to prevent UI from getting stuck
        if (import.meta.env.DEV) {
          console.warn("Query invalidation failed:", error);
        }
      }
    }, 1000); // Wait 1 second for database transaction to complete
  }, [queryClient, user]);

  // Handle video error
  const handleVideoError = useCallback((error: string, jobId: string) => {
    setActiveJobs(prev => prev.filter(job => job.jobId !== jobId));
  }, []);

  // Handle automated multi-shot generation
  const handleAutomatedGeneration = useCallback(async (prompt: string, durationSeconds: number, seed?: number) => {
    if (!user?.id) {
      toast.error(ERROR_MESSAGES.SIGN_IN_REQUIRED);
      return;
    }

    setIsGenerating(true);
    try {
      const options: VideoGenerationOptions = {
        durationSeconds,
        seed: seed || Math.floor(Math.random() * DEFAULT_VALUES.SEED_RANGE),
      };

      const result = await generateVideo(
        'MULTI_SHOT_AUTOMATED',
        prompt,
        options,
        user.id
      );

      if (result.success && result.jobId) {
        toast.success(SUCCESS_MESSAGES.AUTOMATED_STARTED);
        handleVideoGenerated(
          result.jobId,
          result.videoId || "",
          result.estimatedCompletionTime || ""
        );
      } else {
        toast.error(result.error || ERROR_MESSAGES.GENERATION_FAILED);
      }
    } catch (error) {
      if (import.meta.env.DEV) {
        console.error("Error generating automated video:", error);
      }
      toast.error(ERROR_MESSAGES.UNEXPECTED_ERROR);
    } finally {
      setIsGenerating(false);
    }
  }, [user, handleVideoGenerated]);

  // Handle template selection
  const handleTemplateSelect = useCallback(async (template: any) => {
    if (!user?.id) {
      toast.error(ERROR_MESSAGES.SIGN_IN_REQUIRED);
      return;
    }

    setIsGenerating(true);
    try {
      const options: VideoGenerationOptions = {
        seed: Math.floor(Math.random() * DEFAULT_VALUES.SEED_RANGE),
        durationSeconds: 12, // Default duration for multi-shot automated videos
      };

      const result = await generateVideo(
        'MULTI_SHOT_AUTOMATED',
        template.prompt,
        options,
        user.id
      );

      if (result.success && result.jobId) {
        toast.success(SUCCESS_MESSAGES.TEMPLATE_STARTED);
        handleVideoGenerated(
          result.jobId,
          result.videoId || "",
          result.estimatedCompletionTime || ""
        );
      } else {
        toast.error(result.error || ERROR_MESSAGES.GENERATION_FAILED);
      }
    } catch (error) {
      if (import.meta.env.DEV) {
        console.error("Error generating video from template:", error);
      }
      toast.error(ERROR_MESSAGES.UNEXPECTED_ERROR);
    } finally {
      setIsGenerating(false);
    }
  }, [user, handleVideoGenerated]);

  // Handle manual shot generation
  const handleShotGeneration = useCallback(async (shots: VideoShot[]) => {
    if (!user?.id) {
      toast.error(ERROR_MESSAGES.SIGN_IN_REQUIRED);
      return;
    }

    setIsGenerating(true);
    try {
      const options: VideoGenerationOptions = {
        shots: shots,
        durationSeconds: Math.max(12, shots.length * 6), // 6 seconds per shot, minimum 12 seconds
      };

      const result = await generateVideo(
        'MULTI_SHOT_MANUAL',
        'Manual storyboard video', // This won't be used for manual shots
        options,
        user.id
      );

      if (result.success && result.jobId) {
        toast.success(SUCCESS_MESSAGES.STORYBOARD_STARTED);
        handleVideoGenerated(
          result.jobId,
          result.videoId || "",
          result.estimatedCompletionTime || ""
        );
      } else {
        toast.error(result.error || ERROR_MESSAGES.GENERATION_FAILED);
      }
    } catch (error) {
      if (import.meta.env.DEV) {
        console.error("Error generating video from shots:", error);
      }
      toast.error(ERROR_MESSAGES.UNEXPECTED_ERROR);
    } finally {
      setIsGenerating(false);
    }
  }, [user, handleVideoGenerated]);

  return {
    // State
    activeJobs,
    completedVideos,
    isGenerating,
    
    // Handlers
    handleVideoComplete,
    handleVideoError,
    handleAutomatedGeneration,
    handleTemplateSelect,
    handleShotGeneration,
  };
};