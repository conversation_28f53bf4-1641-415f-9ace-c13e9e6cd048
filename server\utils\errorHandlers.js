/**
 * Centralized error handling utilities for VibeNecto backend
 * Consolidates duplicate error handling patterns across routes
 */

const { logger } = require('./logger');
const { metricsCollector } = require('./metrics');

/**
 * Standard API error response format
 */
function createErrorResponse(message, details = null, statusCode = 500) {
  const response = {
    success: false,
    error: message
  };
  
  if (details) {
    response.details = details;
  }
  
  return { response, statusCode };
}

/**
 * Handle generation errors with metrics recording
 */
function handleGenerationError(error, startTime, options = {}) {
  const {
    userId,
    requestId,
    generationType = 'unknown',
    quality = 'standard'
  } = options;
  
  const processingTime = Date.now() - startTime;
  
  // Record metrics for failed generation
  if (generationType === 'image') {
    metricsCollector.recordImageGeneration(quality, false, processingTime);
  }
  
  // Log the error with context
  logger.error(`${generationType} generation error`, {
    error: error.message,
    stack: error.stack,
    processingTime,
    userId,
    requestId
  });
  
  return createErrorResponse(
    error.message || 'An unexpected error occurred during generation',
    null,
    500
  );
}

/**
 * Handle S3 operation errors with fallback logic
 */
function handleS3Error(error, operation, options = {}) {
  const {
    userId,
    requestId,
    key,
    fallbackMessage = null
  } = options;
  
  logger.error(`S3 ${operation} error`, {
    error: error.message,
    operation,
    key,
    userId,
    requestId
  });
  
  if (fallbackMessage) {
    return {
      success: true,
      warning: fallbackMessage + ': ' + error.message
    };
  }
  
  return createErrorResponse(
    `Failed to ${operation} file in S3: ${error.message}`,
    null,
    500
  );
}

/**
 * Handle database operation errors
 */
function handleDatabaseError(error, operation, options = {}) {
  const {
    userId,
    requestId,
    table,
    recordId
  } = options;
  
  logger.error(`Database ${operation} error`, {
    error: error.message,
    operation,
    table,
    recordId,
    userId,
    requestId
  });
  
  return createErrorResponse(
    `Database operation failed: ${error.message}`,
    error.details || null,
    500
  );
}

/**
 * Handle authentication errors
 */
function handleAuthError(message, options = {}) {
  const {
    userId,
    requestId,
    operation = 'authentication'
  } = options;
  
  logger.warn(`Authentication error: ${message}`, {
    operation,
    userId,
    requestId
  });
  
  return createErrorResponse(message, null, 401);
}

/**
 * Handle validation errors
 */
function handleValidationError(message, details = null, options = {}) {
  const {
    userId,
    requestId,
    field
  } = options;
  
  logger.warn('Validation error', {
    message,
    field,
    details,
    userId,
    requestId
  });
  
  return createErrorResponse(message, details, 400);
}

/**
 * Handle rate limiting errors
 */
function handleRateLimitError(message, options = {}) {
  const {
    userId,
    requestId,
    limit,
    remaining
  } = options;
  
  logger.warn('Rate limit exceeded', {
    message,
    limit,
    remaining,
    userId,
    requestId
  });
  
  return createErrorResponse(message, { limit, remaining }, 429);
}

/**
 * Generic error handler for unexpected errors
 */
function handleUnexpectedError(error, context = 'operation', options = {}) {
  const {
    userId,
    requestId
  } = options;
  
  logger.error(`Unexpected error in ${context}`, {
    error: error.message,
    stack: error.stack,
    context,
    userId,
    requestId
  });
  
  return createErrorResponse(
    `An unexpected error occurred during ${context}`,
    null,
    500
  );
}

/**
 * Success response helper
 */
function createSuccessResponse(data = {}, message = null) {
  const response = {
    success: true,
    ...data
  };
  
  if (message) {
    response.message = message;
  }
  
  return response;
}

module.exports = {
  createErrorResponse,
  createSuccessResponse,
  handleGenerationError,
  handleS3Error,
  handleDatabaseError,
  handleAuthError,
  handleValidationError,
  handleRateLimitError,
  handleUnexpectedError
};