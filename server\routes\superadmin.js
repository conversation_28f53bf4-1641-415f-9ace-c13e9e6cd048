const express = require('express');
const { authMiddleware } = require('../middleware/auth');
const { createSuperadminMiddleware } = require('../middleware/superadmin');
const { logger } = require('../utils/logger');

// Data validation utilities
const validateQueryParams = {
  pagination: (query) => {
    const page = parseInt(query.page) || 1;
    const limit = Math.min(parseInt(query.limit) || 50, 100); // Max 100 items per page
    return { page: Math.max(1, page), limit: Math.max(1, limit) };
  },
  
  dateRange: (startDate, endDate) => {
    const start = startDate ? new Date(startDate) : null;
    const end = endDate ? new Date(endDate) : null;
    
    if (start && isNaN(start.getTime())) {
      throw new Error('Invalid start date format');
    }
    if (end && isNaN(end.getTime())) {
      throw new Error('Invalid end date format');
    }
    if (start && end && start > end) {
      throw new Error('Start date cannot be after end date');
    }
    
    return { start, end };
  },
  
  searchString: (search) => {
    if (!search) return '';
    // Sanitize search string - remove special characters that could cause issues
    return search.toString().trim().substring(0, 100);
  },
  
  sortParams: (sortBy, sortOrder, allowedFields) => {
    const validSortBy = allowedFields.includes(sortBy) ? sortBy : allowedFields[0];
    const validSortOrder = ['asc', 'desc'].includes(sortOrder) ? sortOrder : 'desc';
    return { sortBy: validSortBy, sortOrder: validSortOrder };
  },
  
  enumValue: (value, allowedValues, defaultValue) => {
    return allowedValues.includes(value) ? value : defaultValue;
  }
};

// Centralized error handling
const handleRouteError = (error, req, res, endpoint) => {
  const requestId = req.requestId || 'unknown';
  const userId = req.user?.id || null;
  
  // Log the error with context
  logger.error(`Superadmin ${endpoint} error`, {
    error: error.message,
    stack: error.stack,
    userId,
    requestId,
    endpoint
  });
  
  // Determine appropriate status code
  let statusCode = 500;
  let errorMessage = 'Internal server error';
  
  if (error.message.includes('Invalid') || error.message.includes('validation')) {
    statusCode = 400;
    errorMessage = error.message;
  } else if (error.message.includes('Authentication') || error.message.includes('token')) {
    statusCode = 401;
    errorMessage = 'Authentication required';
  } else if (error.message.includes('permission') || error.message.includes('access')) {
    statusCode = 403;
    errorMessage = 'Access denied';
  } else if (error.message.includes('not found')) {
    statusCode = 404;
    errorMessage = 'Resource not found';
  }
  
  // Send standardized error response
  res.status(statusCode).json({
    success: false,
    error: errorMessage,
    meta: {
      timestamp: new Date().toISOString(),
      requestId,
      endpoint
    }
  });
};

// Response validation utilities
const validateResponseData = {
  sanitizeUserData: (users) => {
    if (!Array.isArray(users)) return [];
    return users.map(user => ({
      ...user,
      email: user.email || '',
      name: user.name || 'Unknown',
      role: user.role || 'user',
      created_at: user.created_at || new Date().toISOString(),
      activity: user.activity || {}
    }));
  },
  
  sanitizeStatsData: (data) => {
    return {
      users: data.users || {},
      content: data.content || {},
      system: data.system || {},
      activity: data.activity || {}
    };
  },
  
  sanitizeHealthData: (data) => {
    return {
      status: ['healthy', 'degraded', 'unhealthy'].includes(data.status) ? data.status : 'unknown',
      timestamp: data.timestamp || new Date().toISOString(),
      services: data.services || {}
    };
  }
};

// Router factory function that accepts supabase client
const createSuperadminRoutes = (supabase) => {
  const router = express.Router();
  
  // Apply authentication and superadmin middleware to all routes
  router.use(authMiddleware);
  router.use(createSuperadminMiddleware(supabase));

/**
 * GET /api/superadmin/stats
 * Get comprehensive system statistics
 */
router.get('/stats', async (req, res) => {
  const requestId = req.requestId || 'unknown';
  
  try {
    logger.info('Superadmin stats request', {
      userId: req.user.id,
      email: req.userProfile.email,
      requestId
    });

    // Fetch user statistics
    const { data: userStats, error: userError } = await supabase
      .from('profiles')
      .select('id, created_at, role, email, name')
      .order('created_at', { ascending: false });

    if (userError) {
      logger.error('Error fetching user stats', { error: userError.message, requestId });
    }

    // Fetch image statistics
    const { data: imageStats, error: imageError } = await supabase
      .from('image_history')
      .select('id, created_at, user_id, image_type');

    if (imageError) {
      logger.error('Error fetching image stats', { error: imageError.message, requestId });
    }

    // Fetch video statistics
    const { data: videoStats, error: videoError } = await supabase
      .from('video_history')
      .select('id, created_at, user_id, status, duration_seconds, video_type');

    if (videoError) {
      logger.error('Error fetching video stats', { error: videoError.message, requestId });
    }

    // Fetch voice statistics
    const { data: voiceStats, error: voiceError } = await supabase
      .from('voice_history')
      .select('id, created_at, user_id, character_count, status');

    if (voiceError) {
      logger.error('Error fetching voice stats', { error: voiceError.message, requestId });
    }

    // Fetch usage tracking statistics
    const { data: usageStats, error: usageError } = await supabase
      .from('usage_tracking')
      .select('*');

    if (usageError) {
      logger.error('Error fetching usage stats', { error: usageError.message, requestId });
    }

    // Calculate statistics
    const now = new Date();
    const last30Days = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000);
    const last7Days = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);
    const last24Hours = new Date(now.getTime() - 24 * 60 * 60 * 1000);

    // Enhanced user statistics
    const totalUsers = userStats?.length || 0;
    const recentUsers = userStats?.slice(0, 10) || [];
    const usersByRole = userStats?.reduce((acc, user) => {
      acc[user.role || 'user'] = (acc[user.role || 'user'] || 0) + 1;
      return acc;
    }, {}) || {};
    const newUsersLast30Days = userStats?.filter(u => new Date(u.created_at) >= last30Days).length || 0;
    const newUsersLast7Days = userStats?.filter(u => new Date(u.created_at) >= last7Days).length || 0;
    const newUsersLast24Hours = userStats?.filter(u => new Date(u.created_at) >= last24Hours).length || 0;

    // User growth trend analysis (daily breakdown for last 30 days)
    const userGrowthTrend = [];
    for (let i = 29; i >= 0; i--) {
      const date = new Date(now.getTime() - i * 24 * 60 * 60 * 1000);
      const startOfDay = new Date(date.getFullYear(), date.getMonth(), date.getDate());
      const endOfDay = new Date(date.getFullYear(), date.getMonth(), date.getDate() + 1);
      
      const newUsersThisDay = userStats?.filter(u => {
        const userDate = new Date(u.created_at);
        return userDate >= startOfDay && userDate < endOfDay;
      }).length || 0;
      
      userGrowthTrend.push({
        date: startOfDay.toISOString().split('T')[0],
        newUsers: newUsersThisDay,
        totalUsers: userStats?.filter(u => new Date(u.created_at) <= endOfDay).length || 0
      });
    }

    // Top content creators analysis
    const userContentStats = userStats?.map(user => {
      const userImages = imageStats?.filter(img => img.user_id === user.id).length || 0;
      const userVideos = videoStats?.filter(vid => vid.user_id === user.id).length || 0;
      const userVoices = voiceStats?.filter(voice => voice.user_id === user.id).length || 0;
      const totalContent = userImages + userVideos + userVoices;
      
      return {
        ...user,
        contentStats: {
          images: userImages,
          videos: userVideos,
          voices: userVoices,
          total: totalContent
        }
      };
    }).sort((a, b) => b.contentStats.total - a.contentStats.total) || [];

    const topContentCreators = userContentStats.slice(0, 10);

    // Content statistics
    const totalImages = imageStats?.length || 0;
    const totalVideos = videoStats?.length || 0;
    const totalVoices = voiceStats?.length || 0;
    const totalCharacters = voiceStats?.reduce((sum, v) => sum + (v.character_count || 0), 0) || 0;

    // Recent activity (last 30 days)
    const recentImages = imageStats?.filter(i => new Date(i.created_at) >= last30Days).length || 0;
    const recentVideos = videoStats?.filter(v => new Date(v.created_at) >= last30Days).length || 0;
    const recentVoices = voiceStats?.filter(v => new Date(v.created_at) >= last30Days).length || 0;

    // Video duration statistics
    const completedVideos = videoStats?.filter(v => v.status === 'completed') || [];
    const totalVideoDuration = completedVideos.reduce((sum, v) => sum + (v.duration_seconds || 0), 0);
    const averageVideoDuration = completedVideos.length > 0 ? totalVideoDuration / completedVideos.length : 0;

    // System statistics
    const systemStats = {
      uptime: process.uptime(),
      memory: process.memoryUsage(),
      nodeVersion: process.version,
      platform: process.platform,
      arch: process.arch,
      cpuUsage: process.cpuUsage(),
      timestamp: new Date().toISOString()
    };

    const responseData = {
      // Top-level totals for frontend compatibility
      totalUsers: totalUsers,
      totalImages: totalImages,
      totalVideos: totalVideos,
      totalVoices: totalVoices,
      activeUsers: totalUsers, // Could be calculated differently if needed
      storageUsed: `${Math.round(((totalImages * 2.5) + (totalVideos * 15) + (totalVoices * 0.5)) / 1024 * 100) / 100} GB`,
      generationsToday: imageStats?.filter(i => new Date(i.created_at) >= new Date(now.getTime() - 24 * 60 * 60 * 1000)).length +
                       videoStats?.filter(v => new Date(v.created_at) >= new Date(now.getTime() - 24 * 60 * 60 * 1000)).length +
                       voiceStats?.filter(v => new Date(v.created_at) >= new Date(now.getTime() - 24 * 60 * 60 * 1000)).length || 0,
      generationsThisMonth: recentImages + recentVideos + recentVoices,
      
      // Nested structure for detailed data
      users: {
        total: totalUsers,
        recent: recentUsers,
        byRole: usersByRole,
        newLast30Days: newUsersLast30Days,
        newLast7Days: newUsersLast7Days,
        newLast24Hours: newUsersLast24Hours,
        growthTrend: userGrowthTrend,
        topContentCreators: topContentCreators
      },
      content: {
        images: {
          total: totalImages,
          recent30Days: recentImages,
          byType: imageStats?.reduce((acc, img) => {
            acc[img.image_type || 'standard'] = (acc[img.image_type || 'standard'] || 0) + 1;
            return acc;
          }, {}) || {}
        },
        videos: {
          total: totalVideos,
          recent30Days: recentVideos,
          completed: completedVideos.length,
          totalDuration: totalVideoDuration,
          averageDuration: averageVideoDuration,
          byType: videoStats?.reduce((acc, vid) => {
            acc[vid.video_type || 'text_to_video'] = (acc[vid.video_type || 'text_to_video'] || 0) + 1;
            return acc;
          }, {}) || {}
        },
        voices: {
          total: totalVoices,
          recent30Days: recentVoices,
          totalCharacters: totalCharacters,
          averageCharacters: totalVoices > 0 ? totalCharacters / totalVoices : 0
        }
      },
      system: systemStats,
      activity: {
        last30Days: {
          images: recentImages,
          videos: recentVideos,
          voices: recentVoices,
          users: newUsersLast30Days
        },
        last7Days: {
          images: imageStats?.filter(i => new Date(i.created_at) >= last7Days).length || 0,
          videos: videoStats?.filter(v => new Date(v.created_at) >= last7Days).length || 0,
          voices: voiceStats?.filter(v => new Date(v.created_at) >= last7Days).length || 0,
          users: newUsersLast7Days
        }
      }
    };

    logger.info('Superadmin stats response generated', {
      userId: req.user.id,
      totalUsers,
      totalImages,
      totalVideos,
      totalVoices,
      requestId
    });

    res.json({
      success: true,
      data: responseData,
      meta: {
        timestamp: new Date().toISOString(),
        requestId,
        endpoint: '/stats'
      }
    });

  } catch (error) {
    handleRouteError(error, req, res, '/stats');
  }
});

/**
 * GET /api/superadmin/users
 * Get user management data with detailed information
 */
router.get('/users', async (req, res) => {
  const requestId = req.requestId || 'unknown';
  
  try {
    // Validate and sanitize query parameters
    const { page, limit } = validateQueryParams.pagination(req.query);
    const search = validateQueryParams.searchString(req.query.search);
    const role = validateQueryParams.searchString(req.query.role);
    const { sortBy, sortOrder } = validateQueryParams.sortParams(
      req.query.sortBy,
      req.query.sortOrder,
      ['created_at', 'email', 'name', 'role', 'updated_at']
    );
    const activityFilter = validateQueryParams.enumValue(
      req.query.activityFilter,
      ['all', 'active', 'inactive'],
      'all'
    );
    const offset = (page - 1) * limit;

    logger.info('Superadmin users request', {
      userId: req.user.id,
      page,
      limit,
      search,
      role,
      sortBy,
      sortOrder,
      activityFilter,
      requestId
    });

    // Set a timeout for the entire operation to prevent hanging
    const timeoutPromise = new Promise((_, reject) => {
      setTimeout(() => reject(new Error('Users request timeout')), 30000); // 30 second timeout
    });

    const usersDataPromise = (async () => {
      // Build query with filters
      let query = supabase
        .from('profiles')
        .select('id, email, name, role, is_superadmin, created_at, updated_at')
        .order(sortBy, { ascending: sortOrder === 'asc' });

      // Apply search filter
      if (search) {
        query = query.or(`email.ilike.%${search}%,name.ilike.%${search}%`);
      }

      // Apply role filter
      if (role) {
        query = query.eq('role', role);
      }

      // Get total count for pagination (with timeout)
      const countQuery = supabase
        .from('profiles')
        .select('*', { count: 'exact', head: true });

      // Apply pagination
      query = query.range(offset, offset + limit - 1);

      // Execute queries with error handling
      const [usersResult, countResult] = await Promise.all([
        query.then(result => {
          if (result.error) {
            logger.error('Error fetching users', { error: result.error.message, requestId });
            throw result.error;
          }
          return result;
        }),
        countQuery.then(result => {
          if (result.error) {
            logger.warn('Error fetching user count', { error: result.error.message, requestId });
            return { count: 0 }; // Fallback to 0 if count fails
          }
          return result;
        })
      ]);

      const users = usersResult.data || [];
      const totalCount = countResult.count || 0;

      // If no users found, return early
      if (users.length === 0) {
        return {
          users: [],
          segments: {
            highEngagement: 0,
            mediumEngagement: 0,
            lowEngagement: 0,
            inactive: 0,
            newUsers: 0,
            powerUsers: 0
          },
          pagination: {
            page: parseInt(page),
            limit: parseInt(limit),
            total: totalCount,
            totalPages: Math.ceil(totalCount / limit),
            filtered: 0
          }
        };
      }

      // Simplified activity data fetching with reduced complexity
      const userIds = users.map(u => u.id);
      const now = new Date();
      const last7Days = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);
      const last30Days = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000);

      // Fetch activity data with simplified queries and error handling
      let imageActivity = { data: [] };
      let videoActivity = { data: [] };
      let voiceActivity = { data: [] };

      try {
        // Limit the number of user IDs to prevent query size issues
        const limitedUserIds = userIds.slice(0, 100); // Process max 100 users at a time
        
        const activityPromises = [
          supabase
            .from('image_history')
            .select('user_id, created_at')
            .in('user_id', limitedUserIds)
            .gte('created_at', last30Days.toISOString())
            .limit(1000),
          supabase
            .from('video_history')
            .select('user_id, created_at, status')
            .in('user_id', limitedUserIds)
            .gte('created_at', last30Days.toISOString())
            .limit(1000),
          supabase
            .from('voice_history')
            .select('user_id, created_at, character_count')
            .in('user_id', limitedUserIds)
            .gte('created_at', last30Days.toISOString())
            .limit(1000)
        ];

        const [imageResult, videoResult, voiceResult] = await Promise.all(activityPromises);
        
        imageActivity = imageResult.error ? { data: [] } : imageResult;
        videoActivity = videoResult.error ? { data: [] } : videoResult;
        voiceActivity = voiceResult.error ? { data: [] } : voiceResult;

      } catch (activityError) {
        logger.warn('Error fetching activity data, using defaults', {
          error: activityError.message,
          requestId
        });
        // Continue with empty activity data
      }

      // Enhance user data with simplified activity calculation
      const sanitizedUsers = validateResponseData.sanitizeUserData(users);
      const enhancedUsers = sanitizedUsers.map(user => {
        const userImages = imageActivity.data?.filter(i => i.user_id === user.id) || [];
        const userVideos = videoActivity.data?.filter(v => v.user_id === user.id) || [];
        const userVoices = voiceActivity.data?.filter(v => v.user_id === user.id) || [];

        // Calculate recent activity (simplified)
        const recentImages = userImages.filter(i => new Date(i.created_at) >= last7Days).length;
        const recentVideos = userVideos.filter(v => new Date(v.created_at) >= last7Days).length;
        const recentVoices = userVoices.filter(v => new Date(v.created_at) >= last7Days).length;
        const recentActivity = recentImages + recentVideos + recentVoices;

        // Simplified engagement level calculation
        let engagementLevel = 'inactive';
        if (recentActivity > 10) engagementLevel = 'high';
        else if (recentActivity > 3) engagementLevel = 'medium';
        else if (recentActivity > 0) engagementLevel = 'low';

        // Calculate total content
        const totalContent = userImages.length + userVideos.length + userVoices.length;
        const totalCharacters = userVoices.reduce((sum, v) => sum + (v.character_count || 0), 0);

        // Find last activity (simplified)
        const allActivities = [
          ...userImages.map(i => new Date(i.created_at)),
          ...userVideos.map(v => new Date(v.created_at)),
          ...userVoices.map(v => new Date(v.created_at))
        ];
        const lastActivity = allActivities.length > 0 ?
          new Date(Math.max(...allActivities.map(d => d.getTime()))) : null;

        // Content preferences (simplified)
        const preferences = {
          images: totalContent > 0 ? Math.round((userImages.length / totalContent) * 100) : 0,
          videos: totalContent > 0 ? Math.round((userVideos.length / totalContent) * 100) : 0,
          voices: totalContent > 0 ? Math.round((userVoices.length / totalContent) * 100) : 0
        };

        return {
          ...user,
          activity: {
            totalImages: userImages.length,
            totalVideos: userVideos.length,
            totalVoices: userVoices.length,
            totalContent: totalContent,
            recentActivity: recentActivity,
            monthlyActivity: totalContent, // Simplified to total content
            totalCharacters: totalCharacters,
            lastActivity: lastActivity ? lastActivity.toISOString() : null,
            engagementLevel: engagementLevel,
            preferences: preferences,
            joinedDaysAgo: Math.floor((now.getTime() - new Date(user.created_at).getTime()) / (24 * 60 * 60 * 1000))
          }
        };
      });

      // Apply activity filter
      let filteredUsers = enhancedUsers;
      if (activityFilter === 'active') {
        filteredUsers = enhancedUsers.filter(u => u.activity.recentActivity > 0);
      } else if (activityFilter === 'inactive') {
        filteredUsers = enhancedUsers.filter(u => u.activity.recentActivity === 0);
      }

      // Generate user segments
      const userSegments = {
        highEngagement: enhancedUsers.filter(u => u.activity.engagementLevel === 'high').length,
        mediumEngagement: enhancedUsers.filter(u => u.activity.engagementLevel === 'medium').length,
        lowEngagement: enhancedUsers.filter(u => u.activity.engagementLevel === 'low').length,
        inactive: enhancedUsers.filter(u => u.activity.engagementLevel === 'inactive').length,
        newUsers: enhancedUsers.filter(u => u.activity.joinedDaysAgo <= 7).length,
        powerUsers: enhancedUsers.filter(u => u.activity.totalContent > 20).length
      };

      return {
        users: filteredUsers,
        segments: userSegments,
        pagination: {
          page: parseInt(page),
          limit: parseInt(limit),
          total: totalCount,
          totalPages: Math.ceil(totalCount / limit),
          filtered: filteredUsers.length
        }
      };
    })();

    // Race between the actual operation and timeout
    const responseData = await Promise.race([usersDataPromise, timeoutPromise]);

    logger.info('Superadmin users response generated', {
      userId: req.user.id,
      totalUsers: responseData.users.length,
      requestId
    });

    res.json({
      success: true,
      data: responseData,
      meta: {
        timestamp: new Date().toISOString(),
        requestId,
        endpoint: '/users'
      }
    });

  } catch (error) {
    // Enhanced error handling
    if (error.message === 'Users request timeout') {
      logger.error('Users request timed out', { requestId, userId: req.user.id });
      return res.status(408).json({
        success: false,
        error: 'Request timed out. Please try again.',
        meta: {
          timestamp: new Date().toISOString(),
          requestId,
          endpoint: '/users'
        }
      });
    }
    
    handleRouteError(error, req, res, '/users');
  }
});

/**
 * GET /api/superadmin/activity
 * Get recent system activity and audit logs
 */
router.get('/activity', async (req, res) => {
  const requestId = req.requestId || 'unknown';
  
  try {
    // Validate query parameters
    const limit = Math.min(parseInt(req.query.limit) || 100, 500); // Max 500 activities
    const type = validateQueryParams.enumValue(
      req.query.type,
      ['all', 'content', 'users'],
      'all'
    );

    logger.info('Superadmin activity request', {
      userId: req.user.id,
      limit,
      type,
      requestId
    });

    const activities = [];

    // Fetch recent content generation activity
    if (type === 'all' || type === 'content') {
      // Get user data for enriching activities
      const { data: allUsers } = await supabase
        .from('profiles')
        .select('id, name, email');
      
      const userMap = new Map(allUsers?.map(u => [u.id, u]) || []);

      // Recent images
      const { data: recentImages } = await supabase
        .from('image_history')
        .select('id, created_at, user_id, prompt, image_type')
        .order('created_at', { ascending: false })
        .limit(Math.min(limit / 3, 50));

      recentImages?.forEach(img => {
        const user = userMap.get(img.user_id);
        activities.push({
          id: img.id,
          user_id: img.user_id,
          user_email: user?.email || '',
          action: 'Image Generated',
          resource_type: 'image',
          resource_id: img.id,
          created_at: img.created_at,
          details: {
            imageType: img.image_type,
            prompt: img.prompt?.substring(0, 100) + (img.prompt?.length > 100 ? '...' : '')
          },
          user: {
            id: img.user_id,
            name: user?.name || 'Unknown User',
            email: user?.email || ''
          }
        });
      });

      // Recent videos
      const { data: recentVideos } = await supabase
        .from('video_history')
        .select('id, created_at, user_id, prompt, video_type, status, duration_seconds')
        .order('created_at', { ascending: false })
        .limit(Math.min(limit / 3, 50));

      recentVideos?.forEach(vid => {
        const user = userMap.get(vid.user_id);
        activities.push({
          id: vid.id,
          user_id: vid.user_id,
          user_email: user?.email || '',
          action: 'Video Generated',
          resource_type: 'video',
          resource_id: vid.id,
          created_at: vid.created_at,
          details: {
            videoType: vid.video_type,
            status: vid.status,
            duration: vid.duration_seconds,
            prompt: vid.prompt?.substring(0, 100) + (vid.prompt?.length > 100 ? '...' : '')
          },
          user: {
            id: vid.user_id,
            name: user?.name || 'Unknown User',
            email: user?.email || ''
          }
        });
      });

      // Recent voices
      const { data: recentVoices } = await supabase
        .from('voice_history')
        .select('id, created_at, user_id, voice_id, character_count, status')
        .order('created_at', { ascending: false })
        .limit(Math.min(limit / 3, 50));

      recentVoices?.forEach(voice => {
        const user = userMap.get(voice.user_id);
        activities.push({
          id: voice.id,
          user_id: voice.user_id,
          user_email: user?.email || '',
          action: 'Voice Generated',
          resource_type: 'voice',
          resource_id: voice.id,
          created_at: voice.created_at,
          details: {
            voiceId: voice.voice_id,
            characterCount: voice.character_count,
            status: voice.status
          },
          user: {
            id: voice.user_id,
            name: user?.name || 'Unknown User',
            email: user?.email || ''
          }
        });
      });
    }

    // Fetch recent user registrations
    if (type === 'all' || type === 'users') {
      const { data: recentUsers } = await supabase
        .from('profiles')
        .select('id, created_at, email, name, role')
        .order('created_at', { ascending: false })
        .limit(20);

      recentUsers?.forEach(user => {
        activities.push({
          id: `user_${user.id}`,
          user_id: user.id,
          user_email: user.email || '',
          action: 'User Registered',
          resource_type: 'user',
          resource_id: user.id,
          created_at: user.created_at,
          details: {
            email: user.email,
            name: user.name,
            role: user.role
          },
          user: {
            id: user.id,
            name: user.name || 'Unknown User',
            email: user.email || ''
          }
        });
      });
    }

    // Sort all activities by created_at timestamp
    activities.sort((a, b) => new Date(b.created_at) - new Date(a.created_at));

    // Limit the results
    const limitedActivities = activities.slice(0, parseInt(limit));

    logger.info('Superadmin activity response generated', {
      userId: req.user.id,
      totalActivities: limitedActivities.length,
      requestId
    });

    res.json({
      success: true,
      data: limitedActivities,
      meta: {
        total: limitedActivities.length,
        limit: parseInt(limit),
        type,
        timestamp: new Date().toISOString(),
        requestId,
        endpoint: '/activity'
      }
    });

  } catch (error) {
    handleRouteError(error, req, res, '/activity');
  }
});

/**
 * GET /api/superadmin/health
 * Get system health status
 */
router.get('/health', async (req, res) => {
  const requestId = req.requestId || 'unknown';
  
  try {
    logger.info('Superadmin health check request', {
      userId: req.user.id,
      requestId
    });

    // Check database connectivity
    const { data: dbTest, error: dbError } = await supabase
      .from('profiles')
      .select('count')
      .limit(1);

    const health = {
      status: 'healthy',
      timestamp: new Date().toISOString(),
      services: {
        database: {
          status: dbError ? 'unhealthy' : 'healthy',
          error: dbError?.message || null
        },
        server: {
          status: 'healthy',
          uptime: process.uptime(),
          memory: process.memoryUsage(),
          cpu: process.cpuUsage()
        }
      }
    };

    // Overall health status
    const allServicesHealthy = Object.values(health.services).every(service => service.status === 'healthy');
    health.status = allServicesHealthy ? 'healthy' : 'degraded';

    res.json({
      success: true,
      data: health,
      meta: {
        timestamp: new Date().toISOString(),
        requestId,
        endpoint: '/health'
      }
    });

  } catch (error) {
    handleRouteError(error, req, res, '/health');
  }
});

/**
 * GET /api/superadmin/performance
 * Get comprehensive system performance metrics
 */
router.get('/performance', async (req, res) => {
  const requestId = req.requestId || 'unknown';
  
  try {
    logger.info('Superadmin performance metrics request', {
      userId: req.user.id,
      requestId
    });

    const now = new Date();
    const last24Hours = new Date(now.getTime() - 24 * 60 * 60 * 1000);
    const last7Days = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);
    const last30Days = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000);

    // System performance metrics
    const systemMetrics = {
      server: {
        uptime: process.uptime(),
        memory: process.memoryUsage(),
        cpu: process.cpuUsage(),
        nodeVersion: process.version,
        platform: process.platform,
        arch: process.arch,
        pid: process.pid,
        loadAverage: require('os').loadavg(),
        freeMemory: require('os').freemem(),
        totalMemory: require('os').totalmem(),
        cpuCount: require('os').cpus().length
      },
      timestamp: now.toISOString()
    };

    // Database performance metrics
    const dbStartTime = Date.now();
    const { data: dbTestData, error: dbError } = await supabase
      .from('profiles')
      .select('count')
      .limit(1);
    const dbResponseTime = Date.now() - dbStartTime;

    const databaseMetrics = {
      responseTime: dbResponseTime,
      status: dbError ? 'error' : 'healthy',
      error: dbError?.message || null,
      connectionPool: {
        // Connection pool metrics would require database-specific monitoring
        active: 'N/A',
        idle: 'N/A',
        waiting: 'N/A'
      }
    };

    // Content generation performance metrics
    const [imageStats, videoStats, voiceStats] = await Promise.all([
      supabase.from('image_history').select('created_at, image_type'),
      supabase.from('video_history').select('created_at, status, processing_time_seconds, video_type'),
      supabase.from('voice_history').select('created_at, character_count, status')
    ]);

    // Calculate success rates and performance
    const contentMetrics = {
      images: {
        total: imageStats.data?.length || 0,
        last24h: imageStats.data?.filter(i => new Date(i.created_at) >= last24Hours).length || 0,
        last7d: imageStats.data?.filter(i => new Date(i.created_at) >= last7Days).length || 0,
        last30d: imageStats.data?.filter(i => new Date(i.created_at) >= last30Days).length || 0,
        successRate: 100, // Images are typically successful or not created
        byType: imageStats.data?.reduce((acc, img) => {
          acc[img.image_type || 'standard'] = (acc[img.image_type || 'standard'] || 0) + 1;
          return acc;
        }, {}) || {}
      },
      videos: {
        total: videoStats.data?.length || 0,
        completed: videoStats.data?.filter(v => v.status === 'completed').length || 0,
        failed: videoStats.data?.filter(v => v.status === 'failed').length || 0,
        processing: videoStats.data?.filter(v => v.status === 'processing').length || 0,
        pending: videoStats.data?.filter(v => v.status === 'pending').length || 0,
        last24h: videoStats.data?.filter(v => new Date(v.created_at) >= last24Hours).length || 0,
        last7d: videoStats.data?.filter(v => new Date(v.created_at) >= last7Days).length || 0,
        last30d: videoStats.data?.filter(v => new Date(v.created_at) >= last30Days).length || 0,
        successRate: videoStats.data?.length > 0 ? 
          Math.round((videoStats.data.filter(v => v.status === 'completed').length / videoStats.data.length) * 100) : 0,
        averageProcessingTime: videoStats.data?.filter(v => v.processing_time_seconds)
          .reduce((sum, v, _, arr) => sum + (v.processing_time_seconds / arr.length), 0) || 0,
        byType: videoStats.data?.reduce((acc, vid) => {
          acc[vid.video_type || 'text-to-video'] = (acc[vid.video_type || 'text-to-video'] || 0) + 1;
          return acc;
        }, {}) || {}
      },
      voices: {
        total: voiceStats.data?.length || 0,
        last24h: voiceStats.data?.filter(v => new Date(v.created_at) >= last24Hours).length || 0,
        last7d: voiceStats.data?.filter(v => new Date(v.created_at) >= last7Days).length || 0,
        last30d: voiceStats.data?.filter(v => new Date(v.created_at) >= last30Days).length || 0,
        totalCharacters: voiceStats.data?.reduce((sum, v) => sum + (v.character_count || 0), 0) || 0,
        averageCharacters: voiceStats.data?.length > 0 ? 
          Math.round(voiceStats.data.reduce((sum, v) => sum + (v.character_count || 0), 0) / voiceStats.data.length) : 0,
        successRate: 100 // Voice generation is typically successful or not created
      }
    };

    // Peak usage analysis
    const peakUsageAnalysis = {
      hourlyDistribution: {},
      dailyDistribution: {},
      weeklyDistribution: {}
    };

    // Calculate hourly distribution for last 7 days
    for (let hour = 0; hour < 24; hour++) {
      const hourlyActivity = [
        ...(imageStats.data?.filter(i => {
          const date = new Date(i.created_at);
          return date >= last7Days && date.getHours() === hour;
        }) || []),
        ...(videoStats.data?.filter(v => {
          const date = new Date(v.created_at);
          return date >= last7Days && date.getHours() === hour;
        }) || []),
        ...(voiceStats.data?.filter(v => {
          const date = new Date(v.created_at);
          return date >= last7Days && date.getHours() === hour;
        }) || [])
      ];
      peakUsageAnalysis.hourlyDistribution[hour] = hourlyActivity.length;
    }

    // Calculate daily distribution for last 30 days
    for (let i = 0; i < 30; i++) {
      const date = new Date(now.getTime() - i * 24 * 60 * 60 * 1000);
      const dayKey = date.toISOString().split('T')[0];
      const startOfDay = new Date(date.getFullYear(), date.getMonth(), date.getDate());
      const endOfDay = new Date(date.getFullYear(), date.getMonth(), date.getDate() + 1);
      
      const dailyActivity = [
        ...(imageStats.data?.filter(i => {
          const itemDate = new Date(i.created_at);
          return itemDate >= startOfDay && itemDate < endOfDay;
        }) || []),
        ...(videoStats.data?.filter(v => {
          const itemDate = new Date(v.created_at);
          return itemDate >= startOfDay && itemDate < endOfDay;
        }) || []),
        ...(voiceStats.data?.filter(v => {
          const itemDate = new Date(v.created_at);
          return itemDate >= startOfDay && itemDate < endOfDay;
        }) || [])
      ];
      peakUsageAnalysis.dailyDistribution[dayKey] = dailyActivity.length;
    }

    // Storage usage estimation (would be actual S3 metrics in production)
    const storageMetrics = {
      totalFiles: (imageStats.data?.length || 0) + (videoStats.data?.length || 0) + (voiceStats.data?.length || 0),
      estimatedSize: {
        images: (imageStats.data?.length || 0) * 2.5, // MB estimate
        videos: (videoStats.data?.filter(v => v.status === 'completed').length || 0) * 15, // MB estimate
        voices: (voiceStats.data?.length || 0) * 0.5, // MB estimate
      },
      growth: {
        last24h: contentMetrics.images.last24h + contentMetrics.videos.last24h + contentMetrics.voices.last24h,
        last7d: contentMetrics.images.last7d + contentMetrics.videos.last7d + contentMetrics.voices.last7d,
        last30d: contentMetrics.images.last30d + contentMetrics.videos.last30d + contentMetrics.voices.last30d
      }
    };

    const responseData = {
      system: systemMetrics,
      database: databaseMetrics,
      content: contentMetrics,
      peakUsage: peakUsageAnalysis,
      storage: storageMetrics,
      alerts: [] // Would contain actual alerts in production
    };

    logger.info('Superadmin performance metrics response generated', {
      userId: req.user.id,
      dbResponseTime,
      requestId
    });

    res.json({
      success: true,
      data: responseData,
      meta: {
        timestamp: new Date().toISOString(),
        requestId,
        endpoint: '/performance'
      }
    });

  } catch (error) {
    handleRouteError(error, req, res, '/performance');
  }
});

/**
 * GET /api/superadmin/analytics
 * Get advanced content generation analytics
 */
router.get('/analytics', async (req, res) => {
  const requestId = req.requestId || 'unknown';
  
  try {
    // Validate query parameters
    const { start: startDate, end: endDate } = validateQueryParams.dateRange(
      req.query.startDate,
      req.query.endDate
    );
    const granularity = validateQueryParams.enumValue(
      req.query.granularity,
      ['daily', 'weekly', 'monthly'],
      'daily'
    );

    logger.info('Superadmin analytics request', {
      userId: req.user.id,
      startDate,
      endDate,
      granularity,
      requestId
    });

    const now = new Date();
    const start = startDate ? new Date(startDate) : new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000);
    const end = endDate ? new Date(endDate) : now;

    // Fetch data within date range
    const [imageData, videoData, voiceData, userData] = await Promise.all([
      supabase.from('image_history')
        .select('created_at, user_id, image_type')
        .gte('created_at', start.toISOString())
        .lte('created_at', end.toISOString()),
      supabase.from('video_history')
        .select('created_at, user_id, video_type, status, duration_seconds, processing_time_seconds')
        .gte('created_at', start.toISOString())
        .lte('created_at', end.toISOString()),
      supabase.from('voice_history')
        .select('created_at, user_id, character_count, voice_id')
        .gte('created_at', start.toISOString())
        .lte('created_at', end.toISOString()),
      supabase.from('profiles')
        .select('id, created_at, role')
        .gte('created_at', start.toISOString())
        .lte('created_at', end.toISOString())
    ]);

    // Generate time series data based on granularity
    const generateTimeSeries = (startDate, endDate, granularity) => {
      const series = [];
      const current = new Date(startDate);
      
      while (current <= endDate) {
        const periodStart = new Date(current);
        let periodEnd;
        let label;
        
        switch (granularity) {
          case 'daily':
            periodEnd = new Date(current.getTime() + 24 * 60 * 60 * 1000);
            label = current.toISOString().split('T')[0];
            current.setDate(current.getDate() + 1);
            break;
          case 'weekly':
            periodEnd = new Date(current.getTime() + 7 * 24 * 60 * 60 * 1000);
            label = `Week of ${current.toISOString().split('T')[0]}`;
            current.setDate(current.getDate() + 7);
            break;
          case 'monthly':
            periodEnd = new Date(current.getFullYear(), current.getMonth() + 1, 1);
            label = `${current.getFullYear()}-${String(current.getMonth() + 1).padStart(2, '0')}`;
            current.setMonth(current.getMonth() + 1);
            break;
          default:
            periodEnd = new Date(current.getTime() + 24 * 60 * 60 * 1000);
            label = current.toISOString().split('T')[0];
            current.setDate(current.getDate() + 1);
        }
        
        series.push({ periodStart, periodEnd, label });
      }
      
      return series;
    };

    const timeSeries = generateTimeSeries(start, end, granularity);

    // Calculate analytics for each time period
    const analyticsData = timeSeries.map(period => {
      const periodImages = imageData.data?.filter(i => {
        const date = new Date(i.created_at);
        return date >= period.periodStart && date < period.periodEnd;
      }) || [];

      const periodVideos = videoData.data?.filter(v => {
        const date = new Date(v.created_at);
        return date >= period.periodStart && date < period.periodEnd;
      }) || [];

      const periodVoices = voiceData.data?.filter(v => {
        const date = new Date(v.created_at);
        return date >= period.periodStart && date < period.periodEnd;
      }) || [];

      const periodUsers = userData.data?.filter(u => {
        const date = new Date(u.created_at);
        return date >= period.periodStart && date < period.periodEnd;
      }) || [];

      return {
        period: period.label,
        metrics: {
          images: {
            total: periodImages.length,
            byType: periodImages.reduce((acc, img) => {
              acc[img.image_type || 'standard'] = (acc[img.image_type || 'standard'] || 0) + 1;
              return acc;
            }, {}),
            uniqueUsers: new Set(periodImages.map(i => i.user_id)).size
          },
          videos: {
            total: periodVideos.length,
            completed: periodVideos.filter(v => v.status === 'completed').length,
            failed: periodVideos.filter(v => v.status === 'failed').length,
            averageProcessingTime: periodVideos.filter(v => v.processing_time_seconds)
              .reduce((sum, v, _, arr) => sum + (v.processing_time_seconds / arr.length), 0) || 0,
            totalDuration: periodVideos.reduce((sum, v) => sum + (v.duration_seconds || 0), 0),
            byType: periodVideos.reduce((acc, vid) => {
              acc[vid.video_type || 'text-to-video'] = (acc[vid.video_type || 'text-to-video'] || 0) + 1;
              return acc;
            }, {}),
            uniqueUsers: new Set(periodVideos.map(v => v.user_id)).size
          },
          voices: {
            total: periodVoices.length,
            totalCharacters: periodVoices.reduce((sum, v) => sum + (v.character_count || 0), 0),
            averageCharacters: periodVoices.length > 0 ? 
              Math.round(periodVoices.reduce((sum, v) => sum + (v.character_count || 0), 0) / periodVoices.length) : 0,
            uniqueVoices: new Set(periodVoices.map(v => v.voice_id)).size,
            uniqueUsers: new Set(periodVoices.map(v => v.user_id)).size
          },
          users: {
            newRegistrations: periodUsers.length,
            totalActivity: periodImages.length + periodVideos.length + periodVoices.length,
            activeUsers: new Set([
              ...periodImages.map(i => i.user_id),
              ...periodVideos.map(v => v.user_id),
              ...periodVoices.map(v => v.user_id)
            ]).size
          }
        }
      };
    });

    // Calculate summary statistics
    const summary = {
      totalPeriods: timeSeries.length,
      dateRange: {
        start: start.toISOString(),
        end: end.toISOString(),
        granularity
      },
      totals: {
        images: imageData.data?.length || 0,
        videos: videoData.data?.length || 0,
        voices: voiceData.data?.length || 0,
        users: userData.data?.length || 0
      },
      averages: {
        imagesPerPeriod: Math.round((imageData.data?.length || 0) / timeSeries.length),
        videosPerPeriod: Math.round((videoData.data?.length || 0) / timeSeries.length),
        voicesPerPeriod: Math.round((voiceData.data?.length || 0) / timeSeries.length),
        usersPerPeriod: Math.round((userData.data?.length || 0) / timeSeries.length)
      }
    };

    logger.info('Superadmin analytics response generated', {
      userId: req.user.id,
      periodsAnalyzed: timeSeries.length,
      totalRecords: summary.totals.images + summary.totals.videos + summary.totals.voices,
      requestId
    });

    res.json({
      success: true,
      data: {
        analytics: analyticsData,
        summary
      },
      meta: {
        timestamp: new Date().toISOString(),
        requestId,
        endpoint: '/analytics'
      }
    });

  } catch (error) {
    logger.error('Superadmin analytics error', {
      error: error.message,
      stack: error.stack,
      userId: req.user.id,
      requestId
    });
    res.status(500).json({
      success: false,
      error: 'Failed to fetch analytics data',
      meta: {
        timestamp: new Date().toISOString(),
        requestId,
        endpoint: '/analytics'
      }
    });
  }
});

/**
 * GET /api/superadmin/reports
 * Generate automated system reports
 */
router.get('/reports', async (req, res) => {
  const requestId = req.requestId || 'unknown';
  
  try {
    // Validate user authentication before proceeding
    if (!req.user?.id) {
      logger.error('Reports endpoint accessed without valid user', {
        requestId,
        hasUser: !!req.user,
        path: req.path
      });
      return res.status(401).json({
        success: false,
        error: 'Authentication required',
        meta: {
          timestamp: new Date().toISOString(),
          requestId,
          endpoint: '/reports'
        }
      });
    }

    // Validate query parameters
    const type = validateQueryParams.enumValue(
      req.query.type,
      ['daily', 'weekly', 'monthly'],
      'daily'
    );
    const format = validateQueryParams.enumValue(
      req.query.format,
      ['json', 'csv'],
      'json'
    );

    logger.info('Superadmin reports request', {
      userId: req.user.id,
      type,
      format,
      requestId
    });

    const now = new Date();
    let startDate, endDate, reportTitle;

    switch (type) {
      case 'daily':
        startDate = new Date(now.getFullYear(), now.getMonth(), now.getDate() - 1);
        endDate = new Date(now.getFullYear(), now.getMonth(), now.getDate());
        reportTitle = `Daily Report - ${startDate.toISOString().split('T')[0]}`;
        break;
      case 'weekly':
        startDate = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);
        endDate = now;
        reportTitle = `Weekly Report - ${startDate.toISOString().split('T')[0]} to ${endDate.toISOString().split('T')[0]}`;
        break;
      case 'monthly':
        startDate = new Date(now.getFullYear(), now.getMonth() - 1, 1);
        endDate = new Date(now.getFullYear(), now.getMonth(), 1);
        reportTitle = `Monthly Report - ${startDate.getFullYear()}-${String(startDate.getMonth() + 1).padStart(2, '0')}`;
        break;
      default:
        startDate = new Date(now.getTime() - 24 * 60 * 60 * 1000);
        endDate = now;
        reportTitle = `Daily Report - ${startDate.toISOString().split('T')[0]}`;
    }

    // Fetch comprehensive data for the report period
    const [
      imageStats, videoStats, voiceStats, userStats
    ] = await Promise.all([
      supabase.from('image_history')
        .select('*')
        .gte('created_at', startDate.toISOString())
        .lt('created_at', endDate.toISOString()),
      supabase.from('video_history')
        .select('*')
        .gte('created_at', startDate.toISOString())
        .lt('created_at', endDate.toISOString()),
      supabase.from('voice_history')
        .select('*')
        .gte('created_at', startDate.toISOString())
        .lt('created_at', endDate.toISOString()),
      supabase.from('profiles')
        .select('*')
        .gte('created_at', startDate.toISOString())
        .lt('created_at', endDate.toISOString())
    ]);

    // Generate comprehensive report
    const report = {
      title: reportTitle,
      generatedAt: now.toISOString(),
      period: {
        start: startDate.toISOString(),
        end: endDate.toISOString(),
        type
      },
      summary: {
        newUsers: userStats.data?.length || 0,
        totalImages: imageStats.data?.length || 0,
        totalVideos: videoStats.data?.length || 0,
        totalVoices: voiceStats.data?.length || 0,
        totalActivity: (imageStats.data?.length || 0) + (videoStats.data?.length || 0) + (voiceStats.data?.length || 0)
      },
      details: {
        users: {
          newRegistrations: userStats.data?.length || 0,
          byRole: userStats.data?.reduce((acc, user) => {
            acc[user.role || 'user'] = (acc[user.role || 'user'] || 0) + 1;
            return acc;
          }, {}) || {}
        },
        content: {
          images: {
            total: imageStats.data?.length || 0,
            byType: imageStats.data?.reduce((acc, img) => {
              acc[img.image_type || 'standard'] = (acc[img.image_type || 'standard'] || 0) + 1;
              return acc;
            }, {}) || {},
            uniqueUsers: new Set(imageStats.data?.map(i => i.user_id) || []).size
          },
          videos: {
            total: videoStats.data?.length || 0,
            completed: videoStats.data?.filter(v => v.status === 'completed').length || 0,
            failed: videoStats.data?.filter(v => v.status === 'failed').length || 0,
            processing: videoStats.data?.filter(v => v.status === 'processing').length || 0,
            byType: videoStats.data?.reduce((acc, vid) => {
              acc[vid.video_type || 'text-to-video'] = (acc[vid.video_type || 'text-to-video'] || 0) + 1;
              return acc;
            }, {}) || {},
            uniqueUsers: new Set(videoStats.data?.map(v => v.user_id) || []).size,
            totalDuration: videoStats.data?.reduce((sum, v) => sum + (v.duration_seconds || 0), 0) || 0
          },
          voices: {
            total: voiceStats.data?.length || 0,
            totalCharacters: voiceStats.data?.reduce((sum, v) => sum + (v.character_count || 0), 0) || 0,
            uniqueUsers: new Set(voiceStats.data?.map(v => v.user_id) || []).size,
            averageCharacters: voiceStats.data?.length > 0 ? 
              Math.round(voiceStats.data.reduce((sum, v) => sum + (v.character_count || 0), 0) / voiceStats.data.length) : 0
          }
        },
        system: {
          health: 'healthy', // Would be actual health check in production
          uptime: process.uptime(),
          memory: process.memoryUsage(),
          cpu: process.cpuUsage()
        }
      },
      insights: [],
      recommendations: []
    };

    // Generate insights based on data
    if (report.summary.totalActivity > 0) {
      const activityGrowth = report.summary.totalActivity;
      report.insights.push({
        type: 'activity',
        message: `Total platform activity: ${activityGrowth} content generations`,
        severity: 'info'
      });
    }

    if (report.details.videos.failed > 0) {
      const failureRate = Math.round((report.details.videos.failed / report.details.videos.total) * 100);
      report.insights.push({
        type: 'performance',
        message: `Video generation failure rate: ${failureRate}%`,
        severity: failureRate > 10 ? 'warning' : 'info'
      });
    }

    if (report.summary.newUsers > 0) {
      report.insights.push({
        type: 'growth',
        message: `${report.summary.newUsers} new user registrations`,
        severity: 'info'
      });
    }

    // Generate recommendations
    if (report.details.videos.failed > report.details.videos.completed * 0.1) {
      report.recommendations.push({
        type: 'performance',
        priority: 'high',
        message: 'High video generation failure rate detected. Consider investigating AWS Bedrock service status.'
      });
    }

    if (report.summary.totalActivity > 1000) {
      report.recommendations.push({
        type: 'scaling',
        priority: 'medium',
        message: 'High activity levels detected. Consider monitoring resource usage and scaling if needed.'
      });
    }

    logger.info('Superadmin report generated', {
      userId: req.user.id, // Safe to access since we validated above
      reportType: type,
      totalActivity: report.summary.totalActivity,
      requestId
    });

    res.json({
      success: true,
      data: report,
      meta: {
        timestamp: new Date().toISOString(),
        requestId,
        endpoint: '/reports'
      }
    });

  } catch (error) {
    handleRouteError(error, req, res, '/reports');
  }
});

  return router;
};

module.exports = createSuperadminRoutes;