import { useState, useRef } from "react";
import { useNavigate } from "react-router-dom";
import { Button } from "@/components/ui/button";
import { <PERSON>rk<PERSON>, Wand2, Palette, Image as ImageIcon } from "lucide-react";

const FeaturesSection = () => {
  const navigate = useNavigate();
  const [sliderPosition, setSliderPosition] = useState(50);
  const sliderRef = useRef<HTMLDivElement>(null);

  // Handle slider interaction
  const handleSliderMove = (e: React.MouseEvent<HTMLDivElement> | MouseEvent) => {
    if (!sliderRef.current) return;

    const rect = (sliderRef.current as HTMLDivElement).getBoundingClientRect();
    const x = e.clientX - rect.left;
    const percentage = Math.max(0, Math.min(100, (x / rect.width) * 100));
    setSliderPosition(percentage);
  };

  const handleMouseDown = (e: React.MouseEvent<HTMLDivElement>) => {
    e.preventDefault();
    handleSliderMove(e);

    const handleMouseMove = (e: MouseEvent) => handleSliderMove(e);
    const handleMouseUp = () => {
      document.removeEventListener('mousemove', handleMouseMove);
      document.removeEventListener('mouseup', handleMouseUp);
    };

    document.addEventListener('mousemove', handleMouseMove);
    document.addEventListener('mouseup', handleMouseUp);
  };

  return (
    <section className="relative overflow-hidden py-24 md:py-32">
      <div className="container relative z-20 mx-auto px-4 md:px-8">
        {/* Section header */}
        <div className="text-center mb-16 md:mb-24">
          <div className="inline-block mb-6 px-4 py-1.5 bg-gradient-to-r from-green-500/20 to-emerald-500/20 backdrop-blur-md border border-green-400/30 rounded-full text-sm font-medium">
            <span className="flex items-center gap-2">
              <Sparkles className="w-3.5 h-3.5 text-green-400" />
              <span className="bg-gradient-to-r from-green-300 to-emerald-300 bg-clip-text text-transparent font-semibold">Free to use</span>
            </span>
          </div>

          <h2 className="text-3xl md:text-5xl font-semibold mb-6 leading-tight tracking-tight text-white">
            <span className="bg-gradient-to-r from-cyan-400 to-blue-400 bg-clip-text text-transparent">Features</span> that elevate your <span className="bg-gradient-to-r from-pink-400 to-purple-400 bg-clip-text text-transparent">marketing</span>
          </h2>

          <p className="text-lg text-white/80 mb-0 max-w-2xl mx-auto leading-relaxed">
            Our AI-powered platform gives your marketing team the tools to create stunning visuals with the perfect vibe.
          </p>
        </div>

        {/* Features grid - Seamless Design */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-16 md:gap-20 max-w-7xl mx-auto">
          {/* Feature 1 - AI Image Generation */}
          <div className="group" data-section="image-generation">
            {/* Feature header - Colorful */}
            <div className="flex items-center gap-3 mb-8">
              <div className="w-10 h-10 rounded-xl bg-gradient-to-br from-purple-500/20 to-pink-500/20 flex items-center justify-center">
                <Wand2 className="w-5 h-5 text-purple-400" />
              </div>
              <h3 className="text-xl font-medium bg-gradient-to-r from-purple-300 to-pink-300 bg-clip-text text-transparent">AI Image Generation</h3>
            </div>

            {/* Clean Image Showcase */}
            <div className="space-y-8">
              {/* Basic Model */}
              <div className="group/image">
                <div className="relative overflow-hidden rounded-2xl shadow-xl">
                  <img
                    src="src\Images\aws-generated-image.webp"
                    alt="Basic AI Generation"
                    loading="lazy"
                    className="w-full h-72 object-cover transition-transform duration-500 group-hover/image:scale-105"
                  />
                  <div className="absolute top-6 left-6">
                    <span className="px-4 py-2 bg-black/60 backdrop-blur-sm text-white rounded-full text-sm font-medium">
                      Basic Model
                    </span>
                  </div>
                </div>
              </div>

              {/* Pro Model */}
              <div className="group/image">
                <div className="relative overflow-hidden rounded-2xl shadow-xl">
                  <img
                    src="src\Images\google-pro-model.webp"
                    alt="Pro AI Generation"
                    loading="lazy"
                    className="w-full h-72 object-cover transition-transform duration-500 group-hover/image:scale-105"
                  />
                  <div className="absolute top-6 left-6 flex gap-3">
                    <span className="px-4 py-2 bg-gradient-to-r from-purple-600 to-pink-500 text-white rounded-full text-sm font-medium">
                      Pro Model
                    </span>
                    <span className="px-3 py-1 bg-amber-500/20 backdrop-blur-sm text-amber-300 rounded-full text-sm font-medium">
                      Coming Soon
                    </span>
                  </div>
                </div>
              </div>

              {/* Ultra Model */}
              <div className="group/image">
                <div className="relative overflow-hidden rounded-2xl shadow-xl">
                  <img
                    src="src\Images\google-generated-image.webp"
                    alt="Ultra AI Generation"
                    loading="lazy"
                    className="w-full h-72 object-cover transition-transform duration-500 group-hover/image:scale-105"
                  />
                  <div className="absolute top-6 left-6 flex gap-3">
                    <span className="px-4 py-2 bg-gradient-to-r from-teal-500 to-cyan-400 text-white rounded-full text-sm font-medium">
                      Ultra Model
                    </span>
                    <span className="px-3 py-1 bg-amber-500/20 backdrop-blur-sm text-amber-300 rounded-full text-sm font-medium">
                      Coming Soon
                    </span>
                  </div>
                </div>
              </div>
            </div>

            <p className="text-white/70 text-lg leading-relaxed mt-8">
              Experience dramatically improved image quality, detail, and artistic coherence with our upcoming Pro model. <span className="bg-gradient-to-r from-teal-400 to-cyan-300 bg-clip-text text-transparent font-semibold">The Ultra model will push boundaries even further.</span>
            </p>
          </div>

          {/* Feature 2 - Style Controls */}
          <div className="group">
            {/* Feature header - Colorful */}
            <div className="flex items-center gap-3 mb-8">
              <div className="w-10 h-10 rounded-xl bg-gradient-to-br from-blue-500/20 to-cyan-500/20 flex items-center justify-center">
                <Palette className="w-5 h-5 text-blue-400" />
              </div>
              <h3 className="text-xl font-medium bg-gradient-to-r from-blue-300 to-cyan-300 bg-clip-text text-transparent">Style Controls</h3>
            </div>

            {/* Clean Image Showcase */}
            <div className="space-y-8">
              {/* Basic Model */}
              <div className="group/image">
                <div className="relative overflow-hidden rounded-2xl shadow-xl">
                  <img
                    src="src\Images\aws-generated-image-cyberpunk.webp"
                    alt="Basic Style Control"
                    loading="lazy"
                    className="w-full h-72 object-cover transition-transform duration-500 group-hover/image:scale-105"
                  />
                  <div className="absolute top-6 left-6">
                    <span className="px-4 py-2 bg-black/60 backdrop-blur-sm text-white rounded-full text-sm font-medium">
                      Basic Model
                    </span>
                  </div>
                </div>
              </div>

              {/* Pro Model */}
              <div className="group/image">
                <div className="relative overflow-hidden rounded-2xl shadow-xl">
                  <img
                    src="src\Images\google-pro-model-cyberpunk.webp"
                    alt="Pro Style Control"
                    loading="lazy"
                    className="w-full h-72 object-cover transition-transform duration-500 group-hover/image:scale-105"
                  />
                  <div className="absolute top-6 left-6 flex gap-3">
                    <span className="px-4 py-2 bg-gradient-to-r from-pink-600 to-red-500 text-white rounded-full text-sm font-medium">
                      Pro Model
                    </span>
                    <span className="px-3 py-1 bg-amber-500/20 backdrop-blur-sm text-amber-300 rounded-full text-sm font-medium">
                      Coming Soon
                    </span>
                  </div>
                </div>
              </div>

              {/* Ultra Model Style Control */}
              <div className="group/image">
                <div className="relative overflow-hidden rounded-2xl shadow-xl">
                  <img
                    src="src\Images\google-generated-image-cyberpunk.webp"
                    alt="Ultra Style Control"
                    loading="lazy"
                    className="w-full h-72 object-cover transition-transform duration-500 group-hover/image:scale-105"
                  />
                  <div className="absolute top-6 left-6 flex gap-3">
                    <span className="px-4 py-2 bg-gradient-to-r from-sky-500 to-indigo-500 text-white rounded-full text-sm font-medium">
                      Ultra Model
                    </span>
                    <span className="px-3 py-1 bg-amber-500/20 backdrop-blur-sm text-amber-300 rounded-full text-sm font-medium">
                      Coming Soon
                    </span>
                  </div>
                </div>
              </div>
            </div>

            <p className="text-white/70 text-lg leading-relaxed mt-8">
              Unlock precise style control with advanced artistic techniques and brand-perfect consistency. <span className="bg-gradient-to-r from-sky-400 to-indigo-400 bg-clip-text text-transparent font-semibold">The Ultra model will offer unparalleled stylistic precision.</span>
            </p>
          </div>
        </div>

        {/* Background Removal Feature - Seamless */}
        <div className="mt-24 max-w-6xl mx-auto">
          {/* Feature header - Colorful */}
          <div className="text-center mb-12">
            <div className="flex items-center justify-center gap-3 mb-4">
              <div className="w-10 h-10 rounded-xl bg-gradient-to-br from-orange-500/20 to-red-500/20 flex items-center justify-center">
                <ImageIcon className="w-5 h-5 text-orange-400" />
              </div>
              <h3 className="text-2xl md:text-3xl font-medium bg-gradient-to-r from-orange-300 to-red-300 bg-clip-text text-transparent">Background Removal</h3>
            </div>
            <p className="text-white/80 text-lg">
              Drag the slider to see the magic happen in real-time
            </p>
          </div>

          {/* Interactive Slider Demo */}
          <div className="relative max-w-3xl mx-auto">
              <div
                ref={sliderRef}
                className="relative overflow-hidden rounded-xl cursor-pointer aspect-[4/3]"
                onMouseDown={handleMouseDown}
              >
                {/* Base image (without background) - always visible */}
                <img
                  src="src\Images\background-removed.webp"
                  alt="Image without background"
                  loading="lazy"
                  className="w-full h-full object-cover"
                />

                {/* Overlay image (with background) - clipped by slider */}
                <div
                  className="absolute inset-0 overflow-hidden transition-all duration-150 ease-out"
                  style={{ clipPath: `inset(0 0 0 ${sliderPosition}%)` }}
                >
                  <img
                    src="src\Images\with-background.webp"
                    alt="Image with background"
                    loading="lazy"
                    className="w-full h-full object-cover"
                  />
                </div>

                {/* Slider line */}
                <div
                  className="absolute top-0 bottom-0 w-1 bg-white shadow-lg transition-all duration-150 ease-out"
                  style={{ left: `${sliderPosition}%`, transform: 'translateX(-50%)' }}
                >
                  {/* Slider handle */}
                  <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-8 h-8 bg-white rounded-full shadow-lg flex items-center justify-center cursor-pointer hover:scale-110 transition-transform">
                    <div className="w-3 h-3 bg-blue-500 rounded-full"></div>
                  </div>
                </div>

                {/* Labels */}
                <div className="absolute top-4 left-4">
                  <span className="px-3 py-1 bg-gradient-to-r from-blue-600 to-cyan-500 text-white rounded-full text-sm font-medium">
                    Background Removed
                  </span>
                </div>
                <div className="absolute top-4 right-4">
                  <span className="px-3 py-1 bg-gradient-to-r from-gray-600 to-gray-500 text-white rounded-full text-sm font-medium">
                    With Background
                  </span>
                </div>
            </div>

            {/* Instruction text */}
            <div className="text-center mt-4">
              <p className="text-white/60 text-sm">
                ← Drag the slider to compare →
              </p>
            </div>
          </div>

          {/* Pro Model Coming Soon */}
          <div className="mt-8 text-center">
            <div className="inline-flex items-center gap-2 px-4 py-2 bg-amber-500/20 backdrop-blur-md text-amber-300 rounded-full text-sm font-medium">
              <Sparkles className="w-4 h-4" />
              <span>Pro Model: Even more precise edge detection coming soon</span>
            </div>
          </div>
        </div>

        {/* CTA button */}
        <div className="text-center mt-16">
          <Button
            size="lg"
            onClick={() => navigate("/signup")}
            className="bg-gradient-to-r from-purple-600 to-pink-500 text-white hover:opacity-90 h-12 px-8 rounded-full font-medium"
          >
            Start Creating
          </Button>
        </div>
      </div>
    </section>
  );
};

export default FeaturesSection;