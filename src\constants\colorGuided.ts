// Color Guided Generation Constants
// Centralized configuration for color-guided image generation

export const COLOR_GUIDED_DEFAULTS = {
  // Default color palette
  DEFAULT_COLORS: [
    "#FF5733", // Red-Orange
    "#33FF57", // Green
    "#3357FF"  // Blue
  ],
  
  // Default prompts
  DEFAULT_NEGATIVE_PROMPT: "blurry, low quality, distorted",
  DEFAULT_PROMPT_PLACEHOLDER: "Describe what you want to generate using these colors...",
  DEFAULT_NEGATIVE_PLACEHOLDER: "Elements to avoid in the image...",
  
  // Generation parameters
  DEFAULT_WIDTH: 1024,
  DEFAULT_HEIGHT: 1024,
  DEFAULT_QUALITY: "standard" as const,
  DEFAULT_CFG_SCALE: 7,
  DEFAULT_SEED_RANGE: { min: 1, max: 1000 }
} as const;

export const COLOR_GUIDED_LIMITS = {
  // Color palette limits
  MAX_COLORS: 10,
  MIN_COLORS: 1,
  
  // File size limits
  MAX_FILE_SIZE: 5 * 1024 * 1024, // 5MB
  
  // Image compression settings
  COMPRESSION_THRESHOLD: 1000000, // ~1MB in base64
  MAX_DIMENSION: 1024,
  COMPRESSION_QUALITY: 0.7,
  
  // Prompt limits
  MAX_PROMPT_LENGTH: 1000,
  MAX_NEGATIVE_PROMPT_LENGTH: 500
} as const;

export const COLOR_GUIDED_MESSAGES = {
  // Success messages
  SUCCESS: {
    IMAGE_GENERATED: "Color-guided image generated successfully!",
    IMAGE_DOWNLOADED: "Image downloaded successfully!",
    COLOR_ADDED: "Color added to palette",
    REFERENCE_UPLOADED: "Reference image uploaded successfully"
  },
  
  // Error messages
  ERROR: {
    LOGIN_REQUIRED: "You must be logged in to generate images",
    GENERATION_FAILED: "Failed to generate color-guided image",
    DOWNLOAD_FAILED: "Failed to download image",
    FILE_TOO_LARGE: "File size exceeds 5MB limit",
    INVALID_FILE_TYPE: "Please select an image file",
    FILE_READ_ERROR: "Failed to read the selected file",
    MAX_COLORS_REACHED: "Maximum 10 colors allowed",
    MIN_COLORS_REQUIRED: "At least one color is required",
    PROMPT_REQUIRED: "Please enter a prompt",
    PROMPT_TOO_LONG: "Prompt is too long (max 1000 characters)",
    NEGATIVE_PROMPT_TOO_LONG: "Negative prompt is too long (max 500 characters)",
    UNEXPECTED_ERROR: "An unexpected error occurred"
  },
  
  // Warning messages
  WARNING: {
    SAVE_FAILED: "Color-guided image generated successfully, but failed to save to history",
    HISTORY_SAVE_FAILED: "Image generated successfully, but failed to save to history"
  },
  
  // Info messages
  INFO: {
    COLOR_PALETTE_HELP: "Add up to 10 colors to guide the image generation",
    PROMPT_HELP: "Be specific about the subject, style, and composition",
    NEGATIVE_PROMPT_HELP: "Specify elements you want to exclude from the generated image",
    PROCESSING: "Generating color-guided image...",
    REFERENCE_IMAGE_HELP: "Upload a reference image to extract colors from (optional)"
  }
} as const;

export const COLOR_GUIDED_UI = {
  // Tab labels
  TABS: {
    COLORS_AND_PROMPT: "Colors & Prompt",
    RESULT: "Result"
  },
  
  // Section titles
  SECTIONS: {
    CHOOSE_COLORS: "1. Choose Your Colors",
    DESCRIBE_IMAGE: "2. Describe Your Image",
    NEGATIVE_PROMPT: "3. Negative Prompt (Optional)",
    GENERATE_BUTTON: "Generate Image"
  },
  
  // Button labels
  BUTTONS: {
    ADD_COLOR: "Add Color",
    GENERATE: "Generate Image",
    GENERATING: "Generating...",
    DOWNLOAD: "Download",
    START_OVER: "Start Over",
    RESET: "Reset",
    UPLOAD_IMAGE: "Upload Image"
  },
  
  // Placeholder texts
  PLACEHOLDERS: {
    EMPTY_STATE: "Color-guided image will appear here",
    EMPTY_STATE_SUBTITLE: "Select colors and enter a prompt to generate",
    RESULT_SUCCESS: "Color-guided image generated successfully!"
  }
} as const;

export const COLOR_GUIDED_USE_CASES = [
  {
    icon: "Palette",
    title: "Brand Alignment",
    description: "Ensure generated images match your brand's color scheme."
  },
  {
    icon: "Paintbrush", 
    title: "Artistic Moods",
    description: "Create images with specific vibes (e.g., warm sunset, cool night)."
  },
  {
    icon: "ShoppingBag",
    title: "Product Visualization", 
    description: "Generate product mockups in various color themes."
  }
] as const;

export const COLOR_GUIDED_VALIDATION = {
  // Color validation
  isValidHexColor: (color: string): boolean => {
    return /^#([A-Fa-f0-9]{6}|[A-Fa-f0-9]{3})$/.test(color);
  },
  
  // File validation
  isValidImageFile: (file: File): boolean => {
    return file.type.startsWith("image/");
  },
  
  // Size validation
  isValidFileSize: (file: File): boolean => {
    return file.size <= COLOR_GUIDED_LIMITS.MAX_FILE_SIZE;
  },
  
  // Prompt validation
  isValidPrompt: (prompt: string): boolean => {
    return prompt.trim().length > 0 && prompt.length <= COLOR_GUIDED_LIMITS.MAX_PROMPT_LENGTH;
  },
  
  // Negative prompt validation
  isValidNegativePrompt: (prompt: string): boolean => {
    return prompt.length <= COLOR_GUIDED_LIMITS.MAX_NEGATIVE_PROMPT_LENGTH;
  },
  
  // Color palette validation
  isValidColorCount: (colors: string[]): boolean => {
    return colors.length >= COLOR_GUIDED_LIMITS.MIN_COLORS && 
           colors.length <= COLOR_GUIDED_LIMITS.MAX_COLORS;
  }
} as const;

export const COLOR_GUIDED_GENERATION_OPTIONS = {
  // Default generation options
  getDefaultOptions: () => ({
    width: COLOR_GUIDED_DEFAULTS.DEFAULT_WIDTH,
    height: COLOR_GUIDED_DEFAULTS.DEFAULT_HEIGHT,
    quality: COLOR_GUIDED_DEFAULTS.DEFAULT_QUALITY,
    cfgScale: COLOR_GUIDED_DEFAULTS.DEFAULT_CFG_SCALE,
    seed: Math.floor(Math.random() * COLOR_GUIDED_DEFAULTS.DEFAULT_SEED_RANGE.max) + COLOR_GUIDED_DEFAULTS.DEFAULT_SEED_RANGE.min
  }),
  
  // Image history item template
  createHistoryItem: (userId: string, prompt: string, colors: string[], s3Key: string, s3Url: string) => ({
    user_id: userId,
    prompt: prompt || 'Color-Guided Generation',
    image_type: 'color-guided' as const,
    s3_key: s3Key,
    s3_url: s3Url,
    parameters: {
      platform: 'Advanced Tools',
      style: 'color-guided',
      colors: colors
    }
  }),
  
  // File naming
  generateFileName: (timestamp?: number): string => {
    const time = timestamp || Date.now();
    return `color-guided-${time}.png`;
  }
} as const;

// Type exports for better TypeScript support
export type ColorGuidedDefaults = typeof COLOR_GUIDED_DEFAULTS;
export type ColorGuidedLimits = typeof COLOR_GUIDED_LIMITS;
export type ColorGuidedMessages = typeof COLOR_GUIDED_MESSAGES;
export type ColorGuidedUI = typeof COLOR_GUIDED_UI;
export type ColorGuidedUseCase = typeof COLOR_GUIDED_USE_CASES[number];
export type ColorGuidedValidation = typeof COLOR_GUIDED_VALIDATION;
export type ColorGuidedGenerationOptions = typeof COLOR_GUIDED_GENERATION_OPTIONS;