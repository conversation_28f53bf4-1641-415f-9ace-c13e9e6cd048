{"name": "vibenecto-server", "version": "2.0.0", "description": "Server for VibeNecto AWS Bedrock integration", "main": "server-refactored.js", "scripts": {"start": "node server-refactored.js", "dev": "nodemon --config nodemon.json server-refactored.js"}, "dependencies": {"@aws-sdk/client-bedrock-runtime": "^3.803.0", "@aws-sdk/client-polly": "^3.823.0", "@aws-sdk/client-s3": "^3.803.0", "@aws-sdk/s3-request-presigner": "^3.803.0", "@supabase/supabase-js": "^2.39.0", "cors": "^2.8.5", "dotenv": "^16.5.0", "express": "^4.18.2", "express-rate-limit": "^7.5.0", "helmet": "^8.1.0", "i": "^0.3.7", "joi": "^17.13.3", "jsonwebtoken": "^9.0.2", "winston": "^3.17.0"}, "devDependencies": {"nodemon": "^3.0.1"}}