// Video Generator Constants and Configuration

export const VIDEO_GENERATOR_CONFIG = {
  // Default values
  DEFAULT_PROMPT: 'Generated video',
  DEFAULT_VIDEO_TYPE: 'text-to-video' as const,
  DEFAULT_DURATION: 6,
  
  // Video specifications
  RESOLUTION: '1280×720',
  FRAME_RATE: '24 FPS',
  
  // UI text
  HEADER_TITLE: 'Single Shot Video',
  HEADER_DESCRIPTION: 'Create stunning 6-second videos from text prompts using AI.',
  
  // Tab labels
  GENERATE_TAB: 'Generate Video',
  RESULT_TAB: 'Result',
  
  // Button labels
  PLAY_VIDEO: 'Play Video',
  GENERATE_NEW: 'Generate New',
  DOWNLOAD: 'Download',
  
  // Status messages
  GENERATING_MESSAGE: 'Generating Video...',
  SUCCESS_MESSAGE: 'Video generated successfully!',
  COMPLETION_MESSAGE: 'Video generation completed!',
} as const;

export const USE_CASES = [
  {
    icon: 'Video',
    title: 'Social Media Content',
    description: 'Create engaging short-form videos for Instagram, TikTok, YouTube Shorts, and other social platforms with AI-generated content.',
  },
  {
    icon: 'Video',
    title: 'Marketing Campaigns',
    description: 'Generate promotional videos for products, services, and brand awareness campaigns with professional quality output.',
  },
  {
    icon: 'Video',
    title: 'Creative Projects',
    description: 'Bring your creative ideas to life with AI-powered video generation for artistic content, music videos, and experimental projects.',
  },
  {
    icon: 'Video',
    title: 'Educational Content',
    description: 'Create educational and instructional videos to explain concepts, demonstrate processes, and engage learners effectively.',
  },
  {
    icon: 'Video',
    title: 'Product Demonstrations',
    description: 'Showcase products and services with dynamic video content that highlights features, benefits, and use cases.',
  },
  {
    icon: 'Video',
    title: 'Storytelling & Narratives',
    description: 'Transform written stories, scripts, and narratives into compelling visual content with AI-generated scenes and animations.',
  },
] as const;

export type VideoGeneratorConfig = typeof VIDEO_GENERATOR_CONFIG;
export type UseCase = typeof USE_CASES[number];