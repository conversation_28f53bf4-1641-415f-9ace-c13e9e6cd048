import React, { useState, useRef, useEffect } from "react";
import { Button } from "@/components/ui/button";
import { ZoomIn, ZoomOut, RefreshCw, Download, Copy, Share2 } from "lucide-react";
import { 
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger
} from "@/components/ui/dropdown-menu";
import { base64ToBlob } from "@/utils/base64Utils";

interface ImageViewerProps {
  image: string;
  onDownload?: (format: string) => void;
  onCopy?: () => void;
  onShare?: () => void;
}

const ImageViewer: React.FC<ImageViewerProps> = ({
  image,
  onDownload,
  onCopy,
  onShare
}) => {
  const [scale, setScale] = useState(1);
  const [position, setPosition] = useState({ x: 0, y: 0 });
  const [isDragging, setIsDragging] = useState(false);
  const [dragStart, setDragStart] = useState({ x: 0, y: 0 });
  const containerRef = useRef<HTMLDivElement>(null);

  // Reset position and scale when image changes
  useEffect(() => {
    setScale(1);
    setPosition({ x: 0, y: 0 });
  }, [image]);

  // Handle mouse down for panning
  const handleMouseDown = (e: React.MouseEvent<HTMLDivElement>) => {
    e.preventDefault();
    setIsDragging(true);
    setDragStart({
      x: e.clientX - position.x,
      y: e.clientY - position.y
    });
  };

  // Handle mouse move for panning
  const handleMouseMove = (e: React.MouseEvent<HTMLDivElement>) => {
    if (!isDragging) return;
    
    setPosition({
      x: e.clientX - dragStart.x,
      y: e.clientY - dragStart.y
    });
  };

  // Handle mouse up to stop panning
  const handleMouseUp = () => {
    setIsDragging(false);
  };

  // Handle mouse leave to stop panning
  const handleMouseLeave = () => {
    setIsDragging(false);
  };

  // Handle zoom in
  const handleZoomIn = () => {
    setScale(s => Math.min(s + 0.25, 3));
  };

  // Handle zoom out
  const handleZoomOut = () => {
    setScale(s => Math.max(s - 0.25, 0.5));
  };

  // Handle reset zoom and position
  const handleReset = () => {
    setScale(1);
    setPosition({ x: 0, y: 0 });
  };

  // Handle download with format
  const handleDownloadWithFormat = (format: string) => {
    if (onDownload) {
      onDownload(format);
    } else {
      // Default download implementation
      downloadImage(image, format);
    }
  };

  // Default download implementation
  const downloadImage = (imageUrl: string, format: string = 'png') => {
    const img = new Image();
    img.crossOrigin = 'anonymous';
    img.src = imageUrl;
    
    img.onload = () => {
      const canvas = document.createElement('canvas');
      canvas.width = img.width;
      canvas.height = img.height;
      const ctx = canvas.getContext('2d');
      
      if (ctx) {
        ctx.drawImage(img, 0, 0);
        
        const mimeType = `image/${format}`;
        const quality = format === 'jpeg' ? 0.9 : undefined;
        
        canvas.toBlob((blob) => {
          if (blob) {
            const url = URL.createObjectURL(blob);
            const link = document.createElement('a');
            link.href = url;
            link.download = `marketboost-image-${Date.now()}.${format}`;
            document.body.appendChild(link);
            link.click();
            document.body.removeChild(link);
            URL.revokeObjectURL(url);
          }
        }, mimeType, quality);
      }
    };
  };

  return (
    <div className="image-viewer-container relative w-full h-full flex flex-col items-center justify-center">
      {/* Image controls */}
      <div className="image-controls absolute top-4 left-4 z-10 flex gap-1 bg-white/80 dark:bg-gray-800/80 backdrop-blur-sm rounded-lg p-1 shadow-md">
        <Button 
          variant="ghost" 
          size="icon" 
          onClick={handleZoomIn} 
          className="h-8 w-8"
        >
          <ZoomIn size={16} />
        </Button>
        <Button 
          variant="ghost" 
          size="icon" 
          onClick={handleZoomOut} 
          className="h-8 w-8"
        >
          <ZoomOut size={16} />
        </Button>
        <Button 
          variant="ghost" 
          size="icon" 
          onClick={handleReset} 
          className="h-8 w-8"
        >
          <RefreshCw size={16} />
        </Button>
      </div>

      {/* Download options */}
      <div className="download-controls absolute top-4 right-4 z-10 flex gap-1 bg-white/80 dark:bg-gray-800/80 backdrop-blur-sm rounded-lg p-1 shadow-md">
        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <Button variant="ghost" size="icon" className="h-8 w-8">
              <Download size={16} />
            </Button>
          </DropdownMenuTrigger>
          <DropdownMenuContent align="end">
            <DropdownMenuItem onClick={() => handleDownloadWithFormat('png')}>
              PNG Format
            </DropdownMenuItem>
            <DropdownMenuItem onClick={() => handleDownloadWithFormat('jpeg')}>
              JPEG Format
            </DropdownMenuItem>
            <DropdownMenuItem onClick={() => handleDownloadWithFormat('webp')}>
              WebP Format
            </DropdownMenuItem>
          </DropdownMenuContent>
        </DropdownMenu>
        
        <Button 
          variant="ghost" 
          size="icon" 
          onClick={onCopy} 
          className="h-8 w-8"
        >
          <Copy size={16} />
        </Button>
        
        <Button 
          variant="ghost" 
          size="icon" 
          onClick={onShare} 
          className="h-8 w-8"
        >
          <Share2 size={16} />
        </Button>
      </div>

      {/* Image container with pan and zoom */}
      <div 
        ref={containerRef}
        className="image-container overflow-hidden w-full h-full flex items-center justify-center cursor-grab active:cursor-grabbing"
        onMouseDown={handleMouseDown}
        onMouseMove={handleMouseMove}
        onMouseUp={handleMouseUp}
        onMouseLeave={handleMouseLeave}
      >
        <div
          style={{
            transform: `scale(${scale}) translate(${position.x / scale}px, ${position.y / scale}px)`,
            transition: isDragging ? 'none' : 'transform 0.2s ease-out'
          }}
          className="will-change-transform"
        >
          <img 
            src={image} 
            alt="Generated content" 
            className="max-w-full max-h-full object-contain pointer-events-none"
          />
        </div>
      </div>
    </div>
  );
};

export default ImageViewer;
