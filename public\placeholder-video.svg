<svg xmlns="http://www.w3.org/2000/svg" width="400" height="225" viewBox="0 0 400 225" fill="none">
  <!-- Background -->
  <rect width="400" height="225" fill="#f3f4f6"/>
  
  <!-- Play button circle -->
  <circle cx="200" cy="112.5" r="30" fill="#6b7280" opacity="0.8"/>
  
  <!-- Play triangle -->
  <polygon points="190,97.5 190,127.5 215,112.5" fill="white"/>
  
  <!-- Video placeholder text -->
  <text x="200" y="170" text-anchor="middle" font-family="Arial, sans-serif" font-size="14" fill="#6b7280">
    Video Placeholder
  </text>
  
  <!-- Aspect ratio indicator -->
  <text x="200" y="190" text-anchor="middle" font-family="Arial, sans-serif" font-size="12" fill="#9ca3af">
    16:9 Aspect Ratio
  </text>
</svg>
