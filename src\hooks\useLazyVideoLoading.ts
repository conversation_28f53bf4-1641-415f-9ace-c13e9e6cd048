import { useState, useCallback, useRef } from 'react';

interface VideoLoadingMetrics {
  totalVideos: number;
  loadedVideos: number;
  errorVideos: number;
  videosInView: number;
  averageLoadTime: number;
  cacheHitRate: number;
  progressiveEnhancements: number;
  retryAttempts: number;
  loadStartTime: number;
  loadEndTime?: number;
  isComplete: boolean;
}

interface UseLazyVideoLoadingOptions {
  enableMetrics?: boolean;
  onLoadComplete?: (metrics: VideoLoadingMetrics) => void;
}

interface UseLazyVideoLoadingReturn {
  metrics: VideoLoadingMetrics;
  recordVideoLoad: (loadTime?: number, wasFromCache?: boolean) => void;
  recordVideoError: () => void;
  recordVideoInView: () => void;
  recordVideoOutOfView: () => void;
  recordProgressiveEnhancement: () => void;
  recordRetryAttempt: () => void;
  setTotalVideos: (count: number) => void;
  resetMetrics: () => void;
}

/**
 * Lazy Video Loading Performance Hook - Sprint 18 Phase 2
 * 
 * Tracks performance metrics for video lazy loading and progressive enhancement.
 * Based on the successful image lazy loading metrics from Sprint 17.
 * 
 * Features:
 * - Video loading performance tracking
 * - Cache hit rate monitoring
 * - Progressive enhancement metrics
 * - Retry attempt tracking
 * - Development-only metrics collection
 */
export const useLazyVideoLoading = ({
  enableMetrics = import.meta.env.DEV,
  onLoadComplete,
}: UseLazyVideoLoadingOptions = {}): UseLazyVideoLoadingReturn => {
  const [metrics, setMetrics] = useState<VideoLoadingMetrics>({
    totalVideos: 0,
    loadedVideos: 0,
    errorVideos: 0,
    videosInView: 0,
    averageLoadTime: 0,
    cacheHitRate: 0,
    progressiveEnhancements: 0,
    retryAttempts: 0,
    loadStartTime: Date.now(),
    isComplete: false,
  });

  const loadTimesRef = useRef<number[]>([]);
  const cacheHitsRef = useRef<number>(0);
  const totalLoadsRef = useRef<number>(0);

  const recordVideoLoad = useCallback((loadTime?: number, wasFromCache?: boolean) => {
    if (!enableMetrics) return;

    setMetrics(prev => {
      const newLoadedVideos = prev.loadedVideos + 1;
      
      // Track load time if provided
      if (loadTime !== undefined) {
        loadTimesRef.current.push(loadTime);
      }
      
      // Track cache hits
      totalLoadsRef.current++;
      if (wasFromCache) {
        cacheHitsRef.current++;
      }
      
      // Calculate average load time
      const averageLoadTime = loadTimesRef.current.length > 0
        ? loadTimesRef.current.reduce((sum, time) => sum + time, 0) / loadTimesRef.current.length
        : 0;
      
      // Calculate cache hit rate
      const cacheHitRate = totalLoadsRef.current > 0
        ? (cacheHitsRef.current / totalLoadsRef.current) * 100
        : 0;
      
      const newMetrics = {
        ...prev,
        loadedVideos: newLoadedVideos,
        averageLoadTime,
        cacheHitRate,
      };
      
      // Check if loading is complete
      if (newMetrics.totalVideos > 0 && 
          newLoadedVideos + newMetrics.errorVideos >= newMetrics.totalVideos) {
        newMetrics.isComplete = true;
        newMetrics.loadEndTime = Date.now();
        
        if (onLoadComplete) {
          onLoadComplete(newMetrics);
        }
      }
      
      return newMetrics;
    });
  }, [enableMetrics, onLoadComplete]);

  const recordVideoError = useCallback(() => {
    if (!enableMetrics) return;

    setMetrics(prev => {
      const newErrorVideos = prev.errorVideos + 1;
      const newMetrics = {
        ...prev,
        errorVideos: newErrorVideos,
      };
      
      // Check if loading is complete (including errors)
      if (newMetrics.totalVideos > 0 && 
          newMetrics.loadedVideos + newErrorVideos >= newMetrics.totalVideos) {
        newMetrics.isComplete = true;
        newMetrics.loadEndTime = Date.now();
        
        if (onLoadComplete) {
          onLoadComplete(newMetrics);
        }
      }
      
      return newMetrics;
    });
  }, [enableMetrics, onLoadComplete]);

  const recordVideoInView = useCallback(() => {
    if (!enableMetrics) return;

    setMetrics(prev => ({
      ...prev,
      videosInView: prev.videosInView + 1,
    }));
  }, [enableMetrics]);

  const recordVideoOutOfView = useCallback(() => {
    if (!enableMetrics) return;

    setMetrics(prev => ({
      ...prev,
      videosInView: Math.max(0, prev.videosInView - 1),
    }));
  }, [enableMetrics]);

  const recordProgressiveEnhancement = useCallback(() => {
    if (!enableMetrics) return;

    setMetrics(prev => ({
      ...prev,
      progressiveEnhancements: prev.progressiveEnhancements + 1,
    }));
  }, [enableMetrics]);

  const recordRetryAttempt = useCallback(() => {
    if (!enableMetrics) return;

    setMetrics(prev => ({
      ...prev,
      retryAttempts: prev.retryAttempts + 1,
    }));
  }, [enableMetrics]);

  const setTotalVideos = useCallback((count: number) => {
    if (!enableMetrics) return;

    setMetrics(prev => ({
      ...prev,
      totalVideos: count,
      loadStartTime: Date.now(),
      isComplete: false,
    }));
  }, [enableMetrics]);

  const resetMetrics = useCallback(() => {
    if (!enableMetrics) return;

    loadTimesRef.current = [];
    cacheHitsRef.current = 0;
    totalLoadsRef.current = 0;
    
    setMetrics({
      totalVideos: 0,
      loadedVideos: 0,
      errorVideos: 0,
      videosInView: 0,
      averageLoadTime: 0,
      cacheHitRate: 0,
      progressiveEnhancements: 0,
      retryAttempts: 0,
      loadStartTime: Date.now(),
      isComplete: false,
    });
  }, [enableMetrics]);

  return {
    metrics,
    recordVideoLoad,
    recordVideoError,
    recordVideoInView,
    recordVideoOutOfView,
    recordProgressiveEnhancement,
    recordRetryAttempt,
    setTotalVideos,
    resetMetrics,
  };
};

export default useLazyVideoLoading;
