import React, { useEffect, useState } from 'react';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { Progress } from '@/components/ui/progress';
import { Button } from '@/components/ui/button';
import { <PERSON>rk<PERSON>, AlertCircle, Clock } from 'lucide-react';
import { getUserSubscription, getUserUsageStats, UsageStats, UserSubscription } from '@/services/subscriptionService';
import { useAuth } from '@/contexts/AuthContext';
import { formatDistanceToNow } from 'date-fns';

interface SubscriptionInfoProps {
  onUpgrade?: () => void;
}

const SubscriptionInfo: React.FC<SubscriptionInfoProps> = ({ onUpgrade }) => {
  const { user } = useAuth();
  const [subscription, setSubscription] = useState<UserSubscription | null>(null);
  const [usageStats, setUsageStats] = useState<UsageStats | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchSubscriptionData = async () => {
      if (!user) return;

      try {
        setLoading(true);
        setError(null);

        // Fetch subscription and usage data
        const [subscriptionData, usageData] = await Promise.all([
          getUserSubscription(user.id),
          getUserUsageStats(user.id)
        ]);

        setSubscription(subscriptionData);
        setUsageStats(usageData);
      } catch (err) {
        if (import.meta.env.DEV) {
          console.error('Error fetching subscription data:', err);
        }
        setError('Failed to load subscription information. Please try again later.');
      } finally {
        setLoading(false);
      }
    };

    fetchSubscriptionData();
  }, [user]);

  // Format the reset date
  const formatResetDate = (dateString: string) => {
    try {
      const date = new Date(dateString);
      return formatDistanceToNow(date, { addSuffix: true });
    } catch (err) {
      return 'Unknown';
    }
  };

  // Calculate progress percentage
  const calculateProgress = (used: number, limit: number | null) => {
    if (limit === null) return 0; // Unlimited
    if (limit === 0) return 100; // No usage allowed
    return Math.min(Math.round((used / limit) * 100), 100);
  };

  if (loading) {
    return (
      <Card className="w-full">
        <CardContent className="pt-6">
          <div className="flex items-center justify-center h-32">
            <p className="text-muted-foreground">Loading subscription information...</p>
          </div>
        </CardContent>
      </Card>
    );
  }

  if (error) {
    return (
      <Card className="w-full border-destructive">
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <AlertCircle className="h-5 w-5 text-destructive" />
            <span>Error</span>
          </CardTitle>
        </CardHeader>
        <CardContent>
          <p>{error}</p>
        </CardContent>
        <CardFooter>
          <Button variant="outline" onClick={() => window.location.reload()}>
            Retry
          </Button>
        </CardFooter>
      </Card>
    );
  }

  const isPremium = subscription?.plan_name === 'Premium';
  const standardLimit = usageStats?.standard_images.limit;
  const standardUsed = usageStats?.standard_images.used || 0;
  const premiumLimit = usageStats?.premium_images.limit || 0;
  const premiumUsed = usageStats?.premium_images.used || 0;

  return (
    <Card className="w-full">
      <CardHeader>
        <div className="flex items-center justify-between">
          <div>
            <CardTitle className="flex items-center gap-2">
              {isPremium && <Sparkles className="h-5 w-5 text-yellow-500" />}
              {subscription?.plan_name || 'Free'} Plan
            </CardTitle>
            <CardDescription>
              {isPremium
                ? 'Full access to all features and premium images'
                : 'Basic access with limited features'}
            </CardDescription>
          </div>
          {!isPremium && (
            <Button onClick={onUpgrade} className="bg-gradient-to-r from-purple-500 to-blue-500 text-white">
              <Sparkles className="mr-2 h-4 w-4" />
              Upgrade
            </Button>
          )}
        </div>
      </CardHeader>
      <CardContent className="space-y-4">
        <div className="space-y-2">
          <div className="flex items-center justify-between">
            <span className="text-sm font-medium">Standard Images</span>
            <span className="text-sm text-muted-foreground">
              {standardUsed} / {standardLimit === null ? '∞' : standardLimit}
            </span>
          </div>
          <Progress value={calculateProgress(standardUsed, standardLimit)} className="h-2" />
          <div className="flex items-center text-xs text-muted-foreground">
            <Clock className="mr-1 h-3 w-3" />
            Resets {formatResetDate(usageStats?.standard_images.reset_at || '')}
          </div>
        </div>

        {isPremium && (
          <div className="space-y-2">
            <div className="flex items-center justify-between">
              <span className="text-sm font-medium">Premium Images</span>
              <span className="text-sm text-muted-foreground">
                {premiumUsed} / {premiumLimit}
              </span>
            </div>
            <Progress value={calculateProgress(premiumUsed, premiumLimit)} className="h-2" />
            <div className="flex items-center text-xs text-muted-foreground">
              <Clock className="mr-1 h-3 w-3" />
              Resets {formatResetDate(usageStats?.premium_images.reset_at || '')}
            </div>
          </div>
        )}

        <div className="pt-2">
          <h4 className="text-sm font-medium mb-2">Plan Features:</h4>
          <ul className="text-sm space-y-1 text-muted-foreground">
            <li className="flex items-start">
              <span className="mr-2">•</span>
              {standardLimit === null ? 'Unlimited' : standardLimit} standard images
              {standardLimit !== null && ' per week'}
            </li>
            <li className="flex items-start">
              <span className="mr-2">•</span>
              {isPremium ? `${premiumLimit} premium images per month` : 'No premium images'}
            </li>
            <li className="flex items-start">
              <span className="mr-2">•</span>
              {isPremium ? 'Access to all advanced image tools' : 'Basic image generation only'}
            </li>
          </ul>
        </div>
      </CardContent>
    </Card>
  );
};

export default SubscriptionInfo;
