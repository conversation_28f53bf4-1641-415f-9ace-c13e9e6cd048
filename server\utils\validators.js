/**
 * Common validation utilities for VibeNecto backend
 * Consolidates duplicate validation logic across routes
 */

const { logger } = require('./logger');

/**
 * Validate required fields in request body
 */
function validateRequiredFields(body, requiredFields, options = {}) {
  const { requestId, userId } = options;
  const missing = [];
  
  for (const field of requiredFields) {
    if (!body[field]) {
      missing.push(field);
    }
  }
  
  if (missing.length > 0) {
    logger.warn('Missing required fields', {
      missing,
      requestId,
      userId
    });
    
    return {
      isValid: false,
      error: `Missing required fields: ${missing.join(', ')}`
    };
  }
  
  return { isValid: true };
}

/**
 * Validate user authentication
 */
function validateUserAuth(userId, options = {}) {
  const { requestId, operation = 'operation' } = options;
  
  if (!userId) {
    logger.warn(`${operation} attempted without authentication`, {
      requestId
    });
    
    return {
      isValid: false,
      error: 'Authentication required. User ID is missing.',
      statusCode: 401
    };
  }
  
  return { isValid: true };
}

/**
 * Validate image data (base64)
 */
function validateImageData(imageData, options = {}) {
  const { requestId, userId, field = 'image' } = options;
  
  if (!imageData) {
    return {
      isValid: false,
      error: `${field} is required`
    };
  }
  
  // Check if it's a valid base64 string
  if (typeof imageData !== 'string') {
    logger.warn('Invalid image data type', {
      type: typeof imageData,
      field,
      requestId,
      userId
    });
    
    return {
      isValid: false,
      error: `${field} must be a base64 string`
    };
  }
  
  // Basic base64 validation
  const base64Regex = /^[A-Za-z0-9+/]*={0,2}$/;
  const cleanData = imageData.replace(/^data:image\/[a-z]+;base64,/, '');
  
  if (!base64Regex.test(cleanData)) {
    logger.warn('Invalid base64 image data', {
      field,
      requestId,
      userId
    });
    
    return {
      isValid: false,
      error: `${field} contains invalid base64 data`
    };
  }
  
  return { isValid: true, cleanData };
}

/**
 * Validate text content for voice generation
 */
function validateTextContent(text, options = {}) {
  const { 
    requestId, 
    userId, 
    maxLength = 200000,
    minLength = 1 
  } = options;
  
  if (!text || typeof text !== 'string') {
    return {
      isValid: false,
      error: 'Text content is required and must be a string'
    };
  }
  
  if (text.length < minLength) {
    return {
      isValid: false,
      error: `Text must be at least ${minLength} character(s) long`
    };
  }
  
  if (text.length > maxLength) {
    logger.warn('Text content exceeds maximum length', {
      length: text.length,
      maxLength,
      requestId,
      userId
    });
    
    return {
      isValid: false,
      error: `Text content exceeds maximum length of ${maxLength} characters`
    };
  }
  
  return { isValid: true };
}

/**
 * Validate color array for color-guided generation
 */
function validateColors(colors, options = {}) {
  const { 
    requestId, 
    userId, 
    maxColors = 10,
    minColors = 1 
  } = options;
  
  if (!colors || !Array.isArray(colors)) {
    return {
      isValid: false,
      error: 'Colors must be provided as an array'
    };
  }
  
  if (colors.length < minColors) {
    return {
      isValid: false,
      error: `At least ${minColors} color(s) required`
    };
  }
  
  if (colors.length > maxColors) {
    logger.warn('Too many colors provided', {
      count: colors.length,
      maxColors,
      requestId,
      userId
    });
    
    return {
      isValid: false,
      error: `Maximum ${maxColors} colors allowed`
    };
  }
  
  // Validate color format (hex colors)
  const hexColorRegex = /^#[0-9A-Fa-f]{6}$/;
  const invalidColors = colors.filter(color => !hexColorRegex.test(color));
  
  if (invalidColors.length > 0) {
    return {
      isValid: false,
      error: `Invalid color format. Colors must be hex format (e.g., #FF5733): ${invalidColors.join(', ')}`
    };
  }
  
  return { isValid: true };
}

/**
 * Validate prompt text
 */
function validatePrompt(prompt, options = {}) {
  const { 
    requestId, 
    userId, 
    maxLength = 4000,
    minLength = 1 
  } = options;
  
  if (!prompt || typeof prompt !== 'string') {
    return {
      isValid: false,
      error: 'Prompt is required and must be a string'
    };
  }
  
  if (prompt.trim().length < minLength) {
    return {
      isValid: false,
      error: `Prompt must be at least ${minLength} character(s) long`
    };
  }
  
  if (prompt.length > maxLength) {
    logger.warn('Prompt exceeds maximum length', {
      length: prompt.length,
      maxLength,
      requestId,
      userId
    });
    
    return {
      isValid: false,
      error: `Prompt exceeds maximum length of ${maxLength} characters`
    };
  }
  
  return { isValid: true };
}

/**
 * Validate numeric range
 */
function validateNumericRange(value, field, min, max, options = {}) {
  const { requestId, userId } = options;
  
  if (value === undefined || value === null) {
    return { isValid: true }; // Optional field
  }
  
  const numValue = parseFloat(value);
  
  if (isNaN(numValue)) {
    return {
      isValid: false,
      error: `${field} must be a valid number`
    };
  }
  
  if (numValue < min || numValue > max) {
    logger.warn(`${field} out of range`, {
      value: numValue,
      min,
      max,
      requestId,
      userId
    });
    
    return {
      isValid: false,
      error: `${field} must be between ${min} and ${max}`
    };
  }
  
  return { isValid: true, value: numValue };
}

/**
 * Validate file size (for base64 data)
 */
function validateFileSize(base64Data, maxSizeBytes, options = {}) {
  const { requestId, userId, field = 'file' } = options;
  
  if (!base64Data) {
    return { isValid: true }; // Optional field
  }
  
  // Estimate file size from base64 (base64 is ~33% larger than original)
  const estimatedSize = (base64Data.length * 3) / 4;
  
  if (estimatedSize > maxSizeBytes) {
    logger.warn(`${field} size exceeds limit`, {
      estimatedSize,
      maxSizeBytes,
      requestId,
      userId
    });
    
    return {
      isValid: false,
      error: `${field} size exceeds maximum limit of ${Math.round(maxSizeBytes / 1024 / 1024)}MB`
    };
  }
  
  return { isValid: true };
}

/**
 * Validate usage limits (for voice generation)
 */
function validateUsageLimit(currentUsage, requestedUsage, limit, options = {}) {
  const { requestId, userId, type = 'usage' } = options;
  
  if (currentUsage + requestedUsage > limit) {
    logger.warn(`${type} limit exceeded`, {
      currentUsage,
      requestedUsage,
      limit,
      requestId,
      userId
    });
    
    return {
      isValid: false,
      error: `Monthly ${type} limit exceeded. Used: ${currentUsage}/${limit}.`,
      statusCode: 429,
      usage: {
        used: currentUsage,
        limit: limit,
        remaining: Math.max(0, limit - currentUsage)
      }
    };
  }
  
  return { isValid: true };
}

module.exports = {
  validateRequiredFields,
  validateUserAuth,
  validateImageData,
  validateTextContent,
  validateColors,
  validatePrompt,
  validateNumericRange,
  validateFileSize,
  validateUsageLimit
};