/**
 * Video Cache Invalidation Service - Sprint 18 Phase 1
 * 
 * Smart cache invalidation strategies for video presigned URLs.
 * Handles different scenarios like video deletion, user changes, and cache maintenance.
 * 
 * Based on the image cache invalidation service but adapted for video-specific scenarios.
 */

import { videoPresignedUrlCache } from '@/services/videoPresignedUrlCache';
import { invalidateVideoUrlCache, invalidateVideoUrlCacheForUser } from '@/services/videoPresignedUrlService';

export type VideoCacheInvalidationReason = 
  | 'video_deleted'
  | 'video_updated'
  | 'user_logout'
  | 'user_changed'
  | 'manual_clear'
  | 'cache_full'
  | 'expired_cleanup'
  | 'error_recovery';

export interface VideoCacheInvalidationResult {
  invalidatedCount: number;
  reason: VideoCacheInvalidationReason;
  affectedKeys: string[];
  timestamp: number;
  success: boolean;
  error?: string;
}

class VideoCacheInvalidationService {
  private invalidationHistory: VideoCacheInvalidationResult[] = [];
  private maxHistorySize = 100;

  /**
   * Invalidate cache when a video is deleted
   */
  async onVideoDeleted(videoId: string, userId: string, s3_key?: string): Promise<VideoCacheInvalidationResult> {
    const result: VideoCacheInvalidationResult = {
      invalidatedCount: 0,
      reason: 'video_deleted',
      affectedKeys: [],
      timestamp: Date.now(),
      success: false,
    };

    try {
      // If we have the s3_key, invalidate it directly
      if (s3_key) {
        const deleted = videoPresignedUrlCache.delete(s3_key);
        if (deleted) {
          result.invalidatedCount = 1;
          result.affectedKeys = [s3_key];
        }
      } else {
        // If no s3_key, invalidate by pattern (less efficient but necessary)
        const pattern = new RegExp(`users/${userId}/videos/.*${videoId}`);
        result.invalidatedCount = videoPresignedUrlCache.invalidateByPattern(pattern);
        result.affectedKeys = [`pattern:users/${userId}/videos/.*${videoId}`];
      }

      result.success = true;

      if (import.meta.env.DEV) {
        console.log(`[VideoCacheInvalidation] Video deleted: invalidated ${result.invalidatedCount} cache entries for video ${videoId}`);
      }
    } catch (error) {
      result.error = error instanceof Error ? error.message : 'Unknown error';
      if (import.meta.env.DEV) {
        console.error('[VideoCacheInvalidation] Error invalidating cache for deleted video:', error);
      }
    }

    this.addToHistory(result);
    return result;
  }

  /**
   * Invalidate cache when a video is updated (e.g., status change, metadata update)
   */
  async onVideoUpdated(videoId: string, userId: string, s3_key?: string): Promise<VideoCacheInvalidationResult> {
    const result: VideoCacheInvalidationResult = {
      invalidatedCount: 0,
      reason: 'video_updated',
      affectedKeys: [],
      timestamp: Date.now(),
      success: false,
    };

    try {
      if (s3_key) {
        const deleted = videoPresignedUrlCache.delete(s3_key);
        if (deleted) {
          result.invalidatedCount = 1;
          result.affectedKeys = [s3_key];
        }
      } else {
        // Invalidate by pattern if no specific s3_key
        const pattern = new RegExp(`users/${userId}/videos/.*${videoId}`);
        result.invalidatedCount = videoPresignedUrlCache.invalidateByPattern(pattern);
        result.affectedKeys = [`pattern:users/${userId}/videos/.*${videoId}`];
      }

      result.success = true;

      if (import.meta.env.DEV) {
        console.log(`[VideoCacheInvalidation] Video updated: invalidated ${result.invalidatedCount} cache entries for video ${videoId}`);
      }
    } catch (error) {
      result.error = error instanceof Error ? error.message : 'Unknown error';
      if (import.meta.env.DEV) {
        console.error('[VideoCacheInvalidation] Error invalidating cache for updated video:', error);
      }
    }

    this.addToHistory(result);
    return result;
  }

  /**
   * Invalidate cache when user logs out
   */
  async onUserLogout(userId: string): Promise<VideoCacheInvalidationResult> {
    const result: VideoCacheInvalidationResult = {
      invalidatedCount: 0,
      reason: 'user_logout',
      affectedKeys: [],
      timestamp: Date.now(),
      success: false,
    };

    try {
      result.invalidatedCount = invalidateVideoUrlCacheForUser(userId);
      result.affectedKeys = [`user:${userId}`];
      result.success = true;

      if (import.meta.env.DEV) {
        console.log(`[VideoCacheInvalidation] User logout: invalidated ${result.invalidatedCount} cache entries for user ${userId}`);
      }
    } catch (error) {
      result.error = error instanceof Error ? error.message : 'Unknown error';
      if (import.meta.env.DEV) {
        console.error('[VideoCacheInvalidation] Error invalidating cache for user logout:', error);
      }
    }

    this.addToHistory(result);
    return result;
  }

  /**
   * Invalidate cache when user changes (e.g., switching accounts)
   */
  async onUserChanged(oldUserId: string, newUserId: string): Promise<VideoCacheInvalidationResult> {
    const result: VideoCacheInvalidationResult = {
      invalidatedCount: 0,
      reason: 'user_changed',
      affectedKeys: [],
      timestamp: Date.now(),
      success: false,
    };

    try {
      // Invalidate old user's cache
      const oldUserInvalidated = invalidateVideoUrlCacheForUser(oldUserId);
      
      // Optionally invalidate new user's cache to ensure fresh data
      const newUserInvalidated = invalidateVideoUrlCacheForUser(newUserId);
      
      result.invalidatedCount = oldUserInvalidated + newUserInvalidated;
      result.affectedKeys = [`user:${oldUserId}`, `user:${newUserId}`];
      result.success = true;

      if (import.meta.env.DEV) {
        console.log(`[VideoCacheInvalidation] User changed: invalidated ${result.invalidatedCount} cache entries (old: ${oldUserInvalidated}, new: ${newUserInvalidated})`);
      }
    } catch (error) {
      result.error = error instanceof Error ? error.message : 'Unknown error';
      if (import.meta.env.DEV) {
        console.error('[VideoCacheInvalidation] Error invalidating cache for user change:', error);
      }
    }

    this.addToHistory(result);
    return result;
  }

  /**
   * Manual cache clear (for debugging or maintenance)
   */
  async clearAllCache(): Promise<VideoCacheInvalidationResult> {
    const result: VideoCacheInvalidationResult = {
      invalidatedCount: 0,
      reason: 'manual_clear',
      affectedKeys: ['*'],
      timestamp: Date.now(),
      success: false,
    };

    try {
      const metrics = videoPresignedUrlCache.getMetrics();
      result.invalidatedCount = metrics.cacheSize;
      
      videoPresignedUrlCache.clear();
      result.success = true;

      if (import.meta.env.DEV) {
        console.log(`[VideoCacheInvalidation] Manual clear: invalidated ${result.invalidatedCount} cache entries`);
      }
    } catch (error) {
      result.error = error instanceof Error ? error.message : 'Unknown error';
      if (import.meta.env.DEV) {
        console.error('[VideoCacheInvalidation] Error clearing cache manually:', error);
      }
    }

    this.addToHistory(result);
    return result;
  }

  /**
   * Invalidate cache by video type
   */
  async invalidateByVideoType(videoType: 'text-to-video' | 'multi-shot-auto' | 'multi-shot-manual'): Promise<VideoCacheInvalidationResult> {
    const result: VideoCacheInvalidationResult = {
      invalidatedCount: 0,
      reason: 'manual_clear',
      affectedKeys: [],
      timestamp: Date.now(),
      success: false,
    };

    try {
      // Get all cached entries and filter by video type
      const cache = videoPresignedUrlCache as any; // Access private cache for filtering
      let invalidated = 0;
      
      if (cache.cache) {
        for (const [key, cached] of cache.cache) {
          if (cached.videoType === videoType) {
            videoPresignedUrlCache.delete(key);
            invalidated++;
            result.affectedKeys.push(key);
          }
        }
      }

      result.invalidatedCount = invalidated;
      result.success = true;

      if (import.meta.env.DEV) {
        console.log(`[VideoCacheInvalidation] Video type invalidation: invalidated ${result.invalidatedCount} cache entries for type ${videoType}`);
      }
    } catch (error) {
      result.error = error instanceof Error ? error.message : 'Unknown error';
      if (import.meta.env.DEV) {
        console.error('[VideoCacheInvalidation] Error invalidating cache by video type:', error);
      }
    }

    this.addToHistory(result);
    return result;
  }

  /**
   * Get invalidation history
   */
  getInvalidationHistory(): VideoCacheInvalidationResult[] {
    return [...this.invalidationHistory];
  }

  /**
   * Get invalidation statistics
   */
  getInvalidationStats() {
    const total = this.invalidationHistory.length;
    const successful = this.invalidationHistory.filter(r => r.success).length;
    const totalInvalidated = this.invalidationHistory.reduce((sum, r) => sum + r.invalidatedCount, 0);
    
    const reasonCounts = this.invalidationHistory.reduce((counts, r) => {
      counts[r.reason] = (counts[r.reason] || 0) + 1;
      return counts;
    }, {} as Record<VideoCacheInvalidationReason, number>);

    return {
      totalInvalidations: total,
      successfulInvalidations: successful,
      successRate: total > 0 ? (successful / total) * 100 : 0,
      totalEntriesInvalidated: totalInvalidated,
      averageEntriesPerInvalidation: total > 0 ? totalInvalidated / total : 0,
      reasonBreakdown: reasonCounts,
      lastInvalidation: this.invalidationHistory[this.invalidationHistory.length - 1]?.timestamp,
    };
  }

  /**
   * Add invalidation result to history
   */
  private addToHistory(result: VideoCacheInvalidationResult): void {
    this.invalidationHistory.push(result);
    
    // Keep history size manageable
    if (this.invalidationHistory.length > this.maxHistorySize) {
      this.invalidationHistory = this.invalidationHistory.slice(-this.maxHistorySize);
    }
  }

  /**
   * Clear invalidation history
   */
  clearHistory(): void {
    this.invalidationHistory = [];
  }
}

// Global video cache invalidation service instance
export const videoCacheInvalidationService = new VideoCacheInvalidationService();

// Convenience functions for common invalidation scenarios
export const invalidateVideoCache = {
  onVideoDeleted: (videoId: string, userId: string, s3_key?: string) => 
    videoCacheInvalidationService.onVideoDeleted(videoId, userId, s3_key),
  
  onVideoUpdated: (videoId: string, userId: string, s3_key?: string) => 
    videoCacheInvalidationService.onVideoUpdated(videoId, userId, s3_key),
  
  onUserLogout: (userId: string) => 
    videoCacheInvalidationService.onUserLogout(userId),
  
  onUserChanged: (oldUserId: string, newUserId: string) => 
    videoCacheInvalidationService.onUserChanged(oldUserId, newUserId),
  
  clearAll: () => 
    videoCacheInvalidationService.clearAllCache(),
  
  byVideoType: (videoType: 'text-to-video' | 'multi-shot-auto' | 'multi-shot-manual') => 
    videoCacheInvalidationService.invalidateByVideoType(videoType),
};

export default videoCacheInvalidationService;
