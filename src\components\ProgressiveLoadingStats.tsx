import React, { useState, useEffect } from 'react';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { 
  TrendingUp, 
  Clock, 
  CheckCircle, 
  AlertCircle, 
  RefreshCw,
  Zap,
  Eye,
  EyeOff
} from 'lucide-react';
import { cn } from '@/lib/utils';
import { presignedUrlCache } from '@/services/presignedUrlCache';

interface ProgressiveLoadingStatsProps {
  className?: string;
  showDetailed?: boolean;
}

interface LoadingMetrics {
  totalImages: number;
  immediatelyAvailable: number;
  enhancedInBackground: number;
  failedToEnhance: number;
  cacheHitRate: number;
  averageEnhancementTime: number;
  totalEnhancementTime: number;
}

const ProgressiveLoadingStats: React.FC<ProgressiveLoadingStatsProps> = ({
  className,
  showDetailed = false,
}) => {
  const [metrics, setMetrics] = useState<LoadingMetrics>({
    totalImages: 0,
    immediatelyAvailable: 0,
    enhancedInBackground: 0,
    failedToEnhance: 0,
    cacheHitRate: 0,
    averageEnhancementTime: 0,
    totalEnhancementTime: 0,
  });
  const [isVisible, setIsVisible] = useState(showDetailed);
  const [lastUpdate, setLastUpdate] = useState<Date>(new Date());

  const updateMetrics = () => {
    const cacheMetrics = presignedUrlCache.getMetrics();
    
    // Calculate progressive loading metrics
    const totalRequests = cacheMetrics.totalRequests || 1;
    const cacheHitRate = totalRequests > 0 ? (cacheMetrics.hits / totalRequests) * 100 : 0;
    
    setMetrics({
      totalImages: totalRequests,
      immediatelyAvailable: cacheMetrics.hits,
      enhancedInBackground: cacheMetrics.misses,
      failedToEnhance: 0, // This would need to be tracked separately
      cacheHitRate,
      averageEnhancementTime: 0, // This would need to be tracked separately
      totalEnhancementTime: 0, // This would need to be tracked separately
    });
    
    setLastUpdate(new Date());
  };

  useEffect(() => {
    updateMetrics();
    
    // Update metrics every 5 seconds when visible
    if (isVisible) {
      const interval = setInterval(updateMetrics, 5000);
      return () => clearInterval(interval);
    }
  }, [isVisible]);

  const performanceScore = Math.round(
    (metrics.immediatelyAvailable / Math.max(metrics.totalImages, 1)) * 100
  );

  const getPerformanceColor = (score: number) => {
    if (score >= 80) return 'text-green-600 dark:text-green-400';
    if (score >= 60) return 'text-yellow-600 dark:text-yellow-400';
    return 'text-red-600 dark:text-red-400';
  };

  const getPerformanceBadgeVariant = (score: number) => {
    if (score >= 80) return 'default';
    if (score >= 60) return 'secondary';
    return 'destructive';
  };

  if (!import.meta.env.DEV && !showDetailed) {
    return null; // Only show in development or when explicitly requested
  }

  return (
    <Card className={cn("w-full", className)}>
      <CardHeader className="pb-3">
        <div className="flex items-center justify-between">
          <CardTitle className="text-sm font-medium flex items-center gap-2">
            <Zap className="h-4 w-4 text-brand-purple" />
            Progressive Loading Performance
          </CardTitle>
          <div className="flex items-center gap-2">
            <Badge variant={getPerformanceBadgeVariant(performanceScore)}>
              {performanceScore}% Instant
            </Badge>
            <Button
              variant="ghost"
              size="sm"
              onClick={() => setIsVisible(!isVisible)}
              className="h-6 w-6 p-0"
            >
              {isVisible ? <EyeOff className="h-3 w-3" /> : <Eye className="h-3 w-3" />}
            </Button>
          </div>
        </div>
      </CardHeader>
      
      {isVisible && (
        <CardContent className="pt-0">
          <div className="grid grid-cols-2 md:grid-cols-4 gap-3 mb-4">
            {/* Total Images */}
            <div className="text-center">
              <div className="text-lg font-semibold">{metrics.totalImages}</div>
              <div className="text-xs text-muted-foreground">Total Images</div>
            </div>
            
            {/* Immediately Available */}
            <div className="text-center">
              <div className="text-lg font-semibold text-green-600 dark:text-green-400">
                {metrics.immediatelyAvailable}
              </div>
              <div className="text-xs text-muted-foreground">Instant Load</div>
            </div>
            
            {/* Enhanced in Background */}
            <div className="text-center">
              <div className="text-lg font-semibold text-blue-600 dark:text-blue-400">
                {metrics.enhancedInBackground}
              </div>
              <div className="text-xs text-muted-foreground">Enhanced</div>
            </div>
            
            {/* Cache Hit Rate */}
            <div className="text-center">
              <div className={cn("text-lg font-semibold", getPerformanceColor(metrics.cacheHitRate))}>
                {metrics.cacheHitRate.toFixed(1)}%
              </div>
              <div className="text-xs text-muted-foreground">Cache Hit Rate</div>
            </div>
          </div>

          {/* Performance Insights */}
          <div className="space-y-2">
            <div className="flex items-center gap-2 text-xs">
              <CheckCircle className="h-3 w-3 text-green-500" />
              <span className="text-muted-foreground">
                {metrics.immediatelyAvailable} images loaded instantly from cache
              </span>
            </div>
            
            {metrics.enhancedInBackground > 0 && (
              <div className="flex items-center gap-2 text-xs">
                <RefreshCw className="h-3 w-3 text-blue-500" />
                <span className="text-muted-foreground">
                  {metrics.enhancedInBackground} images enhanced in background
                </span>
              </div>
            )}
            
            {performanceScore >= 80 && (
              <div className="flex items-center gap-2 text-xs">
                <TrendingUp className="h-3 w-3 text-green-500" />
                <span className="text-green-600 dark:text-green-400">
                  Excellent performance! Most images load instantly.
                </span>
              </div>
            )}
            
            {performanceScore < 60 && (
              <div className="flex items-center gap-2 text-xs">
                <AlertCircle className="h-3 w-3 text-yellow-500" />
                <span className="text-yellow-600 dark:text-yellow-400">
                  Consider clearing cache or checking network connection.
                </span>
              </div>
            )}
          </div>

          {/* Last Update */}
          <div className="mt-3 pt-3 border-t border-border">
            <div className="flex items-center justify-between text-xs text-muted-foreground">
              <span>Last updated: {lastUpdate.toLocaleTimeString()}</span>
              <Button
                variant="ghost"
                size="sm"
                onClick={updateMetrics}
                className="h-5 px-2 text-xs"
              >
                <RefreshCw className="h-3 w-3 mr-1" />
                Refresh
              </Button>
            </div>
          </div>
        </CardContent>
      )}
    </Card>
  );
};

export default ProgressiveLoadingStats;
