import { useState, useEffect, useCallback, useRef } from 'react';
import { getPresignedUrl } from '@/services/s3Service';
import { presignedUrlCache } from '@/services/presignedUrlCache';

interface UseProgressiveImageUrlOptions {
  s3_key: string;
  fallbackUrl: string;
  enableRefresh?: boolean;
  refreshTimeout?: number;
  maxRetries?: number;
  retryDelay?: number;
}

interface UseProgressiveImageUrlReturn {
  url: string;
  isLoading: boolean;
  hasError: boolean;
  isUsingFallback: boolean;
  refreshUrl: () => Promise<void>;
  retryCount: number;
}

export const useProgressiveImageUrl = (
  options: UseProgressiveImageUrlOptions
): UseProgressiveImageUrlReturn => {
  const {
    s3_key,
    fallbackUrl,
    enableRefresh = true,
    refreshTimeout = 5000,
    maxRetries = 2,
    retryDelay = 1000,
  } = options;

  // Phase 4: Start with cached URL if available, otherwise fallback
  const [url, setUrl] = useState<string>(() => {
    if (!s3_key) return fallbackUrl;

    // Check cache immediately for instant display
    const cached = presignedUrlCache.get(s3_key);
    if (cached && cached.url) {
      if (import.meta.env.DEV) {
        console.log(`[ProgressiveImageUrl] Starting with cached URL for ${s3_key}`);
      }
      return cached.url;
    }

    if (import.meta.env.DEV) {
      console.log(`[ProgressiveImageUrl] Starting with fallback URL for ${s3_key}`);
    }
    return fallbackUrl;
  });

  const [isLoading, setIsLoading] = useState<boolean>(false);
  const [hasError, setHasError] = useState<boolean>(false);
  const [isUsingFallback, setIsUsingFallback] = useState<boolean>(() => {
    // If we started with a cached URL, we're not using fallback
    if (!s3_key) return true;
    const cached = presignedUrlCache.get(s3_key);
    return !cached || !cached.url;
  });
  const [retryCount, setRetryCount] = useState<number>(0);

  const abortControllerRef = useRef<AbortController | null>(null);
  const timeoutRef = useRef<NodeJS.Timeout | null>(null);

  const refreshUrl = useCallback(async (): Promise<void> => {
    if (!s3_key || !enableRefresh) return;

    // Phase 4: Check cache first, but don't skip refresh if we're using fallback
    const cached = presignedUrlCache.get(s3_key);
    if (cached && !isUsingFallback) {
      // We already have a fresh cached URL and we're not using fallback
      setUrl(cached.url);
      setIsUsingFallback(false);
      setHasError(false);
      return;
    }

    // Cancel any ongoing request
    if (abortControllerRef.current) {
      abortControllerRef.current.abort();
    }

    // Clear any existing timeout
    if (timeoutRef.current) {
      clearTimeout(timeoutRef.current);
    }

    // Phase 4: Only show loading state if we don't have any URL to display
    // This prevents loading spinners when we already have a fallback/cached URL showing
    const shouldShowLoading = !url || url === fallbackUrl;
    if (shouldShowLoading) {
      setIsLoading(true);
    }
    setHasError(false);

    const controller = new AbortController();
    abortControllerRef.current = controller;

    let currentRetry = 0;

    const attemptRefresh = async (): Promise<void> => {
      try {
        // Create timeout promise
        const timeoutPromise = new Promise<never>((_, reject) => {
          timeoutRef.current = setTimeout(() => {
            reject(new Error('Request timeout'));
          }, refreshTimeout);
        });

        // Create abort promise
        const abortPromise = new Promise<never>((_, reject) => {
          controller.signal.addEventListener('abort', () => {
            reject(new Error('Request aborted'));
          });
        });

        // Race between URL fetch, timeout, and abort
        const urlPromise = getPresignedUrl({ key: s3_key });
        const result = await Promise.race([urlPromise, timeoutPromise, abortPromise]);

        // Clear timeout if request completed
        if (timeoutRef.current) {
          clearTimeout(timeoutRef.current);
          timeoutRef.current = null;
        }

        if (result.success && result.url) {
          // Cache the successful result
          presignedUrlCache.set(s3_key, result.url, 45 * 60 * 1000, {
            retryCount: currentRetry,
            loadTime: Date.now() - Date.now(), // Approximate load time
          });

          // Phase 4: Only update URL if it's different from what we're showing
          // This prevents unnecessary re-renders when we already have a good URL
          if (result.url !== url) {
            setUrl(result.url);
          }
          setIsUsingFallback(false);
          setHasError(false);
          setRetryCount(currentRetry);

          if (import.meta.env.DEV) {
            console.log(`[ProgressiveImageUrl] Successfully refreshed URL for ${s3_key} (attempt ${currentRetry + 1})`);
          }
        } else {
          throw new Error(result.error || 'Failed to get presigned URL');
        }
      } catch (error) {
        if (error instanceof Error && error.message === 'Request aborted') {
          // Request was cancelled, don't retry
          return;
        }

        currentRetry++;
        setRetryCount(currentRetry);

        if (currentRetry <= maxRetries) {
          if (import.meta.env.DEV) {
            console.warn(`[ProgressiveImageUrl] Retry ${currentRetry}/${maxRetries} for ${s3_key}:`, error);
          }

          // Exponential backoff
          const delay = retryDelay * Math.pow(2, currentRetry - 1);
          await new Promise(resolve => setTimeout(resolve, delay));
          
          // Check if request was aborted during delay
          if (!controller.signal.aborted) {
            await attemptRefresh();
          }
        } else {
          // All retries failed, keep using fallback
          setHasError(true);
          setIsUsingFallback(true);
          
          if (import.meta.env.DEV) {
            console.error(`[ProgressiveImageUrl] Failed to refresh URL for ${s3_key} after ${maxRetries} retries:`, error);
          }
        }
      } finally {
        setIsLoading(false);
      }
    };

    await attemptRefresh();
  }, [s3_key, enableRefresh, refreshTimeout, maxRetries, retryDelay]);

  // Phase 4: Auto-refresh in background when s3_key changes
  useEffect(() => {
    if (s3_key && enableRefresh) {
      // Check if we need to refresh
      const cached = presignedUrlCache.get(s3_key);
      const needsRefresh = !cached || isUsingFallback;

      if (needsRefresh) {
        // Delay the refresh slightly to allow immediate display of cached/fallback
        const timeoutId = setTimeout(() => {
          refreshUrl();
        }, 50); // 50ms delay for immediate visual feedback

        return () => clearTimeout(timeoutId);
      }
    }

    // Cleanup on unmount or s3_key change
    return () => {
      if (abortControllerRef.current) {
        abortControllerRef.current.abort();
      }
      if (timeoutRef.current) {
        clearTimeout(timeoutRef.current);
      }
    };
  }, [s3_key, enableRefresh, isUsingFallback, refreshUrl]);

  // Phase 4: Update state when fallback URL changes, but preserve cached URLs
  useEffect(() => {
    if (!s3_key) {
      setUrl(fallbackUrl);
      setIsUsingFallback(true);
      setHasError(false);
      setRetryCount(0);
      return;
    }

    // Check if we have a cached URL for this s3_key
    const cached = presignedUrlCache.get(s3_key);
    if (cached && cached.url) {
      setUrl(cached.url);
      setIsUsingFallback(false);
      setHasError(false);
      setRetryCount(0);
    } else {
      setUrl(fallbackUrl);
      setIsUsingFallback(true);
      setHasError(false);
      setRetryCount(0);
    }
  }, [s3_key, fallbackUrl]);

  return {
    url,
    isLoading,
    hasError,
    isUsingFallback,
    refreshUrl,
    retryCount,
  };
};
