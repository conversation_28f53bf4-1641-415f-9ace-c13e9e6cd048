import { useState } from 'react';
import { toast } from 'sonner';

export const useImageActions = () => {
  const [isFullscreen, setIsFullscreen] = useState(false);

  const handleDownload = (generatedImage: string | null) => {
    if (!generatedImage) return;
    
    try {
      const link = document.createElement('a');
      link.href = generatedImage;
      link.download = `vibenecto-image-${Date.now()}.png`;
      link.style.display = 'none';
      
      try {
        document.body.appendChild(link);
        link.click();
      } finally {
        if (document.body.contains(link)) {
          document.body.removeChild(link);
        }
      }
      
      toast.success("Image downloaded successfully!");
    } catch (error) {
      toast.error("Failed to download image");
      if (import.meta.env.DEV) {
        console.error("Download failed", error);
      }
    }
  };

  const handleCopyImage = (generatedImage: string | null) => {
    if (!generatedImage) return;
    
    const img = new Image();
    img.crossOrigin = "anonymous";
    
    img.onload = async () => {
      try {
        const canvas = document.createElement("canvas");
        canvas.width = img.width;
        canvas.height = img.height;
        const ctx = canvas.getContext("2d");
        
        if (!ctx) {
          throw new Error("Failed to get canvas context");
        }
        
        ctx.drawImage(img, 0, 0);
        
        canvas.toBlob(async (blob) => {
          if (blob) {
            try {
              const item = new ClipboardItem({ "image/png": blob });
              await navigator.clipboard.write([item]);
              toast.success("Image copied to clipboard!");
            } catch (clipboardError) {
              toast.error("Failed to copy image to clipboard");
              if (import.meta.env.DEV) {
                console.error("Clipboard write failed:", clipboardError);
              }
            }
          } else {
            toast.error("Failed to create image blob");
          }
        }, "image/png");
      } catch (error) {
        toast.error("Failed to process image for copying");
        if (import.meta.env.DEV) {
          console.error("Canvas processing failed:", error);
        }
      }
    };
    
    img.onerror = () => {
      toast.error("Failed to load image for copying");
      if (import.meta.env.DEV) {
        console.error("Image load failed for copy operation");
      }
    };
    
    img.src = generatedImage;
  };

  const toggleFullscreen = () => {
    setIsFullscreen(prev => !prev);
  };

  return {
    isFullscreen,
    handleDownload,
    handleCopyImage,
    toggleFullscreen,
  };
};