const { logger } = require('../utils/logger');

// Error types for better categorization
const ErrorTypes = {
  VALIDATION_ERROR: 'VALIDATION_ERROR',
  AUTHENTICATION_ERROR: 'AUTHENTICATION_ERROR',
  AUTHOR<PERSON>ZATION_ERROR: 'AUTHORIZATION_ERROR',
  NOT_FOUND_ERROR: 'NOT_FOUND_ERROR',
  RATE_LIMIT_ERROR: 'RATE_LIMIT_ERROR',
  AWS_ERROR: 'AWS_ERROR',
  DATABASE_ERROR: 'DATABASE_ERROR',
  INTERNAL_ERROR: 'INTERNAL_ERROR'
};

// Sanitize request body to remove sensitive information
function sanitizeRequestBody(body) {
  if (!body || typeof body !== 'object') {
    return JSON.stringify(body).substring(0, 200);
  }
  
  const sanitized = { ...body };
  const sensitiveFields = ['password', 'token', 'key', 'secret', 'auth', 'credential'];
  
  for (const field of sensitiveFields) {
    if (sanitized[field]) {
      sanitized[field] = '[REDACTED]';
    }
  }
  
  // Also check for nested objects
  for (const key in sanitized) {
    if (typeof sanitized[key] === 'object' && sanitized[key] !== null) {
      for (const nestedKey of sensitiveFields) {
        if (sanitized[key][nestedKey]) {
          sanitized[key][nestedKey] = '[REDACTED]';
        }
      }
    }
  }
  
  return JSON.stringify(sanitized).substring(0, 500);
}

// Custom error class for better error handling
class AppError extends Error {
  constructor(message, statusCode, type = ErrorTypes.INTERNAL_ERROR, isOperational = true) {
    super(message);
    this.statusCode = statusCode;
    this.type = type;
    this.isOperational = isOperational;
    this.timestamp = new Date().toISOString();
    
    Error.captureStackTrace(this, this.constructor);
  }
}

// Error handler middleware
const errorHandler = (err, req, res, next) => {
  // Create a proper error object without shallow copying
  let error = new Error(err.message);
  error.statusCode = err.statusCode;
  error.type = err.type;
  error.isOperational = err.isOperational;
  error.timestamp = err.timestamp;
  error.stack = err.stack;

  // Log error details
  const errorContext = {
    url: req.url,
    method: req.method,
    ip: req.ip,
    userAgent: req.get('User-Agent'),
    userId: req.user?.id || 'anonymous',
    body: req.body ? sanitizeRequestBody(req.body) : null,
    params: req.params,
    query: req.query
  };

  // Handle specific error types
  if (err.name === 'ValidationError') {
    error = new AppError('Validation Error', 400, ErrorTypes.VALIDATION_ERROR);
  } else if (err.name === 'CastError') {
    error = new AppError('Invalid ID format', 400, ErrorTypes.VALIDATION_ERROR);
  } else if (err.code === 11000) {
    error = new AppError('Duplicate field value', 400, ErrorTypes.VALIDATION_ERROR);
  } else if (err.name === 'JsonWebTokenError') {
    error = new AppError('Invalid token', 401, ErrorTypes.AUTHENTICATION_ERROR);
  } else if (err.name === 'TokenExpiredError') {
    error = new AppError('Token expired', 401, ErrorTypes.AUTHENTICATION_ERROR);
  } else if (err.code === 'ECONNREFUSED') {
    error = new AppError('Database connection failed', 503, ErrorTypes.DATABASE_ERROR);
  } else if (err.code && err.code.startsWith('AWS')) {
    error = new AppError('AWS service error', 503, ErrorTypes.AWS_ERROR);
  }

  // Set default values if not already set
  if (!error.statusCode) {
    error.statusCode = 500;
  }
  if (!error.type) {
    error.type = ErrorTypes.INTERNAL_ERROR;
  }

  // Log error based on severity
  if (error.statusCode >= 500) {
    logger.error('Server Error', {
      error: {
        message: error.message,
        stack: error.stack,
        type: error.type,
        statusCode: error.statusCode
      },
      request: errorContext
    });
  } else if (error.statusCode >= 400) {
    logger.warn('Client Error', {
      error: {
        message: error.message,
        type: error.type,
        statusCode: error.statusCode
      },
      request: errorContext
    });
  }

  // Prepare error response
  const errorResponse = {
    success: false,
    error: {
      message: error.message,
      type: error.type,
      timestamp: error.timestamp || new Date().toISOString()
    }
  };

  // Add stack trace in development
  if (process.env.NODE_ENV === 'development') {
    errorResponse.error.stack = error.stack;
    errorResponse.error.context = errorContext;
  }

  // Add request ID for tracking
  if (req.requestId) {
    errorResponse.error.requestId = req.requestId;
  }

  res.status(error.statusCode).json(errorResponse);
};

// 404 handler for unmatched routes
const notFoundHandler = (req, res, next) => {
  const error = new AppError(
    `Route ${req.originalUrl} not found`,
    404,
    ErrorTypes.NOT_FOUND_ERROR
  );
  next(error);
};

// Async error wrapper to catch async errors
const asyncHandler = (fn) => (req, res, next) => {
  Promise.resolve(fn(req, res, next)).catch(next);
};

// Request ID middleware for tracking
const requestIdMiddleware = (req, res, next) => {
  req.requestId = `req_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  res.setHeader('X-Request-ID', req.requestId);
  next();
};

// Request logging middleware
const requestLogger = (req, res, next) => {
  const startTime = Date.now();
  
  // Log request start
  logger.info('Request Started', {
    requestId: req.requestId,
    method: req.method,
    url: req.url,
    ip: req.ip,
    userAgent: req.get('User-Agent'),
    userId: req.user?.id || 'anonymous'
  });

  // Override res.end to log response
  const originalEnd = res.end;
  res.end = function(...args) {
    const responseTime = Date.now() - startTime;
    
    logger.info('Request Completed', {
      requestId: req.requestId,
      method: req.method,
      url: req.url,
      statusCode: res.statusCode,
      responseTime: `${responseTime}ms`,
      userId: req.user?.id || 'anonymous'
    });

    originalEnd.apply(this, args);
  };

  next();
};

module.exports = {
  AppError,
  ErrorTypes,
  errorHandler,
  notFoundHandler,
  asyncHandler,
  requestIdMiddleware,
  requestLogger
};
