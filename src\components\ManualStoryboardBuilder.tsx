import React, { useState, useRef } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Badge } from "@/components/ui/badge";
import { <PERSON>alog, DialogContent, DialogHeader, <PERSON>alogTitle, DialogTrigger } from "@/components/ui/dialog";
import { toast } from "sonner";
import { VideoShot } from "@/services/videoService";
import {
  Clock,
  Layers,
  Plus,
  X,
  Film,
  Upload,
  Info,
  Check,
  Play,
} from "lucide-react";
import {
  SHOT_LIMITS,
  PROMPT_LIMITS,
  FILE_CONSTRAINTS,
  ERROR_MESSAGES,
  SUCCESS_MESSAGES,
  UI_TEXT,
  calculateEstimatedTimeForShots,
  generateRandomSeed,
  validateImageFile,
  getImageFormat,
} from "@/constants/multiShotVideo";

interface ManualStoryboardBuilderProps {
  onGenerate: (shots: VideoShot[]) => void;
  isGenerating: boolean;
}

const ManualStoryboardBuilder: React.FC<ManualStoryboardBuilderProps> = ({ 
  onGenerate, 
  isGenerating 
}) => {
  const [isDialogOpen, setIsDialogOpen] = useState(false);
  const [globalSeed, setGlobalSeed] = useState(generateRandomSeed());
  const [shots, setShots] = useState<VideoShot[]>([]);
  const [selectedShotIndex, setSelectedShotIndex] = useState(0);
  const fileInputRefs = useRef<{ [key: number]: HTMLInputElement | null }>({});

  // Initialize shots with global seed
  React.useEffect(() => {
    if (shots.length === 0) {
      setShots([{ text: '', seed: globalSeed }]);
    }
  }, [globalSeed, shots.length]);

  // If generating, show progress UI instead of the form
  if (isGenerating) {
    return (
      <div className="bg-green-50 dark:bg-green-950/20 p-8 rounded-lg border-2 border-green-200 dark:border-green-800">
        <div className="text-center space-y-4">
          <div className="flex justify-center">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-green-600" />
          </div>
          <div>
            <h3 className="text-lg font-semibold text-green-900 dark:text-green-100 mb-2">
              {UI_TEXT.GENERATING_STORYBOARD}
            </h3>
            <p className="text-sm text-green-700 dark:text-green-300 mb-4">
              {UI_TEXT.GENERATING_STORYBOARD_DESCRIPTION
                .replace('{count}', shots.length.toString())
                .replace('{plural}', shots.length !== 1 ? 's' : '')}
            </p>
            <div className="flex items-center justify-center gap-2 text-sm">
              <Clock className="h-4 w-4 text-green-600" />
              <span className="font-medium">{UI_TEXT.ESTIMATED_TIME}</span>
              <Badge variant="secondary" className="bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200">
                {calculateEstimatedTimeForShots(shots.length).min}s - {calculateEstimatedTimeForShots(shots.length).max}s
              </Badge>
            </div>
          </div>
        </div>
      </div>
    );
  }

  // Update all shots when global seed changes
  const updateGlobalSeed = (newSeed: number) => {
    setGlobalSeed(newSeed);
    setShots(prev => prev.map(shot => ({ ...shot, seed: newSeed })));
  };

  const generateRandomGlobalSeed = () => {
    const newSeed = generateRandomSeed();
    updateGlobalSeed(newSeed);
  };

  const handleGenerate = () => {
    const validShots = shots.filter(shot => shot.text.trim().length > 0);

    if (validShots.length === 0) {
      toast.error(ERROR_MESSAGES.NO_SHOTS_WITH_CONTENT);
      return;
    }

    // Apply global seed to all shots for visual consistency, but preserve individual seeds if set
    const shotsWithGlobalSeed = validShots.map(shot => ({
      ...shot,
      seed: shot.seed || globalSeed // Use individual seed if set, otherwise use global seed
    }));

    onGenerate(shotsWithGlobalSeed);
    setIsDialogOpen(false); // Close dialog after generation starts
  };

  const addShot = () => {
    if (shots.length >= SHOT_LIMITS.MAX_SHOTS) {
      toast.error(ERROR_MESSAGES.MAX_SHOTS_EXCEEDED);
      return;
    }

    const newShot: VideoShot = {
      text: '',
      seed: globalSeed // Use global seed for consistency
    };
    setShots(prev => [...prev, newShot]);
    setSelectedShotIndex(shots.length); // Select the new shot
  };

  const removeShot = (index: number) => {
    if (shots.length <= SHOT_LIMITS.MIN_SHOTS) {
      toast.error(ERROR_MESSAGES.MIN_SHOTS_REQUIRED);
      return;
    }
    setShots(prev => prev.filter((_, i) => i !== index));
    if (selectedShotIndex >= shots.length - 1) {
      setSelectedShotIndex(Math.max(0, shots.length - 2));
    }
  };

  const updateShot = (index: number, updates: Partial<VideoShot>) => {
    setShots(prev => prev.map((shot, i) =>
      i === index ? { ...shot, ...updates } : shot
    ));
  };

  const handleImageUpload = async (index: number, event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (!file) return;

    const validation = validateImageFile(file);
    if (!validation.valid) {
      toast.error(validation.error);
      return;
    }

    try {
      const reader = new FileReader();
      reader.onload = (e) => {
        const base64 = e.target?.result as string;
        const format = getImageFormat(file);
        updateShot(index, {
          referenceImageKey: base64,
          referenceImageFormat: format
        });
      };
      reader.readAsDataURL(file);
      toast.success(SUCCESS_MESSAGES.IMAGE_UPLOADED);
    } catch (error) {
      if (import.meta.env.DEV) {
        console.error("Error uploading image:", error);
      }
      toast.error(ERROR_MESSAGES.IMAGE_UPLOAD_FAILED);
    }
  };

  const removeReferenceImage = (index: number) => {
    updateShot(index, {
      referenceImageKey: undefined,
      referenceImageFormat: undefined
    });
    if (fileInputRefs.current[index]) {
      fileInputRefs.current[index]!.value = "";
    }
  };

  const estimatedTime = calculateEstimatedTimeForShots(shots.length);

  return (
    <div className="text-center py-8">
      <div className="bg-gray-100 dark:bg-gray-700 rounded-lg p-8 mb-4">
        <Layers size={48} className="mx-auto mb-4 text-gray-400" />
        <h4 className="text-lg font-medium mb-2">Manual Storyboard Builder</h4>
        <p className="text-gray-600 dark:text-gray-400 mb-4">
          Create custom storyboards with precise control over each shot, timing, and visual style for professional video production.
        </p>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm text-gray-600 dark:text-gray-400 mb-6">
          <div className="flex items-center gap-2">
            <Check size={16} className="text-green-600" />
            <span>Shot-by-shot control</span>
          </div>
          <div className="flex items-center gap-2">
            <Check size={16} className="text-green-600" />
            <span>Custom timing per shot</span>
          </div>
          <div className="flex items-center gap-2">
            <Check size={16} className="text-green-600" />
            <span>Reference images support</span>
          </div>
          <div className="flex items-center gap-2">
            <Check size={16} className="text-green-600" />
            <span>Drag & drop reordering</span>
          </div>
        </div>
      </div>

      <Dialog open={isDialogOpen} onOpenChange={setIsDialogOpen}>
        <DialogTrigger asChild>
          <Button className="bg-green-600 hover:bg-green-700">
            <Layers size={16} className="mr-2" />
            {UI_TEXT.BUILD_STORYBOARD}
          </Button>
        </DialogTrigger>
        <DialogContent className="max-w-6xl max-h-[90vh] overflow-y-auto">
          <DialogHeader>
            <DialogTitle className="flex items-center gap-2">
              <Layers size={20} className="text-green-600" />
              Custom Storyboard Builder
              <Badge variant="secondary">{shots.length}/{SHOT_LIMITS.MAX_SHOTS} shots</Badge>
            </DialogTitle>
          </DialogHeader>

          <div className="space-y-6 mt-4">
            {/* Global Seed Control */}
            <div className="bg-blue-50 dark:bg-blue-950/20 p-4 rounded-lg">
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-2">
                  <Info className="h-4 w-4 text-blue-600" />
                  <div>
                    <p className="font-medium text-blue-900 dark:text-blue-100 text-sm">
                      {UI_TEXT.GLOBAL_SEED_TITLE}
                    </p>
                    <p className="text-blue-700 dark:text-blue-300 text-xs">
                      {UI_TEXT.GLOBAL_SEED_HELP}
                    </p>
                  </div>
                </div>
                <div className="flex gap-2">
                  <Input
                    type="number"
                    placeholder="Global seed"
                    value={globalSeed}
                    onChange={(e) => updateGlobalSeed(parseInt(e.target.value) || 0)}
                    className="w-32"
                  />
                  <Button
                    type="button"
                    variant="outline"
                    onClick={generateRandomGlobalSeed}
                    className="px-3"
                  >
                    {UI_TEXT.RANDOM}
                  </Button>
                </div>
              </div>
            </div>

            {/* Horizontal Shot Timeline */}
            <div className="bg-gray-50 dark:bg-gray-800 rounded-lg p-4">
              <div className="flex items-center gap-2 overflow-x-auto pb-2">
                {shots.map((shot, index) => (
                  <div
                    key={index}
                    className={`flex-shrink-0 w-32 h-24 rounded-lg border-2 cursor-pointer transition-all ${
                      selectedShotIndex === index
                        ? 'border-green-500 bg-green-50 dark:bg-green-900/20'
                        : 'border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-700 hover:border-gray-400'
                    }`}
                    onClick={() => setSelectedShotIndex(index)}
                  >
                    <div className="w-full h-full p-2 flex flex-col">
                      <div className="flex items-center justify-between mb-1">
                        <span className="text-xs font-medium">Shot {index + 1}</span>
                        {shots.length > 1 && (
                          <Button
                            variant="ghost"
                            size="icon"
                            className="h-4 w-4 text-red-500 hover:text-red-700"
                            onClick={(e) => {
                              e.stopPropagation();
                              removeShot(index);
                            }}
                          >
                            <X className="h-3 w-3" />
                          </Button>
                        )}
                      </div>
                      <div className="flex-1 bg-gray-100 dark:bg-gray-600 rounded flex items-center justify-center">
                        {shot.referenceImageKey ? (
                          <img
                            src={shot.referenceImageKey}
                            alt={`Shot ${index + 1}`}
                            className="w-full h-full object-cover rounded"
                          />
                        ) : (
                          <Film size={16} className="text-gray-400" />
                        )}
                      </div>
                    </div>
                  </div>
                ))}

                {/* Add Shot Button */}
                <Button
                  variant="outline"
                  className="flex-shrink-0 w-32 h-24 border-2 border-dashed border-gray-300 dark:border-gray-600 hover:border-green-500 hover:bg-green-50 dark:hover:bg-green-900/20"
                  onClick={addShot}
                  disabled={shots.length >= SHOT_LIMITS.MAX_SHOTS}
                >
                  <Plus size={20} className="text-gray-400" />
                </Button>
              </div>
            </div>

            {/* Selected Shot Editor */}
            {shots[selectedShotIndex] && (
              <div className="bg-white dark:bg-gray-800 rounded-lg p-6 shadow-sm">
                <h5 className="text-md font-medium mb-4">Edit Shot {selectedShotIndex + 1}</h5>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  {/* Left Column - Shot Details */}
                  <div className="space-y-4">
                    {/* Shot Description */}
                    <div>
                      <label className="block text-sm font-medium mb-2">
                        {UI_TEXT.SHOT_DESCRIPTION_LABEL}
                      </label>
                      <Textarea
                        placeholder={UI_TEXT.SHOT_DESCRIPTION_PLACEHOLDER}
                        value={shots[selectedShotIndex].text}
                        onChange={(e) => updateShot(selectedShotIndex, { text: e.target.value })}
                        className="min-h-[100px] resize-none"
                        maxLength={PROMPT_LIMITS.SHOT_MAX}
                      />
                      <div className="flex justify-between text-xs text-gray-500 mt-1">
                        <span>{UI_TEXT.SHOT_DESCRIPTION_HELP}</span>
                        <span className={shots[selectedShotIndex].text.length > PROMPT_LIMITS.WARNING_THRESHOLD_SHOT ? "text-red-500" : ""}>
                          {shots[selectedShotIndex].text.length}/{PROMPT_LIMITS.SHOT_MAX}
                        </span>
                      </div>
                    </div>
                  </div>

                  {/* Right Column - Reference Image */}
                  <div>
                    <label className="block text-sm font-medium mb-2">
                      {UI_TEXT.REFERENCE_IMAGE_LABEL}
                    </label>
                    {!shots[selectedShotIndex].referenceImageKey ? (
                      <div
                        className="border-2 border-dashed border-gray-300 rounded-lg p-8 text-center cursor-pointer hover:border-gray-400 transition-colors h-48 flex flex-col items-center justify-center"
                        onClick={() => fileInputRefs.current[selectedShotIndex]?.click()}
                      >
                        <Upload className="h-8 w-8 mx-auto mb-2 text-gray-400" />
                        <p className="text-sm text-gray-600">{UI_TEXT.REFERENCE_IMAGE_UPLOAD}</p>
                        <p className="text-xs text-gray-500 mt-1">{UI_TEXT.REFERENCE_IMAGE_HELP}</p>
                      </div>
                    ) : (
                      <div className="relative h-48">
                        <img
                          src={shots[selectedShotIndex].referenceImageKey}
                          alt={`Shot ${selectedShotIndex + 1} reference`}
                          className="w-full h-full object-cover rounded-lg"
                        />
                        <Button
                          type="button"
                          variant="destructive"
                          size="icon"
                          className="absolute top-2 right-2 h-6 w-6"
                          onClick={() => removeReferenceImage(selectedShotIndex)}
                        >
                          <X className="h-3 w-3" />
                        </Button>
                      </div>
                    )}
                    <input
                      ref={(el) => fileInputRefs.current[selectedShotIndex] = el}
                      type="file"
                      accept="image/*"
                      onChange={(e) => handleImageUpload(selectedShotIndex, e)}
                      className="hidden"
                    />
                  </div>
                </div>
              </div>
            )}

            {/* Generate Button */}
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-2 text-sm">
                <Clock className="h-4 w-4 text-green-600" />
                <span className="font-medium">{UI_TEXT.ESTIMATED_TIME}</span>
                <Badge variant="secondary">
                  {estimatedTime.min}s - {estimatedTime.max}s
                </Badge>
              </div>

              <Button
                onClick={handleGenerate}
                disabled={isGenerating || shots.every(shot => shot.text.trim().length === 0)}
                className="bg-green-600 hover:bg-green-700"
              >
                <Play className="h-4 w-4 mr-2" />
                {UI_TEXT.GENERATE_STORYBOARD}
              </Button>
            </div>
          </div>
        </DialogContent>
      </Dialog>
    </div>
  );
};

export default ManualStoryboardBuilder;