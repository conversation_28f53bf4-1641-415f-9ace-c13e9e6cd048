-- Video Generation Database Schema for VibeNecto
-- This file contains the SQL commands to create video-related tables in Supabase

-- Create video_history table for tracking video generation jobs and metadata
CREATE TABLE IF NOT EXISTS video_history (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE NOT NULL,
  job_id TEXT UNIQUE NOT NULL,
  prompt TEXT NOT NULL,
  video_type TEXT NOT NULL CHECK (video_type IN ('text-to-video', 'multi-shot-auto', 'multi-shot-manual')),
  status TEXT NOT NULL DEFAULT 'pending' CHECK (status IN ('pending', 'processing', 'completed', 'failed')),
  s3_key TEXT,
  s3_url TEXT,
  duration_seconds INTEGER,
  parameters JSONB DEFAULT '{}',
  reference_images JSONB DEFAULT '[]',
  error_message TEXT,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  completed_at TIMESTAMP WITH TIME ZONE,
  estimated_completion_at TIMESTAMP WITH TIME ZONE
);

-- Create video_shots table for multi-shot video composition
CREATE TABLE IF NOT EXISTS video_shots (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  video_id UUID REFERENCES video_history(id) ON DELETE CASCADE NOT NULL,
  shot_number INTEGER NOT NULL,
  prompt TEXT NOT NULL,
  reference_image_s3_key TEXT,
  reference_image_format TEXT DEFAULT 'png',
  s3_key TEXT,
  parameters JSONB DEFAULT '{}',
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  UNIQUE(video_id, shot_number)
);

-- Create indexes for better query performance
CREATE INDEX IF NOT EXISTS idx_video_history_user_id ON video_history(user_id);
CREATE INDEX IF NOT EXISTS idx_video_history_status ON video_history(status);
CREATE INDEX IF NOT EXISTS idx_video_history_created_at ON video_history(created_at DESC);
CREATE INDEX IF NOT EXISTS idx_video_history_job_id ON video_history(job_id);
CREATE INDEX IF NOT EXISTS idx_video_shots_video_id ON video_shots(video_id);
CREATE INDEX IF NOT EXISTS idx_video_shots_shot_number ON video_shots(video_id, shot_number);

-- Enable Row Level Security (RLS) for video_history table
ALTER TABLE video_history ENABLE ROW LEVEL SECURITY;

-- Create RLS policies for video_history - users can only access their own videos
-- PRODUCTION SAFE: Policies will fail gracefully if they already exist
DO $$
BEGIN
  -- Create SELECT policy
  BEGIN
    EXECUTE 'CREATE POLICY "Users can view own video history" ON video_history
      FOR SELECT USING (auth.uid() = user_id)';
    RAISE NOTICE 'Created SELECT policy for video_history';
  EXCEPTION
    WHEN duplicate_object THEN
      RAISE NOTICE 'SELECT policy for video_history already exists';
  END;

  -- Create INSERT policy
  BEGIN
    EXECUTE 'CREATE POLICY "Users can insert own video history" ON video_history
      FOR INSERT WITH CHECK (auth.uid() = user_id)';
    RAISE NOTICE 'Created INSERT policy for video_history';
  EXCEPTION
    WHEN duplicate_object THEN
      RAISE NOTICE 'INSERT policy for video_history already exists';
  END;

  -- Create UPDATE policy
  BEGIN
    EXECUTE 'CREATE POLICY "Users can update own video history" ON video_history
      FOR UPDATE USING (auth.uid() = user_id)';
    RAISE NOTICE 'Created UPDATE policy for video_history';
  EXCEPTION
    WHEN duplicate_object THEN
      RAISE NOTICE 'UPDATE policy for video_history already exists';
  END;

  -- Create DELETE policy
  BEGIN
    EXECUTE 'CREATE POLICY "Users can delete own video history" ON video_history
      FOR DELETE USING (auth.uid() = user_id)';
    RAISE NOTICE 'Created DELETE policy for video_history';
  EXCEPTION
    WHEN duplicate_object THEN
      RAISE NOTICE 'DELETE policy for video_history already exists';
  END;
END $$;

-- Enable Row Level Security (RLS) for video_shots table
ALTER TABLE video_shots ENABLE ROW LEVEL SECURITY;

-- Create RLS policies for video_shots - users can only access shots for their own videos
-- PRODUCTION SAFE: Policies will fail gracefully if they already exist
DO $$
BEGIN
  -- Create SELECT policy for video_shots
  BEGIN
    EXECUTE 'CREATE POLICY "Users can view own video shots" ON video_shots
      FOR SELECT USING (
        EXISTS (
          SELECT 1 FROM video_history
          WHERE video_history.id = video_shots.video_id
          AND video_history.user_id = auth.uid()
        )
      )';
    RAISE NOTICE 'Created SELECT policy for video_shots';
  EXCEPTION
    WHEN duplicate_object THEN
      RAISE NOTICE 'SELECT policy for video_shots already exists';
  END;

  -- Create INSERT policy for video_shots
  BEGIN
    EXECUTE 'CREATE POLICY "Users can insert own video shots" ON video_shots
      FOR INSERT WITH CHECK (
        EXISTS (
          SELECT 1 FROM video_history
          WHERE video_history.id = video_shots.video_id
          AND video_history.user_id = auth.uid()
        )
      )';
    RAISE NOTICE 'Created INSERT policy for video_shots';
  EXCEPTION
    WHEN duplicate_object THEN
      RAISE NOTICE 'INSERT policy for video_shots already exists';
  END;

  -- Create UPDATE policy for video_shots
  BEGIN
    EXECUTE 'CREATE POLICY "Users can update own video shots" ON video_shots
      FOR UPDATE USING (
        EXISTS (
          SELECT 1 FROM video_history
          WHERE video_history.id = video_shots.video_id
          AND video_history.user_id = auth.uid()
        )
      )';
    RAISE NOTICE 'Created UPDATE policy for video_shots';
  EXCEPTION
    WHEN duplicate_object THEN
      RAISE NOTICE 'UPDATE policy for video_shots already exists';
  END;

  -- Create DELETE policy for video_shots
  BEGIN
    EXECUTE 'CREATE POLICY "Users can delete own video shots" ON video_shots
      FOR DELETE USING (
        EXISTS (
          SELECT 1 FROM video_history
          WHERE video_history.id = video_shots.video_id
          AND video_history.user_id = auth.uid()
        )
      )';
    RAISE NOTICE 'Created DELETE policy for video_shots';
  EXCEPTION
    WHEN duplicate_object THEN
      RAISE NOTICE 'DELETE policy for video_shots already exists';
  END;
END $$;

-- Update usage_tracking table to include video generation tracking
-- PRODUCTION SAFE: Add video-related columns to existing usage_tracking table
DO $$
BEGIN
  -- Add video generation tracking columns if they don't exist
  IF NOT EXISTS (SELECT 1 FROM information_schema.columns
                 WHERE table_name = 'usage_tracking' AND column_name = 'video_type') THEN
    ALTER TABLE usage_tracking ADD COLUMN video_type TEXT;
    RAISE NOTICE 'Added video_type column to usage_tracking table';
  ELSE
    RAISE NOTICE 'video_type column already exists in usage_tracking table';
  END IF;

  IF NOT EXISTS (SELECT 1 FROM information_schema.columns
                 WHERE table_name = 'usage_tracking' AND column_name = 'video_duration_seconds') THEN
    ALTER TABLE usage_tracking ADD COLUMN video_duration_seconds INTEGER;
    RAISE NOTICE 'Added video_duration_seconds column to usage_tracking table';
  ELSE
    RAISE NOTICE 'video_duration_seconds column already exists in usage_tracking table';
  END IF;

  IF NOT EXISTS (SELECT 1 FROM information_schema.columns
                 WHERE table_name = 'usage_tracking' AND column_name = 'processing_time_seconds') THEN
    ALTER TABLE usage_tracking ADD COLUMN processing_time_seconds INTEGER;
    RAISE NOTICE 'Added processing_time_seconds column to usage_tracking table';
  ELSE
    RAISE NOTICE 'processing_time_seconds column already exists in usage_tracking table';
  END IF;

  -- Safely update the check constraint to include video types
  -- First, check if the old constraint exists and remove it
  IF EXISTS (SELECT 1 FROM information_schema.table_constraints
             WHERE constraint_name = 'usage_tracking_image_type_check'
             AND table_name = 'usage_tracking') THEN
    ALTER TABLE usage_tracking DROP CONSTRAINT usage_tracking_image_type_check;
    RAISE NOTICE 'Dropped old usage_tracking_image_type_check constraint';
  END IF;

  -- Add new constraint only if it doesn't already exist
  IF NOT EXISTS (SELECT 1 FROM information_schema.table_constraints
                 WHERE constraint_name = 'usage_tracking_content_type_check'
                 AND table_name = 'usage_tracking') THEN
    ALTER TABLE usage_tracking ADD CONSTRAINT usage_tracking_content_type_check
      CHECK (
        image_type IN ('standard', 'premium', 'background-removal', 'color-guided', 'variation', 'conditioning') OR
        video_type IN ('text-to-video', 'multi-shot-auto', 'multi-shot-manual')
      );
    RAISE NOTICE 'Added new usage_tracking_content_type_check constraint';
  ELSE
    RAISE NOTICE 'usage_tracking_content_type_check constraint already exists';
  END IF;

EXCEPTION
  WHEN OTHERS THEN
    RAISE NOTICE 'Error updating usage_tracking table: %', SQLERRM;
    RAISE NOTICE 'This is not critical - video functionality will work without these changes';
END $$;

-- Create a function to automatically update video status based on job completion
CREATE OR REPLACE FUNCTION update_video_completion_status()
RETURNS TRIGGER AS $$
BEGIN
  -- If status is being updated to 'completed' and completed_at is not set
  IF NEW.status = 'completed' AND OLD.status != 'completed' AND NEW.completed_at IS NULL THEN
    NEW.completed_at = NOW();
  END IF;

  -- If status is being updated to 'failed' and completed_at is not set
  IF NEW.status = 'failed' AND OLD.status != 'failed' AND NEW.completed_at IS NULL THEN
    NEW.completed_at = NOW();
  END IF;

  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create trigger to automatically update completion timestamps
DROP TRIGGER IF EXISTS trigger_update_video_completion ON video_history;
CREATE TRIGGER trigger_update_video_completion
  BEFORE UPDATE ON video_history
  FOR EACH ROW
  EXECUTE FUNCTION update_video_completion_status();

-- Create a function to clean up old pending/failed video records
CREATE OR REPLACE FUNCTION cleanup_old_video_records()
RETURNS void AS $$
BEGIN
  -- Delete video records that have been pending for more than 24 hours
  DELETE FROM video_history
  WHERE status = 'pending'
    AND created_at < NOW() - INTERVAL '24 hours';

  -- Delete failed video records older than 7 days
  DELETE FROM video_history
  WHERE status = 'failed'
    AND completed_at < NOW() - INTERVAL '7 days';

  -- Log the cleanup
  RAISE NOTICE 'Video cleanup completed at %', NOW();
END;
$$ LANGUAGE plpgsql;

-- Grant necessary permissions for the service role
-- PRODUCTION SAFE: Grant table permissions (no sequences needed for UUID primary keys)
DO $$
BEGIN
  -- Grant table permissions
  GRANT ALL ON video_history TO service_role;
  GRANT ALL ON video_shots TO service_role;
  RAISE NOTICE 'Granted table permissions to service_role';

  -- Note: No sequence permissions needed because we use UUID primary keys with gen_random_uuid()
  -- which doesn't create sequences like auto-incrementing SERIAL columns would

EXCEPTION
  WHEN OTHERS THEN
    RAISE NOTICE 'Error granting table permissions: % - This is not critical', SQLERRM;
END $$;

-- Create a view for video statistics (optional, for analytics)
CREATE OR REPLACE VIEW video_generation_stats AS
SELECT
  video_type,
  status,
  COUNT(*) as count,
  AVG(duration_seconds) as avg_duration,
  AVG(EXTRACT(EPOCH FROM (completed_at - created_at))) as avg_processing_time_seconds
FROM video_history
WHERE created_at >= NOW() - INTERVAL '30 days'
GROUP BY video_type, status;

-- Grant access to the stats view
GRANT SELECT ON video_generation_stats TO authenticated;
GRANT SELECT ON video_generation_stats TO service_role;

-- ============================================================================
-- PHASE 4: PRODUCTION OPTIMIZATION (C1.1 - Database Performance Optimization)
-- ============================================================================

-- Create indexes for improved query performance
-- These indexes are created CONCURRENTLY to avoid blocking existing operations
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_video_history_user_created
ON video_history(user_id, created_at DESC);

CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_video_history_status
ON video_history(status);

CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_video_history_user_status
ON video_history(user_id, status);

CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_video_shots_video_id
ON video_shots(video_id, shot_number);

CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_usage_tracking_user_id
ON usage_tracking(user_id);

-- Add performance monitoring columns (C1.2)
DO $$
BEGIN
  -- Add processing time tracking
  IF NOT EXISTS (SELECT 1 FROM information_schema.columns
                 WHERE table_name = 'video_history' AND column_name = 'processing_time_seconds') THEN
    ALTER TABLE video_history ADD COLUMN processing_time_seconds INTEGER DEFAULT 0;
    RAISE NOTICE 'Added processing_time_seconds column to video_history';
  END IF;

  -- Add retry count tracking
  IF NOT EXISTS (SELECT 1 FROM information_schema.columns
                 WHERE table_name = 'video_history' AND column_name = 'retry_count') THEN
    ALTER TABLE video_history ADD COLUMN retry_count INTEGER DEFAULT 0;
    RAISE NOTICE 'Added retry_count column to video_history';
  END IF;

  -- Add error details tracking
  IF NOT EXISTS (SELECT 1 FROM information_schema.columns
                 WHERE table_name = 'video_history' AND column_name = 'error_details') THEN
    ALTER TABLE video_history ADD COLUMN error_details TEXT;
    RAISE NOTICE 'Added error_details column to video_history';
  END IF;

EXCEPTION
  WHEN OTHERS THEN
    RAISE NOTICE 'Error adding monitoring columns: % - This is not critical', SQLERRM;
END $$;

-- Enhanced cleanup function for maintenance
CREATE OR REPLACE FUNCTION cleanup_old_videos()
RETURNS void AS $$
BEGIN
    -- Delete videos older than 90 days that are completed or failed
    DELETE FROM video_history
    WHERE created_at < NOW() - INTERVAL '90 days'
    AND status IN ('completed', 'failed');

    -- Log cleanup action
    RAISE NOTICE 'Cleaned up old videos older than 90 days at %', NOW();
END;
$$ LANGUAGE plpgsql;
