# VibeNecto - AI-Powered Content Creation Platform

VibeNecto is an AI-powered content creation platform designed to help marketing teams and creators quickly generate visual and video content for various social media platforms.

**Version**: 2.0.0

## Features

- **AI Image Generation**: Create custom marketing visuals using Amazon Titan Image Generator
  - Background removal and image conditioning
  - Color-guided generation and image variations
  - Multiple aspect ratios and quality settings
- **AI Video Generation**: Create engaging video content using AWS Bedrock Nova Reel
  - Single-shot and multi-shot video generation
  - Text-to-video with reference image support
  - Automated and manual storyboard creation
- **User Authentication**: Secure login and user management with Supabase
- **Content History**: Save and manage your generated images and videos
- **Responsive Design**: Works seamlessly on desktop and mobile devices

## Tech Stack

- **Frontend**: React, TypeScript, Vite, Tailwind CSS, shadcn-ui
- **Backend**: Express.js with comprehensive middleware
- **AI Integration**: AWS Bedrock (Titan Image Generator & Nova Reel Video)
- **Authentication**: Supabase authentication and database
- **Storage**: AWS S3 for images and videos
- **Monitoring**: Winston logging, health checks, and performance metrics

## Prerequisites

Before running the application, ensure you have the following:

1. Node.js installed (v14 or later)
2. AWS CLI installed and configured with appropriate credentials
3. Access to AWS Bedrock and the Titan Image Generator G1 model

## AWS CLI Configuration

Make sure your AWS CLI is properly configured with credentials that have access to AWS Bedrock:

```bash
aws configure
```

You'll need to provide:
- AWS Access Key ID
- AWS Secret Access Key
- Default region (must be a region where Bedrock is available)
- Default output format (json)

## Installation

### Frontend

```bash
# Install frontend dependencies
npm install
```

### Backend

```bash
# Navigate to the server directory
cd server

# Install backend dependencies
npm install
```

## Running the Application

### Start the Backend Server

```bash
# Navigate to the server directory
cd server

# Start the development server
npm run dev
```

This will start the server on port 3001.

### Start the Frontend

```bash
# In a new terminal, from the project root
npm run dev
```

This will start the frontend development server, typically on port 5173.

## Using the Image Generator

1. Navigate to the Image Generator page
2. Enter a detailed prompt describing the image you want to create
3. Select the platform, style, and other parameters
4. Click "Generate Image"
5. Once the image is generated, you can download it

## Development Notes

### Handling Large Responses

When working with image generation, be aware that:

1. The AWS Bedrock Titan Image Generator can produce large base64-encoded responses
2. These responses can exceed Node.js default buffer size when printed to stdout
3. To prevent "stdout maxBuffer length exceeded" errors:
   - Avoid printing large response data to stdout in child processes
   - Increase maxBuffer size when executing commands that may produce large outputs
   - Log file sizes instead of full content when dealing with potentially large files

Example of increasing buffer size in Node.js:
```javascript
exec(command, { maxBuffer: 10 * 1024 * 1024 }, (error, stdout, stderr) => {
  // Handle response
});
```

- The application uses AWS Bedrock SDK for image generation
- The server provides API endpoints for all image generation operations

## Troubleshooting

If you encounter issues with AWS Bedrock SDK, check the following:

1. Ensure your AWS credentials are properly configured in the .env file
2. Verify that you have access to AWS Bedrock and the Titan Image Generator G1 model
3. Check the server logs for detailed error messages

### Important Notes

- The AWS Bedrock Titan Image Generator requires the `negativeText` parameter to have a minimum length of 3 characters
- The correct model ID is `amazon.titan-image-generator-v2:0`
- CFG scale cannot exceed 10 in AWS Bedrock Titan Image Generator
- CFG scale values of 0-5 allow more creative freedom, while 6-10 make the AI follow prompts more precisely

## Project Structure

- `/src` - Frontend React application
  - `/components` - Reusable UI components
  - `/contexts` - React contexts for state management
  - `/pages` - Application pages
  - `/services` - API and service integrations
  - `/utils` - Utility functions
- `/server` - Backend Express server
  - `server-refactored.js` - Main server file (refactored and modular)
  - `/routes` - Modular route handlers for different services
  - `/services` - AWS service modules (Bedrock, Polly, S3)
  - `/utils` - Utility functions for error handling, validation, cleanup
  - `/middleware` - Express middleware for authentication, rate limiting, etc.
