const Joi = require('joi');
const { AppError, ErrorTypes } = require('./errorHandler');

// Validation middleware factory
const validate = (schema, property = 'body') => {
  return (req, res, next) => {
    const { error, value } = schema.validate(req[property], {
      abortEarly: false,
      stripUnknown: true
    });

    if (error) {
      const errorMessage = error.details
        .map(detail => detail.message)
        .join(', ');
      
      return next(new AppError(
        `Validation Error: ${errorMessage}`,
        400,
        ErrorTypes.VALIDATION_ERROR
      ));
    }

    // Replace the original data with validated data
    req[property] = value;
    next();
  };
};

// Common validation schemas
const schemas = {
  // Video generation validation
  videoGeneration: Joi.object({
    prompt: Joi.string()
      .min(1)
      .max(4000)
      .required()
      .messages({
        'string.min': 'Prompt cannot be empty',
        'string.max': 'Prompt cannot exceed 4000 characters',
        'any.required': 'Prompt is required'
      }),
    
    videoType: Joi.string()
      .valid('TEXT_VIDEO', 'MULTI_SHOT_AUTOMATED', 'MULTI_SHOT_MANUAL')
      .required()
      .messages({
        'any.only': 'Video type must be TEXT_VIDEO, MULTI_SHOT_AUTOMATED, or MULTI_SHOT_MANUAL',
        'any.required': 'Video type is required'
      }),
    
    duration: Joi.number()
      .integer()
      .min(6)
      .max(120)
      .multiple(6)
      .when('videoType', {
        is: 'TEXT_VIDEO',
        then: Joi.valid(6),
        otherwise: Joi.number().min(12).max(120)
      })
      .required()
      .messages({
        'number.base': 'Duration must be a number',
        'number.integer': 'Duration must be an integer',
        'number.min': 'Duration must be at least 6 seconds',
        'number.max': 'Duration cannot exceed 120 seconds',
        'number.multiple': 'Duration must be a multiple of 6 seconds',
        'any.required': 'Duration is required'
      }),
    
    seed: Joi.number()
      .integer()
      .min(0)
      .max(2147483647)
      .optional()
      .messages({
        'number.base': 'Seed must be a number',
        'number.integer': 'Seed must be an integer',
        'number.min': 'Seed must be non-negative',
        'number.max': 'Seed must not exceed 2147483647'
      }),
    
    referenceImage: Joi.string()
      .base64()
      .optional()
      .messages({
        'string.base64': 'Reference image must be valid base64'
      }),
    
    shots: Joi.array()
      .items(
        Joi.object({
          prompt: Joi.string()
            .min(1)
            .max(512)
            .optional()
            .messages({
              'string.min': 'Shot prompt cannot be empty',
              'string.max': 'Shot prompt cannot exceed 512 characters'
            }),
          
          text: Joi.string()
            .min(1)
            .max(512)
            .optional()
            .messages({
              'string.min': 'Shot text cannot be empty',
              'string.max': 'Shot text cannot exceed 512 characters'
            }),
          
          referenceImage: Joi.string()
            .base64()
            .optional()
            .messages({
              'string.base64': 'Shot reference image must be valid base64'
            }),
          
          referenceImageKey: Joi.string()
            .pattern(/^[a-zA-Z0-9\-_\/\.]+$/)
            .optional()
            .messages({
              'string.pattern.base': 'Shot reference image key must be a valid S3 key'
            }),
          
          referenceImageFormat: Joi.string()
            .valid('png', 'jpeg')
            .optional()
            .default('png'),
          
          seed: Joi.number()
            .integer()
            .min(0)
            .max(2147483647)
            .optional(),
          
          order: Joi.number()
            .integer()
            .min(1)
            .max(20)
            .optional()
            .messages({
              'number.base': 'Shot order must be a number',
              'number.integer': 'Shot order must be an integer',
              'number.min': 'Shot order must be at least 1',
              'number.max': 'Shot order cannot exceed 20'
            })
        }).or('prompt', 'text') // Require either prompt or text
      )
      .min(1)
      .max(20)
      .when('videoType', {
        is: 'MULTI_SHOT_MANUAL',
        then: Joi.required(),
        otherwise: Joi.forbidden()
      })
      .messages({
        'array.min': 'At least 1 shot is required for manual multi-shot videos',
        'array.max': 'Cannot exceed 20 shots',
        'any.required': 'Shots are required for manual multi-shot videos',
        'any.unknown': 'Shots are not allowed for this video type'
      }),
    
    // Add userId validation within the videoGeneration schema
    // User ID is now required for all generation tasks.
    userId: Joi.string()
      .uuid()
      .required()
      .messages({
        'string.guid': 'User ID must be a valid UUID',
        'any.required': 'User ID is required for video generation'
      })
 }),

 // Image generation validation
 imageGeneration: Joi.object({
   prompt: Joi.string()
     .min(1)
     .max(1000)
     .required()
     .messages({
       'string.min': 'Prompt cannot be empty',
       'string.max': 'Prompt cannot exceed 1000 characters',
       'any.required': 'Prompt is required'
     }),
   
   negativePrompt: Joi.string()
     .min(3)
     .max(1000)
     .optional()
     .allow('')
     .messages({
       'string.min': 'Negative prompt must be at least 3 characters if provided',
       'string.max': 'Negative prompt cannot exceed 1000 characters'
     }),
   
   width: Joi.number()
     .integer()
     .valid(512, 768, 1024, 1152, 1216, 1344, 1536)
     .default(1024)
     .messages({
       'any.only': 'Width must be one of: 512, 768, 1024, 1152, 1216, 1344, 1536'
     }),
   
   height: Joi.number()
     .integer()
     .valid(512, 768, 1024, 1152, 1216, 1344, 1536)
     .default(1024)
     .messages({
       'any.only': 'Height must be one of: 512, 768, 1024, 1152, 1216, 1344, 1536'
     }),
   
   cfgScale: Joi.number()
     .min(0)
     .max(10)
     .default(7)
     .messages({
       'number.min': 'CFG scale must be at least 0',
       'number.max': 'CFG scale cannot exceed 10'
     }),
   
   seed: Joi.number()
     .integer()
     .min(0)
     .max(2147483647)
     .optional()
     .messages({
       'number.base': 'Seed must be a number',
       'number.integer': 'Seed must be an integer',
       'number.min': 'Seed must be non-negative',
       'number.max': 'Seed must not exceed 2147483647'
     }),
   
   quality: Joi.string()
     .valid('standard', 'premium')
     .default('standard')
     .messages({
       'any.only': 'Quality must be either "standard" or "premium"'
     }),
       
   userId: Joi.string()
     .uuid()
     .required()
     .messages({
       'string.guid': 'User ID must be a valid UUID',
       'any.required': 'User ID is required for image generation'
     })
 }),

 // Background removal validation
 backgroundRemoval: Joi.object({
   image: Joi.string().base64().required().messages({
     'string.base64': 'Image must be valid base64',
     'any.required': 'Image is required'
   }),
   userId: Joi.string().uuid().required().messages({
     'string.guid': 'User ID must be a valid UUID',
     'any.required': 'User ID is required for background removal'
   })
 }),

 // Color-guided generation validation (distinct from general image generation)
 colorGuidedGeneration: Joi.object({
   prompt: Joi.string().min(1).max(1000).required().messages({
     'string.min': 'Prompt cannot be empty',
     'string.max': 'Prompt cannot exceed 1000 characters',
     'any.required': 'Prompt is required'
   }),
   options: Joi.object({
     colors: Joi.array().items(Joi.string().hex()).min(1).required().messages({
       'array.min': 'At least one color is required for color-guided generation',
       'any.required': 'Colors array is required'
     }),
     negativePrompt: Joi.string().min(3).max(1000).optional().allow('').messages({
       'string.min': 'Negative prompt must be at least 3 characters if provided',
       'string.max': 'Negative prompt cannot exceed 1000 characters'
     }),
     referenceImage: Joi.string().base64().optional().messages({
       'string.base64': 'Reference image must be valid base64'
     }),
     width: Joi.number().integer().valid(512, 768, 1024, 1152, 1216, 1344, 1536).default(1024),
     height: Joi.number().integer().valid(512, 768, 1024, 1152, 1216, 1344, 1536).default(1024),
     quality: Joi.string().valid('standard', 'premium').default('standard'),
     numberOfImages: Joi.number().integer().min(1).default(1),
     cfgScale: Joi.number().min(0).max(10).default(7),
     seed: Joi.number().integer().min(0).max(2147483647).optional()
   }).required(),
   userId: Joi.string().uuid().required().messages({
     'string.guid': 'User ID must be a valid UUID',
     'any.required': 'User ID is required for color-guided generation'
   })
 }),

 // Image variation validation
 imageVariation: Joi.object({
   options: Joi.object({
     image: Joi.string().base64().required().messages({
       'string.base64': 'Source image must be valid base64',
       'any.required': 'Source image is required in options'
     }),
     negativePrompt: Joi.string().min(3).max(1000).optional().allow(''),
     similarityStrength: Joi.number().min(0.1).max(1.0).default(0.7),
     width: Joi.number().integer().valid(512, 768, 1024, 1152, 1216, 1344, 1536).default(1024),
     height: Joi.number().integer().valid(512, 768, 1024, 1152, 1216, 1344, 1536).default(1024),
     quality: Joi.string().valid('standard', 'premium').default('standard'),
     numberOfImages: Joi.number().integer().min(1).default(1),
     cfgScale: Joi.number().min(0).max(10).default(7),
     seed: Joi.number().integer().min(0).max(2147483647).optional()
   }).required(),
   userId: Joi.string().uuid().required().messages({
     'string.guid': 'User ID must be a valid UUID',
     'any.required': 'User ID is required for image variation'
   })
 }),

 // Image conditioning validation
 imageConditioning: Joi.object({
   prompt: Joi.string().min(1).max(1000).required().messages({
       'string.min': 'Prompt cannot be empty',
       'string.max': 'Prompt cannot exceed 1000 characters',
       'any.required': 'Prompt is required'
   }),
   options: Joi.object({
     referenceImage: Joi.string().base64().required().messages({
       'string.base64': 'Reference image must be valid base64',
       'any.required': 'Reference image is required in options'
     }),
     conditioningMode: Joi.string().valid('CANNY', 'SEGMENTATION').default('CANNY'),
     conditioningStrength: Joi.number().min(0.1).max(1.0).default(0.7),
     negativePrompt: Joi.string().min(3).max(1000).optional().allow(''),
     width: Joi.number().integer().valid(512, 768, 1024, 1152, 1216, 1344, 1536).default(1024),
     height: Joi.number().integer().valid(512, 768, 1024, 1152, 1216, 1344, 1536).default(1024),
     quality: Joi.string().valid('standard', 'premium').default('standard'),
     numberOfImages: Joi.number().integer().min(1).default(1),
     cfgScale: Joi.number().min(0).max(10).default(7),
     seed: Joi.number().integer().min(0).max(2147483647).optional()
   }).required(),
   userId: Joi.string().uuid().required().messages({
     'string.guid': 'User ID must be a valid UUID',
     'any.required': 'User ID is required for image conditioning'
   })
 }),

 // Reference image preprocessing validation
 referenceImagePreprocessing: Joi.object({
   image: Joi.string().base64().required().messages({
     'string.base64': 'Image must be valid base64',
     'any.required': 'Image is required'
   }),
   mode: Joi.string().valid('CANNY', 'SEGMENTATION').default('CANNY').messages({
     'any.only': 'Mode must be either CANNY or SEGMENTATION'
   })
 }),

 // Voice generation validation
 voiceGeneration: Joi.object({
   text: Joi.string()
     .min(1)
     .max(200000)
     .required()
     .messages({
       'string.empty': 'Text content is required',
       'string.min': 'Text content cannot be empty',
       'string.max': 'Text content cannot exceed 200,000 characters',
       'any.required': 'Text content is required'
     }),
   
   textType: Joi.string()
     .valid('text', 'ssml')
     .default('text')
     .messages({
       'any.only': 'Text type must be either "text" or "ssml"'
     }),
   
   voiceId: Joi.string()
     .required()
     .messages({
       'string.empty': 'Voice ID is required',
       'any.required': 'Voice ID is required'
     }),
   
   languageCode: Joi.string()
     .pattern(/^[a-z]{2}-[A-Z]{2}$/)
     .default('en-US')
     .messages({
       'string.pattern.base': 'Language code must be in format xx-XX (e.g., en-US)'
     }),
   
   engine: Joi.string()
     .valid('standard', 'neural')
     .default('standard')
     .messages({
       'any.only': 'Engine must be either "standard" or "neural"'
     }),
   
   outputFormat: Joi.string()
     .valid('mp3', 'ogg', 'pcm')
     .default('mp3')
     .messages({
       'any.only': 'Output format must be mp3, ogg, or pcm'
     }),
   
   sampleRate: Joi.string()
     .valid('8000', '16000', '22050', '24000')
     .default('22050')
     .messages({
       'any.only': 'Sample rate must be 8000, 16000, 22050, or 24000'
     }),
   
   speechRate: Joi.number()
     .min(0.2)
     .max(2.0)
     .default(1.0)
     .messages({
       'number.min': 'Speech rate must be between 0.2 and 2.0',
       'number.max': 'Speech rate must be between 0.2 and 2.0'
     }),
   
   pitch: Joi.string()
     .pattern(/^[+-]\d+(\.\d+)?%$/)
     .default('+0%')
     .messages({
       'string.pattern.base': 'Pitch must be in format +/-XX% (e.g., +10%, -5.5%)'
     }),
   
   volume: Joi.string()
     .pattern(/^[+-]\d+(\.\d+)?dB$/)
     .default('+0dB')
     .messages({
       'string.pattern.base': 'Volume must be in format +/-XXdB (e.g., +6dB, -3.5dB)'
     }),
   
   userId: Joi.string()
     .uuid()
     .required()
     .messages({
       'string.empty': 'User ID is required',
       'string.guid': 'User ID must be a valid UUID',
       'any.required': 'User ID is required'
     })
 }),

 // User ID validation for params
 userId: Joi.string()
   .uuid()
   .required()
   .messages({
     'string.guid': 'User ID must be a valid UUID',
     'any.required': 'User ID is required'
   }),

  // Video ID validation for params
  videoId: Joi.string()
    .uuid()
    .required()
    .messages({
      'string.guid': 'Video ID must be a valid UUID',
      'any.required': 'Video ID is required'
    }),

  // Pagination validation for query params
  pagination: Joi.object({
    page: Joi.number()
      .integer()
      .min(1)
      .default(1)
      .messages({
        'number.base': 'Page must be a number',
        'number.integer': 'Page must be an integer',
        'number.min': 'Page must be at least 1'
      }),
    
    limit: Joi.number()
      .integer()
      .min(1)
      .max(100)
      .default(20)
      .messages({
        'number.base': 'Limit must be a number',
        'number.integer': 'Limit must be an integer',
        'number.min': 'Limit must be at least 1',
        'number.max': 'Limit cannot exceed 100'
      }),
    
    sortBy: Joi.string()
      .valid('created_at', 'updated_at', 'status')
      .default('created_at')
      .messages({
        'any.only': 'Sort by must be one of: created_at, updated_at, status'
      }),
    
    sortOrder: Joi.string()
      .valid('asc', 'desc')
      .default('desc')
      .messages({
        'any.only': 'Sort order must be either "asc" or "desc"'
      })
  })
};

// Specific validation middleware functions
const validateVideoGeneration = validate(schemas.videoGeneration);
const validateImageGeneration = validate(schemas.imageGeneration);
const validateBackgroundRemoval = validate(schemas.backgroundRemoval);
const validateColorGuidedGeneration = validate(schemas.colorGuidedGeneration);
const validateImageVariation = validate(schemas.imageVariation);
const validateImageConditioning = validate(schemas.imageConditioning);
const validateReferenceImagePreprocessing = validate(schemas.referenceImagePreprocessing);
const validateVoiceGeneration = validate(schemas.voiceGeneration);
const validateUserId = validate(schemas.userId, 'params');
const validateVideoId = validate(schemas.videoId, 'params');
const validatePagination = validate(schemas.pagination, 'query');

module.exports = {
  validate,
  schemas,
  validateVideoGeneration,
  validateImageGeneration,
  validateBackgroundRemoval,
  validateColorGuidedGeneration,
  validateImageVariation,
  validateImageConditioning,
  validateReferenceImagePreprocessing,
  validateVoiceGeneration,
  validateUserId,
  validateVideoId,
  validatePagination
};
