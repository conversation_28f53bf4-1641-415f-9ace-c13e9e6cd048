/**
 * Background Removal Upload Component
 * Handles file upload UI and preview display
 */

import React from 'react';
import { Button } from '@/components/ui/button';
import { Upload, X, ImageIcon } from 'lucide-react';
import { MESSAGES, FILE_CONSTRAINTS } from '@/constants/backgroundRemoval';

interface BackgroundRemovalUploadProps {
  sourceImage: string | null;
  isUploading: boolean;
  uploadError: string | null;
  fileInputRef: React.RefObject<HTMLInputElement>;
  onFileUpload: (e: React.ChangeEvent<HTMLInputElement>) => void;
  onTriggerFileInput: () => void;
  onReset: () => void;
}

const BackgroundRemovalUpload: React.FC<BackgroundRemovalUploadProps> = ({
  sourceImage,
  isUploading,
  uploadError,
  fileInputRef,
  onFileUpload,
  onTriggerFileInput,
  onReset,
}) => {
  return (
    <div className="md:col-span-1">
      <h3 className="text-sm font-medium mb-3">Upload Image</h3>
      
      <input
        type="file"
        ref={fileInputRef}
        onChange={onFileUpload}
        accept="image/*"
        className="hidden"
      />
      
      <div
        className="border-2 border-dashed border-gray-200 dark:border-gray-700 rounded-lg p-6 cursor-pointer hover:border-brand-purple transition-colors flex flex-col items-center justify-center h-[400px]"
        onClick={onTriggerFileInput}
      >
        {isUploading ? (
          <div className="flex flex-col items-center justify-center">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-brand-purple mb-3"></div>
            <p className="text-sm text-gray-500">Processing file...</p>
          </div>
        ) : (
          <>
            <Upload size={32} className="mx-auto mb-3 text-gray-400" />
            <h3 className="text-sm font-medium mb-2">Upload an image</h3>
            <p className="text-xs text-gray-500 mb-3">Click to browse or drag and drop</p>
            <Button
              size="sm"
              className="bg-brand-purple hover:bg-brand-purple/90 text-xs"
              onClick={(e) => {
                e.stopPropagation();
                onTriggerFileInput();
              }}
            >
              Select Image
            </Button>
            <div className="mt-4 text-xs text-gray-500 text-center">
              <p className="mb-1">Supported: PNG, JPG, JPEG, WEBP</p>
              <p>Max size: {Math.round(FILE_CONSTRAINTS.MAX_SIZE / (1024 * 1024))}MB</p>
            </div>
          </>
        )}
      </div>
      
      {uploadError && (
        <div className="mt-2 p-2 bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded text-xs text-red-600 dark:text-red-400">
          {uploadError}
        </div>
      )}
    </div>
  );
};

export default BackgroundRemovalUpload;