/**
 * Voice generation service for VibeNecto
 * Handles all voice-related API calls and data management
 */

import { supabase } from '@/lib/supabase';

export interface VoiceGenerationRequest {
  text: string;
  textType?: 'text' | 'ssml';
  voiceId: string;
  languageCode?: string;
  engine?: 'standard' | 'neural';
  outputFormat?: 'mp3' | 'ogg' | 'pcm';
  sampleRate?: string;
  speechRate?: number;
  pitch?: string;
  volume?: string;
  userId: string;
}

export interface VoiceGenerationResponse {
  success: boolean;
  voiceId?: string;
  audio?: string; // Base64 encoded audio
  presignedUrl?: string;
  s3Key?: string;
  contentType?: string;
  characterCount?: number;
  processingTime?: number;
  error?: string;
  warning?: string;
}

export interface VoiceHistoryItem {
  id: string;
  user_id: string;
  text_content: string;
  voice_id: string;
  language_code: string;
  engine: string;
  parameters: {
    textType?: string;
    outputFormat?: string;
    sampleRate?: string;
    speechRate?: number;
    pitch?: string;
    volume?: string;
  };
  s3_key?: string;
  s3_url?: string;
  character_count: number;
  request_characters: number;
  content_type: string;
  status: 'completed' | 'failed' | 'pending';
  created_at: string;
  updated_at: string;
  presigned_url?: string;
}

export interface VoiceUsage {
  charactersUsed: number;
  voicesGenerated: number;
  monthlyLimit: number;
  remaining: number;
  resetDate: string;
}

export interface VoiceUsageResponse {
  success: boolean;
  usage?: VoiceUsage;
  error?: string;
}

export interface VoiceHistoryResponse {
  success: boolean;
  voices?: VoiceHistoryItem[];
  error?: string;
}

export interface DeleteVoiceResponse {
  success: boolean;
  message?: string;
  error?: string;
}

// AWS Polly voice definitions
export interface PollyVoice {
  id: string;
  name: string;
  gender: 'Male' | 'Female';
  languageCode: string;
  languageName: string;
  engine: 'standard' | 'neural' | 'both';
  isNeural?: boolean;
}

// Complete list of AWS Polly standard voices (free tier)
export const AVAILABLE_VOICES: PollyVoice[] = [
  // Arabic
  { id: 'Zeina', name: 'Zeina', gender: 'Female', languageCode: 'ar', languageName: 'Arabic', engine: 'standard' },
  
  // Chinese (Mandarin)
  { id: 'Zhiyu', name: 'Zhiyu', gender: 'Female', languageCode: 'zh-CN', languageName: 'Chinese (Mandarin)', engine: 'standard' },
  
  // Chinese (Cantonese)
  { id: 'Hiujin', name: 'Hiujin', gender: 'Female', languageCode: 'zh-HK', languageName: 'Chinese (Cantonese)', engine: 'standard' },
  
  // Chinese (Taiwan)
  { id: 'Zhiyu', name: 'Zhiyu', gender: 'Female', languageCode: 'zh-TW', languageName: 'Chinese (Taiwan)', engine: 'standard' },
  
  // Czech
  { id: 'Tereza', name: 'Tereza', gender: 'Female', languageCode: 'cs-CZ', languageName: 'Czech', engine: 'standard' },
  
  // Danish
  { id: 'Naja', name: 'Naja', gender: 'Female', languageCode: 'da-DK', languageName: 'Danish', engine: 'standard' },
  { id: 'Mads', name: 'Mads', gender: 'Male', languageCode: 'da-DK', languageName: 'Danish', engine: 'standard' },
  
  // Dutch
  { id: 'Lotte', name: 'Lotte', gender: 'Female', languageCode: 'nl-NL', languageName: 'Dutch', engine: 'standard' },
  { id: 'Ruben', name: 'Ruben', gender: 'Male', languageCode: 'nl-NL', languageName: 'Dutch', engine: 'standard' },
  
  // English (Australia)
  { id: 'Nicole', name: 'Nicole', gender: 'Female', languageCode: 'en-AU', languageName: 'English (Australia)', engine: 'standard' },
  { id: 'Russell', name: 'Russell', gender: 'Male', languageCode: 'en-AU', languageName: 'English (Australia)', engine: 'standard' },
  
  // English (UK)
  { id: 'Amy', name: 'Amy', gender: 'Female', languageCode: 'en-GB', languageName: 'English (UK)', engine: 'standard' },
  { id: 'Emma', name: 'Emma', gender: 'Female', languageCode: 'en-GB', languageName: 'English (UK)', engine: 'standard' },
  { id: 'Brian', name: 'Brian', gender: 'Male', languageCode: 'en-GB', languageName: 'English (UK)', engine: 'standard' },
  
  // English (India)
  { id: 'Aditi', name: 'Aditi', gender: 'Female', languageCode: 'en-IN', languageName: 'English (India)', engine: 'standard' },
  { id: 'Raveena', name: 'Raveena', gender: 'Female', languageCode: 'en-IN', languageName: 'English (India)', engine: 'standard' },
  
  // English (US)
  { id: 'Joanna', name: 'Joanna', gender: 'Female', languageCode: 'en-US', languageName: 'English (US)', engine: 'standard' },
  { id: 'Kendra', name: 'Kendra', gender: 'Female', languageCode: 'en-US', languageName: 'English (US)', engine: 'standard' },
  { id: 'Kimberly', name: 'Kimberly', gender: 'Female', languageCode: 'en-US', languageName: 'English (US)', engine: 'standard' },
  { id: 'Salli', name: 'Salli', gender: 'Female', languageCode: 'en-US', languageName: 'English (US)', engine: 'standard' },
  { id: 'Joey', name: 'Joey', gender: 'Male', languageCode: 'en-US', languageName: 'English (US)', engine: 'standard' },
  { id: 'Justin', name: 'Justin', gender: 'Male', languageCode: 'en-US', languageName: 'English (US)', engine: 'standard' },
  { id: 'Matthew', name: 'Matthew', gender: 'Male', languageCode: 'en-US', languageName: 'English (US)', engine: 'standard' },
  { id: 'Ivy', name: 'Ivy', gender: 'Female', languageCode: 'en-US', languageName: 'English (US)', engine: 'standard' },
  
  // English (Wales)
  { id: 'Geraint', name: 'Geraint', gender: 'Male', languageCode: 'en-GB-WLS', languageName: 'English (Wales)', engine: 'standard' },
  
  // Finnish
  { id: 'Suvi', name: 'Suvi', gender: 'Female', languageCode: 'fi-FI', languageName: 'Finnish', engine: 'standard' },
  
  // French
  { id: 'Celine', name: 'Celine', gender: 'Female', languageCode: 'fr-FR', languageName: 'French (France)', engine: 'standard' },
  { id: 'Lea', name: 'Lea', gender: 'Female', languageCode: 'fr-FR', languageName: 'French (France)', engine: 'standard' },
  { id: 'Mathieu', name: 'Mathieu', gender: 'Male', languageCode: 'fr-FR', languageName: 'French (France)', engine: 'standard' },
  
  // French (Canada)
  { id: 'Chantal', name: 'Chantal', gender: 'Female', languageCode: 'fr-CA', languageName: 'French (Canada)', engine: 'standard' },
  
  // German
  { id: 'Marlene', name: 'Marlene', gender: 'Female', languageCode: 'de-DE', languageName: 'German', engine: 'standard' },
  { id: 'Vicki', name: 'Vicki', gender: 'Female', languageCode: 'de-DE', languageName: 'German', engine: 'standard' },
  { id: 'Hans', name: 'Hans', gender: 'Male', languageCode: 'de-DE', languageName: 'German', engine: 'standard' },
  
  // German (Austria)
  { id: 'Hannah', name: 'Hannah', gender: 'Female', languageCode: 'de-AT', languageName: 'German (Austria)', engine: 'standard' },
  
  // Hindi
  { id: 'Aditi', name: 'Aditi', gender: 'Female', languageCode: 'hi-IN', languageName: 'Hindi', engine: 'standard' },
  
  // Icelandic
  { id: 'Dora', name: 'Dora', gender: 'Female', languageCode: 'is-IS', languageName: 'Icelandic', engine: 'standard' },
  { id: 'Karl', name: 'Karl', gender: 'Male', languageCode: 'is-IS', languageName: 'Icelandic', engine: 'standard' },
  
  // Italian
  { id: 'Carla', name: 'Carla', gender: 'Female', languageCode: 'it-IT', languageName: 'Italian', engine: 'standard' },
  { id: 'Bianca', name: 'Bianca', gender: 'Female', languageCode: 'it-IT', languageName: 'Italian', engine: 'standard' },
  { id: 'Giorgio', name: 'Giorgio', gender: 'Male', languageCode: 'it-IT', languageName: 'Italian', engine: 'standard' },
  
  // Japanese
  { id: 'Mizuki', name: 'Mizuki', gender: 'Female', languageCode: 'ja-JP', languageName: 'Japanese', engine: 'standard' },
  { id: 'Takumi', name: 'Takumi', gender: 'Male', languageCode: 'ja-JP', languageName: 'Japanese', engine: 'standard' },
  
  // Korean
  { id: 'Seoyeon', name: 'Seoyeon', gender: 'Female', languageCode: 'ko-KR', languageName: 'Korean', engine: 'standard' },
  
  // Norwegian
  { id: 'Liv', name: 'Liv', gender: 'Female', languageCode: 'nb-NO', languageName: 'Norwegian', engine: 'standard' },
  
  // Polish
  { id: 'Ewa', name: 'Ewa', gender: 'Female', languageCode: 'pl-PL', languageName: 'Polish', engine: 'standard' },
  { id: 'Maja', name: 'Maja', gender: 'Female', languageCode: 'pl-PL', languageName: 'Polish', engine: 'standard' },
  { id: 'Jacek', name: 'Jacek', gender: 'Male', languageCode: 'pl-PL', languageName: 'Polish', engine: 'standard' },
  { id: 'Jan', name: 'Jan', gender: 'Male', languageCode: 'pl-PL', languageName: 'Polish', engine: 'standard' },
  
  // Portuguese (Brazil)
  { id: 'Vitoria', name: 'Vitoria', gender: 'Female', languageCode: 'pt-BR', languageName: 'Portuguese (Brazil)', engine: 'standard' },
  { id: 'Camila', name: 'Camila', gender: 'Female', languageCode: 'pt-BR', languageName: 'Portuguese (Brazil)', engine: 'standard' },
  { id: 'Ricardo', name: 'Ricardo', gender: 'Male', languageCode: 'pt-BR', languageName: 'Portuguese (Brazil)', engine: 'standard' },
  
  // Portuguese (Portugal)
  { id: 'Ines', name: 'Ines', gender: 'Female', languageCode: 'pt-PT', languageName: 'Portuguese (Portugal)', engine: 'standard' },
  { id: 'Cristiano', name: 'Cristiano', gender: 'Male', languageCode: 'pt-PT', languageName: 'Portuguese (Portugal)', engine: 'standard' },
  
  // Romanian
  { id: 'Carmen', name: 'Carmen', gender: 'Female', languageCode: 'ro-RO', languageName: 'Romanian', engine: 'standard' },
  
  // Russian
  { id: 'Tatyana', name: 'Tatyana', gender: 'Female', languageCode: 'ru-RU', languageName: 'Russian', engine: 'standard' },
  { id: 'Maxim', name: 'Maxim', gender: 'Male', languageCode: 'ru-RU', languageName: 'Russian', engine: 'standard' },
  
  // Spanish (Spain)
  { id: 'Conchita', name: 'Conchita', gender: 'Female', languageCode: 'es-ES', languageName: 'Spanish (Spain)', engine: 'standard' },
  { id: 'Lucia', name: 'Lucia', gender: 'Female', languageCode: 'es-ES', languageName: 'Spanish (Spain)', engine: 'standard' },
  { id: 'Enrique', name: 'Enrique', gender: 'Male', languageCode: 'es-ES', languageName: 'Spanish (Spain)', engine: 'standard' },
  
  // Spanish (Mexico)
  { id: 'Mia', name: 'Mia', gender: 'Female', languageCode: 'es-MX', languageName: 'Spanish (Mexico)', engine: 'standard' },
  
  // Spanish (US)
  { id: 'Penelope', name: 'Penelope', gender: 'Female', languageCode: 'es-US', languageName: 'Spanish (US)', engine: 'standard' },
  { id: 'Lupe', name: 'Lupe', gender: 'Female', languageCode: 'es-US', languageName: 'Spanish (US)', engine: 'standard' },
  { id: 'Miguel', name: 'Miguel', gender: 'Male', languageCode: 'es-US', languageName: 'Spanish (US)', engine: 'standard' },
  
  // Swedish
  { id: 'Astrid', name: 'Astrid', gender: 'Female', languageCode: 'sv-SE', languageName: 'Swedish', engine: 'standard' },
  
  // Turkish
  { id: 'Filiz', name: 'Filiz', gender: 'Female', languageCode: 'tr-TR', languageName: 'Turkish', engine: 'standard' },
  
  // Welsh
  { id: 'Gwyneth', name: 'Gwyneth', gender: 'Female', languageCode: 'cy-GB', languageName: 'Welsh', engine: 'standard' },
];

// Group voices by language for easier selection
export const VOICES_BY_LANGUAGE = AVAILABLE_VOICES.reduce((acc, voice) => {
  if (!acc[voice.languageCode]) {
    acc[voice.languageCode] = {
      languageName: voice.languageName,
      voices: []
    };
  }
  acc[voice.languageCode].voices.push(voice);
  return acc;
}, {} as Record<string, { languageName: string; voices: PollyVoice[] }>);

// Voice generation constants
export const VOICE_LIMITS = {
  MAX_TEXT_LENGTH: 200000,
  MAX_FREE_CHARACTERS_MONTHLY: 10000,
  MIN_SPEECH_RATE: 0.2,
  MAX_SPEECH_RATE: 2.0,
  DEFAULT_SPEECH_RATE: 1.0,
  DEFAULT_VOICE_ID: 'Joanna',
  DEFAULT_LANGUAGE_CODE: 'en-US',
  DEFAULT_ENGINE: 'standard' as const,
  DEFAULT_OUTPUT_FORMAT: 'mp3' as const,
  DEFAULT_SAMPLE_RATE: '22050',
  DEFAULT_PITCH: '+0%',
  DEFAULT_VOLUME: '+0dB'
};

/**
 * Generate voice from text using AWS Polly
 */
export const generateVoice = async (request: VoiceGenerationRequest): Promise<VoiceGenerationResponse> => {
  try {
    const response = await fetch('/api/generate-voice', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(request),
    });

    const data = await response.json();

    if (!response.ok) {
      throw new Error(data.error || `HTTP error! status: ${response.status}`);
    }

    return data;
  } catch (error) {
    console.error('Voice generation error:', error);
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Failed to generate voice'
    };
  }
};

/**
 * Get voice generation history for the current user
 */
export const getVoiceHistory = async (
  userId: string,
  limit: number = 50,
  voiceId?: string,
  languageCode?: string
): Promise<VoiceHistoryResponse> => {
  try {
    const params = new URLSearchParams({
      userId,
      limit: limit.toString(),
    });

    if (voiceId) params.append('voiceId', voiceId);
    if (languageCode) params.append('languageCode', languageCode);

    const response = await fetch(`/api/voice-history?${params}`);
    const data = await response.json();

    if (!response.ok) {
      throw new Error(data.error || `HTTP error! status: ${response.status}`);
    }

    return data;
  } catch (error) {
    console.error('Voice history fetch error:', error);
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Failed to fetch voice history'
    };
  }
};

/**
 * Delete a voice file
 */
export const deleteVoice = async (voiceId: string, userId: string): Promise<DeleteVoiceResponse> => {
  try {
    const response = await fetch(`/api/voice/${voiceId}?userId=${userId}`, {
      method: 'DELETE',
    });

    const data = await response.json();

    if (!response.ok) {
      throw new Error(data.error || `HTTP error! status: ${response.status}`);
    }

    return data;
  } catch (error) {
    console.error('Voice deletion error:', error);
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Failed to delete voice'
    };
  }
};

/**
 * Get current voice usage statistics
 */
export const getVoiceUsage = async (userId: string): Promise<VoiceUsageResponse> => {
  try {
    const response = await fetch(`/api/voice-usage/${userId}`);
    const data = await response.json();

    if (!response.ok) {
      throw new Error(data.error || `HTTP error! status: ${response.status}`);
    }

    return data;
  } catch (error) {
    console.error('Voice usage fetch error:', error);
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Failed to fetch voice usage'
    };
  }
};

/**
 * Get voice by ID from available voices
 */
export const getVoiceById = (voiceId: string): PollyVoice | undefined => {
  return AVAILABLE_VOICES.find(voice => voice.id === voiceId);
};

/**
 * Get voices by language code
 */
export const getVoicesByLanguage = (languageCode: string): PollyVoice[] => {
  return AVAILABLE_VOICES.filter(voice => voice.languageCode === languageCode);
};

/**
 * Validate text length against limits
 */
export const validateTextLength = (text: string): { valid: boolean; error?: string } => {
  if (!text || text.trim().length === 0) {
    return { valid: false, error: 'Text is required' };
  }

  if (text.length > VOICE_LIMITS.MAX_TEXT_LENGTH) {
    return { 
      valid: false, 
      error: `Text is too long. Maximum ${VOICE_LIMITS.MAX_TEXT_LENGTH.toLocaleString()} characters allowed.` 
    };
  }

  return { valid: true };
};

/**
 * Check if user can generate voice based on usage limits
 */
export const canGenerateVoice = (usage: VoiceUsage, textLength: number): { canGenerate: boolean; error?: string } => {
  if (usage.charactersUsed + textLength > usage.monthlyLimit) {
    return {
      canGenerate: false,
      error: `Monthly character limit exceeded. You have ${usage.remaining} characters remaining this month.`
    };
  }

  return { canGenerate: true };
};

/**
 * Format character count for display
 */
export const formatCharacterCount = (count: number): string => {
  return count.toLocaleString();
};

/**
 * Format usage percentage
 */
export const formatUsagePercentage = (used: number, total: number): number => {
  return Math.round((used / total) * 100);
};

/**
 * Get usage status color based on percentage
 */
export const getUsageStatusColor = (percentage: number): string => {
  if (percentage >= 90) return 'text-red-600';
  if (percentage >= 75) return 'text-yellow-600';
  return 'text-green-600';
};