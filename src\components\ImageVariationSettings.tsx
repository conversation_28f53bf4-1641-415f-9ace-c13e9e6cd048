import React from 'react';
import { <PERSON><PERSON>, Slide<PERSON>, Refresh<PERSON><PERSON>, Check } from 'lucide-react';
import { But<PERSON> } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Slider } from '@/components/ui/slider';
import { UI_TEXT, VARIATION_STRENGTH } from '@/constants/imageVariation';

interface ImageVariationSettingsProps {
  sourceImage: string | null;
  variationStrength: number;
  prompt: string;
  negativePrompt: string;
  isProcessing: boolean;
  onVariationStrengthChange: (value: number) => void;
  onPromptChange: (value: string) => void;
  onNegativePromptChange: (value: string) => void;
  onGenerate: () => void;
}

const ImageVariationSettings: React.FC<ImageVariationSettingsProps> = ({
  sourceImage,
  variationStrength,
  prompt,
  negativePrompt,
  isProcessing,
  onVariationStrengthChange,
  onPromptChang<PERSON>,
  onNegativePromptChange,
  onGenerate,
}) => {
  return (
    <div>
      <h3 className="text-sm font-medium mb-3 flex items-center gap-1">
        <Sliders size={14} className="text-brand-purple" />
        {UI_TEXT.SETTINGS.TITLE}
      </h3>

      {!sourceImage ? (
        <div className="flex items-center justify-center border border-dashed border-gray-200 dark:border-gray-700 rounded-lg p-6 h-[400px]">
          <div className="text-center">
            <Copy size={32} className="mx-auto mb-3 text-gray-400" />
            <p className="text-sm text-gray-500 mb-2">{UI_TEXT.SETTINGS.UPLOAD_FIRST}</p>
            <p className="text-xs text-gray-400">{UI_TEXT.SETTINGS.UPLOAD_FIRST_SUBTITLE}</p>
          </div>
        </div>
      ) : (
        <div className="space-y-4">
          {/* Variation strength slider */}
          <div>
            <div className="flex justify-between mb-2">
              <Label htmlFor="variation-strength" className="text-xs">
                {UI_TEXT.SETTINGS.STRENGTH_LABEL}
              </Label>
              <span className="text-xs text-gray-500">{variationStrength}%</span>
            </div>
            <Slider
              id="variation-strength"
              min={VARIATION_STRENGTH.MIN}
              max={VARIATION_STRENGTH.MAX}
              step={VARIATION_STRENGTH.STEP}
              value={[variationStrength]}
              onValueChange={(value) => onVariationStrengthChange(value[0])}
              className="mb-6"
            />
            <div className="flex justify-between text-[10px] text-gray-500 -mt-4 mb-6">
              <span>{VARIATION_STRENGTH.LABELS.MIN_LABEL}</span>
              <span>{VARIATION_STRENGTH.LABELS.MAX_LABEL}</span>
            </div>
          </div>

          {/* Prompt input */}
          <div className="space-y-2">
            <Label htmlFor="prompt" className="text-xs">
              {UI_TEXT.SETTINGS.PROMPT_LABEL}
            </Label>
            <Input
              id="prompt"
              placeholder={UI_TEXT.SETTINGS.PROMPT_PLACEHOLDER}
              value={prompt}
              onChange={(e) => onPromptChange(e.target.value)}
              className="text-sm"
            />
            <p className="text-[10px] text-gray-500">
              {UI_TEXT.SETTINGS.PROMPT_HELP}
            </p>
          </div>

          {/* Negative prompt input */}
          <div className="space-y-2">
            <Label htmlFor="negative-prompt" className="text-xs">
              {UI_TEXT.SETTINGS.NEGATIVE_PROMPT_LABEL}
            </Label>
            <Input
              id="negative-prompt"
              placeholder={UI_TEXT.SETTINGS.NEGATIVE_PROMPT_PLACEHOLDER}
              value={negativePrompt}
              onChange={(e) => onNegativePromptChange(e.target.value)}
              className="text-sm"
            />
            <p className="text-[10px] text-gray-500">
              {UI_TEXT.SETTINGS.NEGATIVE_PROMPT_HELP}
            </p>
          </div>

          {/* Generate button */}
          <Button
            className="w-full bg-brand-purple hover:bg-brand-purple/90 mt-4"
            onClick={onGenerate}
            disabled={isProcessing}
          >
            {isProcessing ? (
              <>
                <RefreshCw size={16} className="mr-2 animate-spin" />
                {UI_TEXT.BUTTONS.GENERATING}
              </>
            ) : (
              <>
                <Check size={16} className="mr-2" />
                {UI_TEXT.BUTTONS.GENERATE}
              </>
            )}
          </Button>
        </div>
      )}
    </div>
  );
};

export default ImageVariationSettings;