import React from 'react';
import { Upload } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { UI_TEXT } from '@/constants/imageVariation';

interface ImageVariationUploadProps {
  sourceImage: string | null;
  isUploading: boolean;
  uploadError: string | null;
  fileInputRef: React.RefObject<HTMLInputElement>;
  onFileUpload: (e: React.ChangeEvent<HTMLInputElement>) => void;
  onTriggerFileInput: () => void;
  onClearImage: () => void;
}

const ImageVariationUpload: React.FC<ImageVariationUploadProps> = ({
  sourceImage,
  isUploading,
  uploadError,
  fileInputRef,
  onFileUpload,
  onTriggerFileInput,
  onClearImage,
}) => {
  return (
    <div>
      <h3 className="text-sm font-medium mb-3">{UI_TEXT.UPLOAD.TITLE}</h3>

      {!sourceImage ? (
        <div>
          <input
            type="file"
            ref={fileInputRef}
            onChange={onFileUpload}
            accept="image/*"
            className="hidden"
            disabled={isUploading}
          />
          <div
            className="border-2 border-dashed border-gray-200 dark:border-gray-700 rounded-lg p-6 cursor-pointer hover:border-brand-purple transition-colors flex flex-col items-center justify-center h-[400px]"
            onClick={onTriggerFileInput}
          >
            <Upload size={32} className="mx-auto mb-3 text-gray-400" />
            <h3 className="text-sm font-medium mb-2">{UI_TEXT.UPLOAD.TITLE}</h3>
            <p className="text-xs text-gray-500 mb-3">{UI_TEXT.UPLOAD.DRAG_DROP}</p>
            <Button
              size="sm"
              className="bg-brand-purple hover:bg-brand-purple/90 text-xs"
              onClick={(e) => {
                e.stopPropagation();
                onTriggerFileInput();
              }}
              disabled={isUploading}
            >
              {isUploading ? 'Uploading...' : UI_TEXT.UPLOAD.SELECT_BUTTON}
            </Button>
            <div className="mt-4 text-xs text-gray-500">
              <p className="mb-1">{UI_TEXT.UPLOAD.SUPPORTED_TEXT}</p>
              <p>{UI_TEXT.UPLOAD.MAX_SIZE_TEXT}</p>
            </div>
            {uploadError && (
              <div className="mt-2 text-xs text-red-500">
                {uploadError}
              </div>
            )}
          </div>
        </div>
      ) : (
        <div>
          <div className="rounded-lg overflow-hidden bg-gray-100 dark:bg-gray-800 mb-3 h-[400px] flex items-center justify-center">
            <img
              src={sourceImage}
              alt="Source"
              className="max-w-full max-h-full object-contain"
            />
          </div>
          <Button
            size="sm"
            variant="outline"
            className="text-xs w-full"
            onClick={onClearImage}
          >
            {UI_TEXT.UPLOAD.CHANGE_BUTTON}
          </Button>
        </div>
      )}
    </div>
  );
};

export default ImageVariationUpload;