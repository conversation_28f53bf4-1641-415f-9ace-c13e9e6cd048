const { logger } = require('../utils/logger');

/**
 * Superadmin middleware factory - <NAME_EMAIL> can access superadmin routes
 * Requires authMiddleware to be applied first for JWT verification
 * @param {Object} supabase - Supabase client instance
 */
const createSuperadminMiddleware = (supabase) => async (req, res, next) => {
  const requestId = req.requestId || 'unknown';
  
  try {
    // Check if user is authenticated (should be set by authMiddleware)
    if (!req.user || !req.user.id) {
      logger.warn('Superadmin route accessed without authentication', { 
        path: req.path, 
        ip: req.ip,
        userAgent: req.get('User-Agent'),
        requestId 
      });
      return res.status(401).json({ 
        success: false, 
        error: 'Authentication required' 
      });
    }

    // Fetch user profile with role information
    const { data: profile, error } = await supabase
      .from('profiles')
      .select('email, role, is_superadmin, name')
      .eq('id', req.user.id)
      .single();

    if (error) {
      logger.error('Failed to fetch user profile for superadmin check', { 
        userId: req.user.id, 
        error: error.message,
        requestId 
      });
      return res.status(500).json({ 
        success: false, 
        error: 'Failed to verify permissions' 
      });
    }

    if (!profile) {
      logger.warn('User profile not found for superadmin check', { 
        userId: req.user.id,
        requestId 
      });
      return res.status(404).json({ 
        success: false, 
        error: 'User profile not found' 
      });
    }

    // Strict email and role verification for superadmin access
    const isAuthorizedEmail = profile.email === '<EMAIL>';
    const hasSuperadminRole = profile.is_superadmin === true;
    const hasCorrectRole = profile.role === 'superadmin';

    if (!isAuthorizedEmail || !hasSuperadminRole || !hasCorrectRole) {
      logger.warn('Unauthorized superadmin access attempt', { 
        userId: req.user.id, 
        email: profile.email,
        role: profile.role,
        is_superadmin: profile.is_superadmin,
        path: req.path,
        ip: req.ip,
        userAgent: req.get('User-Agent'),
        requestId 
      });
      return res.status(403).json({ 
        success: false, 
        error: 'Insufficient permissions' 
      });
    }

    // Log successful superadmin access
    logger.info('Superadmin access granted', {
      userId: req.user.id,
      email: profile.email,
      name: profile.name,
      path: req.path,
      method: req.method,
      ip: req.ip,
      userAgent: req.get('User-Agent'),
      requestId
    });

    // Attach superadmin flag and profile to request
    req.superadmin = true;
    req.userProfile = profile;
    
    next();
  } catch (error) {
    logger.error('Superadmin middleware error', { 
      error: error.message, 
      stack: error.stack,
      userId: req.user?.id,
      path: req.path,
      requestId 
    });
    return res.status(500).json({ 
      success: false, 
      error: 'Internal server error' 
    });
  }
};

/**
 * Helper function factory to check if a user has superadmin privileges
 * Can be used in other parts of the application
 */
const createCheckSuperadminPrivileges = (supabase) => async (userId) => {
  try {
    const { data: profile, error } = await supabase
      .from('profiles')
      .select('email, role, is_superadmin')
      .eq('id', userId)
      .single();

    if (error || !profile) {
      return false;
    }

    return profile.email === '<EMAIL>' &&
           profile.is_superadmin === true &&
           profile.role === 'superadmin';
  } catch (error) {
    logger.error('Error checking superadmin privileges', {
      userId,
      error: error.message
    });
    return false;
  }
};

module.exports = {
  createSuperadminMiddleware,
  createCheckSuperadminPrivileges
};