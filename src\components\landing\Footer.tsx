import { Heart, Mail } from "lucide-react";

const Footer = () => {
  return (
    <footer className="relative overflow-hidden py-12">
      <div className="container relative z-20 mx-auto px-4 md:px-8">
        <div className="flex flex-col items-center text-center">
          {/* Logo and copyright */}
          <div className="mb-6">
            <div className="flex items-center gap-3 mb-4 justify-center">
              <img src="/logo.png" alt="VibeNecto Logo" className="h-12 w-auto brightness-125 contrast-125" />
              <h2 className="text-2xl font-semibold bg-gradient-to-r from-cyan-400 to-purple-400 bg-clip-text text-transparent">VibeNecto</h2>
            </div>
            <p className="text-white/60 text-sm">
              © 2025 VibeNecto. All rights reserved.
            </p>
            <p className="text-white/60 text-sm mt-2 flex items-center justify-center">
              Made with <Heart className="w-4 h-4 text-red-400 mx-1" /> for marketing teams
            </p>
          </div>

          {/* Social links */}
          <div className="flex space-x-4 mt-4">
            <a href="https://twitter.com" className="w-10 h-10 rounded-full bg-gradient-to-br from-blue-500/20 to-cyan-500/20 border border-blue-400/30 flex items-center justify-center text-blue-400 hover:text-blue-300 hover:bg-blue-500/30 transition-colors">
              <svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"><path d="M22 4s-.7 2.1-2 3.4c1.6 10-9.4 17.3-18 11.6 2.2.1 4.4-.6 6-2C3 15.5.5 9.6 3 5c2.2 2.6 5.6 4.1 9 4-.9-4.2 4-6.6 7-3.8 1.1 0 3-1.2 3-1.2z"></path></svg>
            </a>
            <a href="https://instagram.com" className="w-10 h-10 rounded-full bg-gradient-to-br from-pink-500/20 to-purple-500/20 border border-pink-400/30 flex items-center justify-center text-pink-400 hover:text-pink-300 hover:bg-pink-500/30 transition-colors">
              <svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"><rect width="20" height="20" x="2" y="2" rx="5" ry="5"></rect><path d="M16 11.37A4 4 0 1 1 12.63 8 4 4 0 0 1 16 11.37z"></path><line x1="17.5" x2="17.51" y1="6.5" y2="6.5"></line></svg>
            </a>
            <a href="https://linkedin.com" className="w-10 h-10 rounded-full bg-gradient-to-br from-indigo-500/20 to-blue-500/20 border border-indigo-400/30 flex items-center justify-center text-indigo-400 hover:text-indigo-300 hover:bg-indigo-500/30 transition-colors">
              <svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"><path d="M16 8a6 6 0 0 1 6 6v7h-4v-7a2 2 0 0 0-2-2 2 2 0 0 0-2 2v7h-4v-7a6 6 0 0 1 6-6z"></path><rect width="4" height="12" x="2" y="9"></rect><circle cx="4" cy="4" r="2"></circle></svg>
            </a>
          </div>

          {/* Contact email */}
          <div className="mt-6">
            <a href="mailto:<EMAIL>" className="text-white/60 hover:text-white text-sm flex items-center justify-center transition-colors">
              <Mail className="w-4 h-4 mr-2" />
              <EMAIL>
            </a>
          </div>
        </div>
      </div>
    </footer>
  );
};

export default Footer;