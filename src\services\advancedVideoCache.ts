/**
 * Advanced Video Cache Service - Sprint 18 Phase 4
 * 
 * Enhanced caching system with LRU eviction, TTL optimization, and intelligent
 * cache management strategies for optimal video loading performance.
 * 
 * Features:
 * - LRU (Least Recently Used) eviction policy
 * - Dynamic TTL optimization based on usage patterns
 * - Cache warming and preloading strategies
 * - Memory usage monitoring and optimization
 * - Performance analytics and insights
 * - Intelligent cache invalidation
 */

interface CacheEntry {
  url: string;
  timestamp: number;
  ttl: number;
  accessCount: number;
  lastAccessed: number;
  size?: number;
  metadata?: {
    videoType?: string;
    duration?: number;
    fileSize?: number;
    quality?: string;
  };
}

interface CacheStats {
  totalEntries: number;
  totalSize: number;
  hitRate: number;
  missRate: number;
  evictionCount: number;
  averageAccessCount: number;
  memoryUsage: number;
  oldestEntry: number;
  newestEntry: number;
}

interface CacheConfig {
  maxSize: number; // Maximum number of entries
  maxMemory: number; // Maximum memory usage in bytes
  defaultTTL: number; // Default TTL in milliseconds
  minTTL: number; // Minimum TTL
  maxTTL: number; // Maximum TTL
  evictionBatchSize: number; // Number of entries to evict at once
  enableAnalytics: boolean;
  enablePreloading: boolean;
}

class AdvancedVideoCache {
  private cache = new Map<string, CacheEntry>();
  private accessOrder: string[] = []; // For LRU tracking
  private stats = {
    hits: 0,
    misses: 0,
    evictions: 0,
    totalAccesses: 0,
  };
  
  private config: CacheConfig = {
    maxSize: 500, // Increased for video content
    maxMemory: 100 * 1024 * 1024, // 100MB
    defaultTTL: 45 * 60 * 1000, // 45 minutes
    minTTL: 5 * 60 * 1000, // 5 minutes
    maxTTL: 2 * 60 * 60 * 1000, // 2 hours
    evictionBatchSize: 10,
    enableAnalytics: import.meta.env.DEV,
    enablePreloading: true,
  };

  constructor(customConfig?: Partial<CacheConfig>) {
    if (customConfig) {
      this.config = { ...this.config, ...customConfig };
    }
    
    // Start periodic cleanup
    this.startPeriodicCleanup();
    
    if (this.config.enableAnalytics) {
      console.log('[AdvancedVideoCache] Initialized with config:', this.config);
    }
  }

  /**
   * Get cached video URL with LRU tracking
   */
  get(key: string): CacheEntry | null {
    const entry = this.cache.get(key);
    this.stats.totalAccesses++;

    if (!entry) {
      this.stats.misses++;
      return null;
    }

    // Check if entry has expired
    if (this.isExpired(entry)) {
      this.cache.delete(key);
      this.removeFromAccessOrder(key);
      this.stats.misses++;
      return null;
    }

    // Update access tracking for LRU
    entry.accessCount++;
    entry.lastAccessed = Date.now();
    this.updateAccessOrder(key);
    this.stats.hits++;

    return entry;
  }

  /**
   * Set cached video URL with intelligent TTL optimization
   */
  set(key: string, url: string, customTTL?: number, metadata?: CacheEntry['metadata']): void {
    // Calculate optimal TTL based on metadata and usage patterns
    const ttl = customTTL || this.calculateOptimalTTL(metadata);
    
    const entry: CacheEntry = {
      url,
      timestamp: Date.now(),
      ttl,
      accessCount: 1,
      lastAccessed: Date.now(),
      size: this.estimateEntrySize(url, metadata),
      metadata,
    };

    // Check if we need to evict entries before adding
    this.ensureCapacity(entry.size || 0);

    this.cache.set(key, entry);
    this.updateAccessOrder(key);

    if (this.config.enableAnalytics) {
      console.log(`[AdvancedVideoCache] Cached ${key} with TTL ${ttl}ms`);
    }
  }

  /**
   * Calculate optimal TTL based on video metadata and usage patterns
   */
  private calculateOptimalTTL(metadata?: CacheEntry['metadata']): number {
    let ttl = this.config.defaultTTL;

    if (metadata) {
      // Longer TTL for larger videos (they're more expensive to regenerate)
      if (metadata.fileSize && metadata.fileSize > 10 * 1024 * 1024) { // > 10MB
        ttl = Math.min(ttl * 1.5, this.config.maxTTL);
      }

      // Longer TTL for longer videos
      if (metadata.duration && metadata.duration > 30) { // > 30 seconds
        ttl = Math.min(ttl * 1.2, this.config.maxTTL);
      }

      // Adjust based on video type
      if (metadata.videoType === 'multi-shot-manual') {
        ttl = Math.min(ttl * 1.3, this.config.maxTTL); // More complex videos get longer TTL
      }
    }

    return Math.max(ttl, this.config.minTTL);
  }

  /**
   * Estimate memory usage of cache entry
   */
  private estimateEntrySize(url: string, metadata?: CacheEntry['metadata']): number {
    let size = url.length * 2; // URL string size (UTF-16)
    size += 200; // Base object overhead
    
    if (metadata) {
      size += JSON.stringify(metadata).length * 2;
    }
    
    return size;
  }

  /**
   * Ensure cache has capacity for new entry
   */
  private ensureCapacity(newEntrySize: number): void {
    // Check size limit
    while (this.cache.size >= this.config.maxSize) {
      this.evictLRU();
    }

    // Check memory limit
    const currentMemory = this.getCurrentMemoryUsage();
    if (currentMemory + newEntrySize > this.config.maxMemory) {
      this.evictByMemory(newEntrySize);
    }
  }

  /**
   * Evict least recently used entries
   */
  private evictLRU(): void {
    const batchSize = Math.min(this.config.evictionBatchSize, this.accessOrder.length);
    
    for (let i = 0; i < batchSize; i++) {
      const oldestKey = this.accessOrder[0];
      if (oldestKey) {
        this.cache.delete(oldestKey);
        this.accessOrder.shift();
        this.stats.evictions++;
      }
    }

    if (this.config.enableAnalytics) {
      console.log(`[AdvancedVideoCache] Evicted ${batchSize} LRU entries`);
    }
  }

  /**
   * Evict entries to free up memory
   */
  private evictByMemory(requiredSpace: number): void {
    let freedSpace = 0;
    const toEvict: string[] = [];

    // Find entries to evict (starting with LRU)
    for (const key of this.accessOrder) {
      const entry = this.cache.get(key);
      if (entry) {
        toEvict.push(key);
        freedSpace += entry.size || 0;
        
        if (freedSpace >= requiredSpace) {
          break;
        }
      }
    }

    // Evict selected entries
    for (const key of toEvict) {
      this.cache.delete(key);
      this.removeFromAccessOrder(key);
      this.stats.evictions++;
    }

    if (this.config.enableAnalytics) {
      console.log(`[AdvancedVideoCache] Evicted ${toEvict.length} entries to free ${freedSpace} bytes`);
    }
  }

  /**
   * Update LRU access order
   */
  private updateAccessOrder(key: string): void {
    this.removeFromAccessOrder(key);
    this.accessOrder.push(key);
  }

  /**
   * Remove key from access order tracking
   */
  private removeFromAccessOrder(key: string): void {
    const index = this.accessOrder.indexOf(key);
    if (index > -1) {
      this.accessOrder.splice(index, 1);
    }
  }

  /**
   * Check if cache entry has expired
   */
  private isExpired(entry: CacheEntry): boolean {
    return Date.now() - entry.timestamp > entry.ttl;
  }

  /**
   * Get current memory usage
   */
  private getCurrentMemoryUsage(): number {
    let totalSize = 0;
    for (const entry of this.cache.values()) {
      totalSize += entry.size || 0;
    }
    return totalSize;
  }

  /**
   * Get comprehensive cache statistics
   */
  getStats(): CacheStats {
    const entries = Array.from(this.cache.values());
    const totalSize = this.getCurrentMemoryUsage();
    const hitRate = this.stats.totalAccesses > 0 ? (this.stats.hits / this.stats.totalAccesses) * 100 : 0;
    
    return {
      totalEntries: this.cache.size,
      totalSize,
      hitRate,
      missRate: 100 - hitRate,
      evictionCount: this.stats.evictions,
      averageAccessCount: entries.length > 0 ? entries.reduce((sum, e) => sum + e.accessCount, 0) / entries.length : 0,
      memoryUsage: (totalSize / this.config.maxMemory) * 100,
      oldestEntry: entries.length > 0 ? Math.min(...entries.map(e => e.timestamp)) : 0,
      newestEntry: entries.length > 0 ? Math.max(...entries.map(e => e.timestamp)) : 0,
    };
  }

  /**
   * Preload video URLs for better performance
   */
  async preloadVideos(videoKeys: string[], urlGenerator: (key: string) => Promise<string>): Promise<void> {
    if (!this.config.enablePreloading) return;

    const uncachedKeys = videoKeys.filter(key => !this.cache.has(key));
    
    if (uncachedKeys.length === 0) return;

    if (this.config.enableAnalytics) {
      console.log(`[AdvancedVideoCache] Preloading ${uncachedKeys.length} videos`);
    }

    // Preload in batches to avoid overwhelming the system
    const batchSize = 5;
    for (let i = 0; i < uncachedKeys.length; i += batchSize) {
      const batch = uncachedKeys.slice(i, i + batchSize);
      
      await Promise.allSettled(
        batch.map(async (key) => {
          try {
            const url = await urlGenerator(key);
            this.set(key, url, undefined, { videoType: 'preloaded' });
          } catch (error) {
            console.warn(`[AdvancedVideoCache] Failed to preload ${key}:`, error);
          }
        })
      );
    }
  }

  /**
   * Clear expired entries
   */
  clearExpired(): number {
    let clearedCount = 0;
    const now = Date.now();

    for (const [key, entry] of this.cache.entries()) {
      if (now - entry.timestamp > entry.ttl) {
        this.cache.delete(key);
        this.removeFromAccessOrder(key);
        clearedCount++;
      }
    }

    if (this.config.enableAnalytics && clearedCount > 0) {
      console.log(`[AdvancedVideoCache] Cleared ${clearedCount} expired entries`);
    }

    return clearedCount;
  }

  /**
   * Start periodic cleanup process
   */
  private startPeriodicCleanup(): void {
    // Clean expired entries every 5 minutes
    setInterval(() => {
      this.clearExpired();
    }, 5 * 60 * 1000);
  }

  /**
   * Clear all cache entries
   */
  clear(): void {
    this.cache.clear();
    this.accessOrder = [];
    this.stats = { hits: 0, misses: 0, evictions: 0, totalAccesses: 0 };
    
    if (this.config.enableAnalytics) {
      console.log('[AdvancedVideoCache] Cache cleared');
    }
  }

  /**
   * Get cache configuration
   */
  getConfig(): CacheConfig {
    return { ...this.config };
  }

  /**
   * Update cache configuration
   */
  updateConfig(newConfig: Partial<CacheConfig>): void {
    this.config = { ...this.config, ...newConfig };
    
    if (this.config.enableAnalytics) {
      console.log('[AdvancedVideoCache] Configuration updated:', newConfig);
    }
  }
}

// Export singleton instance
export const advancedVideoCache = new AdvancedVideoCache();
export default advancedVideoCache;
