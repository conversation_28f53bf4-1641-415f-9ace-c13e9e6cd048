import React from "react";
import { Video } from "lucide-react";
import { USE_CASES } from "@/constants/videoGenerator";

const VideoUseCases: React.FC = () => {
  return (
    <div className="bg-gray-50 dark:bg-gray-800/50 rounded-lg p-4 mt-auto">
      <h3 className="text-sm font-medium mb-2 text-brand-purple">Perfect for:</h3>
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        {USE_CASES.map((useCase, index) => (
          <div key={index} className="flex items-start gap-2">
            <div className="h-6 w-6 rounded-full bg-purple-100 dark:bg-purple-900/30 flex items-center justify-center flex-shrink-0 mt-0.5">
              <Video size={12} className="text-brand-purple" />
            </div>
            <div>
              <h4 className="text-xs font-medium">{useCase.title}</h4>
              <p className="text-xs text-gray-500 mt-0.5">{useCase.description}</p>
            </div>
          </div>
        ))}
      </div>
    </div>
  );
};

export default VideoUseCases;