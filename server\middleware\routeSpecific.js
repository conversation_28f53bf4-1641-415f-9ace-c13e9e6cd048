/**
 * Route-specific middleware for VibeNecto backend
 * Provides specialized middleware functions for different service types
 */

const { logger } = require('../utils/logger');
const { metricsCollector } = require('../utils/metrics');
const { 
  validateUserAuth,
  validateUsageLimit,
  validateFileSize
} = require('../utils/validators');
const {
  handleAuthError,
  handleValidationError,
  handleRateLimitError
} = require('../utils/errorHandlers');

/**
 * Image generation specific middleware
 */
const imageGenerationMiddleware = {
  /**
   * Pre-processing middleware for image generation
   */
  preProcess: (req, res, next) => {
    const startTime = Date.now();
    req.processingStartTime = startTime;
    
    logger.info('Image generation request preprocessing', {
      userId: req.body.userId,
      prompt: req.body.prompt?.substring(0, 100),
      imageType: req.body.options?.quality || 'standard',
      requestId: req.requestId
    });
    
    next();
  },

  /**
   * Post-processing middleware for image generation
   */
  postProcess: (req, res, next) => {
    const originalSend = res.send;
    
    res.send = function(data) {
      const processingTime = Date.now() - req.processingStartTime;
      const response = typeof data === 'string' ? JSON.parse(data) : data;
      
      // Record metrics
      if (response.success) {
        metricsCollector.recordImageGeneration(
          req.body.options?.quality || 'standard',
          true,
          processingTime
        );
        
        logger.info('Image generation completed successfully', {
          processingTime,
          userId: req.body.userId,
          hasS3Url: !!response.s3Url,
          requestId: req.requestId
        });
      } else {
        metricsCollector.recordImageGeneration(
          req.body.options?.quality || 'standard',
          false,
          processingTime
        );
        
        logger.warn('Image generation failed', {
          processingTime,
          error: response.error,
          userId: req.body.userId,
          requestId: req.requestId
        });
      }
      
      originalSend.call(this, data);
    };
    
    next();
  },

  /**
   * Validate image file size
   */
  validateImageSize: (maxSizeMB = 10) => {
    return (req, res, next) => {
      const { image } = req.body.options || req.body;
      
      if (image) {
        const validation = validateFileSize(
          image, 
          maxSizeMB * 1024 * 1024, 
          {
            requestId: req.requestId,
            userId: req.body.userId,
            field: 'image'
          }
        );
        
        if (!validation.isValid) {
          const errorResponse = handleValidationError(
            validation.error,
            null,
            { requestId: req.requestId, userId: req.body.userId }
          );
          return res.status(errorResponse.statusCode).json(errorResponse.response);
        }
      }
      
      next();
    };
  }
};

/**
 * Video generation specific middleware
 */
const videoGenerationMiddleware = {
  /**
   * Pre-processing middleware for video generation
   */
  preProcess: (req, res, next) => {
    const startTime = Date.now();
    req.processingStartTime = startTime;
    
    logger.info('Video generation request preprocessing', {
      userId: req.body.userId,
      videoType: req.body.videoType,
      duration: req.body.duration,
      shotCount: req.body.shots?.length || 0,
      requestId: req.requestId
    });
    
    next();
  },

  /**
   * Validate video generation parameters
   */
  validateVideoParams: (req, res, next) => {
    const { videoType, duration, shots } = req.body;
    
    // Validate duration based on video type
    if (videoType === 'TEXT_VIDEO' && duration !== 6) {
      const errorResponse = handleValidationError(
        'TEXT_VIDEO must have duration of 6 seconds',
        null,
        { requestId: req.requestId, userId: req.body.userId }
      );
      return res.status(errorResponse.statusCode).json(errorResponse.response);
    }
    
    if (videoType === 'MULTI_SHOT_MANUAL' && (!shots || shots.length === 0)) {
      const errorResponse = handleValidationError(
        'MULTI_SHOT_MANUAL requires at least one shot',
        null,
        { requestId: req.requestId, userId: req.body.userId }
      );
      return res.status(errorResponse.statusCode).json(errorResponse.response);
    }
    
    // Validate shot count
    if (shots && shots.length > 20) {
      const errorResponse = handleValidationError(
        'Cannot exceed 20 shots per video',
        null,
        { requestId: req.requestId, userId: req.body.userId }
      );
      return res.status(errorResponse.statusCode).json(errorResponse.response);
    }
    
    next();
  },

  /**
   * Validate reference images in shots
   */
  validateShotImages: (req, res, next) => {
    const { shots } = req.body;
    
    if (shots && Array.isArray(shots)) {
      for (let i = 0; i < shots.length; i++) {
        const shot = shots[i];
        
        if (shot.referenceImage) {
          const validation = validateFileSize(
            shot.referenceImage,
            10 * 1024 * 1024, // 10MB limit
            {
              requestId: req.requestId,
              userId: req.body.userId,
              field: `shot[${i}].referenceImage`
            }
          );
          
          if (!validation.isValid) {
            const errorResponse = handleValidationError(
              validation.error,
              null,
              { requestId: req.requestId, userId: req.body.userId }
            );
            return res.status(errorResponse.statusCode).json(errorResponse.response);
          }
        }
      }
    }
    
    next();
  }
};

/**
 * Voice generation specific middleware
 */
const voiceGenerationMiddleware = {
  /**
   * Pre-processing middleware for voice generation
   */
  preProcess: (req, res, next) => {
    const startTime = Date.now();
    req.processingStartTime = startTime;
    
    logger.info('Voice generation request preprocessing', {
      userId: req.body.userId,
      textLength: req.body.text?.length,
      voiceId: req.body.voiceId,
      languageCode: req.body.languageCode,
      requestId: req.requestId
    });
    
    next();
  },

  /**
   * Check voice usage limits
   */
  checkUsageLimit: async (req, res, next) => {
    const { createClient } = require('@supabase/supabase-js');
    const supabase = createClient(
      process.env.SUPABASE_URL,
      process.env.SUPABASE_SERVICE_KEY
    );
    
    const { userId, text } = req.body;
    const monthlyLimit = 10000; // Free tier limit
    
    try {
      // Get current usage
      const { data: usageData } = await supabase
        .rpc('get_current_voice_usage', { p_user_id: userId });
      
      const currentUsage = usageData && usageData.length > 0 ? usageData[0].characters_used : 0;
      
      // Validate usage limit
      const validation = validateUsageLimit(
        currentUsage,
        text.length,
        monthlyLimit,
        {
          requestId: req.requestId,
          userId,
          type: 'character'
        }
      );
      
      if (!validation.isValid) {
        const errorResponse = handleRateLimitError(
          validation.error,
          {
            requestId: req.requestId,
            userId,
            limit: monthlyLimit,
            remaining: validation.usage.remaining
          }
        );
        return res.status(errorResponse.statusCode).json({
          ...errorResponse.response,
          usage: validation.usage
        });
      }
      
      // Store usage info for later use
      req.voiceUsage = {
        current: currentUsage,
        requested: text.length,
        limit: monthlyLimit
      };
      
      next();
    } catch (error) {
      logger.error('Error checking voice usage limit', {
        error: error.message,
        userId,
        requestId: req.requestId
      });
      
      // Continue anyway, don't block generation
      next();
    }
  }
};

/**
 * S3 operation specific middleware
 */
const s3OperationMiddleware = {
  /**
   * Pre-processing middleware for S3 operations
   */
  preProcess: (req, res, next) => {
    const startTime = Date.now();
    req.s3StartTime = startTime;
    
    logger.info('S3 operation request preprocessing', {
      operation: req.route?.path || req.path,
      userId: req.body.userId || req.query.userId,
      requestId: req.requestId
    });
    
    next();
  },

  /**
   * Post-processing middleware for S3 operations
   */
  postProcess: (req, res, next) => {
    const originalSend = res.send;
    
    res.send = function(data) {
      const operationTime = Date.now() - req.s3StartTime;
      const response = typeof data === 'string' ? JSON.parse(data) : data;
      
      // Record S3 operation metrics
      const operation = req.route?.path?.includes('upload') ? 'upload' :
                       req.route?.path?.includes('delete') ? 'delete' :
                       req.route?.path?.includes('presigned') ? 'presigned-url' : 'unknown';
      
      metricsCollector.recordS3Operation(operation, operationTime, response.success);
      
      logger.info('S3 operation completed', {
        operation,
        operationTime,
        success: response.success,
        userId: req.body.userId || req.query.userId,
        requestId: req.requestId
      });
      
      originalSend.call(this, data);
    };
    
    next();
  }
};

/**
 * Authentication middleware with enhanced logging
 */
const enhancedAuthMiddleware = (req, res, next) => {
  const { userId } = req.body || req.query || req.params;
  
  if (!userId) {
    logger.warn('Request without user authentication', {
      path: req.path,
      method: req.method,
      requestId: req.requestId
    });
    
    const errorResponse = handleAuthError(
      'Authentication required. User ID is missing.',
      { requestId: req.requestId, operation: req.path }
    );
    return res.status(errorResponse.statusCode).json(errorResponse.response);
  }
  
  // Validate user auth
  const validation = validateUserAuth(userId, {
    requestId: req.requestId,
    operation: req.path
  });
  
  if (!validation.isValid) {
    const errorResponse = handleAuthError(
      validation.error,
      { requestId: req.requestId, operation: req.path }
    );
    return res.status(validation.statusCode || 401).json(errorResponse.response);
  }
  
  // Store validated user ID
  req.validatedUserId = userId;
  
  logger.debug('User authentication validated', {
    userId,
    path: req.path,
    requestId: req.requestId
  });
  
  next();
};

/**
 * Request context middleware
 */
const requestContextMiddleware = (serviceType) => {
  return (req, res, next) => {
    // Add service-specific context
    req.serviceContext = {
      type: serviceType,
      startTime: Date.now(),
      requestId: req.requestId,
      userId: req.body?.userId || req.query?.userId || req.params?.userId
    };
    
    logger.debug(`${serviceType} request context initialized`, {
      serviceType,
      userId: req.serviceContext.userId,
      requestId: req.requestId
    });
    
    next();
  };
};

/**
 * Response time tracking middleware
 */
const responseTimeMiddleware = (req, res, next) => {
  const startTime = Date.now();
  
  const originalSend = res.send;
  res.send = function(data) {
    const responseTime = Date.now() - startTime;
    
    // Add response time header
    res.set('X-Response-Time', `${responseTime}ms`);
    
    // Log response time
    logger.info('Request completed', {
      path: req.path,
      method: req.method,
      statusCode: res.statusCode,
      responseTime,
      userId: req.serviceContext?.userId,
      requestId: req.requestId
    });
    
    originalSend.call(this, data);
  };
  
  next();
};

module.exports = {
  imageGenerationMiddleware,
  videoGenerationMiddleware,
  voiceGenerationMiddleware,
  s3OperationMiddleware,
  enhancedAuthMiddleware,
  requestContextMiddleware,
  responseTimeMiddleware
};