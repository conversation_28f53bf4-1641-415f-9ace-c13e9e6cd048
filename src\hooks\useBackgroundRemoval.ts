/**
 * Custom hook for handling background removal functionality
 * Manages processing state, API calls, S3 upload, and history saving
 */

import { useState, useCallback } from 'react';
import { toast } from 'sonner';
import { useQueryClient } from '@tanstack/react-query';
import { useAuth } from '@/contexts/AuthContext';
import { removeBackgroundFromImage } from '@/services/bedrockService';
import { saveImageHistory, ImageHistoryItem } from '@/services/imageHistoryService';
import { uploadImageToS3 } from '@/services/s3Service';
import { MESSAGES, DEFAULTS } from '@/constants/backgroundRemoval';
import { downloadImage } from '@/utils/imageUtils';

interface UseBackgroundRemovalReturn {
  // State
  resultImage: string | null;
  isProcessing: boolean;
  processingError: string | null;
  
  // Actions
  processImage: (base64Data: string) => Promise<void>;
  resetResult: () => void;
  downloadResult: () => void;
}

export const useBackgroundRemoval = (): UseBackgroundRemovalReturn => {
  const { user } = useAuth();
  const queryClient = useQueryClient();
  
  const [resultImage, setResultImage] = useState<string | null>(null);
  const [isProcessing, setIsProcessing] = useState(false);
  const [processingError, setProcessingError] = useState<string | null>(null);

  // Save image to history with deduplication logic
  const saveToHistory = useCallback(async (imageData: string, s3Url?: string, s3Key?: string) => {
    if (!user?.id) return;

    try {
      let finalS3Url = s3Url;
      let finalS3Key = s3Key;

      // If S3 data not provided, try to upload directly
      if (!finalS3Url || !finalS3Key) {
        try {
          const s3Result = await uploadImageToS3({
            image: imageData.split(',')[1] || imageData,
            userId: user.id,
            imageType: DEFAULTS.IMAGE_TYPE,
            filename: `bg-removal-${Date.now()}.png`
          });

          if (s3Result.success && s3Result.url && s3Result.key) {
            finalS3Url = s3Result.url;
            finalS3Key = s3Result.key;
          }
        } catch (s3Error) {
          if (import.meta.env.DEV) {
            console.error('Failed to upload to S3:', s3Error);
          }
          // Continue without S3 - we'll still show success to user
        }
      }

      // Only save to history if we have S3 data
      if (finalS3Url && finalS3Key) {
        const newHistoryItem: ImageHistoryItem = {
          user_id: user.id,
          prompt: DEFAULTS.PROMPT,
          image_type: DEFAULTS.IMAGE_TYPE,
          s3_key: finalS3Key,
          s3_url: finalS3Url,
          parameters: {
            platform: DEFAULTS.PLATFORM,
            style: DEFAULTS.STYLE
          }
        };

        await saveImageHistory(newHistoryItem);
        
        // Invalidate image history queries to refresh Dashboard
        await queryClient.invalidateQueries({ queryKey: ['imageHistory', user.id] });
        
        return true; // Successfully saved
      }
      
      return false; // Failed to get S3 data
    } catch (error) {
      if (import.meta.env.DEV) {
        console.error('Failed to save image to history:', error);
      }
      
      // Still try to invalidate queries even if save failed
      try {
        await queryClient.invalidateQueries({ queryKey: ['imageHistory', user.id] });
      } catch (invalidateError) {
        if (import.meta.env.DEV) {
          console.error('Failed to invalidate queries after save error:', invalidateError);
        }
      }
      
      return false;
    }
  }, [user?.id, queryClient]);

  // Process image (remove background)
  const processImage = useCallback(async (base64Data: string) => {
    if (!user?.id) {
      toast.error('Please sign in to use this feature');
      return;
    }

    setIsProcessing(true);
    setProcessingError(null);

    try {
      // Call the Bedrock service to remove the background
      const result = await removeBackgroundFromImage(base64Data, user.id);

      if (result.success && result.image) {
        setResultImage(result.image);
        setProcessingError(null);

        // Try to save to history
        const savedSuccessfully = await saveToHistory(
          result.image,
          result.s3Url,
          result.s3Key
        );

        // Show appropriate success message
        if (savedSuccessfully) {
          toast.success(MESSAGES.SUCCESS.BACKGROUND_REMOVED);
        } else {
          toast.warning(MESSAGES.WARNING.SAVE_PARTIAL_FAILURE);
        }
      } else {
        const errorMessage = result.error || MESSAGES.ERROR.BACKGROUND_REMOVAL_FAILED;
        setProcessingError(errorMessage);
        toast.error(errorMessage);
      }
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : MESSAGES.ERROR.PROCESSING_FAILED;
      setProcessingError(errorMessage);
      toast.error(errorMessage);
      
      if (import.meta.env.DEV) {
        console.error('Background removal error:', error);
      }
    } finally {
      setIsProcessing(false);
    }
  }, [user?.id, saveToHistory]);

  // Reset result state
  const resetResult = useCallback(() => {
    setResultImage(null);
    setProcessingError(null);
  }, []);

  // Download result image
  const downloadResult = useCallback(() => {
    if (!resultImage) {
      toast.error('No image to download');
      return;
    }

    try {
      const filename = `background-removed-${Date.now()}.png`;
      downloadImage(resultImage, filename);
      toast.success(MESSAGES.SUCCESS.IMAGE_DOWNLOADED);
    } catch (error) {
      toast.error(MESSAGES.ERROR.DOWNLOAD_FAILED);
      
      if (import.meta.env.DEV) {
        console.error('Download failed:', error);
      }
    }
  }, [resultImage]);

  return {
    // State
    resultImage,
    isProcessing,
    processingError,
    
    // Actions
    processImage,
    resetResult,
    downloadResult,
  };
};