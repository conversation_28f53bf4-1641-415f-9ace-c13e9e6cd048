/**
 * Service for interacting with AWS Bedrock via SDK
 * Handles image generation using Amazon Titan Image Generator G1 V2
 * Supports multiple task types including:
 * - TEXT_IMAGE: Standard text-to-image generation
 * - BACKGROUND_REMOVAL: Remove background from an image
 * - COLOR_GUIDED_GENERATION: Generate images with specific color palettes
 * - IMAGE_VARIATION: Create variations of existing images
 */

import { base64ToDataUrl } from '@/utils/base64Utils';
// import { uploadImageToS3, S3UploadResult } from '@/services/s3Service'; // uploadImageToS3 is not directly used here, but s3Service might be needed elsewhere
// import { useAuth } from '@/contexts/AuthContext'; // useAuth is a hook, cannot be used in service files directly
import { supabase } from '@/lib/supabase'; // Import supabase client
import { trackImageGeneration } from '@/services/subscriptionService';

// Task types supported by AWS Bedrock Titan Image Generator
export type TaskType =
  | 'TEXT_IMAGE'
  | 'BACKGROUND_REMOVAL'
  | 'COLOR_GUIDED_GENERATION'
  | 'IMAGE_VARIATION'
  | 'IMAGE_CONDITIONING'
  | 'PREPROCESS_IMAGE';

// Types for the Bedrock service
export interface ImageGenerationOptions {
  negativePrompt?: string;
  width?: number;
  height?: number;
  quality?: 'standard' | 'premium';
  numberOfImages?: number;
  cfgScale?: number;
  seed?: number;
}

// Options specific to background removal
export interface BackgroundRemovalOptions {
  image: string; // Base64 encoded image
}

// Options specific to color-guided generation
export interface ColorGuidedGenerationOptions extends ImageGenerationOptions {
  colors: string[]; // Array of hex color codes
  referenceImage?: string; // Optional base64 encoded reference image
}

// Options specific to image variation
export interface ImageVariationOptions extends ImageGenerationOptions {
  image: string; // Base64 encoded image
  similarityStrength?: number; // Range: 0.2 to 1.0
}

// Options specific to image conditioning
export interface ImageConditioningOptions extends ImageGenerationOptions {
  referenceImage: string; // Base64 encoded reference image
  conditioningMode?: 'CANNY' | 'SEGMENTATION'; // Mode for conditioning (controlMode in API)
  conditioningStrength?: number; // Range: 0.1 to 1.0 (controlWeight in API)
}

// Options for preprocessing a reference image
export interface PreprocessImageOptions {
  image: string; // Base64 encoded image
  mode: 'CANNY' | 'SEGMENTATION'; // Preprocessing mode
}

export interface ImageGenerationResult {
  success: boolean;
  image?: string;
  error?: string;
  warning?: string;
  s3Key?: string;
  s3Url?: string;
  storeInS3?: boolean;
}

/**
 * Calls the API server to generate an image
 * The server will execute the AWS Bedrock SDK and return the result
 *
 * @param taskType - The type of task to perform (TEXT_IMAGE, BACKGROUND_REMOVAL, etc.)
 * @param params - Parameters for the task
 * @param userId - Optional user ID for S3 storage
 * @returns A promise that resolves to the image generation result
 */
export const callBedrockApi = async (
  taskType: TaskType,
  params: any,
  userId?: string
): Promise<ImageGenerationResult> => {
  if (import.meta.env.DEV) {
    console.log(`Calling Bedrock API for task: ${taskType}`);
  }

  try {
    // Determine the endpoint based on task type
    let endpoint = '/api/generate-image'; // Default endpoint
    let requestBody: any = { taskType, userId };

    // Prepare the request body based on the task type
    switch (taskType) {
      case 'TEXT_IMAGE':
        const { prompt, options } = params;
        requestBody = { prompt, options, taskType, userId };
        break;

      case 'BACKGROUND_REMOVAL':
        endpoint = '/api/remove-background';
        requestBody = {
          image: params.image,
          taskType,
          userId
        };
        break;

      case 'COLOR_GUIDED_GENERATION':
        endpoint = '/api/color-guided-generation';
        requestBody = {
          prompt: params.prompt,
          options: params.options,
          taskType,
          userId
        };
        break;

      case 'IMAGE_VARIATION':
        endpoint = '/api/image-variation';
        requestBody = {
          options: params.options,
          taskType,
          userId
        };
        break;

      case 'IMAGE_CONDITIONING':
        endpoint = '/api/image-conditioning';
        requestBody = {
          prompt: params.prompt,
          options: params.options,
          taskType,
          userId
        };
        break;

      case 'PREPROCESS_IMAGE':
        endpoint = '/api/preprocess-reference-image';
        requestBody = {
          image: params.image,
          mode: params.mode,
          taskType
        };
        break;

      default:
        return {
          success: false,
          error: `Unsupported task type: ${taskType}`
        };
    }

    // Get Supabase token
    const { data: { session } } = await supabase.auth.getSession();
    const token = session?.access_token;

    const headers: HeadersInit = {
      'Content-Type': 'application/json',
    };
    if (token) {
      headers['Authorization'] = `Bearer ${token}`;
    }

    // Call the API server using environment variable
    const apiUrl = import.meta.env.VITE_API_URL || 'http://localhost:3001';
    const response = await fetch(`${apiUrl}${endpoint}`, {
      method: 'POST',
      headers: headers,
      body: JSON.stringify(requestBody)
    });

    if (!response.ok) {
      const errorData = await response.json();
      return {
        success: false,
        error: errorData.error || `Server error: ${response.status}`
      };
    }

    const data = await response.json();

    if (!data.success) {
      return {
        success: false,
        error: data.error || 'Unknown error'
      };
    }

    let processedImage: string | undefined = undefined;
    let imageError: string | undefined = undefined;

    if (data.image && typeof data.image === 'string' && data.image.trim() !== '') {
      if (data.image.startsWith('http')) { // Only treat http/https as direct URLs
        processedImage = data.image;
      } else {
        // For anything else (data URI or raw base64), pass to the robust base64ToDataUrl
        const dataUrl = base64ToDataUrl(data.image); // No need to trim here, utility handles it
        if (dataUrl) { // base64ToDataUrl returns empty string on failure
          processedImage = dataUrl;
        } else {
          imageError = 'Received empty or invalid base64 image data from server.';
          if (import.meta.env.DEV) {
            if (import.meta.env.DEV) {
              console.error(imageError, "Original data.image from server:", "[REDACTED - Base64 data]");
            }
          }
        }
      }
    } else if (data.image) { // data.image exists but is not a non-empty string
      imageError = 'Received invalid image data format from server (not a non-empty string).';
      if (import.meta.env.DEV) {
        console.error(imageError, "Original data.image from server:", data.image);
      }
    }
    // If data.image was null, undefined, or an empty string, processedImage remains undefined.

    const isSuccess = !!processedImage && !imageError;

    const result: ImageGenerationResult = {
      success: isSuccess,
      image: processedImage,
      // If there was an imageError, it takes precedence. Otherwise, use server's error.
      // If successful but server sent an error (e.g. S3 failed but image generated), keep success true.
      error: imageError || (isSuccess ? undefined : data.error),
    };

    // Add S3 information if available
    if (data.s3Url && data.s3Key) {
      result.s3Url = data.s3Url;
      result.s3Key = data.s3Key;
      result.storeInS3 = true;
    }

    // Add server warning if present and no more specific image error occurred
    if (data.warning && !result.error) {
      result.warning = data.warning;
    }

    return result;
  } catch (error) {
    if (import.meta.env.DEV) {
      console.error('Error calling Bedrock API:', error);
    }
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error'
    };
  }
};

/**
 * Generates an image using the AWS Bedrock Titan Image Generator
 * @param prompt - The text prompt describing the image to generate
 * @param options - Additional options for image generation
 * @param userId - The ID of the user generating the image
 * @param storeInS3 - Whether to store the image in S3 (default: true)
 * @returns A promise that resolves to the image generation result
 */
export const generateImage = async (
  prompt: string,
  options: ImageGenerationOptions = {},
  userId?: string,
  storeInS3: boolean = true
): Promise<ImageGenerationResult> => {
  try {
    // Generate the image
    const result = await callBedrockApi('TEXT_IMAGE', { prompt, options }, userId);

    // If generation failed or no user ID provided, return the result as is
    if (!result.success || !userId || !storeInS3) {
      return result;
    }

    // Track image generation usage
    const imageType = options.quality === 'premium' ? 'premium' : 'standard';
    try {
      await trackImageGeneration(userId, imageType as 'standard' | 'premium');
    } catch (trackingError) {
      if (import.meta.env.DEV) {
        console.error('Failed to track image generation:', trackingError);
      }
      // Continue even if tracking fails
    }

    return result;
  } catch (error) {
    if (import.meta.env.DEV) {
      console.error('Image generation failed:', error);
    }
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Image generation failed'
    };
  }
};

/**
 * Removes the background from an image using AWS Bedrock Titan Image Generator
 * @param options - Options for background removal
 * @param userId - The ID of the user generating the image
 * @param storeInS3 - Whether to store the image in S3 (default: true)
 * @returns A promise that resolves to the image generation result
 */
export const removeBackground = async (
  options: BackgroundRemovalOptions,
  userId?: string,
  storeInS3: boolean = true
): Promise<ImageGenerationResult> => {
  try {
    // Remove the background
    const result = await callBedrockApi('BACKGROUND_REMOVAL', { image: options.image }, userId);

    // If operation failed or no user ID provided, return the result as is
    if (!result.success || !userId || !storeInS3) {
      return result;
    }

    // Track image generation usage (background removal counts as premium)
    try {
      await trackImageGeneration(userId, 'premium');
    } catch (trackingError) {
      if (import.meta.env.DEV) {
        console.error('Failed to track image generation:', trackingError);
      }
      // Continue even if tracking fails
    }

    return result;
  } catch (error) {
    if (import.meta.env.DEV) {
      console.error('Background removal failed:', error);
    }
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Background removal failed'
    };
  }
};

/**
 * Simplified function to remove background from an image
 * @param imageBase64 - Base64 encoded image data
 * @param userId - The ID of the user generating the image
 * @returns A promise that resolves to the image generation result
 */
export const removeBackgroundFromImage = async (
  imageBase64: string,
  userId: string
): Promise<ImageGenerationResult> => {
  return removeBackground({ image: imageBase64 }, userId);
};

/**
 * Generates a color-guided image using AWS Bedrock Titan Image Generator
 * @param prompt - The text prompt describing the image to generate
 * @param options - Options for color-guided generation
 * @param userId - The ID of the user generating the image
 * @param storeInS3 - Whether to store the image in S3 (default: true)
 * @returns A promise that resolves to the image generation result
 */
export const generateColorGuidedImage = async (
  prompt: string,
  options: ColorGuidedGenerationOptions,
  userId?: string,
  storeInS3: boolean = true
): Promise<ImageGenerationResult> => {
  try {
    // Generate the color-guided image
    const result = await callBedrockApi('COLOR_GUIDED_GENERATION', { prompt, options }, userId);

    // If operation failed or no user ID provided, return the result as is
    if (!result.success || !userId || !storeInS3) {
      return result;
    }

    // Track image generation usage (color-guided counts as premium)
    try {
      await trackImageGeneration(userId, 'premium');
    } catch (trackingError) {
      if (import.meta.env.DEV) {
        console.error('Failed to track image generation:', trackingError);
      }
      // Continue even if tracking fails
    }

    return result;
  } catch (error) {
    if (import.meta.env.DEV) {
      console.error('Color-guided generation failed:', error);
    }
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Color-guided generation failed'
    };
  }
};

/**
 * Generates image variations using AWS Bedrock Titan Image Generator
 * @param options - Options for image variation
 * @param userId - The ID of the user generating the image
 * @param storeInS3 - Whether to store the image in S3 (default: true)
 * @returns A promise that resolves to the image generation result
 */
export const generateImageVariation = async (
  options: ImageVariationOptions,
  userId?: string,
  storeInS3: boolean = true
): Promise<ImageGenerationResult> => {
  try {
    // Generate the image variation
    const result = await callBedrockApi('IMAGE_VARIATION', { options }, userId);

    // If operation failed or no user ID provided, return the result as is
    if (!result.success || !userId || !storeInS3) {
      return result;
    }

    // Track image generation usage (image variation counts as premium)
    try {
      await trackImageGeneration(userId, 'premium');
    } catch (trackingError) {
      if (import.meta.env.DEV) {
        console.error('Failed to track image generation:', trackingError);
      }
      // Continue even if tracking fails
    }

    return result;
  } catch (error) {
    if (import.meta.env.DEV) {
      console.error('Image variation failed:', error);
    }
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Image variation failed'
    };
  }
};

/**
 * Generates a conditioned image using AWS Bedrock Titan Image Generator
 * @param prompt - The text prompt describing the image to generate
 * @param options - Options for image conditioning
 * @param userId - The ID of the user generating the image
 * @param storeInS3 - Whether to store the image in S3 (default: true)
 * @returns A promise that resolves to the image generation result
 */
export const generateConditionedImage = async (
  prompt: string,
  options: ImageConditioningOptions,
  userId?: string,
  storeInS3: boolean = true
): Promise<ImageGenerationResult> => {
  try {
    // Generate the conditioned image
    const result = await callBedrockApi('IMAGE_CONDITIONING', { prompt, options }, userId);

    // If operation failed or no user ID provided, return the result as is
    if (!result.success || !userId || !storeInS3) {
      return result;
    }

    // Track image generation usage (image conditioning counts as premium)
    try {
      await trackImageGeneration(userId, 'premium');
    } catch (trackingError) {
      if (import.meta.env.DEV) {
        console.error('Failed to track image generation:', trackingError);
      }
      // Continue even if tracking fails
    }

    return result;
  } catch (error) {
    if (import.meta.env.DEV) {
      console.error('Image conditioning failed:', error);
    }
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Image conditioning failed'
    };
  }
};

/**
 * Preprocesses a reference image for conditioning
 * @param options - Options for preprocessing
 * @returns A promise that resolves to the preprocessing result
 */
export const preprocessReferenceImage = async (
  options: PreprocessImageOptions
): Promise<ImageGenerationResult> => {
  try {
    // Preprocess the reference image
    const result = await callBedrockApi('PREPROCESS_IMAGE', {
      image: options.image,
      mode: options.mode
    });

    return result;
  } catch (error) {
    if (import.meta.env.DEV) {
      console.error('Image preprocessing failed:', error);
    }
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Image preprocessing failed'
    };
  }
};
