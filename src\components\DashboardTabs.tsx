import React, { useEffect } from "react";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON>ist, TabsTrigger } from "@/components/ui/tabs";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Skeleton } from "@/components/ui/skeleton";
import {
  Image,
  Video,
  Mic,
  RefreshCw,
} from "lucide-react";
import ImageHistoryGallery from "@/components/ImageHistoryGallery";
import VideoHistoryGallery from "@/components/VideoHistoryGallery";
import VoiceHistoryGallery from "@/components/VoiceHistoryGallery";
import { DashboardTabsProps } from "@/types/dashboard";

const DashboardTabs: React.FC<DashboardTabsProps> = ({
  activeTab,
  setActiveTab,
  stats,
  data,
  actions,
  onSelectImage,
  onSelectVideo,
  onDeleteImage,
  onDeleteVideo,
  onVideoMetricsUpdate,
}) => {
  // Persist active tab to localStorage
  useEffect(() => {
    localStorage.setItem("dashboardActiveTab", activeTab);
  }, [activeTab]);

  if (data.isLoadingImages || data.isLoadingVideos) {
    return (
      <div className="mt-6">
        <Skeleton className="h-10 w-1/3 mb-4" />
        <Skeleton className="h-64 w-full" />
      </div>
    );
  }

  return (
    <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-6">
      <TabsList className="grid w-full grid-cols-3 max-w-lg">
        <TabsTrigger value="images" className="flex items-center gap-2">
          <Image className="h-4 w-4" />
          Images ({stats.totalImages})
        </TabsTrigger>
        <TabsTrigger value="videos" className="flex items-center gap-2">
          <Video className="h-4 w-4" />
          Videos ({stats.totalVideos})
        </TabsTrigger>
        <TabsTrigger value="voices" className="flex items-center gap-2">
          <Mic className="h-4 w-4" />
          Voices ({stats.totalVoices})
        </TabsTrigger>
      </TabsList>

      <TabsContent value="images" className="space-y-6">
        <div className="flex justify-between items-center">
          <h2 className="text-xl font-semibold">Your Images</h2>
          <Button variant="outline" onClick={actions.handleRefreshImages} disabled={data.isFetchingImages}>
            <RefreshCw className={`h-4 w-4 mr-2 ${data.isFetchingImages ? 'animate-spin' : ''}`} />
            {data.isFetchingImages ? "Refreshing..." : "Refresh"}
          </Button>
        </div>
        <ImageHistoryGallery
          onSelectImage={onSelectImage}
          onDeleteImage={onDeleteImage}
          isLoading={data.isLoadingImages || data.isFetchingImages}
          error={data.imageError}
          history={data.images}
          presignedUrls={data.presignedImageUrls}
        />
      </TabsContent>

      <TabsContent value="videos" className="space-y-6">
        <div className="flex justify-between items-center">
          <h2 className="text-xl font-semibold">Your Videos</h2>
          <Button variant="outline" onClick={actions.handleRefreshVideos} disabled={data.isFetchingVideos || actions.isCheckingFallback}>
            <RefreshCw className={`h-4 w-4 mr-2 ${(data.isFetchingVideos || actions.isCheckingFallback) ? 'animate-spin' : ''}`} />
            {actions.isCheckingFallback ? "Checking S3..." : data.isFetchingVideos ? "Refreshing..." : "Refresh"}
          </Button>
        </div>
        <VideoHistoryGallery
          onVideoSelect={onSelectVideo}
          onDeleteVideo={onDeleteVideo}
          isLoading={data.isLoadingVideos || data.isFetchingVideos}
          error={data.videoError}
          videos={data.videos}
          onMetricsUpdate={onVideoMetricsUpdate}
        />
      </TabsContent>

      <TabsContent value="voices" className="space-y-6">
        <div className="flex justify-between items-center">
          <h2 className="text-xl font-semibold">Your Voices</h2>
          <Button variant="outline" onClick={actions.handleRefreshVoices} disabled={data.isFetchingVoices}>
            <RefreshCw className={`h-4 w-4 mr-2 ${data.isFetchingVoices ? 'animate-spin' : ''}`} />
            {data.isFetchingVoices ? "Refreshing..." : "Refresh"}
          </Button>
        </div>
        <VoiceHistoryGallery />
      </TabsContent>
    </Tabs>
  );
};

export default DashboardTabs;