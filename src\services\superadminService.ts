// Superadmin Service - API communication layer for superadmin functionality
import { supabase } from '@/lib/supabase';

// TypeScript interfaces for superadmin data structures
export interface SystemStats {
  totalUsers: number;
  totalImages: number;
  totalVideos: number;
  totalVoices: number;
  activeUsers: number;
  storageUsed: string;
  generationsToday: number;
  generationsThisMonth: number;
  users: {
    total: number;
    recent: UserData[];
    byRole: Record<string, number>;
    newLast30Days: number;
    newLast7Days: number;
    newLast24Hours: number;
    growthTrend: Array<{
      date: string;
      newUsers: number;
      totalUsers: number;
    }>;
    topContentCreators: Array<UserData & {
      contentStats: {
        images: number;
        videos: number;
        voices: number;
        total: number;
      };
    }>;
  };
}

export interface UserData {
  id: string;
  email: string;
  name: string;
  role: string;
  is_superadmin: boolean;
  created_at: string;
  last_sign_in_at?: string;
  total_images?: number;
  total_videos?: number;
  total_voices?: number;
  activity?: {
    totalImages: number;
    totalVideos: number;
    totalVoices: number;
    totalContent: number;
    recentActivity: number;
    monthlyActivity: number;
    totalCharacters: number;
    lastActivity: string | null;
    engagementLevel: 'high' | 'medium' | 'low' | 'inactive';
    preferences: {
      images: number;
      videos: number;
      voices: number;
    };
    joinedDaysAgo: number;
  };
}

export interface UserSegments {
  highEngagement: number;
  mediumEngagement: number;
  lowEngagement: number;
  inactive: number;
  newUsers: number;
  powerUsers: number;
}

export interface ActivityLog {
  id: string;
  user_id: string;
  user_email: string;
  action: string;
  resource_type: string;
  resource_id?: string;
  ip_address?: string;
  user_agent?: string;
  created_at: string;
  details?: Record<string, any>;
  user?: {
    id: string;
    name: string;
    email: string;
  };
}

export interface HealthStatus {
  status: 'healthy' | 'degraded' | 'unhealthy';
  timestamp: string;
  services: {
    database: {
      status: 'healthy' | 'unhealthy';
      error: string | null;
    };
    server: {
      status: 'healthy';
      uptime: number;
      memory: {
        rss: number;
        heapTotal: number;
        heapUsed: number;
        external: number;
        arrayBuffers: number;
      };
      cpu: {
        user: number;
        system: number;
      };
    };
  };
}

export interface PerformanceMetrics {
  system: {
    server: {
      uptime: number;
      memory: {
        rss: number;
        heapTotal: number;
        heapUsed: number;
        external: number;
        arrayBuffers: number;
      };
      cpu: {
        user: number;
        system: number;
      };
      nodeVersion: string;
      platform: string;
      arch: string;
      pid: number;
      loadAverage: number[];
      freeMemory: number;
      totalMemory: number;
      cpuCount: number;
    };
    timestamp: string;
  };
  database: {
    responseTime: number;
    status: 'healthy' | 'error';
    error: string | null;
    connectionPool: {
      active: number;
      idle: number;
      waiting: number;
    };
  };
  content: {
    images: {
      total: number;
      last24h: number;
      last7d: number;
      last30d: number;
      successRate: number;
      byType: Record<string, number>;
    };
    videos: {
      total: number;
      completed: number;
      failed: number;
      processing: number;
      pending: number;
      last24h: number;
      last7d: number;
      last30d: number;
      successRate: number;
      averageProcessingTime: number;
      byType: Record<string, number>;
    };
    voices: {
      total: number;
      last24h: number;
      last7d: number;
      last30d: number;
      totalCharacters: number;
      averageCharacters: number;
      successRate: number;
    };
  };
  peakUsage: {
    hourlyDistribution: Record<string, number>;
    dailyDistribution: Record<string, number>;
    weeklyDistribution: Record<string, number>;
  };
  storage: {
    totalFiles: number;
    estimatedSize: {
      images: number;
      videos: number;
      voices: number;
    };
    growth: {
      last24h: number;
      last7d: number;
      last30d: number;
    };
  };
  alerts: Array<{
    type: string;
    message: string;
    severity: 'info' | 'warning' | 'error';
    timestamp: string;
  }>;
}

export interface AnalyticsData {
  analytics: Array<{
    period: string;
    metrics: {
      images: {
        total: number;
        byType: Record<string, number>;
        uniqueUsers: number;
      };
      videos: {
        total: number;
        completed: number;
        failed: number;
        averageProcessingTime: number;
        totalDuration: number;
        byType: Record<string, number>;
        uniqueUsers: number;
      };
      voices: {
        total: number;
        totalCharacters: number;
        averageCharacters: number;
        uniqueVoices: number;
        uniqueUsers: number;
      };
      users: {
        newRegistrations: number;
        totalActivity: number;
        activeUsers: number;
      };
    };
  }>;
  summary: {
    totalPeriods: number;
    dateRange: {
      start: string;
      end: string;
      granularity: string;
    };
    totals: {
      images: number;
      videos: number;
      voices: number;
      users: number;
    };
    averages: {
      imagesPerPeriod: number;
      videosPerPeriod: number;
      voicesPerPeriod: number;
      usersPerPeriod: number;
    };
  };
}

export interface SystemReport {
  title: string;
  generatedAt: string;
  period: {
    start: string;
    end: string;
    type: string;
  };
  summary: {
    newUsers: number;
    totalImages: number;
    totalVideos: number;
    totalVoices: number;
    totalActivity: number;
  };
  details: {
    users: {
      newRegistrations: number;
      byRole: Record<string, number>;
    };
    content: {
      images: {
        total: number;
        byType: Record<string, number>;
        uniqueUsers: number;
      };
      videos: {
        total: number;
        completed: number;
        failed: number;
        processing: number;
        byType: Record<string, number>;
        uniqueUsers: number;
        totalDuration: number;
      };
      voices: {
        total: number;
        totalCharacters: number;
        uniqueUsers: number;
        averageCharacters: number;
      };
    };
    system: {
      health: string;
      uptime: number;
      memory: {
        rss: number;
        heapTotal: number;
        heapUsed: number;
        external: number;
        arrayBuffers: number;
      };
      cpu: {
        user: number;
        system: number;
      };
    };
  };
  insights: Array<{
    type: string;
    message: string;
    severity: 'info' | 'warning' | 'error';
  }>;
  recommendations: Array<{
    type: string;
    priority: 'low' | 'medium' | 'high';
    message: string;
  }>;
}

export interface SuperadminApiResponse<T> {
  success: boolean;
  data?: T;
  error?: string;
  message?: string;
  meta?: {
    timestamp: string;
    requestId: string;
    endpoint: string;
  };
}

class SuperadminService {
  private async makeAuthenticatedRequest<T>(
    endpoint: string,
    options: RequestInit = {}
  ): Promise<SuperadminApiResponse<T>> {
    try {
      // Get current session for authentication
      const { data: { session } } = await supabase.auth.getSession();
      
      if (!session?.access_token) {
        throw new Error('No authentication token available');
      }

      const response = await fetch(`/api/superadmin${endpoint}`, {
        ...options,
        headers: {
          'Authorization': `Bearer ${session.access_token}`,
          'Content-Type': 'application/json',
          ...options.headers,
        },
      });

      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.error || `HTTP ${response.status}: ${response.statusText}`);
      }

      return data;
    } catch (error) {
      // Only log errors in development environment for security
      if (process.env.NODE_ENV === 'development') {
        console.error(`Superadmin API Error (${endpoint}):`, error);
      }
      
      // Always return structured error response
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error occurred',
      };
    }
  }

  // Get system statistics
  async getSystemStats(): Promise<SuperadminApiResponse<SystemStats>> {
    return this.makeAuthenticatedRequest<SystemStats>('/stats');
  }

  // Get user management data
  async getUsers(params?: {
    page?: number;
    limit?: number;
    search?: string;
    role?: string;
    sortBy?: string;
    sortOrder?: 'asc' | 'desc';
    activityFilter?: 'all' | 'active' | 'inactive';
  }): Promise<SuperadminApiResponse<{
    users: UserData[];
    segments: UserSegments;
    pagination: {
      page: number;
      limit: number;
      total: number;
      totalPages: number;
      filtered: number;
    };
  }>> {
    const queryParams = new URLSearchParams();
    if (params?.page) queryParams.append('page', params.page.toString());
    if (params?.limit) queryParams.append('limit', params.limit.toString());
    if (params?.search) queryParams.append('search', params.search);
    if (params?.role) queryParams.append('role', params.role);
    if (params?.sortBy) queryParams.append('sortBy', params.sortBy);
    if (params?.sortOrder) queryParams.append('sortOrder', params.sortOrder);
    if (params?.activityFilter) queryParams.append('activityFilter', params.activityFilter);

    const endpoint = `/users${queryParams.toString() ? `?${queryParams.toString()}` : ''}`;
    return this.makeAuthenticatedRequest(endpoint);
  }

  // Get system activity logs
  async getActivityLogs(params?: {
    page?: number;
    limit?: number;
    userId?: string;
    action?: string;
    startDate?: string;
    endDate?: string;
  }): Promise<SuperadminApiResponse<ActivityLog[]>> {
    const queryParams = new URLSearchParams();
    if (params?.page) queryParams.append('page', params.page.toString());
    if (params?.limit) queryParams.append('limit', params.limit.toString());
    if (params?.userId) queryParams.append('userId', params.userId);
    if (params?.action) queryParams.append('action', params.action);
    if (params?.startDate) queryParams.append('startDate', params.startDate);
    if (params?.endDate) queryParams.append('endDate', params.endDate);

    const endpoint = `/activity${queryParams.toString() ? `?${queryParams.toString()}` : ''}`;
    return this.makeAuthenticatedRequest(endpoint);
  }

  // Get system health status
  async getHealthStatus(): Promise<SuperadminApiResponse<HealthStatus>> {
    return this.makeAuthenticatedRequest<HealthStatus>('/health');
  }

  // Get comprehensive system performance metrics
  async getPerformanceMetrics(): Promise<SuperadminApiResponse<PerformanceMetrics>> {
    return this.makeAuthenticatedRequest<PerformanceMetrics>('/performance');
  }

  // Get advanced content generation analytics
  async getAnalytics(params?: {
    startDate?: string;
    endDate?: string;
    granularity?: 'daily' | 'weekly' | 'monthly';
  }): Promise<SuperadminApiResponse<AnalyticsData>> {
    const queryParams = new URLSearchParams();
    if (params?.startDate) queryParams.append('startDate', params.startDate);
    if (params?.endDate) queryParams.append('endDate', params.endDate);
    if (params?.granularity) queryParams.append('granularity', params.granularity);

    const endpoint = `/analytics${queryParams.toString() ? `?${queryParams.toString()}` : ''}`;
    return this.makeAuthenticatedRequest<AnalyticsData>(endpoint);
  }

  // Generate automated system reports
  async getReports(params?: {
    type?: 'daily' | 'weekly' | 'monthly';
    format?: 'json' | 'csv';
  }): Promise<SuperadminApiResponse<SystemReport>> {
    const queryParams = new URLSearchParams();
    if (params?.type) queryParams.append('type', params.type);
    if (params?.format) queryParams.append('format', params.format);

    const endpoint = `/reports${queryParams.toString() ? `?${queryParams.toString()}` : ''}`;
    return this.makeAuthenticatedRequest<SystemReport>(endpoint);
  }

  // Utility functions for data transformation
  formatBytes(bytes: number): string {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB', 'TB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  }

  formatUptime(seconds: number): string {
    const days = Math.floor(seconds / (24 * 3600));
    const hours = Math.floor((seconds % (24 * 3600)) / 3600);
    const minutes = Math.floor((seconds % 3600) / 60);
    
    if (days > 0) {
      return `${days}d ${hours}h ${minutes}m`;
    } else if (hours > 0) {
      return `${hours}h ${minutes}m`;
    } else {
      return `${minutes}m`;
    }
  }

  formatDate(dateString: string): string {
    if (!dateString) {
      return 'Unknown date';
    }

    const date = new Date(dateString);
    
    // Check if the date is valid
    if (isNaN(date.getTime())) {
      return 'Invalid date';
    }

    return date.toLocaleString();
  }

  formatRelativeTime(dateString: string): string {
    if (!dateString) {
      return 'Unknown time';
    }

    const date = new Date(dateString);
    
    // Check if the date is valid
    if (isNaN(date.getTime())) {
      return 'Invalid date';
    }

    const now = new Date();
    const diffInSeconds = Math.floor((now.getTime() - date.getTime()) / 1000);

    // Handle negative differences (future dates)
    if (diffInSeconds < 0) {
      return 'Just now';
    }

    if (diffInSeconds < 60) {
      return 'Just now';
    } else if (diffInSeconds < 3600) {
      const minutes = Math.floor(diffInSeconds / 60);
      return `${minutes} minute${minutes > 1 ? 's' : ''} ago`;
    } else if (diffInSeconds < 86400) {
      const hours = Math.floor(diffInSeconds / 3600);
      return `${hours} hour${hours > 1 ? 's' : ''} ago`;
    } else {
      const days = Math.floor(diffInSeconds / 86400);
      return `${days} day${days > 1 ? 's' : ''} ago`;
    }
  }

  // Error categorization for better user experience
  categorizeError(error: string): {
    type: 'network' | 'auth' | 'permission' | 'server' | 'unknown';
    userMessage: string;
  } {
    if (error.includes('fetch') || error.includes('network')) {
      return {
        type: 'network',
        userMessage: 'Network error. Please check your connection and try again.',
      };
    } else if (error.includes('authentication') || error.includes('token')) {
      return {
        type: 'auth',
        userMessage: 'Authentication error. Please sign in again.',
      };
    } else if (error.includes('permission') || error.includes('access denied')) {
      return {
        type: 'permission',
        userMessage: 'Access denied. You do not have permission to perform this action.',
      };
    } else if (error.includes('500') || error.includes('server')) {
      return {
        type: 'server',
        userMessage: 'Server error. Please try again later.',
      };
    } else {
      return {
        type: 'unknown',
        userMessage: 'An unexpected error occurred. Please try again.',
      };
    }
  }
}

// Export singleton instance
export const superadminService = new SuperadminService();
export default superadminService;