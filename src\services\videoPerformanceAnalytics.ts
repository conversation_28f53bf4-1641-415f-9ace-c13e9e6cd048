/**
 * Video Performance Analytics Service - Sprint 18 Phase 4
 * 
 * Comprehensive analytics service for tracking, analyzing, and reporting
 * video loading performance metrics across different dimensions.
 * 
 * Features:
 * - Real-time performance tracking
 * - Historical data analysis
 * - Device and network condition correlation
 * - Performance trend analysis
 * - Automated insights and recommendations
 * - Export capabilities for reporting
 */

interface PerformanceEvent {
  id: string;
  timestamp: number;
  type: 'load_start' | 'load_complete' | 'load_error' | 'cache_hit' | 'cache_miss' | 'lazy_load' | 'viewport_enter' | 'viewport_exit';
  videoId: string;
  duration?: number;
  metadata?: {
    videoType?: string;
    fileSize?: number;
    videoDuration?: number;
    cacheHit?: boolean;
    retryAttempt?: number;
    connectionType?: string;
    deviceType?: string;
    deviceMemory?: number;
    viewportSize?: { width: number; height: number };
  };
}

interface PerformanceMetrics {
  totalEvents: number;
  loadEvents: number;
  errorEvents: number;
  cacheHits: number;
  cacheMisses: number;
  lazyLoads: number;
  averageLoadTime: number;
  medianLoadTime: number;
  p95LoadTime: number;
  errorRate: number;
  cacheHitRate: number;
  lazyLoadRate: number;
  bandwidthSaved: number;
}

interface DeviceMetrics {
  deviceType: string;
  connectionType: string;
  deviceMemory: number;
  metrics: PerformanceMetrics;
  sampleSize: number;
}

interface TrendData {
  timestamp: number;
  metrics: PerformanceMetrics;
}

interface PerformanceInsight {
  id: string;
  type: 'optimization' | 'warning' | 'info';
  title: string;
  description: string;
  impact: 'high' | 'medium' | 'low';
  recommendation?: string;
  data?: any;
}

class VideoPerformanceAnalytics {
  private events: PerformanceEvent[] = [];
  private trends: TrendData[] = [];
  private maxEvents = 10000; // Keep last 10k events
  private maxTrends = 1000; // Keep last 1k trend points
  private trendInterval = 60000; // 1 minute intervals
  private lastTrendCapture = 0;

  constructor() {
    // Start trend capture
    this.startTrendCapture();
    
    if (import.meta.env.DEV) {
      console.log('[VideoPerformanceAnalytics] Analytics service initialized');
    }
  }

  /**
   * Track a performance event
   */
  trackEvent(event: Omit<PerformanceEvent, 'id' | 'timestamp'>): void {
    const fullEvent: PerformanceEvent = {
      ...event,
      id: this.generateEventId(),
      timestamp: Date.now(),
    };

    this.events.push(fullEvent);

    // Maintain event limit
    if (this.events.length > this.maxEvents) {
      this.events = this.events.slice(-this.maxEvents);
    }

    // Capture trend data if interval has passed
    if (Date.now() - this.lastTrendCapture > this.trendInterval) {
      this.captureTrendData();
    }

    if (import.meta.env.DEV) {
      console.log('[VideoPerformanceAnalytics] Event tracked:', fullEvent.type, fullEvent.videoId);
    }
  }

  /**
   * Get current performance metrics
   */
  getCurrentMetrics(): PerformanceMetrics {
    return this.calculateMetrics(this.events);
  }

  /**
   * Get performance metrics for a specific time range
   */
  getMetricsForTimeRange(startTime: number, endTime: number): PerformanceMetrics {
    const filteredEvents = this.events.filter(
      event => event.timestamp >= startTime && event.timestamp <= endTime
    );
    return this.calculateMetrics(filteredEvents);
  }

  /**
   * Get performance metrics by device type
   */
  getMetricsByDevice(): DeviceMetrics[] {
    const deviceGroups = new Map<string, PerformanceEvent[]>();

    // Group events by device characteristics
    for (const event of this.events) {
      if (event.metadata) {
        const key = `${event.metadata.deviceType || 'unknown'}-${event.metadata.connectionType || 'unknown'}-${event.metadata.deviceMemory || 0}`;
        if (!deviceGroups.has(key)) {
          deviceGroups.set(key, []);
        }
        deviceGroups.get(key)!.push(event);
      }
    }

    // Calculate metrics for each device group
    const deviceMetrics: DeviceMetrics[] = [];
    for (const [key, events] of deviceGroups.entries()) {
      const [deviceType, connectionType, deviceMemory] = key.split('-');
      deviceMetrics.push({
        deviceType,
        connectionType,
        deviceMemory: parseInt(deviceMemory),
        metrics: this.calculateMetrics(events),
        sampleSize: events.length,
      });
    }

    return deviceMetrics.sort((a, b) => b.sampleSize - a.sampleSize);
  }

  /**
   * Get performance trends over time
   */
  getTrends(): TrendData[] {
    return [...this.trends];
  }

  /**
   * Generate performance insights and recommendations
   */
  generateInsights(): PerformanceInsight[] {
    const insights: PerformanceInsight[] = [];
    const metrics = this.getCurrentMetrics();
    const deviceMetrics = this.getMetricsByDevice();

    // Cache performance insights
    if (metrics.cacheHitRate < 70) {
      insights.push({
        id: 'low-cache-hit-rate',
        type: 'optimization',
        title: 'Low Cache Hit Rate',
        description: `Cache hit rate is ${metrics.cacheHitRate.toFixed(1)}%, which is below optimal.`,
        impact: 'high',
        recommendation: 'Consider increasing cache TTL or implementing cache warming strategies.',
        data: { cacheHitRate: metrics.cacheHitRate },
      });
    }

    // Load time insights
    if (metrics.averageLoadTime > 2000) {
      insights.push({
        id: 'slow-load-times',
        type: 'warning',
        title: 'Slow Load Times',
        description: `Average load time is ${metrics.averageLoadTime.toFixed(0)}ms, which may impact user experience.`,
        impact: 'high',
        recommendation: 'Optimize video compression, implement CDN, or improve server response times.',
        data: { averageLoadTime: metrics.averageLoadTime },
      });
    }

    // Error rate insights
    if (metrics.errorRate > 5) {
      insights.push({
        id: 'high-error-rate',
        type: 'warning',
        title: 'High Error Rate',
        description: `Error rate is ${metrics.errorRate.toFixed(1)}%, indicating potential reliability issues.`,
        impact: 'high',
        recommendation: 'Investigate error patterns and improve error handling and retry mechanisms.',
        data: { errorRate: metrics.errorRate },
      });
    }

    // Lazy loading insights
    if (metrics.lazyLoadRate < 50) {
      insights.push({
        id: 'low-lazy-loading',
        type: 'optimization',
        title: 'Low Lazy Loading Efficiency',
        description: `Only ${metrics.lazyLoadRate.toFixed(1)}% of videos are being lazy loaded.`,
        impact: 'medium',
        recommendation: 'Adjust lazy loading thresholds and root margins for better efficiency.',
        data: { lazyLoadRate: metrics.lazyLoadRate },
      });
    }

    // Device-specific insights
    const mobileMetrics = deviceMetrics.find(d => d.deviceType === 'mobile');
    const desktopMetrics = deviceMetrics.find(d => d.deviceType === 'desktop');

    if (mobileMetrics && desktopMetrics && mobileMetrics.metrics.averageLoadTime > desktopMetrics.metrics.averageLoadTime * 1.5) {
      insights.push({
        id: 'mobile-performance-gap',
        type: 'optimization',
        title: 'Mobile Performance Gap',
        description: 'Mobile devices are experiencing significantly slower load times than desktop.',
        impact: 'medium',
        recommendation: 'Implement mobile-specific optimizations such as reduced quality or aggressive lazy loading.',
        data: {
          mobileLoadTime: mobileMetrics.metrics.averageLoadTime,
          desktopLoadTime: desktopMetrics.metrics.averageLoadTime,
        },
      });
    }

    // Bandwidth savings insights
    if (metrics.bandwidthSaved > 50 * 1024 * 1024) { // > 50MB
      insights.push({
        id: 'excellent-bandwidth-savings',
        type: 'info',
        title: 'Excellent Bandwidth Savings',
        description: `Lazy loading has saved ${this.formatBytes(metrics.bandwidthSaved)} of bandwidth.`,
        impact: 'low',
        data: { bandwidthSaved: metrics.bandwidthSaved },
      });
    }

    return insights.sort((a, b) => {
      const impactOrder = { high: 3, medium: 2, low: 1 };
      return impactOrder[b.impact] - impactOrder[a.impact];
    });
  }

  /**
   * Export performance data for reporting
   */
  exportData(format: 'json' | 'csv' = 'json'): string {
    const data = {
      summary: this.getCurrentMetrics(),
      deviceBreakdown: this.getMetricsByDevice(),
      trends: this.getTrends(),
      insights: this.generateInsights(),
      exportTimestamp: Date.now(),
      totalEvents: this.events.length,
    };

    if (format === 'json') {
      return JSON.stringify(data, null, 2);
    } else {
      // Simple CSV export for metrics
      const metrics = this.getCurrentMetrics();
      const csv = [
        'Metric,Value',
        `Total Events,${metrics.totalEvents}`,
        `Load Events,${metrics.loadEvents}`,
        `Error Events,${metrics.errorEvents}`,
        `Average Load Time,${metrics.averageLoadTime}`,
        `Error Rate,${metrics.errorRate}%`,
        `Cache Hit Rate,${metrics.cacheHitRate}%`,
        `Lazy Load Rate,${metrics.lazyLoadRate}%`,
        `Bandwidth Saved,${this.formatBytes(metrics.bandwidthSaved)}`,
      ].join('\n');
      return csv;
    }
  }

  /**
   * Calculate performance metrics from events
   */
  private calculateMetrics(events: PerformanceEvent[]): PerformanceMetrics {
    const loadEvents = events.filter(e => e.type === 'load_complete');
    const errorEvents = events.filter(e => e.type === 'load_error');
    const cacheHits = events.filter(e => e.type === 'cache_hit');
    const cacheMisses = events.filter(e => e.type === 'cache_miss');
    const lazyLoads = events.filter(e => e.type === 'lazy_load');

    const loadTimes = loadEvents
      .map(e => e.duration)
      .filter((d): d is number => d !== undefined)
      .sort((a, b) => a - b);

    const averageLoadTime = loadTimes.length > 0 
      ? loadTimes.reduce((sum, time) => sum + time, 0) / loadTimes.length 
      : 0;

    const medianLoadTime = loadTimes.length > 0 
      ? loadTimes[Math.floor(loadTimes.length / 2)] 
      : 0;

    const p95LoadTime = loadTimes.length > 0 
      ? loadTimes[Math.floor(loadTimes.length * 0.95)] 
      : 0;

    const totalLoadAttempts = loadEvents.length + errorEvents.length;
    const errorRate = totalLoadAttempts > 0 ? (errorEvents.length / totalLoadAttempts) * 100 : 0;

    const totalCacheAttempts = cacheHits.length + cacheMisses.length;
    const cacheHitRate = totalCacheAttempts > 0 ? (cacheHits.length / totalCacheAttempts) * 100 : 0;

    const totalVideos = new Set(events.map(e => e.videoId)).size;
    const lazyLoadRate = totalVideos > 0 ? (lazyLoads.length / totalVideos) * 100 : 0;

    // Estimate bandwidth saved (rough calculation)
    const averageVideoSize = 5 * 1024 * 1024; // 5MB average
    const bandwidthSaved = lazyLoads.length * averageVideoSize * 0.6; // Assume 60% savings

    return {
      totalEvents: events.length,
      loadEvents: loadEvents.length,
      errorEvents: errorEvents.length,
      cacheHits: cacheHits.length,
      cacheMisses: cacheMisses.length,
      lazyLoads: lazyLoads.length,
      averageLoadTime,
      medianLoadTime,
      p95LoadTime,
      errorRate,
      cacheHitRate,
      lazyLoadRate,
      bandwidthSaved,
    };
  }

  /**
   * Capture trend data point
   */
  private captureTrendData(): void {
    const trendPoint: TrendData = {
      timestamp: Date.now(),
      metrics: this.getCurrentMetrics(),
    };

    this.trends.push(trendPoint);

    // Maintain trend limit
    if (this.trends.length > this.maxTrends) {
      this.trends = this.trends.slice(-this.maxTrends);
    }

    this.lastTrendCapture = Date.now();
  }

  /**
   * Start automatic trend capture
   */
  private startTrendCapture(): void {
    setInterval(() => {
      if (this.events.length > 0) {
        this.captureTrendData();
      }
    }, this.trendInterval);
  }

  /**
   * Generate unique event ID
   */
  private generateEventId(): string {
    return `${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
  }

  /**
   * Format bytes for display
   */
  private formatBytes(bytes: number): string {
    if (bytes === 0) return '0 B';
    const k = 1024;
    const sizes = ['B', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(1)) + ' ' + sizes[i];
  }

  /**
   * Clear all analytics data
   */
  clear(): void {
    this.events = [];
    this.trends = [];
    this.lastTrendCapture = 0;
    
    if (import.meta.env.DEV) {
      console.log('[VideoPerformanceAnalytics] Analytics data cleared');
    }
  }
}

// Export singleton instance
export const videoPerformanceAnalytics = new VideoPerformanceAnalytics();
export default videoPerformanceAnalytics;
