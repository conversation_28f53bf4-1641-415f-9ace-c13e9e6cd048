/**
 * Cleanup utilities for VibeNecto backend
 * Consolidates resource cleanup and maintenance functions
 */

const { logger } = require('./logger');
const { TIME_INTERVALS } = require('../constants');

/**
 * Generic database cleanup function
 */
async function cleanupDatabaseRecords(supabase, table, conditions, options = {}) {
  const {
    requestId,
    operation = 'cleanup',
    logContext = {}
  } = options;
  
  try {
    logger.info(`Starting ${operation} for ${table}`, {
      conditions,
      ...logContext,
      requestId
    });
    
    let query = supabase.from(table);
    
    // Apply conditions
    for (const [column, condition] of Object.entries(conditions)) {
      if (condition.operator === 'eq') {
        query = query.eq(column, condition.value);
      } else if (condition.operator === 'lt') {
        query = query.lt(column, condition.value);
      } else if (condition.operator === 'gt') {
        query = query.gt(column, condition.value);
      } else if (condition.operator === 'in') {
        query = query.in(column, condition.value);
      }
    }
    
    const { data, error } = await query.delete();
    
    if (error) {
      logger.error(`${operation} failed for ${table}`, {
        error: error.message,
        conditions,
        ...logContext,
        requestId
      });
      return {
        success: false,
        error: error.message
      };
    }
    
    const deletedCount = Array.isArray(data) ? data.length : 0;
    logger.info(`${operation} completed for ${table}`, {
      deletedCount,
      conditions,
      ...logContext,
      requestId
    });
    
    return {
      success: true,
      deletedCount
    };
    
  } catch (error) {
    logger.error(`${operation} exception for ${table}`, {
      error: error.message,
      conditions,
      ...logContext,
      requestId
    });
    
    return {
      success: false,
      error: error.message
    };
  }
}

/**
 * Clean up failed video generation jobs
 */
async function cleanupFailedVideoJobs(supabase, options = {}) {
  const { requestId } = options;
  
  try {
    logger.info('Starting failed video jobs cleanup', { requestId });
    
    // Find videos that have been pending for more than the timeout period
    const timeoutThreshold = new Date(Date.now() - TIME_INTERVALS.VIDEO_CLEANUP_TIMEOUT).toISOString();
    
    const { data: pendingVideos, error: fetchError } = await supabase
      .from('video_history')
      .select('id, job_id, s3_key')
      .eq('status', 'pending')
      .lt('created_at', timeoutThreshold);

    if (fetchError) {
      logger.error('Error fetching pending videos for cleanup', {
        error: fetchError.message,
        requestId
      });
      return {
        success: false,
        error: fetchError.message
      };
    }

    if (!pendingVideos || pendingVideos.length === 0) {
      logger.info('No pending videos found for cleanup', { requestId });
      return {
        success: true,
        processedCount: 0
      };
    }

    logger.info(`Found ${pendingVideos.length} pending videos to cleanup`, { requestId });
    let processedCount = 0;

    for (const video of pendingVideos) {
      try {
        // Check if the job is actually still running
        const { checkVideoGenerationStatus } = require('../services/bedrock-service');
        const statusResult = await checkVideoGenerationStatus({ jobId: video.job_id });
        
        if (statusResult.success) {
          // Update the status based on actual AWS status
          await supabase
            .from('video_history')
            .update({
              status: statusResult.status,
              error_message: statusResult.error || null,
              completed_at: statusResult.status === 'completed' || statusResult.status === 'failed' 
                ? new Date().toISOString() 
                : null
            })
            .eq('id', video.id);
        } else {
          // Mark as failed if we can't check status
          await supabase
            .from('video_history')
            .update({
              status: 'failed',
              error_message: 'Job status check failed during cleanup',
              completed_at: new Date().toISOString()
            })
            .eq('id', video.id);
        }
        
        processedCount++;
      } catch (error) {
        logger.error('Error during individual video cleanup', {
          videoId: video.id,
          jobId: video.job_id,
          error: error.message,
          requestId
        });
      }
    }

    logger.info('Failed video jobs cleanup completed', {
      processedCount,
      totalFound: pendingVideos.length,
      requestId
    });

    return {
      success: true,
      processedCount,
      totalFound: pendingVideos.length
    };

  } catch (error) {
    logger.error('Error in failed video jobs cleanup', {
      error: error.message,
      requestId
    });
    
    return {
      success: false,
      error: error.message
    };
  }
}

/**
 * Clean up old failed videos
 */
async function cleanupOldFailedVideos(supabase, options = {}) {
  const { requestId } = options;
  
  try {
    const cleanupThreshold = new Date(Date.now() - TIME_INTERVALS.OLD_VIDEO_CLEANUP).toISOString();
    
    const result = await cleanupDatabaseRecords(
      supabase,
      'video_history',
      {
        status: { operator: 'eq', value: 'failed' },
        completed_at: { operator: 'lt', value: cleanupThreshold }
      },
      {
        requestId,
        operation: 'old failed videos cleanup',
        logContext: { cleanupThreshold }
      }
    );

    return result;
  } catch (error) {
    logger.error('Error in old failed videos cleanup', {
      error: error.message,
      requestId
    });
    
    return {
      success: false,
      error: error.message
    };
  }
}

/**
 * Clean up temporary files and resources
 */
async function cleanupTemporaryResources(options = {}) {
  const { requestId, resourcePaths = [] } = options;
  
  try {
    logger.info('Starting temporary resources cleanup', {
      resourceCount: resourcePaths.length,
      requestId
    });
    
    let cleanedCount = 0;
    const errors = [];
    
    for (const resourcePath of resourcePaths) {
      try {
        const fs = require('fs').promises;
        await fs.unlink(resourcePath);
        cleanedCount++;
        
        logger.debug('Temporary resource cleaned', {
          resourcePath,
          requestId
        });
      } catch (error) {
        if (error.code !== 'ENOENT') { // Ignore "file not found" errors
          errors.push({
            resourcePath,
            error: error.message
          });
          
          logger.warn('Failed to clean temporary resource', {
            resourcePath,
            error: error.message,
            requestId
          });
        }
      }
    }
    
    logger.info('Temporary resources cleanup completed', {
      cleanedCount,
      errorCount: errors.length,
      requestId
    });
    
    return {
      success: true,
      cleanedCount,
      errors
    };
    
  } catch (error) {
    logger.error('Error in temporary resources cleanup', {
      error: error.message,
      requestId
    });
    
    return {
      success: false,
      error: error.message
    };
  }
}

/**
 * Comprehensive cleanup function that runs all cleanup operations
 */
async function runComprehensiveCleanup(supabase, options = {}) {
  const { requestId } = options;
  
  logger.info('Starting comprehensive cleanup', { requestId });
  const startTime = Date.now();
  const results = {};
  
  try {
    // Clean up failed video jobs
    results.failedVideoJobs = await cleanupFailedVideoJobs(supabase, { requestId });
    
    // Clean up old failed videos
    results.oldFailedVideos = await cleanupOldFailedVideos(supabase, { requestId });
    
    // Clean up temporary resources (if any paths provided)
    if (options.temporaryPaths && options.temporaryPaths.length > 0) {
      results.temporaryResources = await cleanupTemporaryResources({
        requestId,
        resourcePaths: options.temporaryPaths
      });
    }
    
    const totalTime = Date.now() - startTime;
    
    logger.info('Comprehensive cleanup completed', {
      totalTime,
      results: {
        failedVideoJobs: results.failedVideoJobs?.success,
        oldFailedVideos: results.oldFailedVideos?.success,
        temporaryResources: results.temporaryResources?.success
      },
      requestId
    });
    
    return {
      success: true,
      totalTime,
      results
    };
    
  } catch (error) {
    const totalTime = Date.now() - startTime;
    
    logger.error('Error in comprehensive cleanup', {
      error: error.message,
      totalTime,
      requestId
    });
    
    return {
      success: false,
      error: error.message,
      totalTime,
      results
    };
  }
}

/**
 * Schedule cleanup operations
 */
function scheduleCleanupOperations(supabase, options = {}) {
  const {
    cleanupInterval = TIME_INTERVALS.CLEANUP_INTERVAL,
    initialDelay = TIME_INTERVALS.INITIAL_CLEANUP_DELAY
  } = options;
  
  logger.info('Scheduling cleanup operations', {
    cleanupInterval,
    initialDelay
  });
  
  // Run comprehensive cleanup at regular intervals
  const cleanupTimer = setInterval(async () => {
    await runComprehensiveCleanup(supabase, {
      requestId: `cleanup-${Date.now()}`
    });
  }, cleanupInterval);
  
  // Run initial cleanup after delay
  const initialTimer = setTimeout(async () => {
    await runComprehensiveCleanup(supabase, {
      requestId: `initial-cleanup-${Date.now()}`
    });
  }, initialDelay);
  
  // Return cleanup function to stop timers
  return function stopCleanup() {
    clearInterval(cleanupTimer);
    clearTimeout(initialTimer);
    logger.info('Cleanup operations stopped');
  };
}

module.exports = {
  cleanupDatabaseRecords,
  cleanupFailedVideoJobs,
  cleanupOldFailedVideos,
  cleanupTemporaryResources,
  runComprehensiveCleanup,
  scheduleCleanupOperations
};