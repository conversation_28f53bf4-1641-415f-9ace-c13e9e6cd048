import { useEffect, useRef } from 'react';

/**
 * Custom hook to manage audio playback and ensure proper cleanup
 * This hook helps prevent audio from continuing to play when navigating away from pages
 */
export const useAudioManager = () => {
  const audioRef = useRef<HTMLAudioElement | null>(null);

  const stopAllAudio = () => {
    // Stop the managed audio element
    if (audioRef.current && !audioRef.current.paused) {
      audioRef.current.pause();
      audioRef.current.currentTime = 0;
    }

    // Also stop any other audio elements in the document
    const audioElements = document.querySelectorAll('audio');
    audioElements.forEach(audio => {
      if (!audio.paused) {
        audio.pause();
        audio.currentTime = 0;
      }
    });
  };

  const setAudioElement = (audio: HTMLAudioElement | null) => {
    // Stop previous audio if exists
    if (audioRef.current && !audioRef.current.paused) {
      audioRef.current.pause();
      audioRef.current.currentTime = 0;
    }
    audioRef.current = audio;
  };

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      stopAllAudio();
    };
  }, []);

  return {
    stopAllAudio,
    setAudioElement,
    currentAudio: audioRef.current
  };
};