import React from "react";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>rigger, <PERSON><PERSON>Content } from "@/components/ui/tabs";
import VideoGenerationForm from "@/components/VideoGenerationForm";
import VideoStatusTracker from "@/components/VideoStatusTracker";
import VideoResultDisplay from "@/components/VideoResultDisplay";
import { useAuth } from "@/contexts/AuthContext";
import { ActiveJob, CompletedVideo } from "@/hooks/useVideoGeneration";
import { VIDEO_GENERATOR_CONFIG } from "@/constants/videoGenerator";

interface VideoGeneratorTabsProps {
  activeTab: string;
  onTabChange: (tab: string) => void;
  activeJobs: ActiveJob[];
  completedVideos: CompletedVideo[];
  onVideoGenerated: (jobId: string, videoId: string, estimatedCompletionTime: string) => void;
  onVideoComplete: (videoUrl: string, videoId: string, jobId: string) => void;
  onVideoError: (error: string, jobId: string) => void;
  onDownload: (videoUrl: string) => void;
  onGenerateNew: () => void;
}

const VideoGeneratorTabs: React.FC<VideoGeneratorTabsProps> = ({
  activeTab,
  onTabChange,
  activeJobs,
  completedVideos,
  onVideoGenerated,
  onVideoComplete,
  onVideoError,
  onDownload,
  onGenerateNew,
}) => {
  const { user } = useAuth();

  return (
    <div className="flex-1 flex flex-col">
      <Tabs value={activeTab} onValueChange={onTabChange}>
        <TabsList className="grid grid-cols-2 w-full max-w-md mx-auto mb-6">
          <TabsTrigger value="generate">
            {VIDEO_GENERATOR_CONFIG.GENERATE_TAB}
          </TabsTrigger>
          <TabsTrigger 
            value="result" 
            disabled={activeJobs.length === 0 && completedVideos.length === 0}
          >
            {VIDEO_GENERATOR_CONFIG.RESULT_TAB}
          </TabsTrigger>
        </TabsList>

        <TabsContent value="generate" className="m-0">
          {/* Video Generation Form - Centered and Responsive */}
          <div className="max-w-4xl mx-auto">
            <div className="bg-white dark:bg-gray-800 rounded-lg p-6 shadow-sm">
              <h3 className="text-lg font-medium mb-4 text-center">Create Your Video</h3>
              <VideoGenerationForm onVideoGenerated={onVideoGenerated} />
            </div>

            {/* Completed Videos Preview in Generate Tab */}
            <VideoResultDisplay
              completedVideos={completedVideos}
              onDownload={onDownload}
              onGenerateNew={onGenerateNew}
              showInGenerateTab={true}
            />
          </div>
        </TabsContent>

        <TabsContent value="result" className="m-0">
          {/* Active Jobs Display - Generating Video UI */}
          {activeJobs.length > 0 && (
            <div className="max-w-4xl mx-auto">
              <div className="bg-white dark:bg-gray-800 rounded-lg p-6 shadow-sm">
                <h2 className="text-xl font-semibold text-center mb-4">
                  {VIDEO_GENERATOR_CONFIG.GENERATING_MESSAGE}
                </h2>
                <div className="space-y-4">
                  {activeJobs.map((job) => (
                    <VideoStatusTracker
                      key={job.jobId}
                      jobId={job.jobId}
                      userId={user?.id || ''}
                      estimatedCompletionTime={job.estimatedCompletionTime}
                      onComplete={(videoUrl, videoId) => onVideoComplete(videoUrl, videoId, job.jobId)}
                      onError={(error) => onVideoError(error, job.jobId)}
                    />
                  ))}
                </div>
              </div>
            </div>
          )}

          {/* Completed Videos Display in Result Tab */}
          {completedVideos.length > 0 && (
            <VideoResultDisplay
              completedVideos={completedVideos}
              onDownload={onDownload}
              onGenerateNew={onGenerateNew}
              showInGenerateTab={false}
            />
          )}
        </TabsContent>
      </Tabs>
    </div>
  );
};

export default VideoGeneratorTabs;