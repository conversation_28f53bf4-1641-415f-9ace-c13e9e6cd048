const { logger, logPerformance } = require('./logger');

// Metrics storage
const metrics = {
  requests: {
    total: 0,
    successful: 0,
    failed: 0,
    responseTimes: []
  },
  videoGeneration: {
    total: 0,
    successful: 0,
    failed: 0,
    processingTimes: [],
    byType: {
      'TEXT_VIDEO': { total: 0, successful: 0, failed: 0 },
      'MULTI_SHOT_AUTOMATED': { total: 0, successful: 0, failed: 0 },
      'MULTI_SHOT_MANUAL': { total: 0, successful: 0, failed: 0 }
    }
  },
  imageGeneration: {
    total: 0,
    successful: 0,
    failed: 0,
    processingTimes: [],
    byType: {
      'standard': { total: 0, successful: 0, failed: 0 },
      'premium': { total: 0, successful: 0, failed: 0 }
    }
  },
  database: {
    queries: 0,
    slowQueries: 0,
    queryTimes: [],
    errors: 0
  },
  s3: {
    uploads: 0,
    downloads: 0,
    errors: 0,
    uploadTimes: [],
    downloadTimes: []
  },
  system: {
    memoryUsage: [],
    cpuUsage: [],
    uptime: process.uptime()
  }
};

// Helper function to calculate statistics
const calculateStats = (values) => {
  if (values.length === 0) return { avg: 0, min: 0, max: 0, p95: 0 };
  
  const sorted = [...values].sort((a, b) => a - b);
  const sum = values.reduce((a, b) => a + b, 0);
  
  return {
    avg: Math.round(sum / values.length),
    min: sorted[0],
    max: sorted[sorted.length - 1],
    p95: sorted[Math.floor(sorted.length * 0.95)] || sorted[sorted.length - 1]
  };
};

// Metrics collection functions
const metricsCollector = {
  // Record HTTP request metrics
  recordRequest: (req, res, responseTime) => {
    metrics.requests.total++;
    metrics.requests.responseTimes.push(responseTime);
    
    if (res.statusCode >= 200 && res.statusCode < 400) {
      metrics.requests.successful++;
    } else {
      metrics.requests.failed++;
    }
    
    // Keep only last 1000 response times
    if (metrics.requests.responseTimes.length > 1000) {
      metrics.requests.responseTimes = metrics.requests.responseTimes.slice(-1000);
    }
    
    // Log slow requests
    if (responseTime > 5000) {
      logPerformance('slow_request', responseTime, {
        url: req.url,
        method: req.method,
        statusCode: res.statusCode
      });
    }
  },

  // Record video generation metrics
  recordVideoGeneration: (type, success, processingTime = null) => {
    metrics.videoGeneration.total++;
    metrics.videoGeneration.byType[type].total++;
    
    if (success) {
      metrics.videoGeneration.successful++;
      metrics.videoGeneration.byType[type].successful++;
      
      if (processingTime) {
        metrics.videoGeneration.processingTimes.push(processingTime);
        
        // Keep only last 500 processing times
        if (metrics.videoGeneration.processingTimes.length > 500) {
          metrics.videoGeneration.processingTimes = 
            metrics.videoGeneration.processingTimes.slice(-500);
        }
      }
    } else {
      metrics.videoGeneration.failed++;
      metrics.videoGeneration.byType[type].failed++;
    }
  },

  // Record image generation metrics
  recordImageGeneration: (type, success, processingTime = null) => {
    metrics.imageGeneration.total++;
    metrics.imageGeneration.byType[type].total++;
    
    if (success) {
      metrics.imageGeneration.successful++;
      metrics.imageGeneration.byType[type].successful++;
      
      if (processingTime) {
        metrics.imageGeneration.processingTimes.push(processingTime);
        
        // Keep only last 500 processing times
        if (metrics.imageGeneration.processingTimes.length > 500) {
          metrics.imageGeneration.processingTimes = 
            metrics.imageGeneration.processingTimes.slice(-500);
        }
      }
    } else {
      metrics.imageGeneration.failed++;
      metrics.imageGeneration.byType[type].failed++;
    }
  },

  // Record database operation metrics
  recordDatabaseOperation: (queryTime, success = true) => {
    metrics.database.queries++;
    metrics.database.queryTimes.push(queryTime);
    
    if (queryTime > 1000) {
      metrics.database.slowQueries++;
    }
    
    if (!success) {
      metrics.database.errors++;
    }
    
    // Keep only last 1000 query times
    if (metrics.database.queryTimes.length > 1000) {
      metrics.database.queryTimes = metrics.database.queryTimes.slice(-1000);
    }
  },

  // Record S3 operation metrics
  recordS3Operation: (operation, time, success = true) => {
    if (operation === 'upload') {
      metrics.s3.uploads++;
      metrics.s3.uploadTimes.push(time);
      
      // Keep only last 500 upload times
      if (metrics.s3.uploadTimes.length > 500) {
        metrics.s3.uploadTimes = metrics.s3.uploadTimes.slice(-500);
      }
    } else if (operation === 'download') {
      metrics.s3.downloads++;
      metrics.s3.downloadTimes.push(time);
      
      // Keep only last 500 download times
      if (metrics.s3.downloadTimes.length > 500) {
        metrics.s3.downloadTimes = metrics.s3.downloadTimes.slice(-500);
      }
    }
    
    if (!success) {
      metrics.s3.errors++;
    }
  },

  // Record system metrics
  recordSystemMetrics: () => {
    const memUsage = process.memoryUsage();
    const memUsageInMB = {
      rss: Math.round(memUsage.rss / 1024 / 1024),
      heapTotal: Math.round(memUsage.heapTotal / 1024 / 1024),
      heapUsed: Math.round(memUsage.heapUsed / 1024 / 1024),
      external: Math.round(memUsage.external / 1024 / 1024)
    };
    
    metrics.system.memoryUsage.push(memUsageInMB);
    metrics.system.uptime = process.uptime();
    
    // Keep only last 100 memory readings
    if (metrics.system.memoryUsage.length > 100) {
      metrics.system.memoryUsage = metrics.system.memoryUsage.slice(-100);
    }
    
    // Alert on high memory usage
    if (memUsageInMB.heapUsed > 800) { // Alert if using more than 800MB
      logPerformance('high_memory_usage', memUsageInMB.heapUsed, memUsageInMB);
    }
  }
};

// Get current metrics summary
const getMetricsSummary = () => {
  const summary = {
    timestamp: new Date().toISOString(),
    uptime: Math.round(process.uptime()),
    requests: {
      total: metrics.requests.total,
      successful: metrics.requests.successful,
      failed: metrics.requests.failed,
      successRate: metrics.requests.total > 0 
        ? Math.round((metrics.requests.successful / metrics.requests.total) * 100) 
        : 0,
      responseTime: calculateStats(metrics.requests.responseTimes)
    },
    videoGeneration: {
      total: metrics.videoGeneration.total,
      successful: metrics.videoGeneration.successful,
      failed: metrics.videoGeneration.failed,
      successRate: metrics.videoGeneration.total > 0 
        ? Math.round((metrics.videoGeneration.successful / metrics.videoGeneration.total) * 100) 
        : 0,
      processingTime: calculateStats(metrics.videoGeneration.processingTimes),
      byType: metrics.videoGeneration.byType
    },
    imageGeneration: {
      total: metrics.imageGeneration.total,
      successful: metrics.imageGeneration.successful,
      failed: metrics.imageGeneration.failed,
      successRate: metrics.imageGeneration.total > 0 
        ? Math.round((metrics.imageGeneration.successful / metrics.imageGeneration.total) * 100) 
        : 0,
      processingTime: calculateStats(metrics.imageGeneration.processingTimes),
      byType: metrics.imageGeneration.byType
    },
    database: {
      queries: metrics.database.queries,
      slowQueries: metrics.database.slowQueries,
      errors: metrics.database.errors,
      queryTime: calculateStats(metrics.database.queryTimes)
    },
    s3: {
      uploads: metrics.s3.uploads,
      downloads: metrics.s3.downloads,
      errors: metrics.s3.errors,
      uploadTime: calculateStats(metrics.s3.uploadTimes),
      downloadTime: calculateStats(metrics.s3.downloadTimes)
    },
    system: {
      uptime: Math.round(process.uptime()),
      memoryUsage: metrics.system.memoryUsage.length > 0 
        ? metrics.system.memoryUsage[metrics.system.memoryUsage.length - 1] 
        : null
    }
  };
  
  return summary;
};

// Start periodic metrics collection
const startMetricsCollection = () => {
  // Collect system metrics every 30 seconds
  setInterval(() => {
    metricsCollector.recordSystemMetrics();
  }, 30000);
  
  // Log metrics summary every 5 minutes
  setInterval(() => {
    const summary = getMetricsSummary();
    logPerformance('metrics_summary', null, summary);
  }, 5 * 60 * 1000);
  
  logger.info('Metrics collection started');
};

module.exports = {
  metricsCollector,
  getMetricsSummary,
  startMetricsCollection
};
