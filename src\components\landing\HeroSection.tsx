import { useState } from "react";
import { useNavigate } from "react-router-dom";
import { Button } from "@/components/ui/button";
import { Sparkles } from "lucide-react";
import { useAuth } from "@/contexts/AuthContext";

const HeroSection = () => {
  const navigate = useNavigate();
  const { isAuthenticated } = useAuth();

  // Handle "Try This" button clicks
  const handleTryThis = () => {
    if (isAuthenticated) {
      navigate("/dashboard");
    } else {
      navigate("/signin");
    }
  };

  return (
    <section className="relative overflow-hidden min-h-screen">
      {/* Subtle integrated navbar */}
      <div className="relative z-30 w-full py-6 px-4 md:px-8">
        <div className="container mx-auto flex justify-between items-center">
          <div className="flex items-center gap-3">
            <img src="/logo.png" alt="VibeNecto Logo" className="h-12 w-auto brightness-125 contrast-125" />
            <h2 className="text-2xl font-semibold bg-gradient-to-r from-cyan-400 to-purple-400 bg-clip-text text-transparent">VibeNecto</h2>
          </div>
          <div className="flex items-center gap-6">
            <Button
              variant="ghost"
              onClick={() => navigate("/signin")}
              className="text-cyan-300 hover:text-cyan-200 hover:bg-cyan-500/10 border border-cyan-400/30 rounded-full"
            >
              Sign In
            </Button>
            <Button
              onClick={() => navigate("/signup")}
              className="bg-gradient-to-r from-pink-500 to-violet-500 text-white hover:from-pink-400 hover:to-violet-400 rounded-full px-6 shadow-lg"
            >
              Sign Up
            </Button>
          </div>
        </div>
      </div>

      <div className="container relative z-20 mx-auto px-4 md:px-8 pt-20 md:pt-32">
        {/* Hero content - Apple style */}
        <div className="text-center mb-20 md:mb-24">
          <div className="inline-block mb-6 px-4 py-1.5 bg-gradient-to-r from-yellow-500/20 to-orange-500/20 backdrop-blur-md border border-yellow-400/30 rounded-full text-sm font-medium">
            <span className="flex items-center gap-2">
              <Sparkles className="w-3.5 h-3.5 text-yellow-400" />
              <span className="bg-gradient-to-r from-yellow-300 to-orange-300 bg-clip-text text-transparent font-semibold">AI-Powered Vibe Marketing</span>
            </span>
          </div>

          <h1 className="text-4xl md:text-6xl lg:text-7xl font-semibold mb-8 leading-tight tracking-tight">
            <span className="bg-gradient-to-r from-cyan-400 to-blue-400 bg-clip-text text-transparent">Create</span> <span className="bg-gradient-to-r from-purple-400 to-pink-400 bg-clip-text text-transparent">marketing</span> <br className="hidden md:block" />
            <span className="bg-gradient-to-r from-green-400 to-emerald-400 bg-clip-text text-transparent">content</span> <span className="text-white">with the perfect</span> <span className="bg-gradient-to-r from-yellow-400 to-orange-400 bg-clip-text text-transparent">vibe</span><span className="text-white">.</span>
          </h1>

          <p className="text-lg md:text-xl text-white/90 mb-12 max-w-2xl mx-auto leading-relaxed">
            Just like <span className="bg-gradient-to-r from-blue-400 to-cyan-400 bg-clip-text text-transparent font-semibold">vibe coding</span>, we bring <span className="bg-gradient-to-r from-purple-400 to-pink-400 bg-clip-text text-transparent font-semibold">vibe marketing</span> to life. Generate <span className="bg-gradient-to-r from-green-400 to-emerald-400 bg-clip-text text-transparent font-semibold">stunning</span> visuals and videos that perfectly capture your brand's unique energy with our AI-powered platform.
          </p>

          {/* Product Hunt Badge */}
          <div className="mt-8 flex justify-center">
            <a href="https://www.producthunt.com/products/vibenecto?embed=true&utm_source=badge-featured&utm_medium=badge&utm_source=badge-vibenecto&#0045;2&#0045;0" target="_blank"><img src="https://api.producthunt.com/widgets/embed-image/v1/featured.svg?post_id=971120&theme=light&t=1749024754351" alt="Vibenecto&#0032;2&#0046;0 - Turn&#0032;your&#0032;words&#0032;into&#0032;videos | Product Hunt" style={{width: "250px", height: "54px"}} width="250" height="54" /></a>
          </div>
        </div>

        {/* Showcase images with prompts - Subtle style */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6 md:gap-8">
          {/* Image 1 */}
          <div className="group relative overflow-hidden rounded-2xl shadow-lg">
            <div className="absolute inset-0 bg-gradient-to-t from-black/70 via-black/30 to-transparent z-10"></div>
            <img
              src="src\Images\hero1.webp"
              alt="Anime Style"
              loading="lazy"
              className="w-full h-[26rem] object-cover transition-all duration-700 group-hover:scale-105"
            />
            <div className="absolute bottom-0 left-0 right-0 p-6 z-20">
              <div className="text-xs uppercase tracking-wider text-white/70 mb-2">Prompt</div>
              <p className="text-white text-sm mb-4 font-medium leading-relaxed">
                "A village street side vendor selling vegetables"
              </p>
              <div className="flex justify-between items-center">
                <span className="text-xs text-white/60 font-medium">Anime Style</span>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={handleTryThis}
                  className="bg-white/10 border-0 backdrop-blur-md hover:bg-white/20 rounded-full px-4 text-white"
                >
                  Try This
                </Button>
              </div>
            </div>
          </div>

          {/* Image 2 */}
          <div className="group relative overflow-hidden rounded-2xl shadow-lg">
            <div className="absolute inset-0 bg-gradient-to-t from-black/70 via-black/30 to-transparent z-10"></div>
            <img
              src="src\Images\hero2.webp"
              alt="Water Colour Style"
              loading="lazy"
              className="w-full h-[26rem] object-cover transition-all duration-700 group-hover:scale-105"
            />
            <div className="absolute bottom-0 left-0 right-0 p-6 z-20">
              <div className="text-xs uppercase tracking-wider text-white/70 mb-2">Prompt</div>
              <p className="text-white text-sm mb-4 font-medium leading-relaxed">
                "An ancient forest where massive trees stretch beyond the clouds, their roots forming intricate bridges over shimmering turquoise streams."
              </p>
              <div className="flex justify-between items-center">
                <span className="text-xs text-white/60 font-medium">Water Colour</span>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={handleTryThis}
                  className="bg-white/10 border-0 backdrop-blur-md hover:bg-white/20 rounded-full px-4 text-white"
                >
                  Try This
                </Button>
              </div>
            </div>
          </div>

          {/* Image 3 */}
          <div className="group relative overflow-hidden rounded-2xl shadow-lg">
            <div className="absolute inset-0 bg-gradient-to-t from-black/70 via-black/30 to-transparent z-10"></div>
            <img
              src="src\Images\hero3.webp"
              alt="Photorealistic"
              loading="lazy"
              className="w-full h-[26rem] object-cover transition-all duration-700 group-hover:scale-105"
            />
            <div className="absolute bottom-0 left-0 right-0 p-6 z-20">
              <div className="text-xs uppercase tracking-wider text-white/70 mb-2">Prompt</div>
              <p className="text-white text-sm mb-4 font-medium leading-relaxed">
                "A cyberpunk cityscape at night, where neon-lit holograms dance above rain-soaked streets bustling with androids and humans."
              </p>
              <div className="flex justify-between items-center">
                <span className="text-xs text-white/60 font-medium">Photorealistic</span>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={handleTryThis}
                  className="bg-white/10 border-0 backdrop-blur-md hover:bg-white/20 rounded-full px-4 text-white"
                >
                  Try This
                </Button>
              </div>
            </div>
          </div>
        </div>

        <div className="text-center mt-16 mb-8">
          <p className="text-sm tracking-wide text-white/70">
            Create your own <span className="bg-gradient-to-r from-purple-400 to-pink-400 bg-clip-text text-transparent">stunning visuals</span> in seconds
          </p>
        </div>
      </div>
    </section>
  );
};

export default HeroSection;