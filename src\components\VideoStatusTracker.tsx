import React, { useState, useEffect, useCallback, useRef } from "react";
import { Card, Card<PERSON>ontent, CardHeader, CardTitle } from "@/components/ui/card";
import { Progress } from "@/components/ui/progress";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { cn } from "@/lib/utils";
import {
  Clock,
  CheckCircle,
  XCircle,
  Loader2,
  Play,
  AlertCircle,
  RefreshCw
} from "lucide-react";
import { checkVideoStatus, VideoStatusResult, formatDuration, forceVideoFallbackCheck } from "@/services/videoService";
import { toast } from "sonner";

interface VideoStatusTrackerProps {
  jobId: string;
  userId: string;
  estimatedCompletionTime?: string;
  onComplete?: (videoUrl: string, videoId: string) => void;
  onError?: (error: string) => void;
  className?: string;
}

const VideoStatusTracker: React.FC<VideoStatusTrackerProps> = ({
  jobId,
  userId,
  estimatedCompletionTime,
  onComplete,
  onError,
  className
}) => {
  const [status, setStatus] = useState<VideoStatusResult['status']>('pending');
  const [progress, setProgress] = useState(0);
  const [timeElapsed, setTimeElapsed] = useState(0);
  const [estimatedTimeRemaining, setEstimatedTimeRemaining] = useState<number | null>(null);
  const [error, setError] = useState<string | null>(null);
  const [isPolling, setIsPolling] = useState(true);
  const [lastChecked, setLastChecked] = useState<Date>(new Date());
  const [refreshCount, setRefreshCount] = useState(0);
  const [isRefreshing, setIsRefreshing] = useState(false);

  // Calculate estimated completion time
  const estimatedCompletion = estimatedCompletionTime 
    ? new Date(estimatedCompletionTime) 
    : null;

  // Poll for status updates
  const isPollingRef = useRef(isPolling);
  const estimatedCompletionRef = useRef(estimatedCompletion);
  const onCompleteRef = useRef(onComplete);
  const onErrorRef = useRef(onError);
  const timeElapsedRef = useRef(timeElapsed);
  const estimatedTimeRemainingRef = useRef(estimatedTimeRemaining);

  useEffect(() => {
    // Debug: VideoStatusTracker initializing
    isPollingRef.current = isPolling;
    estimatedCompletionRef.current = estimatedCompletion;
    onCompleteRef.current = onComplete;
    onErrorRef.current = onError;
    timeElapsedRef.current = timeElapsed;
    estimatedTimeRemainingRef.current = estimatedTimeRemaining;
  });

  const pollStatus = useCallback(async (forceRefresh = false) => {
    if (!isPollingRef.current && !forceRefresh) {
      if (import.meta.env.DEV) {
        console.log(`[VideoStatusTracker] Polling stopped for job ${jobId}`);
      }
      return;
    }

    try {
      if (import.meta.env.DEV) {
        console.log(`[VideoStatusTracker] Starting pollStatus for job ${jobId}${forceRefresh ? ' (FORCE REFRESH)' : ''}`);
      }
      
      const result = await checkVideoStatus(jobId, userId);
      
      if (import.meta.env.DEV) {
        console.log(`[VideoStatusTracker] pollStatus received result for job ${jobId}:`, result);
      }
      
      if (result.success) {
        setStatus(result.status);
        setLastChecked(new Date());

        // Update progress based on status
        switch (result.status) {
          case 'pending':
            setProgress(10);
            break;
          case 'processing':
            // Calculate progress based on elapsed time with better handling for overruns
            if (estimatedCompletionRef.current) {
              const now = new Date();
              const estimatedCompletion = estimatedCompletionRef.current;
              const estimatedDuration = 300; // Default 5 minutes if no specific duration
              
              // Calculate start time based on estimated completion
              const startTime = new Date(estimatedCompletion.getTime() - estimatedDuration * 1000);
              const totalTime = estimatedCompletion.getTime() - startTime.getTime();
              const elapsed = now.getTime() - startTime.getTime();
              
              if (totalTime > 0 && elapsed >= 0) {
                if (elapsed <= totalTime) {
                  // Normal progress from 20% to 85% during estimated time
                  const progressRatio = elapsed / totalTime;
                  const calculatedProgress = Math.min(85, Math.max(20, 20 + (progressRatio * 65)));
                  setProgress(calculatedProgress);
                } else {
                  // Job is taking longer than estimated - show 85-95% with slow increment
                  const overrun = elapsed - totalTime;
                  const overrunProgress = Math.min(10, (overrun / (totalTime * 0.5)) * 10); // Max 10% additional progress
                  setProgress(Math.min(95, 85 + overrunProgress));
                }
              } else {
                setProgress(20); // Default progress if calculation fails
              }
            } else {
              // Fallback progress calculation based on elapsed time
              const maxTime = 600; // 10 minutes max for fallback
              const progressRatio = Math.min(1, timeElapsedRef.current / maxTime);
              setProgress(Math.min(85, 20 + (progressRatio * 65)));
            }
            break;
          case 'completed':
            if (import.meta.env.DEV) {
              console.log(`[VideoStatusTracker] Video completed! URL: ${result.videoUrl}, ID: ${result.videoId}`);
            }
            setProgress(100);
            setIsPolling(false); // Stop polling immediately
            if (result.videoUrl && result.videoId) {
              onCompleteRef.current?.(result.videoUrl, result.videoId);
              toast.success("Video generation completed!");
            } else {
              if (import.meta.env.DEV) {
                console.warn('Video completed but missing URL or ID:', result);
              }
            }
            break;
          case 'failed':
            if (import.meta.env.DEV) {
              console.log(`[VideoStatusTracker] Video failed: ${result.error}`);
            }
            setProgress(0);
            setIsPolling(false); // Stop polling immediately
            const errorMessage = result.error || 'Video generation failed';
            setError(errorMessage);
            onErrorRef.current?.(errorMessage);
            toast.error("Video generation failed");
            break;
        }
        
        if (import.meta.env.DEV) {
          console.log(`[VideoStatusTracker] pollStatus completed successfully for job ${jobId}${forceRefresh ? ' (FORCE REFRESH)' : ''}`);
        }
      } else {
        if (import.meta.env.DEV) {
          console.error('Failed to check video status:', result.error);
        }
        if (forceRefresh) {
          throw new Error(result.error || 'Failed to check video status');
        }
        // Don't stop polling on API errors, just log them
      }
    } catch (err) {
      if (import.meta.env.DEV) {
        console.error(`[VideoStatusTracker] Error in pollStatus for job ${jobId}${forceRefresh ? ' (FORCE REFRESH)' : ''}:`, err);
      }
      if (forceRefresh) {
        throw err; // Re-throw for manual refresh error handling
      }
      // Don't stop polling on network errors, just log them
    }
  }, [jobId, userId]); // Only depend on stable props/state

  // Update time elapsed and estimated time remaining
  useEffect(() => {
    const interval = setInterval(async () => {
      setTimeElapsed(prev => prev + 1);
      
      if (estimatedCompletion) {
        const now = new Date();
        const remaining = Math.floor((estimatedCompletion.getTime() - now.getTime()) / 1000);
        
        if (remaining > 0) {
          setEstimatedTimeRemaining(remaining);
        } else if (remaining <= 0 && estimatedTimeRemaining !== null && status === 'processing') {
          // Estimated time just reached 0 and we're still processing - check S3 automatically
          if (import.meta.env.DEV) {
            console.log(`[VideoStatusTracker] Estimated time reached 0 for job ${jobId}, automatically checking S3`);
          }
          
          setEstimatedTimeRemaining(null);
          
          try {
            if (import.meta.env.DEV) {
              console.log(`[VideoStatusTracker] Calling forceVideoFallbackCheck for user ${userId}`);
            }
            
            const fallbackResult = await forceVideoFallbackCheck(userId);
            
            if (import.meta.env.DEV) {
              console.log(`[VideoStatusTracker] Auto S3 check result for job ${jobId}:`, fallbackResult);
            }
            
            if (fallbackResult.success && fallbackResult.recoveredVideos && fallbackResult.recoveredVideos.length > 0) {
              // Check if our specific job was recovered
              const recoveredVideo = fallbackResult.recoveredVideos.find(v => v.jobId === jobId);
              if (recoveredVideo) {
                if (import.meta.env.DEV) {
                  console.log(`[VideoStatusTracker] Auto S3 check found completed video for job ${jobId}!`, recoveredVideo);
                }
                
                // Update status to completed and trigger completion
                setStatus('completed');
                setProgress(100);
                setIsPolling(false);
                
                if (recoveredVideo.videoUrl) {
                  onCompleteRef.current?.(recoveredVideo.videoUrl, recoveredVideo.id);
                  toast.success("🎯 Video found! Generation was completed.");
                }
              } else {
                if (import.meta.env.DEV) {
                  console.log(`[VideoStatusTracker] Auto S3 check recovered ${fallbackResult.recoveredVideos.length} videos, but not job ${jobId}`);
                }
              }
            } else {
              if (import.meta.env.DEV) {
                console.log(`[VideoStatusTracker] Auto S3 check found no recovered videos for job ${jobId}`);
              }
            }
          } catch (error) {
            if (import.meta.env.DEV) {
              console.error(`[VideoStatusTracker] Auto S3 check failed for job ${jobId}:`, error);
            }
            // Continue with normal processing, don't show error to user
          }
        } else {
          // Job is taking longer than estimated - show "Processing..." instead of negative time
          setEstimatedTimeRemaining(null);
        }
      }
    }, 1000);

    return () => clearInterval(interval);
  }, [estimatedCompletion, estimatedTimeRemaining, status, jobId, userId]);

  // Start polling with exponential backoff for errors
  useEffect(() => {
    if (isPolling && (status === 'pending' || status === 'processing')) {
      if (import.meta.env.DEV) {
        console.log(`[VideoStatusTracker] Starting polling for job ${jobId}, current status: ${status}`);
      }
      
      let pollInterval = 15000; // Start with 15 seconds
      let errorCount = 0;
      let intervalId: NodeJS.Timeout;
      let isActive = true; // Track if this effect is still active
      
      const poll = async () => {
        // Double-check polling should continue and effect is still active
        if (!isPollingRef.current || !isActive) {
          if (import.meta.env.DEV) {
            console.log(`[VideoStatusTracker] Polling stopped during execution for job ${jobId}, isPolling: ${isPollingRef.current}, isActive: ${isActive}`);
          }
          return;
        }
        
        try {
          if (import.meta.env.DEV) {
            console.log(`[VideoStatusTracker] Executing poll for job ${jobId}, attempt ${errorCount + 1}`);
          }
          
          await pollStatus();
          errorCount = 0; // Reset error count on success
          pollInterval = 15000; // Reset interval on success
          
          if (import.meta.env.DEV) {
            console.log(`[VideoStatusTracker] Poll successful for job ${jobId}, scheduling next poll in ${pollInterval}ms`);
          }
          
          // Schedule next poll only if still polling and effect is active
          if (isPollingRef.current && isActive) {
            intervalId = setTimeout(poll, pollInterval);
          }
        } catch (error) {
          errorCount++;
          if (import.meta.env.DEV) {
            console.error(`[VideoStatusTracker] Polling error for job ${jobId}, attempt ${errorCount}:`, error);
          }

          // Exponential backoff: increase interval on consecutive errors
          pollInterval = Math.min(60000, 15000 * Math.pow(2, errorCount - 1)); // Max 60 seconds
          
          if (errorCount >= 5) {
            if (import.meta.env.DEV) {
              console.error(`[VideoStatusTracker] Too many polling errors for job ${jobId}, stopping polling`);
            }
            setIsPolling(false);
            setError('Connection lost. Please refresh to check status.');
            return;
          }
          
          if (import.meta.env.DEV) {
            console.log(`[VideoStatusTracker] Scheduling retry for job ${jobId} in ${pollInterval}ms`);
          }
          
          // Schedule next poll only if still polling and effect is active
          if (isPollingRef.current && isActive) {
            intervalId = setTimeout(poll, pollInterval);
          }
        }
      };
      
      // Start initial poll
      poll();
      
      return () => {
        isActive = false; // Mark effect as inactive
        if (intervalId) {
          clearTimeout(intervalId);
        }
        if (import.meta.env.DEV) {
          console.log(`[VideoStatusTracker] Cleanup polling for job ${jobId}`);
        }
      };
    } else if (!isPolling) {
      if (import.meta.env.DEV) {
        console.log(`[VideoStatusTracker] Polling disabled for job ${jobId}, status: ${status}`);
      }
    }
  }, [isPolling, status, jobId]); // Remove pollStatus dependency to prevent effect recreation

  // Retry polling with status refresh (not full reset)
  const retryPolling = async () => {
    if (import.meta.env.DEV) {
      console.log(`[VideoStatusTracker] Manual refresh triggered for job ${jobId}, current isRefreshing: ${isRefreshing}, status: ${status}`);
    }
    
    // Prevent multiple simultaneous refreshes
    if (isRefreshing) {
      if (import.meta.env.DEV) {
        console.log(`[VideoStatusTracker] Manual refresh already in progress for job ${jobId}, ignoring`);
      }
      return;
    }
    
    // Set refreshing state
    setIsRefreshing(true);
    setRefreshCount(prev => prev + 1);
    
    // Only reset error state and last checked time
    setError(null);
    setLastChecked(new Date());
    
    try {
      if (import.meta.env.DEV) {
        console.log(`[VideoStatusTracker] Starting manual refresh for job ${jobId}`);
      }
      
      // For processing videos, try S3 fallback first
      if (status === 'processing') {
        if (import.meta.env.DEV) {
          console.log(`[VideoStatusTracker] Video is processing, trying S3 fallback check for job ${jobId}`);
        }
        
        try {
          const fallbackResult = await forceVideoFallbackCheck(userId);
          
          if (fallbackResult.success && fallbackResult.recoveredVideos && fallbackResult.recoveredVideos.length > 0) {
            // Check if our specific job was recovered
            const recoveredVideo = fallbackResult.recoveredVideos.find(v => v.jobId === jobId);
            if (recoveredVideo) {
              if (import.meta.env.DEV) {
                console.log(`[VideoStatusTracker] S3 fallback found completed video for job ${jobId}!`, recoveredVideo);
              }
              
              // Update status to completed and trigger completion
              setStatus('completed');
              setProgress(100);
              setIsPolling(false);
              
              if (recoveredVideo.videoUrl) {
                onCompleteRef.current?.(recoveredVideo.videoUrl, recoveredVideo.id);
                toast.success("🎯 Video found! Generation was completed.");
              }
              
              return; // Exit early, no need to do regular status check
            } else if (fallbackResult.recoveredVideos.length > 0) {
              if (import.meta.env.DEV) {
                console.log(`[VideoStatusTracker] S3 fallback recovered ${fallbackResult.recoveredVideos.length} other videos, but not job ${jobId}`);
              }
              toast.success(`🎯 Found ${fallbackResult.recoveredVideos.length} completed video(s)!`);
            }
          } else if (fallbackResult.success && fallbackResult.totalProcessing === 0) {
            if (import.meta.env.DEV) {
              console.log(`[VideoStatusTracker] S3 fallback found no processing videos for user`);
            }
          }
        } catch (fallbackError) {
          if (import.meta.env.DEV) {
            console.warn(`[VideoStatusTracker] S3 fallback failed for job ${jobId}:`, fallbackError);
          }
          // Continue with regular status check
        }
      }
      
      // Do regular status check (either as fallback or for non-processing videos)
      await pollStatus(true);
      
      if (import.meta.env.DEV) {
        console.log(`[VideoStatusTracker] Manual refresh completed successfully for job ${jobId}`);
      }
      toast.success("Status refreshed successfully");
    } catch (error) {
      if (import.meta.env.DEV) {
        console.error(`[VideoStatusTracker] Manual refresh failed for job ${jobId}:`, error);
      }
      toast.error("Failed to refresh status");
    } finally {
      if (import.meta.env.DEV) {
        console.log(`[VideoStatusTracker] Manual refresh cleanup for job ${jobId}, setting isRefreshing to false`);
      }
      setIsRefreshing(false);
    }
  };

  // Get status color and icon
  const getStatusDisplay = () => {
    switch (status) {
      case 'pending':
        return {
          color: 'bg-yellow-500',
          icon: Clock,
          text: 'Pending',
          description: 'Your video generation request is queued'
        };
      case 'processing':
        return {
          color: 'bg-blue-500',
          icon: Loader2,
          text: 'Processing',
          description: 'AI is generating your video'
        };
      case 'completed':
        return {
          color: 'bg-green-500',
          icon: CheckCircle,
          text: 'Completed',
          description: 'Your video is ready!'
        };
      case 'failed':
        return {
          color: 'bg-red-500',
          icon: XCircle,
          text: 'Failed',
          description: error || 'Video generation failed'
        };
      default:
        return {
          color: 'bg-gray-500',
          icon: AlertCircle,
          text: 'Unknown',
          description: 'Unknown status'
        };
    }
  };

  const statusDisplay = getStatusDisplay();
  const StatusIcon = statusDisplay.icon;

  return (
    <Card className={cn("w-full", className)}>
      <CardHeader className="pb-3">
        <CardTitle className="flex items-center gap-2 text-lg">
          <div className={cn("p-2 rounded-full", statusDisplay.color)}>
            <StatusIcon 
              className={cn(
                "h-4 w-4 text-white",
                status === 'processing' && "animate-spin"
              )} 
            />
          </div>
          Video Generation Status
          <Badge variant={status === 'completed' ? 'default' : status === 'failed' ? 'destructive' : 'secondary'}>
            {statusDisplay.text}
          </Badge>
        </CardTitle>
      </CardHeader>
      
      <CardContent className="space-y-4">
        {/* Progress Bar */}
        <div className="space-y-2">
          <div className="flex justify-between text-sm">
            <span className="text-muted-foreground">Progress</span>
            <span className="font-medium">{Math.round(progress)}%</span>
          </div>
          <Progress value={progress} className="h-2" />
        </div>

        {/* Status Description */}
        <p className="text-sm text-muted-foreground">
          {statusDisplay.description}
        </p>

        {/* Time Information */}
        <div className="grid grid-cols-2 gap-4 text-sm">
          <div>
            <span className="text-muted-foreground">Time Elapsed:</span>
            <p className="font-medium">{formatDuration(timeElapsed)}</p>
          </div>
          {status !== 'completed' && status !== 'failed' && (
            <div>
              <span className="text-muted-foreground">Est. Remaining:</span>
              <p className="font-medium">
                {estimatedTimeRemaining !== null
                  ? formatDuration(estimatedTimeRemaining)
                  : "Processing..."
                }
              </p>
            </div>
          )}
        </div>

        {/* Job Information */}
        <div className="pt-2 border-t">
          <div className="text-xs text-muted-foreground space-y-1">
            <p>Last checked: {lastChecked.toLocaleTimeString()}</p>
          </div>
        </div>

        {/* Manual Refresh Actions */}
        {(status === 'failed' || status === 'processing') && (
          <div className="pt-2 border-t">
            <Button
              variant="outline"
              size="sm"
              onClick={retryPolling}
              disabled={isRefreshing}
              className="w-full"
            >
              <RefreshCw className={cn("h-4 w-4 mr-2", isRefreshing && "animate-spin")} />
              {isRefreshing
                ? status === 'processing' ? 'Checking...' : 'Refreshing...'
                : status === 'failed'
                  ? 'Retry Status Check'
                  : 'Refresh Status'
              }
            </Button>
          </div>
        )}

        {/* Processing Stage Visualization */}
        {status === 'processing' && (
          <div className="pt-2 border-t">
            <div className="text-xs text-muted-foreground mb-2">Processing Stages:</div>
            <div className="space-y-1">
              <div className="flex items-center gap-2 text-xs">
                <CheckCircle className="h-3 w-3 text-green-500" />
                <span>Request received</span>
              </div>
              <div className="flex items-center gap-2 text-xs">
                <CheckCircle className="h-3 w-3 text-green-500" />
                <span>AI model initialized</span>
              </div>
              <div className="flex items-center gap-2 text-xs">
                <Loader2 className="h-3 w-3 text-blue-500 animate-spin" />
                <span>Generating video content</span>
              </div>
              <div className="flex items-center gap-2 text-xs">
                <Clock className="h-3 w-3 text-gray-400" />
                <span>Finalizing and uploading</span>
              </div>
            </div>
          </div>
        )}

        {/* Completion Actions */}
        {status === 'completed' && (
          <div className="pt-2 border-t">
            <div className="flex items-center gap-2 text-sm text-green-600">
              <Play className="h-4 w-4" />
              <span>Your video is ready to view!</span>
            </div>
          </div>
        )}
      </CardContent>
    </Card>
  );
};

export default VideoStatusTracker;
