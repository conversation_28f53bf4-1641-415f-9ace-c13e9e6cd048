// Voice Generator - Redesigned with Apple/Canva style
import React, { useState, useEffect } from 'react';
import { useForm } from "react-hook-form";
import { z } from "zod";
import { zodResolver } from "@hookform/resolvers/zod";
import { useAuth } from '@/contexts/AuthContext';
import { Button } from "@/components/ui/button";
import {
  Card,
  CardContent,
} from "@/components/ui/card";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormMessage,
} from "@/components/ui/form";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Textarea } from "@/components/ui/textarea";
import { Slider } from "@/components/ui/slider";
import { Progress } from "@/components/ui/progress";
import { Badge } from "@/components/ui/badge";
import { toast } from "sonner";
import DashboardSidebar from "@/components/DashboardSidebar";
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from "@/components/ui/tooltip";
import {
  generateVoice,
  getVoiceUsage,
  AVAILABLE_VOICES,
  VOICES_BY_LANGUAGE,
  VOICE_LIMITS,
  validateTextLength,
  canGenerateVoice,
  formatCharacterCount,
  formatUsagePercentage,
  getUsageStatusColor,
  type VoiceGenerationRequest,
  type VoiceUsage,
  type PollyVoice
} from '@/services/voiceService';
import {
  RefreshCw,
  Mic,
  Volume2,
  Play,
  Pause,
  Download,
  Copy,
  Info,
  Undo,
  MessageSquare,
  Languages,
  Sliders,
  Settings2,
  User,
  Gauge,
  VolumeX
} from "lucide-react";

const voiceFormSchema = z.object({
  text: z.string().min(1, {
    message: "Text is required.",
  }).max(VOICE_LIMITS.MAX_TEXT_LENGTH, `Text must be less than ${formatCharacterCount(VOICE_LIMITS.MAX_TEXT_LENGTH)} characters`),
  voiceId: z.string().min(1, "Voice selection is required"),
  languageCode: z.string().min(1, "Language selection is required"),
  speechRate: z.number().min(VOICE_LIMITS.MIN_SPEECH_RATE).max(VOICE_LIMITS.MAX_SPEECH_RATE).default(1.0),
  pitch: z.number().min(-20).max(20).default(0),
  volume: z.number().min(-20).max(20).default(0),
});

type VoiceFormValues = z.infer<typeof voiceFormSchema>;

interface AudioPlayerProps {
  audioData: string;
  contentType: string;
  onDownload: () => void;
}

const AudioPlayer: React.FC<AudioPlayerProps> = ({ audioData, contentType, onDownload }) => {
  const [isPlaying, setIsPlaying] = useState(false);
  const [audio, setAudio] = useState<HTMLAudioElement | null>(null);

  useEffect(() => {
    if (audioData) {
      const audioBlob = new Blob([
        Uint8Array.from(atob(audioData), c => c.charCodeAt(0))
      ], { type: contentType });
      const audioUrl = URL.createObjectURL(audioBlob);
      const audioElement = new Audio(audioUrl);
      
      const handleEnded = () => setIsPlaying(false);
      audioElement.addEventListener('ended', handleEnded);
      setAudio(audioElement);

      return () => {
        // Clean up properly when component unmounts or audioData changes
        audioElement.pause();
        audioElement.currentTime = 0;
        audioElement.removeEventListener('ended', handleEnded);
        URL.revokeObjectURL(audioUrl);
        setIsPlaying(false);
      };
    }
  }, [audioData, contentType]);

  // Only cleanup when component unmounts (not on every render)
  useEffect(() => {
    return () => {
      if (audio) {
        audio.pause();
        audio.currentTime = 0;
      }
    };
  }, []); // Empty dependency array - only runs on mount/unmount

  const togglePlayback = () => {
    if (!audio) return;

    if (isPlaying) {
      audio.pause();
      setIsPlaying(false);
    } else {
      audio.play();
      setIsPlaying(true);
    }
  };

  const handleDownload = () => {
    if (!audioData) return;
    
    const audioBlob = new Blob([
      Uint8Array.from(atob(audioData), c => c.charCodeAt(0))
    ], { type: contentType });
    const url = URL.createObjectURL(audioBlob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `vibenecto-voice-${Date.now()}.mp3`;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
    onDownload();
  };

  const handleCopyAudio = () => {
    if (!audioData) return;
    
    try {
      const audioBlob = new Blob([
        Uint8Array.from(atob(audioData), c => c.charCodeAt(0))
      ], { type: contentType });
      
      const item = new ClipboardItem({ [contentType]: audioBlob });
      navigator.clipboard.write([item]).then(() => {
        toast.success("Audio copied to clipboard!");
      }).catch(() => {
        toast.error("Failed to copy audio to clipboard");
      });
    } catch (error) {
      toast.error("Failed to copy audio");
    }
  };

  return (
    <Card className="w-full max-w-2xl shadow-2xl rounded-xl overflow-hidden">
      <CardContent className="p-0 relative">
        <div className="bg-gradient-to-br from-brand-purple/10 to-brand-teal/10 p-8 text-center">
          <div className="flex items-center justify-center w-20 h-20 mx-auto mb-4 rounded-full bg-brand-purple/20">
            <Volume2 size={32} className="text-brand-purple" />
          </div>
          <h3 className="text-xl font-semibold mb-2">Voice Generated Successfully!</h3>
          <p className="text-gray-600 dark:text-gray-400 mb-6">Your audio is ready to play or download</p>
          
          <div className="flex items-center justify-center gap-4">
            <TooltipProvider>
              <Tooltip>
                <TooltipTrigger asChild>
                  <Button
                    onClick={togglePlayback}
                    size="lg"
                    className="bg-brand-purple hover:bg-brand-purple/90 text-white"
                  >
                    {isPlaying ? <Pause size={18} className="mr-2" /> : <Play size={18} className="mr-2" />}
                    {isPlaying ? 'Pause' : 'Play'}
                  </Button>
                </TooltipTrigger>
                <TooltipContent>Play/Pause Audio</TooltipContent>
              </Tooltip>
              <Tooltip>
                <TooltipTrigger asChild>
                  <Button
                    onClick={handleDownload}
                    variant="outline"
                    size="lg"
                  >
                    <Download size={18} className="mr-2" />
                    Download
                  </Button>
                </TooltipTrigger>
                <TooltipContent>Download Audio File</TooltipContent>
              </Tooltip>
              <Tooltip>
                <TooltipTrigger asChild>
                  <Button
                    onClick={handleCopyAudio}
                    variant="ghost"
                    size="lg"
                  >
                    <Copy size={18} className="mr-2" />
                    Copy
                  </Button>
                </TooltipTrigger>
                <TooltipContent>Copy Audio</TooltipContent>
              </Tooltip>
            </TooltipProvider>
          </div>
        </div>
      </CardContent>
    </Card>
  );
};

const VoiceGenerator = () => {
  const { user } = useAuth();
  const [isGenerating, setIsGenerating] = useState(false);
  const [generatedAudio, setGeneratedAudio] = useState<{
    audio: string;
    contentType: string;
    characterCount: number;
    processingTime: number;
  } | null>(null);
  const [usage, setUsage] = useState<VoiceUsage | null>(null);
  const [loadingUsage, setLoadingUsage] = useState(true);

  // Cleanup any playing audio when component unmounts (navigation away)
  useEffect(() => {
    return () => {
      // Only stop audio when actually navigating away from the page
      const audioElements = document.querySelectorAll('audio');
      audioElements.forEach(audio => {
        if (!audio.paused) {
          audio.pause();
          audio.currentTime = 0;
        }
      });
    };
  }, []); // Empty dependency array - only runs on unmount

  const form = useForm<VoiceFormValues>({
    resolver: zodResolver(voiceFormSchema),
    defaultValues: {
      text: "",
      voiceId: AVAILABLE_VOICES[0].id,
      languageCode: AVAILABLE_VOICES[0].languageCode,
      speechRate: 1.0,
      pitch: 0,
      volume: 0,
    },
  });

  // Load usage data on component mount
  useEffect(() => {
    if (user?.id) {
      loadUsageData();
    }
  }, [user?.id]);

  const loadUsageData = async () => {
    if (!user?.id) return;

    setLoadingUsage(true);
    try {
      const response = await getVoiceUsage(user.id);
      if (response.success && response.usage) {
        setUsage(response.usage);
      } else {
        toast.error('Failed to load usage data');
      }
    } catch (error) {
      console.error('Error loading usage data:', error);
      toast.error('Failed to load usage data');
    } finally {
      setLoadingUsage(false);
    }
  };

  const onSubmit = async (data: VoiceFormValues) => {
    if (!user) {
      toast.error("You must be logged in to generate voice");
      return;
    }

    // Validate text
    const textValidation = validateTextLength(data.text);
    if (!textValidation.valid) {
      toast.error(textValidation.error);
      return;
    }

    // Check usage limits
    if (usage) {
      const usageCheck = canGenerateVoice(usage, data.text.length);
      if (!usageCheck.canGenerate) {
        toast.error(usageCheck.error);
        return;
      }
    }

    setIsGenerating(true);
    setGeneratedAudio(null);

    try {
      const request: VoiceGenerationRequest = {
        text: data.text,
        voiceId: data.voiceId,
        languageCode: data.languageCode,
        engine: 'standard',
        outputFormat: 'mp3',
        sampleRate: '22050',
        speechRate: data.speechRate,
        pitch: data.pitch === 0 ? '+0%' : `${data.pitch > 0 ? '+' : ''}${data.pitch}%`,
        volume: data.volume === 0 ? '+0dB' : `${data.volume > 0 ? '+' : ''}${data.volume}dB`,
        userId: user.id
      };

      const response = await generateVoice(request);

      if (response.success && response.audio) {
        setGeneratedAudio({
          audio: response.audio,
          contentType: response.contentType || 'audio/mpeg',
          characterCount: response.characterCount || data.text.length,
          processingTime: response.processingTime || 0
        });

        toast.success('Voice generated successfully!');
        
        // Reload usage data
        await loadUsageData();
      } else {
        toast.error(response.error || 'Failed to generate voice');
      }
    } catch (error) {
      console.error('Voice generation error:', error);
      toast.error('Failed to generate voice');
    } finally {
      setIsGenerating(false);
    }
  };

  const handleDownload = () => {
    toast.success('Audio file downloaded successfully!');
  };

  const handleReset = () => {
    form.reset();
    setGeneratedAudio(null);
  };

  const selectedLanguage = form.watch("languageCode");
  const selectedVoiceId = form.watch("voiceId");
  const textValue = form.watch("text");
  
  const characterCount = textValue?.length || 0;
  const usagePercentage = usage ? formatUsagePercentage(usage.charactersUsed, usage.monthlyLimit) : 0;
  const remainingCharacters = usage ? usage.remaining : 0;

  // Update voice options when language changes
  useEffect(() => {
    const languageVoices = VOICES_BY_LANGUAGE[selectedLanguage]?.voices || [];
    if (languageVoices.length > 0 && !languageVoices.find(v => v.id === selectedVoiceId)) {
      form.setValue("voiceId", languageVoices[0].id);
    }
  }, [selectedLanguage, selectedVoiceId, form]);

  return (
    <>
      <DashboardSidebar />
      <main className="ml-64 h-screen bg-gray-50 dark:bg-gray-900 flex">
        {/* Left Panel - Voice Configuration (50%) */}
        <div className="w-1/2 border-r border-gray-200 dark:border-gray-800 flex flex-col h-full">
          {/* Header */}
          <div className="p-4 border-b border-gray-200 dark:border-gray-800">
            <h2 className="text-lg font-semibold flex items-center gap-2">
              <Mic size={18} className="text-brand-purple" />
              Voice Generation
            </h2>
          </div>


          {/* Form content - no scroll */}
          <div className="flex-1 p-4">
            <Form {...form}>
              <form onSubmit={form.handleSubmit(onSubmit)} className="h-full flex flex-col">
                <div className="flex-1 space-y-4">
                  {/* Text Input Section */}
                  <FormField
                    control={form.control}
                    name="text"
                    render={({ field }) => (
                      <FormItem>
                        <div className="flex items-center justify-between mb-1">
                          <label className="text-sm font-medium flex items-center gap-1.5">
                            <MessageSquare size={14} className="text-gray-500" /> Text
                          </label>
                          <TooltipProvider>
                            <Tooltip>
                              <TooltipTrigger asChild>
                                <Info size={14} className="text-gray-400 cursor-pointer" />
                              </TooltipTrigger>
                              <TooltipContent>
                                <p className="w-80 text-xs">Enter the text you want to convert to speech</p>
                              </TooltipContent>
                            </Tooltip>
                          </TooltipProvider>
                        </div>
                        <FormControl>
                          <Textarea
                            placeholder="Enter your text here..."
                            className="resize-none h-32 text-sm"
                            maxLength={VOICE_LIMITS.MAX_TEXT_LENGTH}
                            {...field}
                          />
                        </FormControl>
                        <div className="flex items-center justify-between mt-1">
                          <span className="text-xs text-gray-500">
                            {formatCharacterCount(characterCount)} / {formatCharacterCount(VOICE_LIMITS.MAX_TEXT_LENGTH)} characters
                          </span>
                          {characterCount > 0 && usage && (
                            <span className={`text-xs ${getUsageStatusColor(
                              formatUsagePercentage(usage.charactersUsed + characterCount, usage.monthlyLimit)
                            )}`}>
                              Will use {formatCharacterCount(characterCount)} chars
                            </span>
                          )}
                        </div>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  {/* Language and Voice Selection */}
                  <div className="grid grid-cols-2 gap-3">
                    <FormField
                      control={form.control}
                      name="languageCode"
                      render={({ field }) => (
                        <FormItem>
                          <label className="text-sm font-medium flex items-center gap-1.5 mb-1">
                            <Languages size={14} className="text-gray-500" />
                            Language
                          </label>
                          <Select
                            onValueChange={field.onChange}
                            defaultValue={field.value}
                          >
                            <FormControl>
                              <SelectTrigger className="text-sm">
                                <SelectValue placeholder="Select language" />
                              </SelectTrigger>
                            </FormControl>
                            <SelectContent>
                              {Object.entries(VOICES_BY_LANGUAGE).map(([code, { languageName }]) => (
                                <SelectItem key={code} value={code}>
                                  {languageName}
                                </SelectItem>
                              ))}
                            </SelectContent>
                          </Select>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    <FormField
                      control={form.control}
                      name="voiceId"
                      render={({ field }) => (
                        <FormItem>
                          <label className="text-sm font-medium flex items-center gap-1.5 mb-1">
                            <User size={14} className="text-gray-500" />
                            Voice
                          </label>
                          <Select
                            onValueChange={field.onChange}
                            value={field.value}
                          >
                            <FormControl>
                              <SelectTrigger className="text-sm">
                                <SelectValue placeholder="Select voice" />
                              </SelectTrigger>
                            </FormControl>
                            <SelectContent>
                              {VOICES_BY_LANGUAGE[selectedLanguage]?.voices.map((voice) => (
                                <SelectItem key={voice.id} value={voice.id}>
                                  {voice.name} ({voice.gender})
                                </SelectItem>
                              ))}
                            </SelectContent>
                          </Select>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                  </div>

                  {/* Advanced Parameters Section */}
                  <div className="space-y-3">
                    <div className="flex items-center gap-1.5">
                      <Settings2 size={14} className="text-gray-500" />
                      <h3 className="text-sm font-medium">Advanced Parameters</h3>
                    </div>

                    {/* Speech Rate Section */}
                    <FormField
                      control={form.control}
                      name="speechRate"
                      render={({ field: { value, onChange, ...fieldProps } }) => (
                        <FormItem>
                          <div className="flex justify-between items-center mb-1">
                            <div className="flex items-center gap-1.5">
                              <label className="text-sm font-medium flex items-center gap-1.5">
                                <Gauge size={14} className="text-gray-500" />
                                Speech Rate
                              </label>
                              <TooltipProvider>
                                <Tooltip>
                                  <TooltipTrigger asChild>
                                    <Info size={14} className="text-gray-400 cursor-pointer" />
                                  </TooltipTrigger>
                                  <TooltipContent>
                                    <p className="w-80 text-xs">Controls how fast the voice speaks</p>
                                  </TooltipContent>
                                </Tooltip>
                              </TooltipProvider>
                            </div>
                            <span className="text-xs text-gray-500">{value}x</span>
                          </div>
                          <FormControl>
                            <Slider
                              min={VOICE_LIMITS.MIN_SPEECH_RATE}
                              max={VOICE_LIMITS.MAX_SPEECH_RATE}
                              step={0.1}
                              value={[value]}
                              onValueChange={(vals) => onChange(vals[0])}
                              {...fieldProps}
                            />
                          </FormControl>
                          <div className="flex justify-between text-xs text-gray-500 mt-0.5">
                            <span>Slower</span>
                            <span>Faster</span>
                          </div>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    {/* Pitch Section */}
                    <FormField
                      control={form.control}
                      name="pitch"
                      render={({ field: { value, onChange, ...fieldProps } }) => (
                        <FormItem>
                          <div className="flex justify-between items-center mb-1">
                            <div className="flex items-center gap-1.5">
                              <label className="text-sm font-medium flex items-center gap-1.5">
                                <Sliders size={14} className="text-gray-500" />
                                Pitch
                              </label>
                              <TooltipProvider>
                                <Tooltip>
                                  <TooltipTrigger asChild>
                                    <Info size={14} className="text-gray-400 cursor-pointer" />
                                  </TooltipTrigger>
                                  <TooltipContent>
                                    <p className="w-80 text-xs">Controls the voice pitch (higher or lower)</p>
                                  </TooltipContent>
                                </Tooltip>
                              </TooltipProvider>
                            </div>
                            <span className="text-xs text-gray-500">
                              {value === 0 ? 'Normal' : `${value > 0 ? '+' : ''}${value}%`}
                            </span>
                          </div>
                          <FormControl>
                            <Slider
                              min={-20}
                              max={20}
                              step={1}
                              value={[value]}
                              onValueChange={(vals) => onChange(vals[0])}
                              {...fieldProps}
                            />
                          </FormControl>
                          <div className="flex justify-between text-xs text-gray-500 mt-0.5">
                            <span>Lower</span>
                            <span>Higher</span>
                          </div>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    {/* Volume Section */}
                    <FormField
                      control={form.control}
                      name="volume"
                      render={({ field: { value, onChange, ...fieldProps } }) => (
                        <FormItem>
                          <div className="flex justify-between items-center mb-1">
                            <div className="flex items-center gap-1.5">
                              <label className="text-sm font-medium flex items-center gap-1.5">
                                <VolumeX size={14} className="text-gray-500" />
                                Volume
                              </label>
                              <TooltipProvider>
                                <Tooltip>
                                  <TooltipTrigger asChild>
                                    <Info size={14} className="text-gray-400 cursor-pointer" />
                                  </TooltipTrigger>
                                  <TooltipContent>
                                    <p className="w-80 text-xs">Controls the voice volume level</p>
                                  </TooltipContent>
                                </Tooltip>
                              </TooltipProvider>
                            </div>
                            <span className="text-xs text-gray-500">
                              {value === 0 ? 'Normal' : `${value > 0 ? '+' : ''}${value}dB`}
                            </span>
                          </div>
                          <FormControl>
                            <Slider
                              min={-20}
                              max={20}
                              step={1}
                              value={[value]}
                              onValueChange={(vals) => onChange(vals[0])}
                              {...fieldProps}
                            />
                          </FormControl>
                          <div className="flex justify-between text-xs text-gray-500 mt-0.5">
                            <span>Quieter</span>
                            <span>Louder</span>
                          </div>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                  </div>
                </div>

                {/* Submit Button */}
                <div className="pt-4 border-t border-gray-200 dark:border-gray-800 mt-4">
                  <Button
                    type="submit"
                    className="w-full bg-brand-purple hover:bg-brand-purple/90 text-white"
                    disabled={isGenerating || !textValue?.trim()}
                  >
                    {isGenerating ? (
                      <>
                        <RefreshCw size={16} className="mr-2 animate-spin" />
                        Generating...
                      </>
                    ) : (
                      <>
                        <Mic size={16} className="mr-2" />
                        Generate Voice
                      </>
                    )}
                  </Button>
                  <Button
                    type="button"
                    variant="outline"
                    size="sm"
                    onClick={handleReset}
                    disabled={isGenerating}
                    className="mt-2 w-full text-xs"
                  >
                    <Undo size={14} className="mr-1.5" />
                    Reset All
                  </Button>
                </div>
              </form>
            </Form>
          </div>

          {/* Sidebar footer */}
          <div className="p-4 border-t border-gray-200 dark:border-gray-800">
            <div className="text-xs text-gray-500 flex items-center justify-between">
              <span>Voice Generator v1.0</span>
              <a href="#" className="text-brand-purple hover:underline">Help</a>
            </div>
          </div>
        </div>

        {/* Right Panel - Audio Output (50%) */}
        <div className="w-1/2 flex flex-col items-center justify-center p-6 bg-gray-100 dark:bg-gray-800/50 relative overflow-y-auto">
          {isGenerating ? (
            <div className="flex flex-col items-center justify-center text-center">
              <div className="relative w-48 h-48">
                <div className="absolute inset-0 border-4 border-dashed border-gray-300 dark:border-gray-700 rounded-full animate-spin-slow"></div>
                <Mic size={64} className="text-gray-400 dark:text-gray-600 absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2" />
              </div>
              <p className="mt-6 text-lg font-semibold text-gray-700 dark:text-gray-300">Generating your voice...</p>
              <p className="text-sm text-gray-500 dark:text-gray-400">This might take a moment. Please wait.</p>
            </div>
          ) : generatedAudio ? (
            <AudioPlayer
              audioData={generatedAudio.audio}
              contentType={generatedAudio.contentType}
              onDownload={handleDownload}
            />
          ) : (
            <div className="text-center text-gray-500 dark:text-gray-400">
              <Mic size={64} className="mx-auto mb-4 opacity-50" />
              <p className="text-lg font-medium">Your generated voice will appear here</p>
              <p className="text-sm">Fill in the options on the left and click "Generate Voice"</p>
            </div>
          )}
        </div>
      </main>
    </>
  );
};

export default VoiceGenerator;