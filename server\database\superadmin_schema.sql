-- VibeNecto Superadmin Database Schema
-- Sprint 7: Backend Infrastructure Implementation
-- Goal: Add role management and superadmin capabilities

-- Add role and superadmin columns to profiles table
ALTER TABLE profiles ADD COLUMN IF NOT EXISTS role VARCHAR(20) DEFAULT 'user';
ALTER TABLE profiles ADD COLUMN IF NOT EXISTS is_superadmin BOOLEAN DEFAULT FALSE;

-- <PERSON>reate indexes for performance optimization
CREATE INDEX IF NOT EXISTS idx_profiles_role ON profiles(role);
CREATE INDEX IF NOT EXISTS idx_profiles_superadmin ON profiles(is_superadmin);

-- Set superadmin <NAME_EMAIL>
UPDATE profiles 
SET role = 'superadmin', is_superadmin = TRUE 
WHERE email = '<EMAIL>';

-- Create function to automatically assign superadmin role
CREATE OR REPLACE FUNCTION set_superadmin_role()
RETURNS TRIGGER AS $$
BEGIN
  IF NEW.email = '<EMAIL>' THEN
    NEW.role = 'superadmin';
    NEW.is_superadmin = TRUE;
  END IF;
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- <PERSON>reate trigger for automatic superadmin role assignment
DROP TRIGGER IF EXISTS trigger_set_superadmin_role ON profiles;
CREATE TRIGGER trigger_set_superadmin_role
  BEFORE INSERT OR UPDATE ON profiles
  FOR EACH ROW
  EXECUTE FUNCTION set_superadmin_role();

-- Add comments for documentation
COMMENT ON COLUMN profiles.role IS 'User role: user, superadmin';
COMMENT ON COLUMN profiles.is_superadmin IS 'Boolean flag for superadmin access';
COMMENT ON FUNCTION set_superadmin_role() IS 'Automatically assigns superadmin <NAME_EMAIL>';

-- Verify the schema changes
DO $$
BEGIN
  -- Check if columns exist
  IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'profiles' AND column_name = 'role') THEN
    RAISE EXCEPTION 'Column role was not added to profiles table';
  END IF;
  
  IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'profiles' AND column_name = 'is_superadmin') THEN
    RAISE EXCEPTION 'Column is_superadmin was not added to profiles table';
  END IF;
  
  RAISE NOTICE 'Superadmin schema successfully applied';
END $$;