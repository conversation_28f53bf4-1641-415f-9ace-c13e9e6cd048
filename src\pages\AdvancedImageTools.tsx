import React, { useEffect } from "react";
import { Card, CardContent } from "@/components/ui/card";
import DashboardSidebar from "@/components/DashboardSidebar";
import { useNavigate } from "react-router-dom";
import { useAuth } from "@/contexts/AuthContext";
import {
  ImageI<PERSON>,
  Palette,
  Copy,
  ArrowR<PERSON>,
  Wand2
} from "lucide-react";
import { Button } from "@/components/ui/button";

const AdvancedImageTools = () => {
  const navigate = useNavigate();
  const { user, loading } = useAuth();

  useEffect(() => {
    if (!loading && !user) {
      navigate('/signin');
    }
  }, [user, loading, navigate]);

  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="animate-pulse text-brand-purple font-semibold">Loading...</div>
      </div>
    );
  }

  if (!user) {
    return null;
  }

  return (
    <>
      <DashboardSidebar />

      <main className="ml-64 min-h-screen bg-gray-50 dark:bg-gray-900 p-6">
          <div className="max-w-5xl mx-auto">
            <h2 className="text-2xl font-medium mb-6 text-center">Select a Tool</h2>

            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
              {/* Image Conditioning Card */}
              <Card
                className="group hover:shadow-lg transition-all duration-300 cursor-pointer overflow-hidden"
                onClick={() => navigate('/image-conditioning')}
              >
                <div className="h-40 bg-gradient-to-br from-indigo-50 to-purple-50 dark:from-gray-800 dark:to-gray-700 flex items-center justify-center">
                  <Wand2 size={64} className="text-brand-purple opacity-75 group-hover:scale-110 transition-transform duration-300" />
                </div>
                <CardContent className="p-6">
                  <div className="flex items-center justify-between mb-2">
                    <h3 className="text-lg font-medium">Image Conditioning</h3>
                    <ArrowRight size={16} className="text-brand-purple opacity-0 group-hover:opacity-100 transition-opacity" />
                  </div>
                  <p className="text-gray-600 dark:text-gray-400 text-sm">
                    Guide image generation using reference images to control layout and composition.
                  </p>
                </CardContent>
              </Card>

              {/* Background Removal Card */}
              <Card
                className="group hover:shadow-lg transition-all duration-300 cursor-pointer overflow-hidden"
                onClick={() => navigate('/background-removal')}
              >
                <div className="h-40 bg-gradient-to-br from-purple-50 to-blue-50 dark:from-gray-800 dark:to-gray-700 flex items-center justify-center">
                  <ImageIcon size={64} className="text-brand-purple opacity-75 group-hover:scale-110 transition-transform duration-300" />
                </div>
                <CardContent className="p-6">
                  <div className="flex items-center justify-between mb-2">
                    <h3 className="text-lg font-medium">Background Removal</h3>
                    <ArrowRight size={16} className="text-brand-purple opacity-0 group-hover:opacity-100 transition-opacity" />
                  </div>
                  <p className="text-gray-600 dark:text-gray-400 text-sm">
                    Remove backgrounds from images with AI precision, creating transparent PNGs for your designs.
                  </p>
                </CardContent>
              </Card>

              {/* Color-Guided Generation Card */}
              <Card
                className="group hover:shadow-lg transition-all duration-300 cursor-pointer overflow-hidden"
                onClick={() => navigate('/color-guided-generation')}
              >
                <div className="h-40 bg-gradient-to-br from-pink-50 to-orange-50 dark:from-gray-800 dark:to-gray-700 flex items-center justify-center">
                  <Palette size={64} className="text-brand-purple opacity-75 group-hover:scale-110 transition-transform duration-300" />
                </div>
                <CardContent className="p-6">
                  <div className="flex items-center justify-between mb-2">
                    <h3 className="text-lg font-medium">Color-Guided Generation</h3>
                    <ArrowRight size={16} className="text-brand-purple opacity-0 group-hover:opacity-100 transition-opacity" />
                  </div>
                  <p className="text-gray-600 dark:text-gray-400 text-sm">
                    Generate images with specific color palettes to match your brand or aesthetic preferences.
                  </p>
                </CardContent>
              </Card>

              {/* Image Variation Card */}
              <Card
                className="group hover:shadow-lg transition-all duration-300 cursor-pointer overflow-hidden"
                onClick={() => navigate('/image-variation')}
              >
                <div className="h-40 bg-gradient-to-br from-blue-50 to-teal-50 dark:from-gray-800 dark:to-gray-700 flex items-center justify-center">
                  <Copy size={64} className="text-brand-purple opacity-75 group-hover:scale-110 transition-transform duration-300" />
                </div>
                <CardContent className="p-6">
                  <div className="flex items-center justify-between mb-2">
                    <h3 className="text-lg font-medium">Image Variation</h3>
                    <ArrowRight size={16} className="text-brand-purple opacity-0 group-hover:opacity-100 transition-opacity" />
                  </div>
                  <p className="text-gray-600 dark:text-gray-400 text-sm">
                    Create variations of existing images while maintaining their core elements and style.
                  </p>
                </CardContent>
              </Card>
            </div>

            {/* Tools description */}
            <div className="mt-12">
              <Card>
                <CardContent className="p-6">
                  <h3 className="text-lg font-medium mb-4">Advanced Image Tools</h3>
                  <p className="text-gray-600 dark:text-gray-400 mb-4">
                    Our advanced image tools leverage AWS Bedrock's Titan Image Generator to provide professional-grade image manipulation capabilities.
                  </p>
                  <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mt-6">
                    <div>
                      <h4 className="text-md font-medium mb-2 flex items-center gap-2">
                        <Wand2 size={16} className="text-brand-purple" />
                        Image Conditioning
                      </h4>
                      <p className="text-sm text-gray-600 dark:text-gray-400">
                        Control layout and composition using reference images for more predictable results.
                      </p>
                    </div>
                    <div>
                      <h4 className="text-md font-medium mb-2 flex items-center gap-2">
                        <ImageIcon size={16} className="text-brand-purple" />
                        Background Removal
                      </h4>
                      <p className="text-sm text-gray-600 dark:text-gray-400">
                        Automatically detect and remove backgrounds from any image with AI precision.
                      </p>
                    </div>
                    <div>
                      <h4 className="text-md font-medium mb-2 flex items-center gap-2">
                        <Palette size={16} className="text-brand-purple" />
                        Color-Guided Generation
                      </h4>
                      <p className="text-sm text-gray-600 dark:text-gray-400">
                        Create images that follow specific color palettes for brand consistency.
                      </p>
                    </div>
                    <div>
                      <h4 className="text-md font-medium mb-2 flex items-center gap-2">
                        <Copy size={16} className="text-brand-purple" />
                        Image Variation
                      </h4>
                      <p className="text-sm text-gray-600 dark:text-gray-400">
                        Generate variations of existing images with controllable similarity.
                      </p>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </div>
          </div>
        </main>
    </>
  );
};

export default AdvancedImageTools;
