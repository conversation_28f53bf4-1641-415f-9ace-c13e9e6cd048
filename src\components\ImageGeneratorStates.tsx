import React from 'react';
import { Button } from '@/components/ui/button';
import { ImageIcon } from 'lucide-react';

interface ImageGeneratorStatesProps {
  isGenerating: boolean;
  error: string | null;
  onClearError: () => void;
}

export const ImageGeneratorStates: React.FC<ImageGeneratorStatesProps> = ({
  isGenerating,
  error,
  onClearError,
}) => {
  if (isGenerating) {
    return (
      <div className="flex flex-col items-center justify-center text-center">
        <div className="relative w-48 h-48">
          <div className="absolute inset-0 border-4 border-dashed border-gray-300 dark:border-gray-700 rounded-full animate-spin-slow"></div>
          <ImageIcon size={64} className="text-gray-400 dark:text-gray-600 absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2" />
        </div>
        <p className="mt-6 text-lg font-semibold text-gray-700 dark:text-gray-300">Generating your masterpiece...</p>
        <p className="text-sm text-gray-500 dark:text-gray-400">This might take a moment. Please wait.</p>
      </div>
    );
  }

  if (error) {
    return (
      <div className="flex flex-col items-center justify-center p-8 text-center">
        <div className="h-16 w-16 flex items-center justify-center rounded-full bg-red-100 text-red-600 mb-4">
          <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
            <circle cx="12" cy="12" r="10"></circle>
            <line x1="12" y1="8" x2="12" y2="12"></line>
            <line x1="12" y1="16" x2="12.01" y2="16"></line>
          </svg>
        </div>
        <h3 className="text-xl font-semibold text-red-600 mb-2">Generation Failed</h3>
        <p className="text-gray-600 dark:text-gray-400 mb-4">{error}</p>
        <Button onClick={onClearError} variant="outline">Try Again</Button>
      </div>
    );
  }

  return (
    <div className="text-center text-gray-500 dark:text-gray-400">
      <ImageIcon size={64} className="mx-auto mb-4 opacity-50" />
      <p className="text-lg font-medium">Your generated image will appear here</p>
      <p className="text-sm">Fill in the options on the left and click "Generate Image"</p>
    </div>
  );
};