import { useState } from "react";
import { toast } from "sonner";
import { forceVideoFallbackCheck } from "@/services/videoService";

interface DashboardActionsProps {
  user: any;
  refetchImages: () => Promise<any>;
  refetchVideos: () => Promise<any>;
  refetchVoices: () => Promise<any>;
  refetchVoiceUsage: () => Promise<any>;
}

interface DashboardActions {
  isCheckingFallback: boolean;
  handleRefreshImages: () => Promise<void>;
  handleRefreshVideos: () => Promise<void>;
  handleRefreshVoices: () => Promise<void>;
}

export const useDashboardActions = ({
  user,
  refetchImages,
  refetchVideos,
  refetchVoices,
  refetchVoiceUsage,
}: DashboardActionsProps): DashboardActions => {
  const [isCheckingFallback, setIsCheckingFallback] = useState(false);

  const handleRefreshImages = async () => {
    if (user?.id) {
      try {
        if (import.meta.env.DEV) {
          console.log('Starting background image refresh...');
        }
        
        // Use direct refetch instead of invalidating queries to avoid blank UI
        const refetchPromise = refetchImages();
        const timeoutPromise = new Promise((_, reject) =>
          setTimeout(() => reject(new Error('Refetch timeout')), 10000)
        );
        
        await Promise.race([refetchPromise, timeoutPromise]);

        if (import.meta.env.DEV) {
          console.log('Image refresh completed successfully');
        }
        toast.success("Images refreshed successfully");
      } catch (error) {
        if (import.meta.env.DEV) {
          console.error("Failed to refresh images:", error);
        }
        toast.error("Failed to refresh images");
      }
    }
  };

  const handleRefreshVideos = async () => {
    if (user?.id) {
      try {
        if (import.meta.env.DEV) {
          console.log('Starting video refresh with S3 fallback check...');
        }
        
        // Set fallback checking state
        setIsCheckingFallback(true);
        
        // First, force S3 fallback check for any processing videos
        const fallbackResult = await forceVideoFallbackCheck(user.id);
        
        setIsCheckingFallback(false);
        
        if (fallbackResult.success && fallbackResult.recoveredVideos && fallbackResult.recoveredVideos.length > 0) {
          toast.success(`🎯 Recovered ${fallbackResult.recoveredVideos.length} completed video(s) from S3!`);
          if (import.meta.env.DEV) {
            console.log('S3 fallback recovered videos:', fallbackResult.recoveredVideos);
          }
        } else if (fallbackResult.success && fallbackResult.totalProcessing === 0) {
          if (import.meta.env.DEV) {
            console.log('No processing videos found for fallback check');
          }
        } else if (!fallbackResult.success) {
          if (import.meta.env.DEV) {
            console.warn('S3 fallback check failed:', fallbackResult.error);
          }
          // Don't show error toast for fallback failure, continue with normal refresh
        }
        
        // Then do the normal video history refresh
        const refetchPromise = refetchVideos();
        const timeoutPromise = new Promise((_, reject) =>
          setTimeout(() => reject(new Error('Refetch timeout')), 10000)
        );
        
        await Promise.race([refetchPromise, timeoutPromise]);

        if (import.meta.env.DEV) {
          console.log('Video refresh completed successfully');
        }
        
        // Show success message (fallback success already shown above if applicable)
        if (!fallbackResult.success || !fallbackResult.recoveredVideos || fallbackResult.recoveredVideos.length === 0) {
          toast.success("Videos refreshed successfully");
        }
      } catch (error) {
        setIsCheckingFallback(false);
        if (import.meta.env.DEV) {
          console.error("Failed to refresh videos:", error);
        }
        toast.error("Failed to refresh videos");
      }
    }
  };

  const handleRefreshVoices = async () => {
    if (user?.id) {
      try {
        if (import.meta.env.DEV) {
          console.log('Starting voice refresh...');
        }
        
        const refetchPromise = Promise.all([refetchVoices(), refetchVoiceUsage()]);
        const timeoutPromise = new Promise((_, reject) =>
          setTimeout(() => reject(new Error('Refetch timeout')), 10000)
        );
        
        await Promise.race([refetchPromise, timeoutPromise]);

        if (import.meta.env.DEV) {
          console.log('Voice refresh completed successfully');
        }
        toast.success("Voices refreshed successfully");
      } catch (error) {
        if (import.meta.env.DEV) {
          console.error("Failed to refresh voices:", error);
        }
        toast.error("Failed to refresh voices");
      }
    }
  };

  return {
    isCheckingFallback,
    handleRefreshImages,
    handleRefreshVideos,
    handleRefreshVoices,
  };
};