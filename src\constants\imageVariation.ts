/**
 * Constants for Image Variation functionality
 * Centralized configuration for file constraints, default values, and UI text
 */

// File upload constraints
export const FILE_CONSTRAINTS = {
  MAX_SIZE_MB: 5,
  MAX_SIZE_BYTES: 5 * 1024 * 1024, // 5MB in bytes
  SUPPORTED_FORMATS: ['PNG', 'JPG', 'JPEG', 'WEBP'],
  SUPPORTED_MIME_TYPES: ['image/png', 'image/jpeg', 'image/jpg', 'image/webp'] as const,
  ACCEPT_STRING: 'image/*'
} as const;

// Default values for image variation
export const DEFAULT_VALUES = {
  VARIATION_STRENGTH: 50, // Percentage (0-100)
  NEGATIVE_PROMPT_FALLBACK: 'blurry, low quality, distorted',
  PROMPT_PLACEHOLDER: 'Image Variation',
  IMAGE_TYPE: 'image-variation' as const,
  PLATFORM: 'Advanced Tools'
} as const;

// Variation strength configuration
export const VARIATION_STRENGTH = {
  MIN: 10,
  MAX: 100,
  STEP: 5,
  DEFAULT: 50,
  // Convert percentage to 0-1 scale for API
  toApiValue: (percentage: number) => percentage / 100,
  // Labels for UI
  LABELS: {
    MIN_LABEL: 'Subtle Changes',
    MAX_LABEL: 'Major Changes'
  }
} as const;

// Toast messages
export const TOAST_MESSAGES = {
  SUCCESS: 'Image variation created successfully!',
  DOWNLOAD_SUCCESS: 'Image downloaded successfully!',
  ERROR: {
    AUTH_REQUIRED: 'You must be logged in to generate image variations',
    PROCESSING_FAILED: 'Failed to process image',
    GENERATION_FAILED: 'Failed to create image variation',
    SAVE_WARNING: 'Image variation created successfully, but failed to save to history',
    S3_WARNING: 'Variation created successfully, but failed to save to history'
  }
} as const;

// UI text and labels
export const UI_TEXT = {
  TITLE: 'Create Image Variations',
  TOOLTIP: 'Create variations of existing images while maintaining core elements.',
  TABS: {
    UPLOAD_SETTINGS: 'Upload & Settings',
    RESULT: 'Result'
  },
  UPLOAD: {
    TITLE: 'Upload Image',
    DRAG_DROP: 'Click to browse or drag and drop',
    SELECT_BUTTON: 'Select Image',
    CHANGE_BUTTON: 'Change Image',
    SUPPORTED_TEXT: 'Supported: PNG, JPG, JPEG, WEBP',
    MAX_SIZE_TEXT: 'Max size: 5MB'
  },
  SETTINGS: {
    TITLE: 'Variation Settings',
    STRENGTH_LABEL: 'Variation Strength',
    PROMPT_LABEL: 'Prompt (Optional)',
    PROMPT_PLACEHOLDER: 'Describe what you want to see in the variation...',
    PROMPT_HELP: 'Add specific details to guide the variation',
    NEGATIVE_PROMPT_LABEL: 'Negative Prompt (Optional)',
    NEGATIVE_PROMPT_PLACEHOLDER: 'Describe what you want to avoid...',
    NEGATIVE_PROMPT_HELP: 'Specify elements you don\'t want in the result',
    UPLOAD_FIRST: 'Upload an image first',
    UPLOAD_FIRST_SUBTITLE: 'Settings will appear here after you upload an image'
  },
  BUTTONS: {
    GENERATE: 'Generate Variation',
    GENERATING: 'Generating...',
    START_OVER: 'Start Over',
    DOWNLOAD: 'Download'
  },
  RESULT: {
    SUCCESS_MESSAGE: 'Image variation created successfully!'
  }
} as const;

// Use cases for the help section
export const USE_CASES = [
  {
    title: 'A/B Testing',
    description: 'Generate multiple variations of the same image to test different visual approaches and determine which performs best with your audience.'
  },
  {
    title: 'Marketing Asset Creation',
    description: 'Create diverse marketing assets based on a single source image, maintaining brand consistency while exploring creative alternatives.'
  },
  {
    title: 'Content Series',
    description: 'Develop a series of related images for social media campaigns, blog posts, or email newsletters that maintain visual cohesion.'
  },
  {
    title: 'Creative Exploration',
    description: 'Explore different artistic interpretations of your original image to discover new creative directions and visual possibilities.'
  }
] as const;

// Form validation
export const VALIDATION = {
  NEGATIVE_PROMPT_MIN_LENGTH: 3,
  isValidFile: (file: File): boolean => {
    return (
      file.size <= FILE_CONSTRAINTS.MAX_SIZE_BYTES &&
      (FILE_CONSTRAINTS.SUPPORTED_MIME_TYPES as readonly string[]).includes(file.type)
    );
  },
  getFileError: (file: File): string | null => {
    if (file.size > FILE_CONSTRAINTS.MAX_SIZE_BYTES) {
      return `File size must be less than ${FILE_CONSTRAINTS.MAX_SIZE_MB}MB`;
    }
    if (!(FILE_CONSTRAINTS.SUPPORTED_MIME_TYPES as readonly string[]).includes(file.type)) {
      return `File type must be one of: ${FILE_CONSTRAINTS.SUPPORTED_FORMATS.join(', ')}`;
    }
    return null;
  }
} as const;

// Processing parameters
export const PROCESSING = {
  // Parameters for the image variation API
  getVariationOptions: (strength: number, negativePrompt: string) => ({
    similarityStrength: VARIATION_STRENGTH.toApiValue(strength),
    negativePrompt: negativePrompt && negativePrompt.length >= VALIDATION.NEGATIVE_PROMPT_MIN_LENGTH 
      ? negativePrompt 
      : DEFAULT_VALUES.NEGATIVE_PROMPT_FALLBACK
  }),
  
  // Parameters for history saving
  getHistoryParameters: (strength: number, negativePrompt: string) => ({
    platform: DEFAULT_VALUES.PLATFORM,
    style: DEFAULT_VALUES.IMAGE_TYPE,
    strength,
    negativePrompt
  }),
  
  // Generate filename for downloads
  generateFilename: () => `image-variation-${Date.now()}.png`
} as const;

export default {
  FILE_CONSTRAINTS,
  DEFAULT_VALUES,
  VARIATION_STRENGTH,
  TOAST_MESSAGES,
  UI_TEXT,
  USE_CASES,
  VALIDATION,
  PROCESSING
};