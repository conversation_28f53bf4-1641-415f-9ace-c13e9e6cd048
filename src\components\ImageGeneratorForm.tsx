import React from 'react';
import { UseFormReturn } from 'react-hook-form';
import { FormField, FormControl, FormItem, FormMessage } from '@/components/ui/form';
import { Textarea } from '@/components/ui/textarea';
import { MessageSquare, Ban, <PERSON>lette, Frame, <PERSON>, Shuffle } from 'lucide-react';
import { FormFieldWithTooltip } from './form/FormFieldWithTooltip';
import { SelectField } from './form/SelectField';
import { <PERSON>lide<PERSON><PERSON>ield } from './form/SliderField';
import { curatedStyles, imageSizes, qualityOptions, CFG_SCALE_CONFIG, SEED_CONFIG } from '@/constants/imageGenerator';
import { ImageFormValues } from '@/hooks/useImageForm';

interface ImageGeneratorFormProps {
  form: UseFormReturn<ImageFormValues>;
  activeSidebarSection: 'design' | 'advanced';
}

export const ImageGeneratorForm: React.FC<ImageGeneratorFormProps> = ({
  form,
  activeSidebarSection,
}) => {
  if (activeSidebarSection === 'design') {
    return (
      <>
        {/* Prompt Section */}
        <FormFieldWithTooltip
          icon={<MessageSquare size={14} className="text-gray-500" />}
          label="Prompt"
          tooltip="Be specific about colors, objects, mood, and context"
        >
          <FormField
            control={form.control}
            name="prompt"
            render={({ field }) => (
              <FormItem>
                <FormControl>
                  <Textarea
                    placeholder="Describe the image you want to create..."
                    className="resize-none h-32 text-sm"
                    {...field}
                  />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />
        </FormFieldWithTooltip>

        {/* Visual Style Section */}
        <FormField
          control={form.control}
          name="style"
          render={({ field }) => (
            <SelectField
              icon={<Palette size={14} className="text-gray-500" />}
              label="Visual Style"
              placeholder="Select style"
              value={field.value || ""}
              onChange={field.onChange}
              options={curatedStyles}
            />
          )}
        />

        {/* Image Size Section */}
        <FormField
          control={form.control}
          name="size"
          render={({ field }) => (
            <SelectField
              icon={<Frame size={14} className="text-gray-500" />}
              label="Image Size"
              placeholder="Select size"
              value={field.value}
              onChange={field.onChange}
              options={imageSizes}
            />
          )}
        />
      </>
    );
  }

  return (
    <>
      {/* Negative Prompt Section */}
      <FormFieldWithTooltip
        icon={<Ban size={14} className="text-gray-500" />}
        label="Negative Prompt"
        tooltip="Specify elements to exclude (min 3 characters)"
      >
        <FormField
          control={form.control}
          name="negativePrompt"
          render={({ field }) => (
            <FormItem>
              <FormControl>
                <Textarea
                  placeholder="Elements to avoid..."
                  className="resize-none h-20 text-sm"
                  {...field}
                />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />
      </FormFieldWithTooltip>

      {/* Quality Section */}
      <FormField
        control={form.control}
        name="quality"
        render={({ field }) => (
          <SelectField
            icon={<Star size={14} className="text-gray-500" />}
            label="Quality"
            placeholder="Select quality"
            value={field.value}
            onChange={field.onChange}
            options={qualityOptions}
            description="Premium costs more but produces better results"
          />
        )}
      />

      {/* CFG Scale Section */}
      <FormField
        control={form.control}
        name="cfgScale"
        render={({ field: { value, onChange } }) => (
          <SliderField
            icon={<></>}
            label="Creativity (CFG Scale)"
            tooltip="Controls prompt adherence. Lower: more creative. Higher: more precise."
            value={value}
            onChange={onChange}
            min={CFG_SCALE_CONFIG.min}
            max={CFG_SCALE_CONFIG.max}
            step={CFG_SCALE_CONFIG.step}
            leftLabel="Creative"
            rightLabel="Precise"
          />
        )}
      />

      {/* Seed Section */}
      <FormField
        control={form.control}
        name="seed"
        render={({ field: { value, onChange } }) => (
          <SliderField
            icon={<Shuffle size={14} className="text-gray-500" />}
            label="Seed"
            tooltip="Same seed creates similar images"
            value={value || SEED_CONFIG.default}
            onChange={onChange}
            min={SEED_CONFIG.min}
            max={SEED_CONFIG.max}
            step={SEED_CONFIG.step}
            description="Same seed creates similar images"
          />
        )}
      />
    </>
  );
};