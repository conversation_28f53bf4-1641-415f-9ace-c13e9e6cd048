/**
 * Video Optimization Engine - Sprint 18 Phase 4
 * 
 * Intelligent optimization engine that automatically applies performance
 * optimizations based on real-time analytics and device conditions.
 * 
 * Features:
 * - Automatic optimization application
 * - Device and network condition adaptation
 * - Performance-based configuration tuning
 * - A/B testing for optimization strategies
 * - Real-time optimization monitoring
 */

import { advancedVideoCache } from './advancedVideoCache';
import { videoPerformanceAnalytics } from './videoPerformanceAnalytics';

interface OptimizationRule {
  id: string;
  name: string;
  description: string;
  condition: (metrics: any, context: OptimizationContext) => boolean;
  action: (context: OptimizationContext) => Promise<void>;
  priority: number;
  category: 'caching' | 'lazy-loading' | 'network' | 'device';
  enabled: boolean;
}

interface OptimizationContext {
  deviceType?: 'mobile' | 'tablet' | 'desktop';
  connectionType?: string;
  deviceMemory?: number;
  viewportSize?: { width: number; height: number };
  currentMetrics: any;
  cacheConfig: any;
  lazyConfig: any;
}

interface OptimizationResult {
  ruleId: string;
  applied: boolean;
  timestamp: number;
  impact?: {
    before: any;
    after: any;
    improvement: number;
  };
  error?: string;
}

class VideoOptimizationEngine {
  private rules: OptimizationRule[] = [];
  private appliedOptimizations: OptimizationResult[] = [];
  private isRunning = false;
  private optimizationInterval = 30000; // 30 seconds
  private maxOptimizationHistory = 1000;

  constructor() {
    this.initializeRules();
    this.startOptimizationLoop();
    
    if (import.meta.env.DEV) {
      console.log('[VideoOptimizationEngine] Optimization engine initialized');
    }
  }

  /**
   * Initialize optimization rules
   */
  private initializeRules(): void {
    this.rules = [
      // Cache optimization rules
      {
        id: 'increase-cache-ttl',
        name: 'Increase Cache TTL',
        description: 'Increase cache TTL for better hit rates',
        condition: (metrics, context) => metrics.cacheHitRate < 70 && metrics.averageLoadTime > 1000,
        action: async (context) => {
          const currentConfig = advancedVideoCache.getConfig();
          advancedVideoCache.updateConfig({
            defaultTTL: Math.min(currentConfig.defaultTTL * 1.2, currentConfig.maxTTL),
          });
        },
        priority: 8,
        category: 'caching',
        enabled: true,
      },

      {
        id: 'optimize-cache-size',
        name: 'Optimize Cache Size',
        description: 'Adjust cache size based on device memory',
        condition: (metrics, context) => {
          const memoryUsage = metrics.memoryUsage || 0;
          return (context.deviceMemory && context.deviceMemory < 4 && memoryUsage > 80) ||
                 (context.deviceMemory && context.deviceMemory >= 8 && memoryUsage < 50);
        },
        action: async (context) => {
          const currentConfig = advancedVideoCache.getConfig();
          let newMaxSize = currentConfig.maxSize;
          
          if (context.deviceMemory && context.deviceMemory < 4) {
            newMaxSize = Math.max(100, currentConfig.maxSize * 0.8); // Reduce for low memory
          } else if (context.deviceMemory && context.deviceMemory >= 8) {
            newMaxSize = Math.min(1000, currentConfig.maxSize * 1.3); // Increase for high memory
          }
          
          advancedVideoCache.updateConfig({ maxSize: newMaxSize });
        },
        priority: 6,
        category: 'caching',
        enabled: true,
      },

      // Lazy loading optimization rules
      {
        id: 'adjust-lazy-loading-mobile',
        name: 'Optimize Lazy Loading for Mobile',
        description: 'Adjust lazy loading parameters for mobile devices',
        condition: (metrics, context) => 
          context.deviceType === 'mobile' && metrics.lazyLoadRate < 60,
        action: async (context) => {
          // This would update the lazy loading configuration
          // Implementation depends on how lazy config is managed globally
          if (import.meta.env.DEV) {
            console.log('[VideoOptimizationEngine] Applying mobile lazy loading optimization');
          }
        },
        priority: 7,
        category: 'lazy-loading',
        enabled: true,
      },

      {
        id: 'aggressive-lazy-loading-slow-connection',
        name: 'Aggressive Lazy Loading for Slow Connections',
        description: 'Use more aggressive lazy loading for slow connections',
        condition: (metrics, context) => 
          (context.connectionType === '2g' || context.connectionType === '3g') && 
          metrics.averageLoadTime > 3000,
        action: async (context) => {
          if (import.meta.env.DEV) {
            console.log('[VideoOptimizationEngine] Applying aggressive lazy loading for slow connection');
          }
        },
        priority: 9,
        category: 'lazy-loading',
        enabled: true,
      },

      // Network optimization rules
      {
        id: 'reduce-concurrent-loads',
        name: 'Reduce Concurrent Loads',
        description: 'Limit concurrent video loads for better performance',
        condition: (metrics, context) => 
          metrics.errorRate > 10 && context.connectionType !== '4g',
        action: async (context) => {
          if (import.meta.env.DEV) {
            console.log('[VideoOptimizationEngine] Reducing concurrent loads');
          }
        },
        priority: 5,
        category: 'network',
        enabled: true,
      },

      // Device-specific optimization rules
      {
        id: 'low-end-device-optimization',
        name: 'Low-End Device Optimization',
        description: 'Apply optimizations for low-end devices',
        condition: (metrics, context) => 
          context.deviceMemory && context.deviceMemory < 2 && metrics.averageLoadTime > 2000,
        action: async (context) => {
          // Apply multiple optimizations for low-end devices
          const currentConfig = advancedVideoCache.getConfig();
          advancedVideoCache.updateConfig({
            maxSize: Math.max(50, currentConfig.maxSize * 0.5),
            maxMemory: Math.max(10 * 1024 * 1024, currentConfig.maxMemory * 0.5),
            evictionBatchSize: Math.max(5, currentConfig.evictionBatchSize * 0.5),
          });
        },
        priority: 10,
        category: 'device',
        enabled: true,
      },

      {
        id: 'high-end-device-optimization',
        name: 'High-End Device Optimization',
        description: 'Apply optimizations for high-end devices',
        condition: (metrics, context) => 
          context.deviceMemory && context.deviceMemory >= 8 && 
          metrics.cacheHitRate > 80 && metrics.averageLoadTime < 1000,
        action: async (context) => {
          // Enable more aggressive caching and preloading for high-end devices
          const currentConfig = advancedVideoCache.getConfig();
          advancedVideoCache.updateConfig({
            maxSize: Math.min(1000, currentConfig.maxSize * 1.5),
            maxMemory: Math.min(200 * 1024 * 1024, currentConfig.maxMemory * 1.5),
            enablePreloading: true,
          });
        },
        priority: 4,
        category: 'device',
        enabled: true,
      },
    ];
  }

  /**
   * Run optimization analysis and apply rules
   */
  async runOptimization(context: OptimizationContext): Promise<OptimizationResult[]> {
    if (!this.isRunning) return [];

    const results: OptimizationResult[] = [];
    const applicableRules = this.getApplicableRules(context);

    for (const rule of applicableRules) {
      try {
        const beforeMetrics = { ...context.currentMetrics };
        
        await rule.action(context);
        
        // Wait a bit for the optimization to take effect
        await new Promise(resolve => setTimeout(resolve, 1000));
        
        const afterMetrics = videoPerformanceAnalytics.getCurrentMetrics();
        const improvement = this.calculateImprovement(beforeMetrics, afterMetrics);
        
        const result: OptimizationResult = {
          ruleId: rule.id,
          applied: true,
          timestamp: Date.now(),
          impact: {
            before: beforeMetrics,
            after: afterMetrics,
            improvement,
          },
        };
        
        results.push(result);
        this.appliedOptimizations.push(result);
        
        if (import.meta.env.DEV) {
          console.log(`[VideoOptimizationEngine] Applied optimization: ${rule.name} (${improvement.toFixed(1)}% improvement)`);
        }
        
      } catch (error) {
        const result: OptimizationResult = {
          ruleId: rule.id,
          applied: false,
          timestamp: Date.now(),
          error: error instanceof Error ? error.message : 'Unknown error',
        };
        
        results.push(result);
        
        if (import.meta.env.DEV) {
          console.error(`[VideoOptimizationEngine] Failed to apply optimization: ${rule.name}`, error);
        }
      }
    }

    // Maintain optimization history limit
    if (this.appliedOptimizations.length > this.maxOptimizationHistory) {
      this.appliedOptimizations = this.appliedOptimizations.slice(-this.maxOptimizationHistory);
    }

    return results;
  }

  /**
   * Get rules that are applicable to current context
   */
  private getApplicableRules(context: OptimizationContext): OptimizationRule[] {
    return this.rules
      .filter(rule => rule.enabled && rule.condition(context.currentMetrics, context))
      .sort((a, b) => b.priority - a.priority); // Higher priority first
  }

  /**
   * Calculate improvement percentage between metrics
   */
  private calculateImprovement(before: any, after: any): number {
    // Simple improvement calculation based on key metrics
    let improvement = 0;
    let factors = 0;

    // Cache hit rate improvement
    if (before.cacheHitRate !== undefined && after.cacheHitRate !== undefined) {
      improvement += (after.cacheHitRate - before.cacheHitRate);
      factors++;
    }

    // Load time improvement (inverse - lower is better)
    if (before.averageLoadTime !== undefined && after.averageLoadTime !== undefined) {
      const loadTimeImprovement = ((before.averageLoadTime - after.averageLoadTime) / before.averageLoadTime) * 100;
      improvement += loadTimeImprovement;
      factors++;
    }

    // Error rate improvement (inverse - lower is better)
    if (before.errorRate !== undefined && after.errorRate !== undefined) {
      const errorRateImprovement = ((before.errorRate - after.errorRate) / Math.max(before.errorRate, 1)) * 100;
      improvement += errorRateImprovement;
      factors++;
    }

    return factors > 0 ? improvement / factors : 0;
  }

  /**
   * Start automatic optimization loop
   */
  private startOptimizationLoop(): void {
    this.isRunning = true;
    
    setInterval(async () => {
      if (!this.isRunning) return;

      try {
        const context = await this.buildOptimizationContext();
        await this.runOptimization(context);
      } catch (error) {
        if (import.meta.env.DEV) {
          console.error('[VideoOptimizationEngine] Optimization loop error:', error);
        }
      }
    }, this.optimizationInterval);
  }

  /**
   * Build optimization context from current environment
   */
  private async buildOptimizationContext(): Promise<OptimizationContext> {
    const currentMetrics = videoPerformanceAnalytics.getCurrentMetrics();
    const cacheConfig = advancedVideoCache.getConfig();
    
    // Detect device and network characteristics
    const deviceMemory = (navigator as any)?.deviceMemory || 4;
    const connectionType = (navigator as any)?.connection?.effectiveType || 'unknown';
    const viewportSize = {
      width: window.innerWidth,
      height: window.innerHeight,
    };
    
    let deviceType: 'mobile' | 'tablet' | 'desktop' = 'desktop';
    if (viewportSize.width < 768) {
      deviceType = 'mobile';
    } else if (viewportSize.width < 1024) {
      deviceType = 'tablet';
    }

    return {
      deviceType,
      connectionType,
      deviceMemory,
      viewportSize,
      currentMetrics,
      cacheConfig,
      lazyConfig: {}, // Would be populated with actual lazy loading config
    };
  }

  /**
   * Get optimization history
   */
  getOptimizationHistory(): OptimizationResult[] {
    return [...this.appliedOptimizations];
  }

  /**
   * Get available optimization rules
   */
  getOptimizationRules(): OptimizationRule[] {
    return [...this.rules];
  }

  /**
   * Enable or disable specific optimization rule
   */
  setRuleEnabled(ruleId: string, enabled: boolean): void {
    const rule = this.rules.find(r => r.id === ruleId);
    if (rule) {
      rule.enabled = enabled;
      
      if (import.meta.env.DEV) {
        console.log(`[VideoOptimizationEngine] Rule ${ruleId} ${enabled ? 'enabled' : 'disabled'}`);
      }
    }
  }

  /**
   * Manually apply specific optimization
   */
  async applyOptimization(ruleId: string): Promise<OptimizationResult> {
    const rule = this.rules.find(r => r.id === ruleId);
    if (!rule) {
      throw new Error(`Optimization rule ${ruleId} not found`);
    }

    const context = await this.buildOptimizationContext();
    const beforeMetrics = { ...context.currentMetrics };

    try {
      await rule.action(context);
      
      // Wait for optimization to take effect
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      const afterMetrics = videoPerformanceAnalytics.getCurrentMetrics();
      const improvement = this.calculateImprovement(beforeMetrics, afterMetrics);
      
      const result: OptimizationResult = {
        ruleId: rule.id,
        applied: true,
        timestamp: Date.now(),
        impact: {
          before: beforeMetrics,
          after: afterMetrics,
          improvement,
        },
      };
      
      this.appliedOptimizations.push(result);
      
      if (import.meta.env.DEV) {
        console.log(`[VideoOptimizationEngine] Manually applied optimization: ${rule.name}`);
      }
      
      return result;
      
    } catch (error) {
      const result: OptimizationResult = {
        ruleId: rule.id,
        applied: false,
        timestamp: Date.now(),
        error: error instanceof Error ? error.message : 'Unknown error',
      };
      
      this.appliedOptimizations.push(result);
      return result;
    }
  }

  /**
   * Stop optimization engine
   */
  stop(): void {
    this.isRunning = false;
    
    if (import.meta.env.DEV) {
      console.log('[VideoOptimizationEngine] Optimization engine stopped');
    }
  }

  /**
   * Start optimization engine
   */
  start(): void {
    this.isRunning = true;
    
    if (import.meta.env.DEV) {
      console.log('[VideoOptimizationEngine] Optimization engine started');
    }
  }
}

// Export singleton instance
export const videoOptimizationEngine = new VideoOptimizationEngine();
export default videoOptimizationEngine;
