import React, { useState, useRef } from "react";
import { toast } from "sonner";
import DashboardSidebar from "@/components/DashboardSidebar";
import {
  ImageIcon,
  ArrowLeft,
  Info,
  Download,
  Upload,
  Check,
  X,
  Trash2,
  RefreshCw,
  Wand2,
  SplitSquareVertical,
  Palette, // For Style Transfer example
  Home, // For Redecorate Room example
  Edit // For Sketch to Photo example
} from "lucide-react";
import {
  DEFAULT_NEGATIVE_PROMPT,
  DEFAULT_CONDITIONING_STRENGTH,
  DEFAULT_CONDITIONING_MODE,
  MAX_FILE_SIZE,
  ACCEPTED_FILE_TYPES,
  IMAGE_DIMENSIONS,
  QUALITY_SETTING,
  CONDITIONING_STRENGTH_RANGE,
  TOAST_MESSAGES
} from "@/constants/imageConditioning";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import { Link } from "react-router-dom";
import { <PERSON><PERSON><PERSON>, <PERSON>lt<PERSON>Content, <PERSON><PERSON><PERSON><PERSON>rovider, TooltipTrigger } from "@/components/ui/tooltip";
import { Ta<PERSON>, TabsList, TabsTrigger, TabsContent } from "@/components/ui/tabs";
import { Slider } from "@/components/ui/slider";
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { useAuth } from "@/contexts/AuthContext";
import { saveImageHistory, ImageHistoryItem } from "@/services/imageHistoryService";
import { uploadImageToS3 } from "@/services/s3Service";
import { generateConditionedImage, preprocessReferenceImage } from "@/services/bedrockService";
import { useQueryClient } from "@tanstack/react-query";

const ImageConditioningPage = () => {
  const { user } = useAuth();
  const queryClient = useQueryClient();
  const [sourceImage, setSourceImage] = useState<string | null>(null);
  const [resultImage, setResultImage] = useState<string | null>(null);
  const [isProcessing, setIsProcessing] = useState(false);
  const [activeTab, setActiveTab] = useState("upload");
  const [conditioningMode, setConditioningMode] = useState<"CANNY" | "SEGMENTATION">(DEFAULT_CONDITIONING_MODE);
  const [conditioningStrength, setConditioningStrength] = useState(DEFAULT_CONDITIONING_STRENGTH);
  const [prompt, setPrompt] = useState("");
  const [negativePrompt, setNegativePrompt] = useState(DEFAULT_NEGATIVE_PROMPT);
  const fileInputRef = useRef<HTMLInputElement>(null);

  // Handle file upload
  const handleFileUpload = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (!file) return;

    // Check file size (max 5MB)
    if (file.size > MAX_FILE_SIZE) {
      toast.error(TOAST_MESSAGES.FILE_TOO_LARGE);
      return;
    }

    const reader = new FileReader();
    reader.onload = (event) => {
      if (event.target?.result) {
        setSourceImage(event.target.result as string);
        // Reset result image
        setResultImage(null);
      }
    };
    reader.readAsDataURL(file);
  };

  // Trigger file input click
  const triggerFileInput = () => {
    fileInputRef.current?.click();
  };

  // Helper function to save image to history
  const saveImageToHistory = async (imageData: {
    s3Key: string;
    s3Url: string;
    prompt: string;
    parameters: any;
  }) => {
    const newHistoryItem: ImageHistoryItem = {
      user_id: user!.id,
      prompt: imageData.prompt,
      image_type: 'image-variation',
      s3_key: imageData.s3Key,
      s3_url: imageData.s3Url,
      parameters: imageData.parameters
    };

    // Save to Supabase
    await saveImageHistory(newHistoryItem);
    // Aggressively invalidate and ensure refetch for image history
    await queryClient.removeQueries({ queryKey: ['imageHistory', user!.id], exact: true });
    await queryClient.invalidateQueries({ queryKey: ['imageHistory', user!.id] });
  };

  // Generate the conditioned image
  const generateImage = async () => {
    if (!sourceImage || !user?.id || !prompt) {
      if (!prompt) toast.error(TOAST_MESSAGES.ENTER_PROMPT);
      if (!sourceImage) toast.error(TOAST_MESSAGES.UPLOAD_IMAGE);
      return;
    }

    setIsProcessing(true);

    try {
      // Extract the base64 data from the source image
      const base64Data = sourceImage.split(',')[1];

      // Show a toast explaining what's happening
      toast.info(`Generating image using ${conditioningMode.toLowerCase()} conditioning with ${conditioningStrength.toFixed(1)} strength...`);

      // Call the Bedrock service to generate the conditioned image
      // Note: We're now using image variation under the hood for conditioning
      const result = await generateConditionedImage(
        prompt,
        {
          referenceImage: base64Data,
          conditioningMode,
          conditioningStrength,
          negativePrompt,
          quality: QUALITY_SETTING,
          width: IMAGE_DIMENSIONS.width,
          height: IMAGE_DIMENSIONS.height
        },
        user.id
      );

      if (result.success && result.image) {
        // Set the result image
        setResultImage(result.image);
        setActiveTab("result");

        // Check if we have S3 information
        if (result.s3Url && result.s3Key) {
          await saveImageToHistory({
            s3Key: result.s3Key,
            s3Url: result.s3Url,
            prompt: prompt,
            parameters: {
              conditioningMode,
              conditioningStrength,
              negativePrompt
            }
          });
          toast.success(TOAST_MESSAGES.GENERATION_SUCCESS);
        } else {
          // If S3 upload failed in the service, try to upload directly
          try {
            const s3Result = await uploadImageToS3({
              image: result.image.split(',')[1],
              userId: user.id,
              imageType: 'conditioning',
              filename: `conditioning-${Date.now()}.png`
            });

            if (s3Result.success && s3Result.url && s3Result.key) {
              await saveImageToHistory({
                s3Key: s3Result.key,
                s3Url: s3Result.url,
                prompt: prompt,
                parameters: {
                  conditioningMode,
                  conditioningStrength,
                  negativePrompt
                }
              });
              toast.success(TOAST_MESSAGES.GENERATION_SUCCESS);
            } else {
              toast.warning(TOAST_MESSAGES.SAVE_WARNING);
            }
          } catch (s3Error) {
            if (import.meta.env.DEV) {
              console.error('Failed to upload to S3:', s3Error);
            }
            toast.warning(TOAST_MESSAGES.SAVE_WARNING);
          }
        }
      } else {
        toast.error(result.error || "Failed to generate image");
      }
    } catch (error) {
      toast.error("Failed to generate image");
      if (import.meta.env.DEV) {
        console.error(error);
      }
    } finally {
      setIsProcessing(false);
    }
  };

  // Reset the process
  const resetProcess = () => {
    setSourceImage(null);
    setResultImage(null);
    setActiveTab("upload");
    setPrompt("");
    if (fileInputRef.current) {
      fileInputRef.current.value = '';
    }
  };

  // Download result image
  const downloadImage = () => {
    if (!resultImage) return;

    const link = document.createElement('a');
    link.href = resultImage;
    link.download = `conditioned-image-${Date.now()}.png`;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);

    toast.success(TOAST_MESSAGES.DOWNLOAD_SUCCESS);
  };

  return (
    <>
      <DashboardSidebar />

      <main className="ml-64 min-h-screen bg-white dark:bg-gray-900 overflow-auto">
        {/* Main content area */}
        <div className="container mx-auto px-4 py-6">
          {/* Tool header */}
          <div className="flex items-center mb-4">
            <div className="flex items-center gap-2">
              <Link
                to="/advanced-image-tools"
                className="flex items-center text-gray-500 hover:text-gray-700 transition-colors"
              >
                <ArrowLeft size={16} />
              </Link>
              <div className="h-6 w-6 rounded-full bg-purple-100 dark:bg-purple-900/30 flex items-center justify-center">
                <Wand2 size={14} className="text-brand-purple" />
              </div>
              <h2 className="text-sm font-medium text-brand-purple">Image Conditioning</h2>
              <TooltipProvider>
                <Tooltip>
                  <TooltipTrigger asChild>
                    <Button variant="ghost" size="sm" className="h-6 w-6 rounded-full p-0 ml-1">
                      <Info size={12} className="text-gray-400" />
                    </Button>
                  </TooltipTrigger>
                  <TooltipContent side="right">
                    <p className="text-xs">Guide generation with a reference image.</p>
                  </TooltipContent>
                </Tooltip>
              </TooltipProvider>
            </div>
          </div>
          
          {/* Use Cases Section */}
          <div className="mb-6 px-2 py-4 border rounded-lg bg-slate-50 dark:bg-slate-800/50 border-slate-200 dark:border-slate-700">
            <h3 className="text-md font-semibold mb-3 text-center text-gray-700 dark:text-gray-200">Example Use Cases</h3>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-center">
              <div className="flex flex-col items-center p-3 rounded-md bg-slate-100 dark:bg-slate-700/50">
                <Palette size={24} className="mb-2 text-brand-purple" />
                <p className="text-sm font-medium text-gray-800 dark:text-gray-100">Style Transfer</p>
                <p className="text-xs text-gray-600 dark:text-gray-400">Keep pose, change style (e.g., photo to cartoon).</p>
              </div>
              <div className="flex flex-col items-center p-3 rounded-md bg-slate-100 dark:bg-slate-700/50">
                <Home size={24} className="mb-2 text-brand-purple" />
                <p className="text-sm font-medium text-gray-800 dark:text-gray-100">Scene Redecoration</p>
                <p className="text-xs text-gray-600 dark:text-gray-400">Maintain layout, change theme (e.g., redecorate a room).</p>
              </div>
              <div className="flex flex-col items-center p-3 rounded-md bg-slate-100 dark:bg-slate-700/50">
                <Edit size={24} className="mb-2 text-brand-purple" />
                <p className="text-sm font-medium text-gray-800 dark:text-gray-100">Sketch to Image</p>
                <p className="text-xs text-gray-600 dark:text-gray-400">Transform drawings into detailed images.</p>
              </div>
            </div>
          </div>

          {/* Main tool area */}
          <div className="mb-6">
            <Tabs value={activeTab} onValueChange={setActiveTab}>
              <TabsList className="grid grid-cols-2 w-full max-w-md mx-auto mb-4">
                <TabsTrigger value="upload" disabled={activeTab === "result"}>
                  Upload & Configure
                </TabsTrigger>
                <TabsTrigger value="result" disabled={!resultImage}>
                  Result
                </TabsTrigger>
              </TabsList>

              <TabsContent value="upload" className="m-0">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  {/* Left column: Upload and conditioning settings */}
                  <div>
                    <h3 className="text-sm font-medium mb-3">1. Upload Reference Image</h3>
                    <input
                      type="file"
                      ref={fileInputRef}
                      onChange={handleFileUpload}
                      accept={ACCEPTED_FILE_TYPES}
                      className="hidden"
                    />
                    <div
                      className="border-2 border-dashed border-gray-200 dark:border-gray-700 rounded-lg p-6 cursor-pointer hover:border-brand-purple transition-colors flex flex-col items-center justify-center h-[250px]"
                      onClick={triggerFileInput}
                    >
                      {sourceImage ? (
                        <img
                          src={sourceImage}
                          alt="Source"
                          className="max-w-full max-h-full object-contain"
                        />
                      ) : (
                        <>
                          <Upload size={32} className="mx-auto mb-3 text-gray-400" />
                          <h3 className="text-sm font-medium mb-2">Upload a reference image</h3>
                          <p className="text-xs text-gray-500 mb-3">Click to browse or drag and drop</p>
                          <Button
                            size="sm"
                            className="bg-brand-purple hover:bg-brand-purple/90 text-xs"
                            onClick={(e) => {
                              e.stopPropagation();
                              triggerFileInput();
                            }}
                          >
                            Select Image
                          </Button>
                        </>
                      )}
                    </div>

                    {/* Conditioning Mode and Strength - Always visible */}
                    <div className="mt-4"> {/* Ensure this div is always rendered */}
                      <h3 className="text-sm font-medium mb-3">2. Conditioning Settings</h3>
                      <div className="space-y-4">
                        <div>
                          <Label className="text-xs font-medium text-gray-700 dark:text-gray-300">Conditioning Mode</Label>
                          <RadioGroup
                            value={conditioningMode}
                            onValueChange={(value) => setConditioningMode(value as "CANNY" | "SEGMENTATION")}
                            className="flex space-x-4 mt-1"
                          >
                            <div className="flex items-center space-x-2">
                              <RadioGroupItem value="CANNY" id="canny" />
                              <Label htmlFor="canny" className="text-sm">Canny Edge</Label>
                              <TooltipProvider>
                                <Tooltip>
                                  <TooltipTrigger asChild>
                                    <Info size={12} className="text-gray-400 hover:text-gray-600 dark:hover:text-gray-200 cursor-help" />
                                  </TooltipTrigger>
                                  <TooltipContent side="bottom" className="max-w-xs">
                                    <p className="text-xs">Detects and uses the primary edges/outlines of your reference image to guide the structure of the new image. Good for line art or preserving distinct shapes.</p>
                                  </TooltipContent>
                                </Tooltip>
                              </TooltipProvider>
                            </div>
                            <div className="flex items-center space-x-2">
                              <RadioGroupItem value="SEGMENTATION" id="segmentation" />
                              <Label htmlFor="segmentation" className="text-sm">Segmentation</Label>
                              <TooltipProvider>
                                <Tooltip>
                                  <TooltipTrigger asChild>
                                    <Info size={12} className="text-gray-400 hover:text-gray-600 dark:hover:text-gray-200 cursor-help" />
                                  </TooltipTrigger>
                                  <TooltipContent side="bottom" className="max-w-xs">
                                    <p className="text-xs">Divides your reference image into colored segments representing different objects or areas. The AI uses these segments to guide content placement. Good for re-theming scenes.</p>
                                  </TooltipContent>
                                </Tooltip>
                              </TooltipProvider>
                            </div>
                          </RadioGroup>
                        </div>

                        <div>
                          <div className="flex items-center justify-between mb-1">
                            <Label className="text-xs font-medium text-gray-700 dark:text-gray-300">Conditioning Strength</Label>
                            <span className="text-xs text-gray-500 dark:text-gray-400">{conditioningStrength.toFixed(1)}</span>
                          </div>
                          <Slider
                            value={[conditioningStrength]}
                            min={CONDITIONING_STRENGTH_RANGE.min}
                            max={CONDITIONING_STRENGTH_RANGE.max}
                            step={CONDITIONING_STRENGTH_RANGE.step}
                            onValueChange={(value) => setConditioningStrength(value[0])}
                            className="mb-1"
                            disabled={!sourceImage} // Disabled if no source image
                          />
                          <div className="flex justify-between text-xs text-gray-500 dark:text-gray-400">
                            <span>More Creative</span>
                            <span>More Structured</span>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>

                  {/* Right column: Prompt and Generate Button */}
                  <div>
                    <h3 className="text-sm font-medium mb-3">3. Describe Your Desired Image</h3>
                    <Textarea
                      placeholder="e.g., 'A futuristic city skyline at sunset, cyberpunk style'"
                      value={prompt}
                      onChange={(e) => setPrompt(e.target.value)}
                      className="h-32 mb-4" // Increased height
                    />

                    <h3 className="text-sm font-medium mb-2">Negative Prompt (Optional)</h3>
                    <Textarea
                      placeholder="e.g., 'blurry, low quality, text, watermark'"
                      value={negativePrompt}
                      onChange={(e) => setNegativePrompt(e.target.value)}
                      className="h-20 mb-4" // Increased height
                    />
                    
                    {/* Generate Button - always visible but disabled conditionally */}
                    <div className="flex items-center justify-center gap-2 mt-4 pt-4 border-t border-gray-200 dark:border-gray-700">
                      <Button
                        size="sm"
                        variant="outline"
                          className="h-8 text-xs gap-1"
                          onClick={resetProcess}
                        >
                          <X size={12} />
                          Cancel
                        </Button>
                        <Button
                          size="sm"
                          className="h-8 text-xs gap-1 bg-brand-purple hover:bg-brand-purple/90"
                          onClick={generateImage}
                          disabled={isProcessing || !prompt}
                        >
                          {isProcessing ? (
                            <>
                              <RefreshCw size={12} className="animate-spin" />
                              Processing...
                            </>
                          ) : (
                            <>
                              <Check size={12} />
                              Generate Image
                            </>
                          )}
                        </Button>
                      </div>
                    </div>
                  </div>
                </TabsContent>

              <TabsContent value="result" className="m-0">
                {resultImage && (
                  <div>
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                      <div>
                        <h3 className="text-sm font-medium mb-3">Reference Image</h3>
                        <div className="rounded-lg overflow-hidden bg-gray-100 dark:bg-gray-800 mb-4 h-[300px] flex items-center justify-center">
                          <img
                            src={sourceImage!}
                            alt="Reference"
                            className="max-w-full max-h-full object-contain"
                          />
                        </div>
                      </div>
                      <div>
                        <h3 className="text-sm font-medium mb-3">Generated Image</h3>
                        <div className="rounded-lg overflow-hidden bg-gray-100 dark:bg-gray-800 mb-4 h-[300px] flex items-center justify-center">
                          <img
                            src={resultImage}
                            alt="Result"
                            className="max-w-full max-h-full object-contain"
                          />
                        </div>
                      </div>
                    </div>
                    <p className="text-center text-sm text-gray-500 mb-4">
                      Image generated successfully using {conditioningMode.toLowerCase()} conditioning!
                    </p>
                    <div className="flex items-center justify-center gap-2">
                      <Button
                        size="sm"
                        variant="outline"
                        className="h-8 text-xs gap-1"
                        onClick={resetProcess}
                      >
                        <Trash2 size={12} />
                        Start Over
                      </Button>
                      <Button
                        size="sm"
                        className="h-8 text-xs gap-1 bg-brand-purple hover:bg-brand-purple/90"
                        onClick={downloadImage}
                      >
                        <Download size={12} />
                        Download
                      </Button>
                    </div>
                  </div>
                )}
              </TabsContent>
            </Tabs>
          </div>

          {/* "Perfect for:" section removed */}
        </div>
      </main>
    </>
  );
};

export default ImageConditioningPage;
