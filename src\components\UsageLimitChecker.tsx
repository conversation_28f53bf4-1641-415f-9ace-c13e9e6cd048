import React, { useState, useEffect } from 'react';
import { <PERSON><PERSON><PERSON>ir<PERSON>, <PERSON>rk<PERSON> } from 'lucide-react';
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert';
import { Button } from '@/components/ui/button';
import { hasReachedUsageLimit } from '@/services/subscriptionService';
import { useAuth } from '@/contexts/AuthContext';

interface UsageLimitCheckerProps {
  imageType: 'standard' | 'premium';
  onContinue: () => void;
  onCancel?: () => void;
}

/**
 * Component to check if the user has reached their usage limit
 * Shows an alert if the limit is reached and provides options to upgrade or cancel
 */
const UsageLimitChecker: React.FC<UsageLimitCheckerProps> = ({ 
  imageType, 
  onContinue, 
  onCancel 
}) => {
  const { user } = useAuth();
  const [checking, setChecking] = useState(true);
  const [limitReached, setLimitReached] = useState(false);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const checkUsageLimit = async () => {
      if (!user) {
        setChecking(false);
        return;
      }

      try {
        setChecking(true);
        setError(null);
        
        const isLimitReached = await hasReachedUsageLimit(user.id, imageType);
        setLimitReached(isLimitReached);
      } catch (err) {
        if (import.meta.env.DEV) {
          console.error('Error checking usage limit:', err);
        }
        setError('Failed to check usage limits. Proceeding anyway.');
      } finally {
        setChecking(false);
      }
    };

    checkUsageLimit();
  }, [user, imageType]);

  // If still checking or no limit reached, render nothing (or a loading state)
  if (checking) {
    return null; // Or return a loading spinner
  }

  // If there was an error checking limits, show a warning but allow to continue
  if (error) {
    return (
      <Alert className="mb-4">
        <AlertCircle className="h-4 w-4" />
        <AlertTitle>Warning</AlertTitle>
        <AlertDescription>{error}</AlertDescription>
        <div className="mt-2 flex gap-2">
          <Button size="sm" onClick={onContinue}>Continue</Button>
          {onCancel && (
            <Button size="sm" variant="outline" onClick={onCancel}>Cancel</Button>
          )}
        </div>
      </Alert>
    );
  }

  // If limit reached, show an alert
  if (limitReached) {
    return (
      <Alert variant="destructive" className="mb-4">
        <AlertCircle className="h-4 w-4" />
        <AlertTitle>Usage Limit Reached</AlertTitle>
        <AlertDescription>
          {imageType === 'standard' 
            ? 'You have reached your weekly limit for standard images. Upgrade to Premium for unlimited standard images.'
            : 'You have reached your monthly limit for premium images.'}
        </AlertDescription>
        <div className="mt-4 flex gap-2">
          <Button className="bg-gradient-to-r from-purple-500 to-blue-500 text-white">
            <Sparkles className="mr-2 h-4 w-4" />
            Upgrade to Premium
          </Button>
          {onCancel && (
            <Button variant="outline" onClick={onCancel}>Cancel</Button>
          )}
        </div>
      </Alert>
    );
  }

  // If no limit reached, trigger the continue callback
  useEffect(() => {
    if (!checking && !limitReached && !error) {
      onContinue();
    }
  }, [checking, limitReached, error, onContinue]);

  return null;
};

export default UsageLimitChecker;
