import React, { useEffect, useState } from 'react';
import { Card, CardContent } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { AlertTriangle, ImagePlus, Trash2 } from 'lucide-react';
import { ImageHistoryItem } from '@/services/imageHistoryService';
import { Link } from 'react-router-dom';
import { useAuth } from '@/contexts/AuthContext';
import { canDeleteMedia } from '@/services/deleteService';
import LazyImage from '@/components/LazyImage';
import ImageSkeleton from '@/components/ImageSkeleton';

import ImageWithRetry from '@/components/ImageWithRetry';
import { useLazyLoading } from '@/hooks/useLazyLoading';

interface ImageHistoryGalleryProps {
  onSelectImage: (image: ImageHistoryItem) => void;
  onDeleteImage?: (image: ImageHistoryItem) => void;
  isLoading: boolean;
  error: Error | null;
  history: ImageHistoryItem[];
  presignedUrls: { [key: string]: string };
}

const ImageHistoryGallery: React.FC<ImageHistoryGalleryProps> = ({
  onSelectImage,
  onDeleteImage,
  isLoading,
  error,
  history,
  presignedUrls,
}) => {
  const { user } = useAuth();
  const [imageUrls, setImageUrls] = useState<{ [key: string]: string }>({});

  // Initialize lazy loading performance tracking
  const {
    metrics,
    recordImageLoad,
    recordImageError,
    recordImageInView,
    recordImageOutOfView,
    setTotalImages,
    resetMetrics,
  } = useLazyLoading({
    enableMetrics: import.meta.env.DEV, // Only track in development
    onLoadComplete: (metrics) => {
      if (import.meta.env.DEV) {
        console.log('[ImageHistoryGallery] Lazy loading complete:', metrics);
      }
    },
  });

  const handleImageClick = (item: ImageHistoryItem, event: React.MouseEvent) => {
    // Prevent opening image details when clicking on delete button
    if ((event.target as HTMLElement).closest('.delete-button')) {
      return;
    }
    onSelectImage(item);
  };

  const handleDeleteClick = (item: ImageHistoryItem, event: React.MouseEvent) => {
    event.stopPropagation();
    if (onDeleteImage) {
      onDeleteImage(item);
    }
  };

  useEffect(() => {
    // Update local image URLs when presignedUrls prop changes
    setImageUrls(presignedUrls);
  }, [presignedUrls]);

  // Update total images count for performance tracking
  useEffect(() => {
    setTotalImages(history.length);
    if (history.length === 0) {
      resetMetrics();
    }
  }, [history.length, setTotalImages, resetMetrics]);

  if (isLoading) {
    return (
      <div className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-5 xl:grid-cols-6 gap-4">
        {[...Array(12)].map((_, i) => (
          <ImageSkeleton key={i} />
        ))}
      </div>
    );
  }

  if (error) {
    return (
      <div className="flex flex-col items-center justify-center text-center py-10 px-4 border-2 border-dashed border-red-300 dark:border-red-700 rounded-lg">
        <AlertTriangle className="h-12 w-12 text-red-500 mb-4" />
        <h3 className="text-xl font-semibold mb-2 text-red-700 dark:text-red-400">Error Loading Images</h3>
        <p className="text-sm text-muted-foreground mb-4">
          We encountered an issue trying to load your image history. Please try again later.
        </p>
        <p className="text-xs text-red-500 dark:text-red-600">Error: {error.message}</p>
        {/* Optionally, add a retry button that calls a passed-in refetch function */}
      </div>
    );
  }

  if (history.length === 0) {
    return (
      <div className="flex flex-col items-center justify-center text-center py-10 px-4 border-2 border-dashed border-gray-300 dark:border-gray-700 rounded-lg">
        <ImagePlus className="h-12 w-12 text-gray-400 dark:text-gray-500 mb-4" />
        <h3 className="text-xl font-semibold mb-2">No Images Yet</h3>
        <p className="text-sm text-muted-foreground mb-4">
          You haven't generated any images. Let's create your first masterpiece!
        </p>
        <Button asChild>
          <Link to="/image-generator">Create New Image</Link>
        </Button>
      </div>
    );
  }

  return (
    <>
      <div className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-5 xl:grid-cols-6 gap-4">
        {history.map((item) => (
          <Card
            key={item.id}
            className="overflow-hidden cursor-pointer group relative aspect-square"
            onClick={(e) => handleImageClick(item, e)}
          >
            <CardContent className="p-0">
              <ImageWithRetry
                s3_key={item.s3_key}
                fallbackUrl={imageUrls[item.s3_key] || item.s3_url || '/placeholder.svg'}
                alt={item.prompt?.substring(0, 50) || 'Generated Image'}
                className="w-full h-full"
                rootMargin="100px" // Start loading when image is 100px away from viewport
                threshold={0.1}
                showRetryButton={true}
                showConnectionStatus={false} // Disabled per user request
                onError={(e) => {
                  (e.target as HTMLImageElement).src = '/placeholder.svg';
                }}
                onLoadComplete={recordImageLoad}
                onLoadError={recordImageError}
                onInView={recordImageInView}
                onOutOfView={recordImageOutOfView}
              />
              <div className="absolute inset-0 bg-black bg-opacity-0 group-hover:bg-opacity-40 transition-opacity duration-300 flex items-center justify-center">
                <p className="text-white text-xs text-center p-2 opacity-0 group-hover:opacity-100 transition-opacity duration-300">
                  {item.prompt?.substring(0, 100) || 'No description'}...
                </p>
              </div>

              {/* Delete Button */}
              {user && canDeleteMedia(item.user_id, user.id) && onDeleteImage && (
                <div className="absolute top-2 right-2 opacity-0 group-hover:opacity-100 transition-opacity duration-300">
                  <Button
                    variant="destructive"
                    size="icon"
                    className="h-8 w-8 bg-red-600/80 hover:bg-red-600 border-0 delete-button"
                    onClick={(e) => handleDeleteClick(item, e)}
                    title="Delete Image"
                  >
                    <Trash2 className="h-4 w-4 text-white" />
                  </Button>
                </div>
              )}
            </CardContent>
          </Card>
        ))}
      </div>
    </>
  );
};

export default ImageHistoryGallery;
