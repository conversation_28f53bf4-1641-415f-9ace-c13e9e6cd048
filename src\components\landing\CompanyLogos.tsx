const CompanyLogos = () => {
  return (
    <section className="relative overflow-hidden py-20 md:py-24">
      <div className="container relative z-20 mx-auto px-4 md:px-8">
        {/* Section header */}
        <div className="text-center mb-16">
          <h2 className="text-2xl md:text-3xl font-semibold mb-6 leading-tight tracking-tight text-white">
            Used by teams of <span className="bg-gradient-to-r from-purple-400 to-pink-400 bg-clip-text text-transparent">leading companies</span>
          </h2>
        </div>

        {/* Company logos - White versions */}
        <div className="grid grid-cols-2 md:grid-cols-4 gap-8 md:gap-12 max-w-5xl mx-auto">
          {/* Logo 1 - AccuWeather */}
          <div className="flex items-center justify-center">
            <img
              src="https://upload.wikimedia.org/wikipedia/commons/2/2e/AccuWeather_Logo.svg"
              alt="AccuWeather"
              loading="lazy"
              className="h-10 w-auto object-contain opacity-70 hover:opacity-100 transition-opacity"
            />
          </div>

          {/* Logo 2 - CA Technologies */}
          <div className="flex items-center justify-center">
            <img
              src="https://upload.wikimedia.org/wikipedia/commons/0/08/CA_Technologies_logo.svg"
              alt="CA Technologies"
              loading="lazy"
              className="h-8 w-auto object-contain opacity-70 hover:opacity-100 transition-opacity invert"
            />
          </div>

          {/* Logo 3 - Dell */}
          <div className="flex items-center justify-center">
            <img
              src="https://upload.wikimedia.org/wikipedia/commons/4/48/Dell_Logo.svg"
              alt="Dell"
              loading="lazy"
              className="h-8 w-auto object-contain opacity-70 hover:opacity-100 transition-opacity invert"
            />
          </div>

          {/* Logo 4 - Amazon */}
          <div className="flex items-center justify-center">
            <img
              src="https://www.freepnglogos.com/uploads/amazon-png-logo-vector/woodland-gardening-amazon-png-logo-vector-8.png"
              alt="Amazon"
              loading="lazy"
              className="h-8 w-auto object-contain opacity-70 hover:opacity-100 transition-opacity invert"
            />
          </div>

          {/* Logo 5 - Hyatt */}
          <div className="flex items-center justify-center">
            <img
              src="https://upload.wikimedia.org/wikipedia/commons/9/91/Hyatt_Logo.svg"
              alt="Hyatt"
              loading="lazy"
              className="h-8 w-auto object-contain opacity-70 hover:opacity-100 transition-opacity invert"
            />
          </div>

          {/* Logo 6 - LifeTouch */}
          <div className="flex items-center justify-center">
            <img
              src="https://upload.wikimedia.org/wikipedia/commons/9/9a/Lifetouch_logo.svg"
              alt="LifeTouch"
              loading="lazy"
              className="h-8 w-auto object-contain opacity-70 hover:opacity-100 transition-opacity"
            />
          </div>

          {/* Logo 7 - Krispy Kreme */}
          <div className="flex items-center justify-center">
            <img
              src="https://upload.wikimedia.org/wikipedia/commons/8/8b/Logo.KrispyKreme.svg"
              alt="Krispy Kreme"
              loading="lazy"
              className="h-8 w-auto object-contain opacity-70 hover:opacity-100 transition-opacity invert"
            />
          </div>

          {/* Logo 8 - Ray-Ban */}
          <div className="flex items-center justify-center">
            <img
              src="https://upload.wikimedia.org/wikipedia/commons/7/7b/Ray-Ban_logo_2.svg"
              alt="Ray-Ban"
              loading="lazy"
              className="h-8 w-auto object-contain opacity-70 hover:opacity-100 transition-opacity"
            />
          </div>
        </div>
      </div>
    </section>
  );
};

export default CompanyLogos;