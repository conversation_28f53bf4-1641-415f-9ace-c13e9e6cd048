import { useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { useAuth } from '@/contexts/AuthContext';

const SuperadminRedirect = () => {
  const { user, checkSuperadminAccess, isLoading } = useAuth();
  const navigate = useNavigate();

  useEffect(() => {
    if (!isLoading && user) {
      if (checkSuperadminAccess()) {
        // Redirect superadmin users to superadmin dashboard
        navigate('/superadmin', { replace: true });
      }
    }
  }, [user, isLoading, checkSuperadminAccess, navigate]);

  return null; // This component doesn't render anything
};

export default SuperadminRedirect;