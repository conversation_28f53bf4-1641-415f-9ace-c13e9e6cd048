import { useState, useEffect, useCallback, useRef } from 'react';
import { getPresignedUrl } from '@/services/s3Service';
import { videoPresignedUrlCache } from '@/services/videoPresignedUrlCache';

interface UseProgressiveVideoUrlOptions {
  s3_key: string;
  fallbackUrl: string;
  enableRefresh?: boolean;
  refreshTimeout?: number;
  maxRetries?: number;
  retryDelay?: number;
  videoMetadata?: {
    type?: 'text-to-video' | 'multi-shot-auto' | 'multi-shot-manual';
    duration?: number;
    fileSize?: number;
  };
}

interface UseProgressiveVideoUrlReturn {
  url: string;
  isLoading: boolean;
  hasError: boolean;
  isUsingFallback: boolean;
  refreshUrl: () => Promise<void>;
  retryCount: number;
  videoMetadata?: {
    type?: string;
    duration?: number;
    fileSize?: number;
  };
}

/**
 * Progressive Video URL Hook - Sprint 18 Phase 2
 * 
 * Provides progressive loading for video URLs with immediate display using cached/fallback URLs
 * and background enhancement with fresh presigned URLs. Based on the successful image progressive
 * loading pattern from Sprint 17.
 * 
 * Features:
 * - Immediate display with cached or fallback URLs
 * - Background refresh of fresh presigned URLs
 * - Intelligent caching with video-specific TTL
 * - Retry logic with exponential backoff
 * - Video metadata support for optimization
 */
export const useProgressiveVideoUrl = ({
  s3_key,
  fallbackUrl,
  enableRefresh = true,
  refreshTimeout = 8000, // Longer timeout for videos
  maxRetries = 2,
  retryDelay = 1000,
  videoMetadata,
}: UseProgressiveVideoUrlOptions): UseProgressiveVideoUrlReturn => {
  const [url, setUrl] = useState<string>(fallbackUrl);
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const [hasError, setHasError] = useState<boolean>(false);
  const [isUsingFallback, setIsUsingFallback] = useState<boolean>(true);
  const [retryCount, setRetryCount] = useState<number>(0);
  const [cachedVideoMetadata, setCachedVideoMetadata] = useState<{
    type?: string;
    duration?: number;
    fileSize?: number;
  } | undefined>(videoMetadata);

  const abortControllerRef = useRef<AbortController | null>(null);
  const refreshTimeoutRef = useRef<NodeJS.Timeout | null>(null);

  // Phase 2: Progressive URL refresh with background enhancement
  const refreshUrl = useCallback(async (): Promise<void> => {
    if (!s3_key || !enableRefresh) {
      return;
    }

    // Cancel any existing request
    if (abortControllerRef.current) {
      abortControllerRef.current.abort();
    }

    // Create new abort controller for this request
    const controller = new AbortController();
    abortControllerRef.current = controller;

    setIsLoading(true);
    setHasError(false);

    let currentRetry = 0;

    const attemptRefresh = async (): Promise<void> => {
      try {
        if (import.meta.env.DEV) {
          console.log(`[ProgressiveVideoUrl] Refreshing URL for ${s3_key} (attempt ${currentRetry + 1})`);
        }

        // Create timeout promise for video requests
        const timeoutPromise = new Promise<never>((_, reject) => {
          setTimeout(() => reject(new Error('Request timeout')), refreshTimeout);
        });

        // Race between the actual request and timeout
        const result = await Promise.race([
          getPresignedUrl(s3_key),
          timeoutPromise
        ]);

        // Check if request was aborted
        if (controller.signal.aborted) {
          return;
        }

        if (result.success && result.url) {
          // Cache the successful result with video metadata
          videoPresignedUrlCache.set(s3_key, result.url, 45 * 60 * 1000, {
            retryCount: currentRetry,
            loadTime: Date.now() - Date.now(), // Approximate load time
            videoType: videoMetadata?.type,
            duration: videoMetadata?.duration,
            fileSize: videoMetadata?.fileSize,
          });

          // Phase 2: Only update URL if it's different from what we're showing
          // This prevents unnecessary re-renders when we already have a good URL
          if (result.url !== url) {
            setUrl(result.url);
          }
          setIsUsingFallback(false);
          setHasError(false);
          setRetryCount(currentRetry);

          // Update cached metadata if available
          const cached = videoPresignedUrlCache.get(s3_key);
          if (cached) {
            setCachedVideoMetadata({
              type: cached.videoType,
              duration: cached.duration,
              fileSize: cached.fileSize,
            });
          }

          if (import.meta.env.DEV) {
            console.log(`[ProgressiveVideoUrl] Successfully refreshed URL for ${s3_key} (attempt ${currentRetry + 1})`);
          }
        } else {
          throw new Error(result.error || 'Failed to get presigned URL');
        }
      } catch (error: any) {
        // Check if request was aborted
        if (controller.signal.aborted) {
          return;
        }

        currentRetry++;
        setRetryCount(currentRetry);

        if (currentRetry <= maxRetries) {
          if (import.meta.env.DEV) {
            console.warn(`[ProgressiveVideoUrl] Retry ${currentRetry}/${maxRetries} for ${s3_key}:`, error);
          }

          // Exponential backoff for video requests
          const delay = retryDelay * Math.pow(2, currentRetry - 1);
          await new Promise(resolve => setTimeout(resolve, delay));
          
          // Check if request was aborted during delay
          if (!controller.signal.aborted) {
            await attemptRefresh();
          }
        } else {
          // All retries failed, keep using fallback
          setHasError(true);
          setIsUsingFallback(true);
          
          if (import.meta.env.DEV) {
            console.error(`[ProgressiveVideoUrl] Failed to refresh URL for ${s3_key} after ${maxRetries} retries:`, error);
          }
        }
      } finally {
        setIsLoading(false);
      }
    };

    await attemptRefresh();
  }, [s3_key, enableRefresh, refreshTimeout, maxRetries, retryDelay, url, videoMetadata]);

  // Phase 2: Automatic background refresh with delay for videos
  useEffect(() => {
    if (!s3_key || !enableRefresh) {
      return;
    }

    // Clear any existing timeout
    if (refreshTimeoutRef.current) {
      clearTimeout(refreshTimeoutRef.current);
    }

    // Check if we have a cached URL first
    const cached = videoPresignedUrlCache.get(s3_key);
    if (cached && cached.url) {
      // We have a cached URL, use it immediately
      setUrl(cached.url);
      setIsUsingFallback(false);
      setHasError(false);
      setRetryCount(0);
      setCachedVideoMetadata({
        type: cached.videoType,
        duration: cached.duration,
        fileSize: cached.fileSize,
      });

      if (import.meta.env.DEV) {
        console.log(`[ProgressiveVideoUrl] Using cached URL for ${s3_key}`);
      }
    } else {
      // No cached URL, start with fallback and refresh in background
      setUrl(fallbackUrl);
      setIsUsingFallback(true);
      setHasError(false);
      setRetryCount(0);

      // Start background refresh after a short delay to allow UI to render
      refreshTimeoutRef.current = setTimeout(() => {
        refreshUrl();
      }, 100); // Short delay for immediate visual feedback
    }

    return () => {
      if (refreshTimeoutRef.current) {
        clearTimeout(refreshTimeoutRef.current);
      }
    };
  }, [s3_key, fallbackUrl, enableRefresh, refreshUrl]);

  // Phase 2: Update state when fallback URL changes, but preserve cached URLs
  useEffect(() => {
    if (!s3_key) {
      setUrl(fallbackUrl);
      setIsUsingFallback(true);
      setHasError(false);
      setRetryCount(0);
      setCachedVideoMetadata(videoMetadata);
      return;
    }

    // Check if we have a cached URL for this s3_key
    const cached = videoPresignedUrlCache.get(s3_key);
    if (cached && cached.url) {
      setUrl(cached.url);
      setIsUsingFallback(false);
      setHasError(false);
      setRetryCount(0);
      setCachedVideoMetadata({
        type: cached.videoType,
        duration: cached.duration,
        fileSize: cached.fileSize,
      });
    } else {
      setUrl(fallbackUrl);
      setIsUsingFallback(true);
      setHasError(false);
      setRetryCount(0);
      setCachedVideoMetadata(videoMetadata);
    }
  }, [s3_key, fallbackUrl, videoMetadata]);

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      if (abortControllerRef.current) {
        abortControllerRef.current.abort();
      }
      if (refreshTimeoutRef.current) {
        clearTimeout(refreshTimeoutRef.current);
      }
    };
  }, []);

  return {
    url,
    isLoading,
    hasError,
    isUsingFallback,
    refreshUrl,
    retryCount,
    videoMetadata: cachedVideoMetadata,
  };
};

export default useProgressiveVideoUrl;
