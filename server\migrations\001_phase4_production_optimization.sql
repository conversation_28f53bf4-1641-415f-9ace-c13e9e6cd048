-- ============================================================================
-- VibeNecto Phase 4: Production Optimization Migration
-- This migration adds performance indexes and monitoring columns
-- ============================================================================

-- Start transaction
BEGIN;

-- Log migration start
DO $$
BEGIN
  RAISE NOTICE 'Starting VibeNecto Phase 4 production optimization migration at %', NOW();
END $$;

-- ============================================================================
-- PERFORMANCE INDEXES (C1.1)
-- ============================================================================

-- Create indexes for improved query performance
-- These indexes are created CONCURRENTLY to avoid blocking existing operations

-- Video history indexes for user queries
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_video_history_user_created 
ON video_history(user_id, created_at DESC);

-- Video status filtering
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_video_history_status 
ON video_history(status);

-- Combined user and status filtering
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_video_history_user_status 
ON video_history(user_id, status);

-- Video shots relationship index
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_video_shots_video_id 
ON video_shots(video_id, shot_order);

-- Usage tracking user lookup
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_usage_tracking_user_id 
ON usage_tracking(user_id);

-- Profiles email lookup (if not already exists)
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_profiles_email 
ON profiles(email);

-- Video history job_id lookup for status checks
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_video_history_job_id 
ON video_history(job_id);

-- Video history completion time for analytics
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_video_history_completed_at 
ON video_history(completed_at) WHERE completed_at IS NOT NULL;

-- ============================================================================
-- MONITORING COLUMNS (C1.2)
-- ============================================================================

-- Add performance monitoring columns to video_history
DO $$
BEGIN
  -- Add processing time tracking
  IF NOT EXISTS (SELECT 1 FROM information_schema.columns 
                 WHERE table_name = 'video_history' AND column_name = 'processing_time_seconds') THEN
    ALTER TABLE video_history ADD COLUMN processing_time_seconds INTEGER DEFAULT 0;
    RAISE NOTICE 'Added processing_time_seconds column to video_history';
  ELSE
    RAISE NOTICE 'processing_time_seconds column already exists in video_history';
  END IF;

  -- Add retry count tracking
  IF NOT EXISTS (SELECT 1 FROM information_schema.columns 
                 WHERE table_name = 'video_history' AND column_name = 'retry_count') THEN
    ALTER TABLE video_history ADD COLUMN retry_count INTEGER DEFAULT 0;
    RAISE NOTICE 'Added retry_count column to video_history';
  ELSE
    RAISE NOTICE 'retry_count column already exists in video_history';
  END IF;

  -- Add error details tracking
  IF NOT EXISTS (SELECT 1 FROM information_schema.columns 
                 WHERE table_name = 'video_history' AND column_name = 'error_details') THEN
    ALTER TABLE video_history ADD COLUMN error_details TEXT;
    RAISE NOTICE 'Added error_details column to video_history';
  ELSE
    RAISE NOTICE 'error_details column already exists in video_history';
  END IF;

  -- Add API response time tracking
  IF NOT EXISTS (SELECT 1 FROM information_schema.columns 
                 WHERE table_name = 'video_history' AND column_name = 'api_response_time_ms') THEN
    ALTER TABLE video_history ADD COLUMN api_response_time_ms INTEGER;
    RAISE NOTICE 'Added api_response_time_ms column to video_history';
  ELSE
    RAISE NOTICE 'api_response_time_ms column already exists in video_history';
  END IF;

EXCEPTION
  WHEN OTHERS THEN
    RAISE NOTICE 'Error adding monitoring columns: % - This is not critical', SQLERRM;
END $$;

-- ============================================================================
-- ANALYTICS VIEWS
-- ============================================================================

-- Create or replace performance analytics view
CREATE OR REPLACE VIEW video_performance_analytics AS
SELECT 
  DATE_TRUNC('day', created_at) as date,
  video_type,
  status,
  COUNT(*) as total_videos,
  AVG(processing_time_seconds) as avg_processing_time,
  AVG(api_response_time_ms) as avg_api_response_time,
  COUNT(*) FILTER (WHERE status = 'completed') as completed_count,
  COUNT(*) FILTER (WHERE status = 'failed') as failed_count,
  COUNT(*) FILTER (WHERE retry_count > 0) as retried_count,
  AVG(retry_count) as avg_retry_count
FROM video_history 
WHERE created_at >= NOW() - INTERVAL '30 days'
GROUP BY DATE_TRUNC('day', created_at), video_type, status
ORDER BY date DESC, video_type;

-- Create or replace user activity analytics view
CREATE OR REPLACE VIEW user_activity_analytics AS
SELECT 
  DATE_TRUNC('day', created_at) as date,
  COUNT(DISTINCT user_id) as active_users,
  COUNT(*) as total_generations,
  COUNT(*) FILTER (WHERE video_type = 'TEXT_VIDEO') as text_video_count,
  COUNT(*) FILTER (WHERE video_type = 'MULTI_SHOT_AUTOMATED') as auto_multishot_count,
  COUNT(*) FILTER (WHERE video_type = 'MULTI_SHOT_MANUAL') as manual_multishot_count,
  AVG(duration_seconds) as avg_duration
FROM video_history 
WHERE created_at >= NOW() - INTERVAL '30 days'
GROUP BY DATE_TRUNC('day', created_at)
ORDER BY date DESC;

-- ============================================================================
-- MAINTENANCE FUNCTIONS
-- ============================================================================

-- Enhanced cleanup function for maintenance
CREATE OR REPLACE FUNCTION cleanup_old_videos(days_to_keep INTEGER DEFAULT 90)
RETURNS TABLE(deleted_count INTEGER, freed_space_estimate TEXT) AS $$
DECLARE
  deleted_videos INTEGER;
  avg_file_size INTEGER := 10; -- Estimate 10MB per video
BEGIN
  -- Delete videos older than specified days that are completed or failed
  DELETE FROM video_history 
  WHERE created_at < NOW() - INTERVAL '1 day' * days_to_keep
  AND status IN ('completed', 'failed');
  
  GET DIAGNOSTICS deleted_videos = ROW_COUNT;
  
  -- Return results
  deleted_count := deleted_videos;
  freed_space_estimate := (deleted_videos * avg_file_size)::TEXT || ' MB (estimated)';
  
  -- Log cleanup action
  RAISE NOTICE 'Cleaned up % old videos older than % days at %', 
    deleted_videos, days_to_keep, NOW();
  
  RETURN NEXT;
END;
$$ LANGUAGE plpgsql;

-- Function to analyze database performance
CREATE OR REPLACE FUNCTION analyze_database_performance()
RETURNS TABLE(
  table_name TEXT,
  total_size TEXT,
  index_size TEXT,
  row_count BIGINT,
  last_vacuum TIMESTAMP,
  last_analyze TIMESTAMP
) AS $$
BEGIN
  RETURN QUERY
  SELECT 
    schemaname||'.'||tablename as table_name,
    pg_size_pretty(pg_total_relation_size(schemaname||'.'||tablename)) as total_size,
    pg_size_pretty(pg_indexes_size(schemaname||'.'||tablename)) as index_size,
    n_tup_ins + n_tup_upd as row_count,
    last_vacuum,
    last_analyze
  FROM pg_stat_user_tables 
  WHERE schemaname = 'public'
  ORDER BY pg_total_relation_size(schemaname||'.'||tablename) DESC;
END;
$$ LANGUAGE plpgsql;

-- ============================================================================
-- SECURITY ENHANCEMENTS
-- ============================================================================

-- Create RLS policies for new columns (if RLS is enabled)
DO $$
BEGIN
  -- Enable RLS on video_history if not already enabled
  IF NOT EXISTS (
    SELECT 1 FROM pg_tables 
    WHERE tablename = 'video_history' 
    AND rowsecurity = true
  ) THEN
    RAISE NOTICE 'RLS not enabled on video_history, skipping policy creation';
  ELSE
    -- Policies should already exist from the main schema
    RAISE NOTICE 'RLS policies already configured for video_history';
  END IF;
END $$;

-- ============================================================================
-- GRANTS AND PERMISSIONS
-- ============================================================================

-- Grant access to new views
GRANT SELECT ON video_performance_analytics TO authenticated;
GRANT SELECT ON video_performance_analytics TO service_role;

GRANT SELECT ON user_activity_analytics TO authenticated;
GRANT SELECT ON user_activity_analytics TO service_role;

-- Grant execute permissions on maintenance functions to service role only
GRANT EXECUTE ON FUNCTION cleanup_old_videos(INTEGER) TO service_role;
GRANT EXECUTE ON FUNCTION analyze_database_performance() TO service_role;

-- ============================================================================
-- VALIDATION AND CONSTRAINTS
-- ============================================================================

-- Add check constraints for new columns
DO $$
BEGIN
  -- Ensure processing time is non-negative
  IF NOT EXISTS (
    SELECT 1 FROM information_schema.check_constraints 
    WHERE constraint_name = 'video_history_processing_time_check'
  ) THEN
    ALTER TABLE video_history 
    ADD CONSTRAINT video_history_processing_time_check 
    CHECK (processing_time_seconds >= 0);
    RAISE NOTICE 'Added processing_time_seconds check constraint';
  END IF;

  -- Ensure retry count is non-negative
  IF NOT EXISTS (
    SELECT 1 FROM information_schema.check_constraints 
    WHERE constraint_name = 'video_history_retry_count_check'
  ) THEN
    ALTER TABLE video_history 
    ADD CONSTRAINT video_history_retry_count_check 
    CHECK (retry_count >= 0);
    RAISE NOTICE 'Added retry_count check constraint';
  END IF;

  -- Ensure API response time is positive when set
  IF NOT EXISTS (
    SELECT 1 FROM information_schema.check_constraints 
    WHERE constraint_name = 'video_history_api_response_time_check'
  ) THEN
    ALTER TABLE video_history 
    ADD CONSTRAINT video_history_api_response_time_check 
    CHECK (api_response_time_ms IS NULL OR api_response_time_ms > 0);
    RAISE NOTICE 'Added api_response_time_ms check constraint';
  END IF;

EXCEPTION
  WHEN OTHERS THEN
    RAISE NOTICE 'Error adding constraints: % - This is not critical', SQLERRM;
END $$;

-- ============================================================================
-- FINALIZATION
-- ============================================================================

-- Update statistics for query planner
ANALYZE video_history;
ANALYZE video_shots;
ANALYZE usage_tracking;
ANALYZE profiles;

-- Log migration completion
DO $$
BEGIN
  RAISE NOTICE 'VibeNecto Phase 4 production optimization migration completed successfully at %', NOW();
  RAISE NOTICE 'New features added:';
  RAISE NOTICE '- Performance indexes for faster queries';
  RAISE NOTICE '- Monitoring columns for tracking performance';
  RAISE NOTICE '- Analytics views for insights';
  RAISE NOTICE '- Maintenance functions for cleanup';
  RAISE NOTICE '- Enhanced constraints for data integrity';
END $$;

-- Commit transaction
COMMIT;

-- Final message
\echo 'Phase 4 migration completed successfully!'
\echo 'Run ANALYZE on your tables to update query planner statistics.'
\echo 'Monitor the new performance metrics in your application logs.'
