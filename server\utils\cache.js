const { logger } = require('./logger');

// In-memory cache implementation (can be replaced with Redis in production)
class MemoryCache {
  constructor() {
    this.cache = new Map();
    this.timers = new Map();
    this.stats = {
      hits: 0,
      misses: 0,
      sets: 0,
      deletes: 0
    };
  }

  // Set a value with optional TTL (time to live) in seconds
  set(key, value, ttl = 3600) {
    try {
      // Clear existing timer if any
      if (this.timers.has(key)) {
        clearTimeout(this.timers.get(key));
      }

      // Store the value
      this.cache.set(key, {
        value,
        createdAt: Date.now(),
        ttl
      });

      // Set expiration timer
      if (ttl > 0) {
        const timer = setTimeout(() => {
          this.delete(key);
        }, ttl * 1000);
        
        this.timers.set(key, timer);
      }

      this.stats.sets++;
      
      logger.info('Cache SET', { key, ttl, size: this.cache.size });
      return true;
    } catch (error) {
      logger.error('Cache SET error', { key, error: error.message });
      return false;
    }
  }

  // Get a value from cache
  get(key) {
    try {
      const item = this.cache.get(key);
      
      if (!item) {
        this.stats.misses++;
        logger.debug('Cache MISS', { key });
        return null;
      }

      // Check if item has expired
      const now = Date.now();
      const age = (now - item.createdAt) / 1000;
      
      if (item.ttl > 0 && age > item.ttl) {
        this.delete(key);
        this.stats.misses++;
        logger.debug('Cache MISS (expired)', { key, age, ttl: item.ttl });
        return null;
      }

      this.stats.hits++;
      logger.debug('Cache HIT', { key, age });
      return item.value;
    } catch (error) {
      logger.error('Cache GET error', { key, error: error.message });
      this.stats.misses++;
      return null;
    }
  }

  // Delete a value from cache
  delete(key) {
    try {
      const deleted = this.cache.delete(key);
      
      if (this.timers.has(key)) {
        clearTimeout(this.timers.get(key));
        this.timers.delete(key);
      }

      if (deleted) {
        this.stats.deletes++;
        logger.debug('Cache DELETE', { key, size: this.cache.size });
      }

      return deleted;
    } catch (error) {
      logger.error('Cache DELETE error', { key, error: error.message });
      return false;
    }
  }

  // Clear all cache entries
  clear() {
    try {
      // Clear all timers
      for (const timer of this.timers.values()) {
        clearTimeout(timer);
      }
      
      this.cache.clear();
      this.timers.clear();
      
      logger.info('Cache CLEAR', { previousSize: this.cache.size });
      return true;
    } catch (error) {
      logger.error('Cache CLEAR error', { error: error.message });
      return false;
    }
  }

  // Get cache statistics
  getStats() {
    const hitRate = this.stats.hits + this.stats.misses > 0 
      ? Math.round((this.stats.hits / (this.stats.hits + this.stats.misses)) * 100)
      : 0;

    return {
      ...this.stats,
      hitRate: `${hitRate}%`,
      size: this.cache.size,
      memoryUsage: this.getMemoryUsage()
    };
  }

  // Estimate memory usage
  getMemoryUsage() {
    let totalSize = 0;
    
    for (const [key, item] of this.cache.entries()) {
      try {
        const keySize = typeof key === 'string' ? key.length : JSON.stringify(key).length;
        const valueStr = JSON.stringify(item.value);
        const valueSize = valueStr.length > 10000 ? 10000 : valueStr.length; // Cap at 10KB
        totalSize += keySize + valueSize + 50; // Add overhead estimate
      } catch (error) {
        totalSize += 1000; // Estimate for unparseable objects
      }
      totalSize += 50; // Overhead estimate
    }
    
    return {
      bytes: totalSize,
      kb: Math.round(totalSize / 1024),
      mb: Math.round(totalSize / (1024 * 1024))
    };
  }

  // Clean up expired entries
  cleanup() {
    const now = Date.now();
    let cleaned = 0;
    
    for (const [key, item] of this.cache.entries()) {
      const age = (now - item.createdAt) / 1000;
      
      if (item.ttl > 0 && age > item.ttl) {
        this.delete(key);
        cleaned++;
      }
    }
    
    if (cleaned > 0) {
      logger.info('Cache cleanup completed', { cleaned, remaining: this.cache.size });
    }
    
    return cleaned;
  }
}

// Create cache instance
const cache = new MemoryCache();

// Cache helper functions for specific use cases
const cacheHelpers = {
  // Cache video generation status
  cacheVideoStatus: (jobId, status, ttl = 300) => {
    const key = `video_status:${jobId}`;
    return cache.set(key, status, ttl);
  },

  getVideoStatus: (jobId) => {
    const key = `video_status:${jobId}`;
    return cache.get(key);
  },

  // Cache presigned URLs
  cachePresignedUrl: (s3Key, url, ttl = 3600) => {
    const key = `presigned_url:${s3Key}`;
    return cache.set(key, url, ttl);
  },

  getPresignedUrl: (s3Key) => {
    const key = `presigned_url:${s3Key}`;
    return cache.get(key);
  },

  // Cache user session data
  cacheUserSession: (userId, sessionData, ttl = 1800) => {
    const key = `user_session:${userId}`;
    return cache.set(key, sessionData, ttl);
  },

  getUserSession: (userId) => {
    const key = `user_session:${userId}`;
    return cache.get(key);
  },

  // Cache database query results
  cacheQueryResult: (queryKey, result, ttl = 600) => {
    const key = `query:${queryKey}`;
    return cache.set(key, result, ttl);
  },

  getQueryResult: (queryKey) => {
    const key = `query:${queryKey}`;
    return cache.get(key);
  },

  // Cache API responses
  cacheApiResponse: (endpoint, params, response, ttl = 300) => {
    const key = `api:${endpoint}:${JSON.stringify(params)}`;
    return cache.set(key, response, ttl);
  },

  getApiResponse: (endpoint, params) => {
    const key = `api:${endpoint}:${JSON.stringify(params)}`;
    return cache.get(key);
  },

  // Invalidate cache by pattern
  invalidatePattern: (pattern) => {
    let invalidated = 0;
    
    for (const key of cache.cache.keys()) {
      if (key.includes(pattern)) {
        cache.delete(key);
        invalidated++;
      }
    }
    
    logger.info('Cache pattern invalidation', { pattern, invalidated });
    return invalidated;
  },

  // Warm up cache with common data
  warmUpCache: async () => {
    try {
      logger.info('Starting cache warm-up');
      
      // Add any common data that should be pre-cached
      // This can be expanded based on application needs
      
      logger.info('Cache warm-up completed');
    } catch (error) {
      logger.error('Cache warm-up failed', { error: error.message });
    }
  }
};

// Start periodic cleanup
const startCacheCleanup = () => {
  // Clean up expired entries every 5 minutes
  setInterval(() => {
    cache.cleanup();
  }, 5 * 60 * 1000);

  // Log cache statistics every 10 minutes
  setInterval(() => {
    const stats = cache.getStats();
    logger.info('Cache statistics', stats);
  }, 10 * 60 * 1000);

  logger.info('Cache cleanup scheduler started');
};

// Cache middleware for Express routes
const cacheMiddleware = (ttl = 300, keyGenerator = null) => {
  return (req, res, next) => {
    // Generate cache key
    const key = keyGenerator 
      ? keyGenerator(req) 
      : `${req.method}:${req.originalUrl}`;
    
    // Try to get cached response
    const cachedResponse = cache.get(key);
    
    if (cachedResponse) {
      logger.debug('Serving cached response', { key });
      return res.json(cachedResponse);
    }

    // Override res.json to cache the response
    const originalJson = res.json;
    res.json = function(data) {
      // Only cache successful responses
      if (res.statusCode >= 200 && res.statusCode < 300) {
        cache.set(key, data, ttl);
        logger.debug('Response cached', { key, ttl });
      }
      
      return originalJson.call(this, data);
    };

    next();
  };
};

module.exports = {
  cache,
  cacheHelpers,
  startCacheCleanup,
  cacheMiddleware
};
