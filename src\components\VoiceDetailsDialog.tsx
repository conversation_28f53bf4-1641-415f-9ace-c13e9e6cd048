import React, { useState, useEffect } from 'react';
import {
  <PERSON><PERSON>,
  <PERSON>alogContent,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import {
  Play,
  Pause,
  Download,
  Calendar,
  User,
  Globe,
  Settings,
  Volume2,
  MessageSquare,
  Clock,
  Hash
} from 'lucide-react';
import { toast } from 'sonner';
import {
  getVoiceById,
  formatCharacterCount,
  VOICES_BY_LANGUAGE,
  type VoiceHistoryItem
} from '@/services/voiceService';

interface VoiceDetailsDialogProps {
  voice: VoiceHistoryItem | null;
  open: boolean;
  onOpenChange: (open: boolean) => void;
}

export const VoiceDetailsDialog: React.FC<VoiceDetailsDialogProps> = ({
  voice,
  open,
  onOpenChange,
}) => {
  const [isPlaying, setIsPlaying] = useState(false);
  const [audio, setAudio] = useState<HTMLAudioElement | null>(null);

  useEffect(() => {
    if (voice?.presigned_url) {
      const audioElement = new Audio(voice.presigned_url);
      audioElement.addEventListener('ended', () => setIsPlaying(false));
      setAudio(audioElement);

      return () => {
        audioElement.removeEventListener('ended', () => setIsPlaying(false));
        audioElement.pause();
        audioElement.currentTime = 0;
      };
    }
  }, [voice?.presigned_url]);

  useEffect(() => {
    if (audio) {
      if (isPlaying) {
        audio.currentTime = 0; // Reset to beginning
        audio.play().catch(console.error);
      } else {
        audio.pause();
        audio.currentTime = 0; // Reset to beginning when paused
      }
    }
  }, [isPlaying, audio]);

  // Reset playing state when dialog closes
  useEffect(() => {
    if (!open && isPlaying) {
      setIsPlaying(false);
    }
  }, [open, isPlaying]);

  const handleTogglePlay = () => {
    setIsPlaying(!isPlaying);
  };

  const handleDownload = () => {
    if (voice?.presigned_url) {
      const a = document.createElement('a');
      a.href = voice.presigned_url;
      a.download = `voice-${voice.id}.mp3`;
      document.body.appendChild(a);
      a.click();
      document.body.removeChild(a);
      toast.success('Download started');
    } else {
      toast.error('Download URL not available');
    }
  };

  if (!voice) return null;

  const voiceInfo = getVoiceById(voice.voice_id);
  const createdDate = new Date(voice.created_at);

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-2xl max-h-[80vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <Volume2 className="h-5 w-5 text-brand-purple" />
            Voice Details
          </DialogTitle>
        </DialogHeader>

        <div className="space-y-6">
          {/* Status Badge */}
          <div className="flex justify-between items-center">
            <Badge variant={voice.status === 'completed' ? 'default' : 'secondary'} className="text-sm">
              {voice.status}
            </Badge>
            <div className="flex items-center gap-2 text-sm text-gray-500">
              <Calendar className="h-4 w-4" />
              {createdDate.toLocaleDateString()} at {createdDate.toLocaleTimeString()}
            </div>
          </div>

          {/* Voice Information Grid */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-3">
              <div className="flex items-center gap-2">
                <User className="h-4 w-4 text-gray-500" />
                <span className="font-medium">Voice:</span>
                <span>{voiceInfo?.name || voice.voice_id}</span>
              </div>
              <div className="flex items-center gap-2">
                <Globe className="h-4 w-4 text-gray-500" />
                <span className="font-medium">Language:</span>
                <span>{VOICES_BY_LANGUAGE[voice.language_code]?.languageName || voice.language_code}</span>
              </div>
              <div className="flex items-center gap-2">
                <Settings className="h-4 w-4 text-gray-500" />
                <span className="font-medium">Engine:</span>
                <span className="capitalize">{voice.engine}</span>
              </div>
            </div>
            <div className="space-y-3">
              <div className="flex items-center gap-2">
                <Volume2 className="h-4 w-4 text-gray-500" />
                <span className="font-medium">Characters:</span>
                <span>{formatCharacterCount(voice.character_count)}</span>
              </div>
              <div className="flex items-center gap-2">
                <Hash className="h-4 w-4 text-gray-500" />
                <span className="font-medium">Voice ID:</span>
                <span className="text-xs font-mono bg-gray-100 dark:bg-gray-800 px-2 py-1 rounded">
                  {voice.id}
                </span>
              </div>
            </div>
          </div>

          {/* Text Content */}
          <div className="space-y-2">
            <div className="flex items-center gap-2">
              <MessageSquare className="h-4 w-4 text-gray-500" />
              <span className="font-medium">Text Content:</span>
            </div>
            <div className="bg-gray-50 dark:bg-gray-800 rounded-lg p-4 border">
              <p className="text-sm text-gray-700 dark:text-gray-300 whitespace-pre-wrap">
                {voice.text_content}
              </p>
            </div>
          </div>

          {/* Audio Parameters */}
          {(voice.parameters?.speechRate || voice.parameters?.pitch || voice.parameters?.volume) && (
            <div className="space-y-2">
              <h4 className="font-medium flex items-center gap-2">
                <Settings className="h-4 w-4 text-gray-500" />
                Audio Parameters
              </h4>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm">
                {voice.parameters?.speechRate && (
                  <div className="bg-gray-50 dark:bg-gray-800 rounded-lg p-3">
                    <span className="font-medium">Speech Rate:</span>
                    <div className="text-lg font-mono">{voice.parameters.speechRate}x</div>
                  </div>
                )}
                {voice.parameters?.pitch && (
                  <div className="bg-gray-50 dark:bg-gray-800 rounded-lg p-3">
                    <span className="font-medium">Pitch:</span>
                    <div className="text-lg font-mono">{voice.parameters.pitch}</div>
                  </div>
                )}
                {voice.parameters?.volume && (
                  <div className="bg-gray-50 dark:bg-gray-800 rounded-lg p-3">
                    <span className="font-medium">Volume:</span>
                    <div className="text-lg font-mono">{voice.parameters.volume}</div>
                  </div>
                )}
              </div>
            </div>
          )}

          {/* Action Buttons */}
          <div className="flex items-center gap-3 pt-4 border-t">
            <Button
              onClick={handleTogglePlay}
              className="bg-brand-purple hover:bg-brand-purple/90 text-white"
              disabled={!voice.presigned_url}
            >
              {isPlaying ? <Pause className="h-4 w-4 mr-2" /> : <Play className="h-4 w-4 mr-2" />}
              {isPlaying ? 'Pause' : 'Play'}
            </Button>
            <Button
              onClick={handleDownload}
              variant="outline"
              disabled={!voice.presigned_url}
            >
              <Download className="h-4 w-4 mr-2" />
              Download
            </Button>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
};