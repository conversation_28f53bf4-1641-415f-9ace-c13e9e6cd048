import React from "react";
import { But<PERSON> } from "@/components/ui/button";
import VideoPlayer from "@/components/VideoPlayer";
import { Download, Copy, Share2, Trash2, Play } from "lucide-react";
import { UI_TEXT } from "@/constants/multiShotVideo";

interface MultiShotResultProps {
  videoUrl: string;
  videoId: string;
  onDownload: (videoUrl: string, filename?: string) => void;
  onGenerateNew: () => void;
}

const MultiShotResult: React.FC<MultiShotResultProps> = ({
  videoUrl,
  videoId,
  onDownload,
  onGenerateNew,
}) => {
  const handleDownload = () => {
    onDownload(videoUrl, `multi-shot-video-${Date.now()}.mp4`);
  };

  return (
    <div className="max-w-4xl mx-auto">
      <div className="bg-white dark:bg-gray-800 rounded-lg p-6 shadow-sm">
        <h3 className="text-lg font-medium mb-4 text-center">
          {UI_TEXT.GENERATED_VIDEO_TITLE}
        </h3>

        <div className="rounded-lg overflow-hidden bg-gray-100 dark:bg-gray-900 mb-4 aspect-video flex items-center justify-center">
          <div className="relative group w-full h-full">
            <VideoPlayer
              src={videoUrl}
              title={`Generated Video ${videoId}`}
              autoPlay={false}
              className="w-full h-full"
            />

            {/* Video actions overlay */}
            <div className="absolute bottom-4 left-1/2 transform -translate-x-1/2 opacity-0 group-hover:opacity-100 transition-opacity flex gap-2 bg-black/50 backdrop-blur-sm rounded-full p-1.5">
              <Button 
                variant="ghost" 
                size="icon" 
                className="h-8 w-8 text-white hover:bg-white/20"
                onClick={handleDownload}
              >
                <Download size={16} />
              </Button>
              <Button variant="ghost" size="icon" className="h-8 w-8 text-white hover:bg-white/20">
                <Copy size={16} />
              </Button>
              <Button variant="ghost" size="icon" className="h-8 w-8 text-white hover:bg-white/20">
                <Share2 size={16} />
              </Button>
            </div>
          </div>
        </div>

        <p className="text-center text-sm text-gray-500 mb-4">
          {UI_TEXT.VIDEO_SUCCESS_MESSAGE}
        </p>

        <div className="flex items-center justify-center gap-3">
          <Button
            size="sm"
            variant="outline"
            className="h-9 text-sm gap-2"
            onClick={onGenerateNew}
          >
            <Trash2 size={14} />
            {UI_TEXT.GENERATE_NEW}
          </Button>
          <Button
            size="sm"
            className="h-9 text-sm gap-2 bg-brand-purple hover:bg-brand-purple/90"
            onClick={handleDownload}
          >
            <Download size={14} />
            {UI_TEXT.DOWNLOAD}
          </Button>
        </div>
      </div>
    </div>
  );
};

export default MultiShotResult;