const rateLimit = require('express-rate-limit');
const { logger, logSecurity } = require('../utils/logger');

// Store for tracking rate limit violations with TTL
const violationStore = new Map();
const VIOLATION_TTL = 15 * 60 * 1000; // 15 minutes

// Cleanup function for violations with proper TTL
const cleanupViolations = () => {
  const now = Date.now();
  for (const [key, data] of violationStore.entries()) {
    if (now - data.timestamp > VIOLATION_TTL) {
      violationStore.delete(key);
    }
  }
};

// Schedule periodic cleanup every 5 minutes
setInterval(cleanupViolations, 5 * 60 * 1000);

// Custom rate limit handler
const rateLimitHandler = (req, res) => {
  const clientId = req.ip;
  const userId = req.user?.id || 'anonymous';

  // Track violations with timestamp
  const violationKey = `${clientId}_${userId}`;
  const existingData = violationStore.get(violationKey) || { count: 0, timestamp: Date.now() };
  const violations = existingData.count;
  violationStore.set(violationKey, { count: violations + 1, timestamp: Date.now() });

  // Log security event
  logSecurity('Rate Limit Exceeded', {
    ip: clientId,
    userId,
    url: req.url,
    method: req.method,
    violations: violations + 1,
    userAgent: req.get('User-Agent')
  });

  // Trigger cleanup if store is getting large
  if (violationStore.size > 100) {
    cleanupViolations();
    logger.info('Performed cleanup of rate limit violation store', {
      remainingEntries: violationStore.size
    });
  }

  res.status(429).json({
    success: false,
    error: {
      message: 'Too many requests. Please try again later.',
      type: 'RATE_LIMIT_ERROR',
      retryAfter: Math.ceil(req.rateLimit.resetTime / 1000),
      timestamp: new Date().toISOString()
    }
  });
};

// Skip rate limiting for certain conditions
const skipRateLimit = (req) => {
  // Skip for health checks
  if (req.path === '/health' || req.path === '/health/detailed') {
    return true;
  }

  // Skip for admin users (if implemented)
  if (req.user && req.user.role === 'admin') {
    return true;
  }

  return false;
};

// General API rate limiter
const generalLimiter = rateLimit({
  windowMs: 15 * 60 * 1000, // 15 minutes
  max: 300, // limit each IP to 300 requests per windowMs (increased from 100)
  message: 'Too many requests from this IP, please try again later.',
  standardHeaders: true,
  legacyHeaders: false,
  skip: skipRateLimit,
  handler: rateLimitHandler,
  keyGenerator: (req) => {
    // Use combination of IP and user ID if available from various sources
    const userId = req.user?.id || req.body?.userId || req.query?.userId;
    return userId ? `${req.ip}_${userId}` : req.ip;
  }
});

// Strict rate limiter for video generation
const videoGenerationLimiter = rateLimit({
  windowMs: 15 * 60 * 1000, // 15 minutes
  max: 10, // 10 video generations per 15 minutes (userId is now guaranteed by validation)
  message: 'Too many video generation requests. Please try again later.',
  standardHeaders: true,
  legacyHeaders: false,
  skip: skipRateLimit,
  handler: rateLimitHandler,
  keyGenerator: (req) => {
    // userId is guaranteed to be in req.body by prior validation middleware
    return `video_${req.body.userId}`;
  }
});

// Strict rate limiter for image generation
const imageGenerationLimiter = rateLimit({
  windowMs: 15 * 60 * 1000, // 15 minutes
  max: 30, // 30 image generations per 15 minutes (userId is now guaranteed by validation)
  message: 'Too many image generation requests. Please try again later.',
  standardHeaders: true,
  legacyHeaders: false,
  skip: skipRateLimit,
  handler: rateLimitHandler,
  keyGenerator: (req) => {
    // userId is guaranteed to be in req.body by prior validation middleware
    return `image_${req.body.userId}`;
  }
});

// Authentication rate limiter (for login attempts)
const authLimiter = rateLimit({
  windowMs: 15 * 60 * 1000, // 15 minutes
  max: 5, // limit each IP to 5 auth requests per windowMs
  message: 'Too many authentication attempts. Please try again later.',
  standardHeaders: true,
  legacyHeaders: false,
  handler: (req, res) => { // Custom handler for authLimiter
    const clientId = req.ip;
    // For auth attempts, userId might not be available yet or relevant in the same way
    const userIdForLog = req.body?.email || 'anonymous_auth_attempt'; // Log email if available

    // Track violations for auth attempts specifically by IP with timestamp
    const violationKey = `auth_${clientId}`;
    const existingData = violationStore.get(violationKey) || { count: 0, timestamp: Date.now() };
    const violations = existingData.count;
    violationStore.set(violationKey, { count: violations + 1, timestamp: Date.now() });

    logSecurity('Authentication Rate Limit Exceeded', {
      ip: clientId,
      identifier: userIdForLog, // Use email or other identifier if available
      url: req.url,
      method: req.method,
      violations: violations + 1,
      userAgent: req.get('User-Agent')
    });

    // Trigger cleanup if store is getting large
    if (violationStore.size > 100) {
      cleanupViolations();
      logger.info('Performed cleanup of auth rate limit violation store', {
        remainingEntries: violationStore.size
      });
    }

    res.status(429).json({
      success: false,
      error: {
        message: 'Too many authentication attempts. Please try again later.',
        type: 'RATE_LIMIT_ERROR',
        retryAfter: Math.ceil(req.rateLimit.resetTime / 1000), // req.rateLimit is populated by express-rate-limit
        timestamp: new Date().toISOString()
      }
    });
  }
});

// File upload rate limiter
const uploadLimiter = rateLimit({
  windowMs: 15 * 60 * 1000, // 15 minutes
  max: 20, // 20 uploads per 15 minutes
  message: 'Too many file uploads. Please try again later.',
  standardHeaders: true,
  legacyHeaders: false,
  skip: skipRateLimit,
  handler: rateLimitHandler,
  keyGenerator: (req) => {
    // For uploads, userId is expected in req.body
    const userId = req.user?.id || req.body?.userId;
    return userId ? `upload_${userId}` : `upload_${req.ip}`;
  }
});

// Video history rate limiter (more generous for data fetching)
const videoHistoryLimiter = rateLimit({
  windowMs: 5 * 60 * 1000, // 5 minutes
  max: 50, // 50 requests per 5 minutes
  // Consider if authenticated users should have a higher max here. For now, it's fixed.
  message: 'Too many video history requests. Please try again later.',
  standardHeaders: true,
  legacyHeaders: false,
  skip: skipRateLimit,
  handler: rateLimitHandler,
  keyGenerator: (req) => {
    // For video history, userId is expected in req.query
    const userId = req.user?.id || req.query?.userId;
    return userId ? `history_${userId}` : `history_${req.ip}`;
  }
});

// Presigned URL rate limiter (very generous for media loading)
const presignedUrlLimiter = rateLimit({
  windowMs: 5 * 60 * 1000, // 5 minutes
  max: 200, // 200 requests per 5 minutes
  // This route doesn't inherently take userId, so differentiation is harder without req.user
  // Sticking to IP or req.user if somehow available.
  message: 'Too many presigned URL requests. Please try again later.',
  standardHeaders: true,
  legacyHeaders: false,
  skip: skipRateLimit,
  handler: rateLimitHandler,
  keyGenerator: (req) => {
    // If req.user is populated by a future auth middleware, it will be used.
    // Otherwise, falls back to IP.
    const userId = req.user?.id;
    return userId ? `presigned_${userId}` : `presigned_${req.ip}`;
  }
});

// Adaptive rate limiter that adjusts based on server load (REMOVED as it's unused)
// const createAdaptiveLimiter = (baseMax, windowMs = 15 * 60 * 1000) => {
//   return rateLimit({
//     windowMs,
//     max: (req) => {
//       // Get current server load (simplified)
//       const memUsage = process.memoryUsage();
//       const memUsagePercent = memUsage.heapUsed / memUsage.heapTotal;
//
//       // Reduce limits if memory usage is high
//       let adjustedMax = baseMax;
//       if (memUsagePercent > 0.8) {
//         adjustedMax = Math.floor(baseMax * 0.5); // Reduce by 50%
//       } else if (memUsagePercent > 0.6) {
//         adjustedMax = Math.floor(baseMax * 0.75); // Reduce by 25%
//       }
//
//       // Apply user-based adjustments
//       if (req.user) {
//         return Math.floor(adjustedMax * 1.5); // 50% more for authenticated users
//       }
//
//       return adjustedMax;
//     },
//     standardHeaders: true,
//     legacyHeaders: false,
//     skip: skipRateLimit,
//     handler: rateLimitHandler
//   });
// };

module.exports = {
  generalLimiter,
  videoGenerationLimiter,
  imageGenerationLimiter,
  authLimiter,
  uploadLimiter,
  videoHistoryLimiter,
  presignedUrlLimiter
  // createAdaptiveLimiter // Removed from exports
};
