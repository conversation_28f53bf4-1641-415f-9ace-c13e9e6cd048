# VibeNecto Server

This server provides an API for the VibeNecto application to interact with AWS Bedrock for AI image generation.

## Prerequisites

Before running the server, ensure you have the following:

1. Node.js installed (v14 or later)
2. AWS credentials with access to AWS Bedrock and S3
3. Access to AWS Bedrock and the Titan Image Generator G1 model

## AWS Configuration

Configure your AWS credentials in the `.env` file:

```
AWS_REGION=us-east-1
AWS_ACCESS_KEY_ID=your_aws_access_key_id
AWS_SECRET_ACCESS_KEY=your_aws_secret_access_key
S3_BUCKET_NAME=your-s3-bucket-name
```

## Installation

Install the server dependencies:

```bash
cd server
npm install
```

## Running the Server

Start the development server:

```bash
npm run dev
```

This will start the server on port 3001 (or the port specified in the PORT environment variable).

## API Endpoints

### Generate Image

**Endpoint:** `POST /api/generate-image`

Generates an image using AWS Bedrock Titan Image Generator.

**Request Body:**

```json
{
  "prompt": "A beautiful landscape with mountains and a lake",
  "options": {
    "negativePrompt": "blurry, low quality",
    "width": 1024,
    "height": 1024,
    "quality": "standard",
    "numberOfImages": 1,
    "cfgScale": 8.0,
    "seed": 42
  },
  "userId": "user-id-for-s3-storage"
}
```

**Response:**

```json
{
  "success": true,
  "image": "base64-encoded-image-data",
  "s3Url": "presigned-s3-url",
  "s3Key": "s3-object-key"
}
```

### Remove Background

**Endpoint:** `POST /api/remove-background`

Removes the background from an image using AWS Bedrock.

**Request Body:**

```json
{
  "image": "base64-encoded-image-data",
  "userId": "user-id-for-s3-storage"
}
```

**Response:**

```json
{
  "success": true,
  "image": "base64-encoded-image-data",
  "s3Url": "presigned-s3-url",
  "s3Key": "s3-object-key"
}
```

### Color-Guided Generation

**Endpoint:** `POST /api/color-guided-generation`

Generates an image with specific color guidance.

**Request Body:**

```json
{
  "prompt": "A beautiful landscape with mountains and a lake",
  "options": {
    "negativePrompt": "blurry, low quality",
    "colors": ["#FF5733", "#33FF57", "#3357FF"],
    "referenceImage": "optional-base64-encoded-reference-image",
    "width": 1024,
    "height": 1024,
    "quality": "standard",
    "cfgScale": 8.0,
    "seed": 42
  },
  "userId": "user-id-for-s3-storage"
}
```

**Response:**

```json
{
  "success": true,
  "image": "base64-encoded-image-data",
  "s3Url": "presigned-s3-url",
  "s3Key": "s3-object-key"
}
```

### Image Variation

**Endpoint:** `POST /api/image-variation`

Generates variations of an existing image.

**Request Body:**

```json
{
  "options": {
    "image": "base64-encoded-image-data",
    "negativePrompt": "blurry, low quality",
    "similarityStrength": 0.7,
    "width": 1024,
    "height": 1024,
    "quality": "standard",
    "cfgScale": 8.0,
    "seed": 42
  },
  "userId": "user-id-for-s3-storage"
}
```

**Response:**

```json
{
  "success": true,
  "image": "base64-encoded-image-data",
  "s3Url": "presigned-s3-url",
  "s3Key": "s3-object-key"
}
```

### S3 Operations

**Upload to S3:** `POST /api/s3/upload`
**Generate Presigned URL:** `POST /api/s3/presigned-url`
**Delete from S3:** `POST /api/s3/delete`

## Error Handling

All endpoints return a standard error format:

```json
{
  "success": false,
  "error": "Error message"
}
```

## Troubleshooting

If you encounter issues with AWS Bedrock SDK, check the following:

1. Ensure your AWS credentials are properly configured in the .env file
2. Verify that you have access to AWS Bedrock and the Titan Image Generator G1 model
3. Check the server logs for detailed error messages
4. Verify that your S3 bucket exists and is properly configured with the correct permissions
