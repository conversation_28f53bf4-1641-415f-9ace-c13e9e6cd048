import React from 'react';
import { Palette, Paintbrush, ShoppingBag } from 'lucide-react';
import { COLOR_GUIDED_USE_CASES } from '@/constants/colorGuided';

const ColorGuidedUseCases: React.FC = () => {
  const getIcon = (iconName: string) => {
    switch (iconName) {
      case 'Palette':
        return <Palette size={24} className="mb-2 text-brand-purple" />;
      case 'Paintbrush':
        return <Paintbrush size={24} className="mb-2 text-brand-purple" />;
      case 'ShoppingBag':
        return <ShoppingBag size={24} className="mb-2 text-brand-purple" />;
      default:
        return <Palette size={24} className="mb-2 text-brand-purple" />;
    }
  };

  return (
    <div className="mb-6 px-2 py-4 border rounded-lg bg-slate-50 dark:bg-slate-800/50 border-slate-200 dark:border-slate-700">
      <h3 className="text-md font-semibold mb-3 text-center text-gray-700 dark:text-gray-200">
        How to Use Color Guided Generation
      </h3>
      
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-center">
        {COLOR_GUIDED_USE_CASES.map((useCase, index) => (
          <div 
            key={index}
            className="flex flex-col items-center p-3 rounded-md bg-slate-100 dark:bg-slate-700/50"
          >
            {getIcon(useCase.icon)}
            <p className="text-sm font-medium text-gray-800 dark:text-gray-100">
              {useCase.title}
            </p>
            <p className="text-xs text-gray-600 dark:text-gray-400">
              {useCase.description}
            </p>
          </div>
        ))}
      </div>
    </div>
  );
};

export default ColorGuidedUseCases;